import http from '@/api/http';
import { Compo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from '@/decorators';

interface dataSourceType {
  options: {
    method: string;
    headers: {
      'Content-Type'?: string;
      'x-gray-set': {
        value: string;
      };
    };
    uri: {
      value: string;
    };
  };
  id: string;
}

interface dataSourceMap {
  [x: string]: {
    load: (data?: object) => any;
  };
}

@Component
@Spectra
export default class BaseTenonPage extends Weaver() {
  state = {
    baseUrl: '',
    isSt: '',
  };
  dataSourceMap: dataSourceMap = {};
  dataSource: dataSourceType[] = [];

  created() {
    this.initBaseUrl();
    const { dataSource } = this;
    dataSource.forEach((item) => {
      const {
        id,
        options: { method, headers, uri },
      } = item;
      // eslint-disable-next-line no-eval
      const url = eval(uri.value);
      const options = {};
      if (headers && headers['Content-Type']) {
        Object.assign(options, {
          headers: {
            'Content-Type': headers['Content-Type'],
          },
        });
      }

      this.$set(this.dataSourceMap, id, {
        load: (data = {}) => {
          Object.assign(options, method === 'POST' ? { data } : { params: data });
          return http[method.toLowerCase()](url, options);
        },
      });
    });
  }

  initBaseUrl() {
    this.state.isSt = window.location.hostname.includes('.st.') ? 'https://model.sankuai.com' : '';
    this.state.baseUrl = '/webApi';
    if (this.state.isSt) {
      this.state.baseUrl = this.state.isSt + this.state.baseUrl;
    }
  }
}
