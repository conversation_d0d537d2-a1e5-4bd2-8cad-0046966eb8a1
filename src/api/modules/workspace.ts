import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1436247, catid=248429, projectId=35802, created=2024-07-12 17:09:49, last-modified=2025-05-30 15:50:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/listCandidateModel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436247)
 */
export function postWorkspaceModelListCandidateModel(
  params: model.workspace.IPostWorkspaceModelListCandidateModelParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelListCandidateModelResponse>('/webApi/workspace/model/listCandidateModel', {
    data: pick(params, [
      'creator',
      'curPage',
      'customTagValue',
      'modelFamily',
      'modelFormat',
      'modelName',
      'modelPath',
      'modelStage',
      'regularMode',
      'showCount',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1483198, catid=248429, projectId=35802, created=2024-10-24 15:17:46, last-modified=2025-05-30 15:50:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/mergeMasterApproveResult)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1483198)
 */
export function postWorkspaceModelMergeMasterApproveResult(
  params: model.workspace.IPostWorkspaceModelMergeMasterApproveResultParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelMergeMasterApproveResultResponse>(
    '/webApi/workspace/model/mergeMasterApproveResult',
    {
      data: pick(params, ['batchId', 'message', 'modelMetaIds', 'result']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1513384, catid=248429, projectId=35802, created=2025-03-06 17:26:41, last-modified=2025-05-30 15:50:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/listEvalModelNameV3)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513384)
 */
export function postWorkspaceModelListEvalModelNameV3(
  params: model.workspace.IPostWorkspaceModelListEvalModelNameV3Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelListEvalModelNameV3Response>('/webApi/workspace/model/listEvalModelNameV3', {
    data: pick(params, ['customChart', 'evalDataSet', 'evalDataSetSize', 'evalMetaId', 'evalModelName', 'modelIdRequest']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1513545, catid=248429, projectId=35802, created=2025-03-07 16:51:33, last-modified=2025-05-30 15:50:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/cancelCollectView)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513545)
 */
export function postWorkspaceModelCancelCollectView(
  params: model.workspace.IPostWorkspaceModelCancelCollectViewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelCancelCollectViewResponse>('/webApi/workspace/model/cancelCollectView', {
    params: pick(params, ['viewId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1513370, catid=248429, projectId=35802, created=2025-03-06 17:26:38, last-modified=2025-05-30 15:50:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/diffMetricsChart)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513370)
 */
export function postWorkspaceModelDiffMetricsChart(
  params: model.workspace.IPostWorkspaceModelDiffMetricsChartParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelDiffMetricsChartResponse>('/webApi/workspace/model/diffMetricsChart', {
    data: pick(params, [
      'curveModifier',
      'customChart',
      'dataSubSetChart',
      'dataSubSetIds',
      'evalDataSet',
      'evalDataSetSize',
      'evalMetaId',
      'evalModelMetricsDimension',
      'evalModelMetricsNames',
      'evalModelName',
      'evalModelNameRules',
      'heartbeatTtl',
      'ignoreInvalidData',
      'modelIdRequest',
      'nonversionDataSubset',
      'queryUuid',
      'rightActiveTab',
      'statValue',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1513378, catid=248429, projectId=35802, created=2025-03-06 17:26:40, last-modified=2025-05-30 15:50:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/getDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513378)
 */
export function getWorkspaceModelGetDetail(params: model.workspace.IGetWorkspaceModelGetDetailParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.workspace.IGetWorkspaceModelGetDetailResponse>('/webApi/workspace/model/getDetail', {
    params: pick(params, ['viewId']),
    ...options,
  });
}

/**
 * @desc id=1481897, catid=248429, projectId=35802, created=2024-10-17 20:16:45, last-modified=2025-05-30 15:50:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/submitMergeMasterInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1481897)
 */
export function postWorkspaceModelSubmitMergeMasterInfo(
  params: model.workspace.IPostWorkspaceModelSubmitMergeMasterInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelSubmitMergeMasterInfoResponse>('/webApi/workspace/model/submitMergeMasterInfo', {
    data: pick(params, ['metricsRequests', 'modelIdRequest', 'rightActiveTab']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1513365, catid=248429, projectId=35802, created=2025-03-06 17:26:37, last-modified=2025-05-30 15:50:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/collectView)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513365)
 */
export function postWorkspaceModelCollectView(
  params: model.workspace.IPostWorkspaceModelCollectViewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelCollectViewResponse>('/webApi/workspace/model/collectView', {
    params: pick(params, ['viewId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1501185, catid=248429, projectId=35802, created=2024-12-17 15:57:18, last-modified=2025-05-30 15:50:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/listDataSubSetAboutCategoryByRunSpecSet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1501185)
 */
export function getWorkspaceModelListDataSubSetAboutCategoryByRunSpecSet(
  params: model.workspace.IGetWorkspaceModelListDataSubSetAboutCategoryByRunSpecSetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.workspace.IGetWorkspaceModelListDataSubSetAboutCategoryByRunSpecSetResponse>(
    '/webApi/workspace/model/listDataSubSetAboutCategoryByRunSpecSet',
    {
      params: pick(params, ['runSpecSetName']),
      ...options,
    }
  );
}

/**
 * @desc id=1482894, catid=248429, projectId=35802, created=2024-10-23 10:27:26, last-modified=2025-05-30 15:50:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/getMergeMasterApproveContent)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1482894)
 */
export function getWorkspaceModelGetMergeMasterApproveContent(
  params: model.workspace.IGetWorkspaceModelGetMergeMasterApproveContentParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.workspace.IGetWorkspaceModelGetMergeMasterApproveContentResponse>(
    '/webApi/workspace/model/getMergeMasterApproveContent',
    {
      params: pick(params, ['batchId', 'modelMetaId', 'page']),
      ...options,
    }
  );
}

/**
 * @desc id=1436235, catid=248429, projectId=35802, created=2024-07-12 17:09:40, last-modified=2025-05-30 15:50:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/diffTensorBoard)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436235)
 */
export function postWorkspaceModelDiffTensorBoard(
  params: model.workspace.IPostWorkspaceModelDiffTensorBoardParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelDiffTensorBoardResponse>('/webApi/workspace/model/diffTensorBoard', {
    data: pick(params, ['modelAndMetaIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1436245, catid=248429, projectId=35802, created=2024-07-12 17:09:47, last-modified=2025-05-30 15:50:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/getModelList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436245)
 */
export function postWorkspaceModelGetModelList(
  params: model.workspace.IPostWorkspaceModelGetModelListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelGetModelListResponse>('/webApi/workspace/model/getModelList', {
    data: pick(params, ['modelAndMetaIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1514048, catid=248429, projectId=35802, created=2025-03-11 15:21:19, last-modified=2025-05-30 15:50:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/getLatestByUser)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1514048)
 */
export function getWorkspaceModelGetLatestByUser(options?: FlowHttpRequestOptions) {
  return http.get<model.workspace.IGetWorkspaceModelGetLatestByUserResponse>('/webApi/workspace/model/getLatestByUser', {
    ...options,
  });
}

/**
 * @desc id=1507564, catid=248429, projectId=35802, created=2025-03-03 11:22:17, last-modified=2025-05-30 15:50:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/listView)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507564)
 */
export function postWorkspaceModelListView(params: model.workspace.IPostWorkspaceModelListViewParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.workspace.IPostWorkspaceModelListViewResponse>('/webApi/workspace/model/listView', {
    data: pick(params, ['aboutMe', 'curPage', 'longTermView', 'myCollection', 'queryKeyword', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1436239, catid=248429, projectId=35802, created=2024-07-12 17:09:46, last-modified=2025-05-30 15:50:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/diffTrainParams)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436239)
 */
export function postWorkspaceModelDiffTrainParams(
  params: model.workspace.IPostWorkspaceModelDiffTrainParamsParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelDiffTrainParamsResponse>('/webApi/workspace/model/diffTrainParams', {
    data: pick(params, ['modelAndMetaIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1436236, catid=248429, projectId=35802, created=2024-07-12 17:09:41, last-modified=2025-05-30 15:50:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/diffTrainMetrics)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436236)
 */
export function postWorkspaceModelDiffTrainMetrics(
  params: model.workspace.IPostWorkspaceModelDiffTrainMetricsParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelDiffTrainMetricsResponse>('/webApi/workspace/model/diffTrainMetrics', {
    data: pick(params, ['modelAndMetaIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1436260, catid=248429, projectId=35802, created=2024-07-12 17:10:01, last-modified=2024-09-25 14:46:45
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/listEvalModelName)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436260)
 */
export function postWorkspaceModelListEvalModelName(
  params: model.workspace.IPostWorkspaceModelListEvalModelNameParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelListEvalModelNameResponse>('/webApi/workspace/model/listEvalModelName', {
    data: pick(params, ['modelAndMetaIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1436269, catid=248429, projectId=35802, created=2024-07-12 17:10:02, last-modified=2025-05-30 15:50:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/listEvalModelNameV2)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436269)
 */
export function postWorkspaceModelListEvalModelNameV2(
  params: model.workspace.IPostWorkspaceModelListEvalModelNameV2Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelListEvalModelNameV2Response>('/webApi/workspace/model/listEvalModelNameV2', {
    data: pick(params, ['customChart', 'evalDataSet', 'evalDataSetSize', 'evalMetaId', 'evalModelName', 'modelIdRequest']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1436254, catid=248429, projectId=35802, created=2024-07-12 17:09:59, last-modified=2025-05-30 15:50:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/listEvalMetricsDimension)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436254)
 */
export function postWorkspaceModelListEvalMetricsDimension(
  params: model.workspace.IPostWorkspaceModelListEvalMetricsDimensionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelListEvalMetricsDimensionResponse>(
    '/webApi/workspace/model/listEvalMetricsDimension',
    {
      data: pick(params, ['customChart', 'evalDataSet', 'evalDataSetSize', 'evalMetaId', 'evalModelName', 'modelIdRequest']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1436250, catid=248429, projectId=35802, created=2024-07-12 17:09:57, last-modified=2025-05-30 15:50:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/listEvalDataSetAndSize)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436250)
 */
export function postWorkspaceModelListEvalDataSetAndSize(
  params: model.workspace.IPostWorkspaceModelListEvalDataSetAndSizeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelListEvalDataSetAndSizeResponse>('/webApi/workspace/model/listEvalDataSetAndSize', {
    data: pick(params, ['modelAndMetaIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1436231, catid=248429, projectId=35802, created=2024-07-12 17:09:35, last-modified=2025-05-30 15:50:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/workspace/model/diffModelEvalMetricsChart)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1436231)
 */
export function postWorkspaceModelDiffModelEvalMetricsChart(
  params: model.workspace.IPostWorkspaceModelDiffModelEvalMetricsChartParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.workspace.IPostWorkspaceModelDiffModelEvalMetricsChartResponse>(
    '/webApi/workspace/model/diffModelEvalMetricsChart',
    {
      data: pick(params, [
        'curveModifier',
        'customChart',
        'dataSubSetChart',
        'dataSubSetIds',
        'evalDataSet',
        'evalDataSetSize',
        'evalMetaId',
        'evalModelMetricsDimension',
        'evalModelMetricsNames',
        'evalModelName',
        'evalModelNameRules',
        'heartbeatTtl',
        'ignoreInvalidData',
        'modelIdRequest',
        'nonversionDataSubset',
        'queryUuid',
        'rightActiveTab',
        'statValue',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
