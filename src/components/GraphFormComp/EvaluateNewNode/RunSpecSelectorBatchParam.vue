<template>
  <div>
    <mtd-button type="text-primary" @click="showModal = true" :disabled="!runSpecSetList?.length"
      >批量配置数据集
      <mtd-icon name="setting" />
    </mtd-button>
    <mtd-modal v-model="showModal" :width="1200" :closable="false" @open="batchInitParams">
      <div slot="title" class="flex justify-between items-center">
        <div class="flex items-center">
          <span style="font-weight: bold; font-size: 18px">批量配置参数</span>&nbsp;
          <span style="color: #3888ff">数据集&nbsp;{{ runSpecSetList.length }}</span>
        </div>
        <div>
          <mtd-button style="width: 80px" @click="showModal = false">取消</mtd-button>&nbsp;&nbsp;&nbsp;
          <mtd-button style="width: 80px" type="primary" @click="batchSetParams">确定</mtd-button>
        </div>
      </div>
      <context-param-definition
        style="margin-top: 10px"
        ref="contextParamDefinition"
        :needSource="false"
        :value="contextParams"
        :curSource="source"
        :canEditGlobalAlias="true"
        :bulkEdit="true"
      ></context-param-definition>
    </mtd-modal>
  </div>
</template>
<script lang="ts">
import ContextParamDefinition from '@/components/Eval/ContextParamDefinition.vue';
import { ContextParam } from '@/model/customEval';
import { EvalSource } from '@/utils';
import { cloneDeep } from 'lodash';
import { Component, Prop, Ref, Vue } from 'vue-property-decorator';
@Component({
  components: {
    ContextParamDefinition,
  },
})
export default class RunSpecSelectorBatchParam extends Vue {
  @Prop()
  runSpecSetList!: any;
  showModal = false;

  source = EvalSource.GLOBAL_ALIAS_JOB;

  contextParams: any = [];
  changedParams: any = [];
  @Ref()
  contextParamDefinition!: ContextParamDefinition;
  batchSetParams() {
    const params = this.contextParams.filter((x) => x.isBulk);
    this.runSpecSetList.forEach((runSpecSet) => {
      runSpecSet.envGlobalAliasParams = runSpecSet.envGlobalAliasParams.map((sourceParam) => {
        const matchedParam = params.find((param) => param.name === sourceParam.name);
        return matchedParam ? { ...sourceParam, value: matchedParam.value } : sourceParam;
      });
    });
    if (params.length) this.$emit('change');
    this.showModal = false;
  }

  batchInitParams() {
    const benchmarkList = cloneDeep(this.runSpecSetList);
    const initParamsMap = new Map<string, ContextParam>();
    benchmarkList.forEach((benchmark) => {
      benchmark.envGlobalAliasParams.forEach((params) => {
        if (!initParamsMap.has(params.name)) {
          initParamsMap.set(params.name, { ...params, bulkDefaultValue: params.value, referValue: null });
        } else {
          const mappedParam = initParamsMap.get(params.name)!;
          if (mappedParam.value !== params.value) {
            mappedParam.value = null;
            mappedParam.bulkDefaultValue = '<多值>';
          }
        }
      });
    });

    this.contextParams = [...initParamsMap.values()];
  }
}
</script>
<style lang="scss"></style>
