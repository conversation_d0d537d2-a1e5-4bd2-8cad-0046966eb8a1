<template>
  <div>
    <div v-if="modelInfo?.extTokenizerPath">{{ modelInfo?.extTokenizerPath }}</div>
    <div v-else>
      <mtd-button size="small" type="primary" @click="onAddExtTokenizerPath">新增 </mtd-button>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from '@/decorators';
import modelManage from '@/store/modules/modelManage';
import { useGraphModelStore } from '@/views/modelManage/graph-components/use-graph-model-store';
@Component
@Spectra
export default class CustomComp extends Weaver(modelManage) {
  @Prop()
  isEdit!: boolean;
  @Prop()
  model!: any;
  @Prop()
  context!: any;
  get modelInfo() {
    return this.experimentBaseInfo?.modelMeta;
  }
  graphModelStore = useGraphModelStore();
  onAddExtTokenizerPath() {
    window.open(
      this.$router.resolve({
        name: 'model-manage-model-detail',
        params: {
          metaId: String(this.modelInfo?.metaId ?? this.graphModelStore.modelInfo.modelMetaId),
        },
        query: {
          type: 'addExtTokenizerPath',
        },
      }).href,
      '_blank',
      'noopener'
    );
  }
}
</script>
