import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1516545, catid=246786, projectId=35802, created=2025-03-26 16:44:21, last-modified=2025-03-27 15:35:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/getJobRunSpecFlow)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516545)
 */
export function postEvalFlowJobGetJobRunSpecFlow(
  params: model.evalFlowJob.IPostEvalFlowJobGetJobRunSpecFlowParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobGetJobRunSpecFlowResponse>('/webApi/evalFlowJob/getJobRunSpecFlow', {
    data: pick(params, ['jobId', 'jobRunSpecId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1502175, catid=246786, projectId=35802, created=2024-12-23 10:31:59, last-modified=2024-12-23 14:54:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/batchKill)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502175)
 */
export function postEvalFlowJobBatchKill(params: model.evalFlowJob.IPostEvalFlowJobBatchKillParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobBatchKillResponse>('/webApi/evalFlowJob/batchKill', {
    data: pick(params, ['idList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1502176, catid=246786, projectId=35802, created=2024-12-23 10:33:12, last-modified=2024-12-23 14:53:29
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/batchRestart)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502176)
 */
export function postEvalFlowJobBatchRestart(
  params: model.evalFlowJob.IPostEvalFlowJobBatchRestartParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobBatchRestartResponse>('/webApi/evalFlowJob/batchRestart', {
    data: pick(params, ['idList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1484406, catid=246786, projectId=35802, created=2024-10-29 16:17:53, last-modified=2024-10-29 16:18:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/getFridayResource)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1484406)
 */
export function postEvalFlowJobGetFridayResource(
  params: model.evalFlowJob.IPostEvalFlowJobGetFridayResourceParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobGetFridayResourceResponse>('/webApi/evalFlowJob/getFridayResource', {
    data: pick(params, ['name']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1482077, catid=246786, projectId=35802, created=2024-10-18 20:22:21, last-modified=2024-10-18 20:23:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/listLogTypeMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1482077)
 */
export function postEvalFlowJobListLogTypeMeta(
  params: model.evalFlowJob.IPostEvalFlowJobListLogTypeMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobListLogTypeMetaResponse>('/webApi/evalFlowJob/listLogTypeMeta', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1429102, catid=246786, projectId=35802, created=2024-06-27 14:56:45, last-modified=2025-04-10 14:29:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/preview)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1429102)
 */
export function postEvalFlowJobPreview(params: model.evalFlowJob.IPostEvalFlowJobPreviewParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobPreviewResponse>('/webApi/evalFlowJob/preview', {
    data: pick(params, ['runSpecSetId', 'benchmarkList', 'modelId', 'modelMetaId', 'modelMetaBindGroupId', 'expTaskDetailId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1481651, catid=246786, projectId=35802, created=2024-10-16 15:53:51, last-modified=2025-04-21 17:01:17
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/previewBenchmark)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1481651)
 */
export function postEvalFlowJobPreviewBenchmark(
  params: model.evalFlowJob.IPostEvalFlowJobPreviewBenchmarkParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobPreviewBenchmarkResponse>('/webApi/evalFlowJob/previewBenchmark', {
    data: pick(params, ['runSpecSetIdList', 'modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1429183, catid=246786, projectId=35802, created=2024-06-27 15:43:20, last-modified=2025-04-01 15:07:46
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1429183)
 */
export function postEvalFlowJobList(params: model.evalFlowJob.IPostEvalFlowJobListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobListResponse>('/webApi/evalFlowJob/list', {
    data: pick(params, [
      'expId',
      'expNodeId',
      'expRunId',
      'expRunAttemptId',
      'expTaskDetailId',
      'runSpecSetId',
      'flowId',
      'flowVersion',
      'modelMetaId',
      'modelId',
      'modelMetaName',
      'modelName',
      'evalModelFamily',
      'evalModelName',
      'statusList',
      'taskUser',
      'batchNumberList',
      'offset',
      'limit',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426995, catid=246786, projectId=35802, created=2024-06-25 18:46:13, last-modified=2025-03-26 16:44:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426995)
 */
export function postEvalFlowJobGet(params: model.evalFlowJob.IPostEvalFlowJobGetParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobGetResponse>('/webApi/evalFlowJob/get', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1430039, catid=246786, projectId=35802, created=2024-07-02 10:20:07, last-modified=2025-04-01 15:25:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/getStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1430039)
 */
export function postEvalFlowJobGetStatus(params: model.evalFlowJob.IPostEvalFlowJobGetStatusParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobGetStatusResponse>('/webApi/evalFlowJob/getStatus', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1469419, catid=246786, projectId=35802, created=2024-09-09 11:04:51, last-modified=2024-09-12 17:24:35
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/getJobGroupStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1469419)
 */
export function postEvalFlowJobGetJobGroupStatus(
  params: model.evalFlowJob.IPostEvalFlowJobGetJobGroupStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobGetJobGroupStatusResponse>('/webApi/evalFlowJob/getJobGroupStatus', {
    data: pick(params, ['expTaskDetailId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1469319, catid=246786, projectId=35802, created=2024-09-06 17:19:51, last-modified=2024-10-16 16:29:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/submitJobGroup)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1469319)
 */
export function postEvalFlowJobSubmitJobGroup(
  params: model.evalFlowJob.IPostEvalFlowJobSubmitJobGroupParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobSubmitJobGroupResponse>('/webApi/evalFlowJob/submitJobGroup', {
    data: pick(params, [
      'expId',
      'expNodeId',
      'expRunId',
      'expRunAttemptId',
      'expTaskDetailId',
      'modelId',
      'modelMetaId',
      'benchmarkList',
      'evalModelFamily',
      'evalModelName',
      'hadoopUser',
      'taskUser',
      'cpuJobQueue',
      'gpuJobQueue',
      'submitInfoList',
      'scheduleStrategy',
      'slaClass',
      'coreGitBranch',
      'pluginGitBranch',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1429190, catid=246786, projectId=35802, created=2024-06-27 15:44:00, last-modified=2024-06-27 15:44:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/kill)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1429190)
 */
export function postEvalFlowJobKill(params: model.evalFlowJob.IPostEvalFlowJobKillParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobKillResponse>('/webApi/evalFlowJob/kill', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1469432, catid=246786, projectId=35802, created=2024-09-09 11:26:31, last-modified=2024-09-12 17:17:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/killJobGroup)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1469432)
 */
export function postEvalFlowJobKillJobGroup(
  params: model.evalFlowJob.IPostEvalFlowJobKillJobGroupParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobKillJobGroupResponse>('/webApi/evalFlowJob/killJobGroup', {
    data: pick(params, ['expTaskDetailId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1429177, catid=246786, projectId=35802, created=2024-06-27 15:39:52, last-modified=2024-06-27 15:40:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/setSlaClass)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1429177)
 */
export function postEvalFlowJobSetSlaClass(
  params: model.evalFlowJob.IPostEvalFlowJobSetSlaClassParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobSetSlaClassResponse>('/webApi/evalFlowJob/setSlaClass', {
    data: pick(params, ['id', 'slaClass']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1430230, catid=246786, projectId=35802, created=2024-07-02 15:36:02, last-modified=2024-07-02 15:40:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/setConsumeClusterExpectConsumerCount)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1430230)
 */
export function postEvalFlowJobSetConsumeClusterExpectConsumerCount(
  params: model.evalFlowJob.IPostEvalFlowJobSetConsumeClusterExpectConsumerCountParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobSetConsumeClusterExpectConsumerCountResponse>(
    '/webApi/evalFlowJob/setConsumeClusterExpectConsumerCount',
    {
      data: pick(params, ['jobId', 'clusterName', 'expectConsumerCount']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1427727, catid=246786, projectId=35802, created=2024-06-26 16:54:36, last-modified=2024-10-18 20:39:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJob/listLog)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1427727)
 */
export function postEvalFlowJobListLog(params: model.evalFlowJob.IPostEvalFlowJobListLogParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlowJob.IPostEvalFlowJobListLogResponse>('/webApi/evalFlowJob/listLog', {
    data: pick(params, [
      'jobId',
      'vertexAnchor',
      'consumerDefineRootAnchor',
      'clusterName',
      'consumerId',
      'jobRunSpecId',
      'dataUnitId',
      'runSpecSetId',
      'runSpecId',
      'instanceId',
      'logLevel',
      'eventType',
      'eventSubType',
      'searchFilter',
      'offset',
      'limit',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
