import * as model from '@/model';
import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import http from '../http';

/**
 * @desc id=1417628, catid=227034, projectId=35802, created=2024-06-05 18:28:45, last-modified=2024-06-05 18:29:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/exportDataSubSet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1417628)
 */
export function postModelEvalDataSubSetExportDataSubSet(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetExportDataSubSetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetExportDataSubSetResponse>(
    '/webApi/modelEvalDataSubSet/exportDataSubSet',
    {
      data: pick(params, ['sourceCategoryId', 'targetCategoryId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1378450, catid=227034, projectId=35802, created=2024-03-11 14:09:36, last-modified=2024-03-11 14:33:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/batchSetSubSetCategoryRelation)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1378450)
 */
export function postModelEvalDataSubSetBatchSetSubSetCategoryRelation(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchSetSubSetCategoryRelationParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchSetSubSetCategoryRelationResponse>(
    '/webApi/modelEvalDataSubSet/batchSetSubSetCategoryRelation',
    {
      data: pick(params, [
        'metaVersionId',
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'statusFilter',
        'categoryIdsFilter',
        'subSetTags',
        'limit',
        'offset',
        'idList',
        'dataSetId',
        'categoryList',
        'actionType',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1419004, catid=227034, projectId=35802, created=2024-06-11 14:10:32, last-modified=2024-06-11 15:44:28
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/instanceList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1419004)
 */
export function postModelEvalDataSubSetInstanceList(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetInstanceListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetInstanceListResponse>('/webApi/modelEvalDataSubSet/instanceList', {
    data: pick(params, ['dataSubSetId', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1425519, catid=227034, projectId=35802, created=2024-06-21 09:58:38, last-modified=2024-06-21 09:58:38
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/simpleList_1718935118094)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425519)
 */
export function postModelEvalDataSubSetSimpleList1718935118094(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetSimpleList1718935118094Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetSimpleList1718935118094Response>(
    '/webApi/modelEvalDataSubSet/simpleList_1718935118094',
    {
      data: pick(params, [
        'metaVersionId',
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'statusFilter',
        'categoryIdsFilter',
        'subSetTags',
        'limit',
        'offset',
        'idList',
        'dataSetId',
        'stats',
        'categoryTags',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1378494, catid=227034, projectId=35802, created=2024-03-11 14:35:33, last-modified=2024-03-12 19:42:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/batchUpsertField)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1378494)
 */
export function postModelEvalDataSubSetBatchUpsertField(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchUpsertFieldParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchUpsertFieldResponse>(
    '/webApi/modelEvalDataSubSet/batchUpsertField',
    {
      data: pick(params, [
        'metaVersionId',
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'statusFilter',
        'categoryIdsFilter',
        'subSetTags',
        'limit',
        'offset',
        'idList',
        'dataSetId',
        'updateField',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1420879, catid=227034, projectId=35802, created=2024-06-16 17:32:25, last-modified=2024-06-16 17:38:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/batchFileUploadPreviewUnVersioned)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1420879)
 */
export function postModelEvalDataSubSetBatchFileUploadPreviewUnVersioned(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchFileUploadPreviewUnVersionedParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchFileUploadPreviewUnVersionedResponse>(
    '/webApi/modelEvalDataSubSet/batchFileUploadPreviewUnVersioned',
    {
      data: pick(params, ['file']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1390528, catid=227034, projectId=35802, created=2024-04-09 16:55:39, last-modified=2024-04-09 16:56:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/batchExportDataSubSetDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1390528)
 */
export function postModelEvalDataSubSetBatchExportDataSubSetDetail(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchExportDataSubSetDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchExportDataSubSetDetailResponse>(
    '/webApi/modelEvalDataSubSet/batchExportDataSubSetDetail',
    {
      data: pick(params, [
        'metaVersionId',
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'statusFilter',
        'categoryIdsFilter',
        'subSetTags',
        'limit',
        'offset',
        'idList',
        'dataSetId',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1296940, catid=227034, projectId=35802, created=2023-10-13 19:28:01, last-modified=2024-03-13 10:29:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/batchFileUploadPreview)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296940)
 */
export function postModelEvalDataSubSetBatchFileUploadPreview(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchFileUploadPreviewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchFileUploadPreviewResponse>(
    '/webApi/modelEvalDataSubSet/batchFileUploadPreview',
    {
      data: pick(params, ['metaVersionId', 'file']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc 直接上传原始内容
 * @desc id=1296941, catid=227034, projectId=35802, created=2023-10-13 19:28:02, last-modified=2023-11-21 14:38:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/uploadFile)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296941)
 */
export function postModelEvalDataSubSetUploadFile(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetUploadFileParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetUploadFileResponse>('/webApi/modelEvalDataSubSet/uploadFile', {
    data: pick(params, ['file']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296947, catid=227034, projectId=35802, created=2023-10-13 19:28:02, last-modified=2024-01-29 10:40:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296947)
 */
export function postModelEvalDataSubSetList(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetListResponse>('/webApi/modelEvalDataSubSet/list', {
    data: pick(params, [
      'metaVersionId',
      'keyword',
      'statTypeFilter',
      'publicStatusFilter',
      'statusFilter',
      'categoryIdsFilter',
      'subSetTags',
      'limit',
      'offset',
      'idList',
      'dataSetId',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296949, catid=227034, projectId=35802, created=2023-10-13 19:28:02, last-modified=2024-04-07 15:49:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/simpleList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296949)
 */
export function postModelEvalDataSubSetSimpleList(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetSimpleListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetSimpleListResponse>('/webApi/modelEvalDataSubSet/simpleList', {
    data: pick(params, [
      'metaVersionId',
      'keyword',
      'statTypeFilter',
      'publicStatusFilter',
      'statusFilter',
      'categoryIdsFilter',
      'subSetTags',
      'limit',
      'offset',
      'idList',
      'dataSetId',
      'stats',
      'categoryTags',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296957, catid=227034, projectId=35802, created=2023-10-13 19:28:03, last-modified=2024-06-11 14:16:35
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296957)
 */
export function postModelEvalDataSubSetUpsert(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetUpsertResponse>('/webApi/modelEvalDataSubSet/upsert', {
    data: pick(params, [
      'metaVersionId',
      'id',
      'modelEvalDataSetId',
      'weight',
      'name',
      'label',
      'statType',
      'publicStatus',
      'description',
      'tags',
      'stats',
      'files',
      'categoryList',
      'auditInfo',
      'autoEvalDataSizeOrderList',
      'bindingRunSpecList',
      'evalType',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296966, catid=227034, projectId=35802, created=2023-10-13 19:28:03, last-modified=2023-11-21 14:38:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296966)
 */
export function postModelEvalDataSubSetDelete(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetDeleteResponse>('/webApi/modelEvalDataSubSet/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296998, catid=227034, projectId=35802, created=2023-10-13 19:28:04, last-modified=2023-11-21 14:38:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/batchListFileUrl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296998)
 */
export function postModelEvalDataSubSetBatchListFileUrl(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchListFileUrlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchListFileUrlResponse>(
    '/webApi/modelEvalDataSubSet/batchListFileUrl',
    {
      data: pick(params, ['queryList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1297012, catid=227034, projectId=35802, created=2023-10-13 19:28:05, last-modified=2023-11-21 14:38:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/listTagMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297012)
 */
export function postModelEvalDataSubSetListTagMeta(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetListTagMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetListTagMetaResponse>('/webApi/modelEvalDataSubSet/listTagMeta', {
    data: pick(params, ['metaVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297028, catid=227034, projectId=35802, created=2023-10-13 19:28:05, last-modified=2024-06-11 14:15:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297028)
 */
export function postModelEvalDataSubSetGet(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetGetResponse>('/webApi/modelEvalDataSubSet/get', {
    data: pick(params, ['metaVersionId', 'id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1362990, catid=227034, projectId=35802, created=2024-01-29 17:35:27, last-modified=2024-03-11 14:35:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/batchSetSubSetTag)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1362990)
 */
export function postModelEvalDataSubSetBatchSetSubSetTag(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchSetSubSetTagParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchSetSubSetTagResponse>(
    '/webApi/modelEvalDataSubSet/batchSetSubSetTag',
    {
      data: pick(params, [
        'metaVersionId',
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'statusFilter',
        'categoryIdsFilter',
        'subSetTags',
        'limit',
        'offset',
        'idList',
        'dataSetId',
        'setSubSetTag',
        'actionType',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1364436, catid=227034, projectId=35802, created=2024-01-31 16:44:12, last-modified=2024-04-09 10:28:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSet/batchExportDataSubSetCase)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1364436)
 */
export function postModelEvalDataSubSetBatchExportDataSubSetCase(
  params: model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchExportDataSubSetCaseParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSet.IPostModelEvalDataSubSetBatchExportDataSubSetCaseResponse>(
    '/webApi/modelEvalDataSubSet/batchExportDataSubSetCase',
    {
      data: pick(params, [
        'metaVersionId',
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'statusFilter',
        'categoryIdsFilter',
        'subSetTags',
        'limit',
        'offset',
        'idList',
        'dataSetId',
        'charset',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
