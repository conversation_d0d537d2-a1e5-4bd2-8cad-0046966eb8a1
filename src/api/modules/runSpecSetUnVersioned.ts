import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1513357, catid=227021, projectId=35802, created=2025-03-06 16:06:29, last-modified=2025-03-11 15:48:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/refreshRunSpecSetWeight)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513357)
 */
export function postRunSpecSetUnVersionedRefreshRunSpecSetWeight(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedRefreshRunSpecSetWeightParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedRefreshRunSpecSetWeightResponse>(
    '/webApi/runSpecSetUnVersioned/refreshRunSpecSetWeight',
    {
      data: pick(params, ['runSpecSetId', 'dataSetIdList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1513346, catid=227021, projectId=35802, created=2025-03-06 16:01:13, last-modified=2025-03-06 16:03:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/checkBatchQuickCreate)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513346)
 */
export function postRunSpecSetUnVersionedCheckBatchQuickCreate(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedCheckBatchQuickCreateParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedCheckBatchQuickCreateResponse>(
    '/webApi/runSpecSetUnVersioned/checkBatchQuickCreate',
    {
      data: pick(params, ['dataSetIdList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1418184, catid=227021, projectId=35802, created=2024-06-06 20:35:23, last-modified=2024-09-05 14:31:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/simpleListOnline)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418184)
 */
export function postRunSpecSetUnVersionedSimpleListOnline(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedSimpleListOnlineParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedSimpleListOnlineResponse>(
    '/webApi/runSpecSetUnVersioned/simpleListOnline',
    {
      data: pick(params, [
        'keywordFilter',
        'dataSubSetIdListFilter',
        'statNameFilter',
        'flowIdExistFilter',
        'execPlanTypeFilter',
        'limit',
        'offset',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc limit/offSet 不填，默认查询全部，可用于新增评测执行单元集合时获取脚本列表使用
 * @desc id=1476145, catid=227021, projectId=35802, created=2024-09-23 19:04:29, last-modified=2024-09-23 19:12:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/listExpOldRunSpecSet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1476145)
 */
export function postRunSpecSetUnVersionedListExpOldRunSpecSet(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedListExpOldRunSpecSetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedListExpOldRunSpecSetResponse>(
    '/webApi/runSpecSetUnVersioned/listExpOldRunSpecSet',
    {
      data: pick(params, ['experimentId', 'taskId', 'version']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1513339, catid=227021, projectId=35802, created=2025-03-06 15:55:12, last-modified=2025-03-06 16:01:39
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/getDefaultRunSpecSet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513339)
 */
export function postRunSpecSetUnVersionedGetDefaultRunSpecSet(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedGetDefaultRunSpecSetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedGetDefaultRunSpecSetResponse>(
    '/webApi/runSpecSetUnVersioned/getDefaultRunSpecSet',
    {
      data: pick(params, ['name', 'scoreType', 'dataSetIdList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1418191, catid=227021, projectId=35802, created=2024-06-06 20:39:04, last-modified=2025-03-06 15:24:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418191)
 */
export function postRunSpecSetUnVersionedGet(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedGetResponse>('/webApi/runSpecSetUnVersioned/get', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1468725, catid=227021, projectId=35802, created=2024-09-05 14:50:02, last-modified=2024-11-20 15:32:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/simpleList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1468725)
 */
export function postRunSpecSetUnVersionedSimpleList(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedSimpleListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedSimpleListResponse>('/webApi/runSpecSetUnVersioned/simpleList', {
    data: pick(params, [
      'keywordFilter',
      'statusListFilter',
      'execTypeFilter',
      'benchmarkFilter',
      'requestScene',
      'publicStatusFilter',
      'flowIdExistFilter',
      'execPlanTypeFilter',
      'limit',
      'offset',
      'statSourceTypeFilter',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1418193, catid=227021, projectId=35802, created=2024-06-06 20:50:41, last-modified=2025-02-11 16:10:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418193)
 */
export function postRunSpecSetUnVersionedList(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedListResponse>('/webApi/runSpecSetUnVersioned/list', {
    data: pick(params, [
      'keywordFilter',
      'statusListFilter',
      'execTypeFilter',
      'benchmarkFilter',
      'requestScene',
      'publicStatusFilter',
      'flowIdExistFilter',
      'execPlanTypeFilter',
      'categoryId',
      'limit',
      'offset',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1483391, catid=227021, projectId=35802, created=2024-10-25 10:15:18, last-modified=2024-10-25 14:36:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/simpleGet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1483391)
 */
export function postRunSpecSetUnVersionedSimpleGet(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedSimpleGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedSimpleGetResponse>('/webApi/runSpecSetUnVersioned/simpleGet', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1418176, catid=227021, projectId=35802, created=2024-06-06 20:25:41, last-modified=2025-02-11 14:56:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418176)
 */
export function postRunSpecSetUnVersionedUpsert(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedUpsertResponse>('/webApi/runSpecSetUnVersioned/upsert', {
    data: pick(params, [
      'id',
      'name',
      'status',
      'metaVersionId',
      'execType',
      'paramOverrideConfig',
      'execPlanConfig',
      'statSourceType',
      'bindingDataSubSetList',
      'bindingRunSpecSetList',
      'ownerList',
      'benchmarkType',
      'publicStatus',
      'weight',
      'stats',
      'authType',
      'authMisList',
      'authOrgList',
      'flowId',
      'flowVersion',
      'description',
      'categoryId',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1513352, catid=227021, projectId=35802, created=2025-03-06 16:04:36, last-modified=2025-03-06 16:06:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/deleteRunSpecSet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513352)
 */
export function postRunSpecSetUnVersionedDeleteRunSpecSet(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedDeleteRunSpecSetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedDeleteRunSpecSetResponse>(
    '/webApi/runSpecSetUnVersioned/deleteRunSpecSet',
    {
      data: pick(params, ['runSpecSetId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1495998, catid=227021, projectId=35802, created=2024-11-27 18:25:51, last-modified=2025-01-13 19:10:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/quickCreate)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1495998)
 */
export function postRunSpecSetUnVersionedQuickCreate(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedQuickCreateParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedQuickCreateResponse>('/webApi/runSpecSetUnVersioned/quickCreate', {
    data: pick(params, ['name', 'scoreType', 'evaluationType', 'instanceType', 'files', 'fewshot']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1496816, catid=227021, projectId=35802, created=2024-12-02 18:15:58, last-modified=2024-12-02 18:17:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/checkQuickCreate)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1496816)
 */
export function postRunSpecSetUnVersionedCheckQuickCreate(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedCheckQuickCreateParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedCheckQuickCreateResponse>(
    '/webApi/runSpecSetUnVersioned/checkQuickCreate',
    {
      data: pick(params, ['dataSubSetId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1513523, catid=227021, projectId=35802, created=2025-03-07 16:08:17, last-modified=2025-03-07 16:09:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/getRunSpecSetByName)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513523)
 */
export function postRunSpecSetUnVersionedGetRunSpecSetByName(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedGetRunSpecSetByNameParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedGetRunSpecSetByNameResponse>(
    '/webApi/runSpecSetUnVersioned/getRunSpecSetByName',
    {
      data: pick(params, ['name']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1496706, catid=227021, projectId=35802, created=2024-12-02 15:22:34, last-modified=2024-12-02 16:59:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/deepCopy)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1496706)
 */
export function postRunSpecSetUnVersionedDeepCopy(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedDeepCopyParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedDeepCopyResponse>('/webApi/runSpecSetUnVersioned/deepCopy', {
    data: pick(params, ['id', 'name', 'replaceNameList', 'statSourceType', 'dataSubSetSuffix']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1505048, catid=227021, projectId=35802, created=2025-01-16 15:21:01, last-modified=2025-01-17 11:22:30
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/setTop)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505048)
 */
export function postRunSpecSetUnVersionedSetTop(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedSetTopParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedSetTopResponse>('/webApi/runSpecSetUnVersioned/setTop', {
    data: pick(params, ['id', 'operate']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1513277, catid=227034, projectId=35802, created=2025-03-06 11:29:01, last-modified=2025-03-11 16:40:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSetUnVersioned/getDefaultSubDataSet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1513277)
 */
export function postRunSpecSetUnVersionedGetDefaultSubDataSet(
  params: model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedGetDefaultSubDataSetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSetUnVersioned.IPostRunSpecSetUnVersionedGetDefaultSubDataSetResponse>(
    '/webApi/runSpecSetUnVersioned/getDefaultSubDataSet',
    {
      data: pick(params, ['scoreType', 'instanceType', 'fewshot']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
