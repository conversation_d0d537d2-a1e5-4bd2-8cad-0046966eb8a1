<template>
  <div>
    <mtd-radio-group v-model="type" @input="changeType">
      <mtd-radio value="path">路径</mtd-radio>
      <mtd-radio value="version">版本 </mtd-radio>
    </mtd-radio-group>
    <div v-if="type === 'path'">
      <mtd-input v-if="isEdit" :value="model" class="common-width" @change="change"> </mtd-input>
      <span v-else>{{ model }}</span>
    </div>
    <DatasetVersionSelect
      class="common-width"
      v-if="type === 'version'"
      ref="datasetVersionSelectRef"
      :versions="context.nodeParam.specialDatasets"
      :disabled="!isEdit"
      :onlyMultiple="true"
      :multipleType="true"
      :storageLocationVisible="storageLocationVisible"
      :dataStrategyVisible="dataStrategyVisible"
      :nodeParam="context.nodeParam"
      :snapshotSelectVisible="snapshotSelectVisible"
      @change="changeVersion"
      field="specialDatasets"
    />
  </div>
</template>
<script lang="ts">
import { Compo<PERSON>, <PERSON>p, S<PERSON>ra, <PERSON> } from '@/decorators';
import modelManage from '@/store/modules/modelManage';
import DatasetVersionSelect from '@/views/DataManage/PretrainDataset/components/DatasetVersionSelect.vue';
import { cloneDeep } from 'lodash';

@Component({ components: { DatasetVersionSelect } })
@Spectra
export default class BaselineData extends Weaver(modelManage) {
  @Prop()
  model!: any;
  @Prop()
  context: any;
  @Prop()
  globalContext: any;
  @Prop()
  isEdit!: boolean;
  @Prop()
  item: any;

  get storageLocationVisible() {
    return this.item?.storageLocationVisible || '';
  }
  get dataStrategyVisible() {
    return this.item?.dataStrategyVisible || '';
  }
  get snapshotSelectVisible() {
    return this.item?.snapshotSelectVisible || '';
  }
  flowDatasetsCfg = { visible: false };
  type = 'path';

  lastBaselineData = '';
  lastVersion: any = [];
  changeType() {
    const { nodeParam } = this.context;
    if (this.type === 'version') {
      this.lastBaselineData = nodeParam.specialData;
      this.$set(nodeParam, 'specialData', '');
      this.isVersionDef = false;
      if (!this.item?.noDefault) {
        this.lastVersion?.length ? this.$set(nodeParam, 'specialDatasets', cloneDeep(this.lastVersion)) : this.getDefVersion();
      }
    } else {
      this.$set(nodeParam, 'specialData', this.lastBaselineData);
      this.lastVersion = cloneDeep(nodeParam.specialDatasets);
      this.$set(nodeParam, 'specialDatasets', []);
    }
  }

  changeVersion(v) {
    const { nodeParam } = this.context;
    this.isVersionDef = false;
    if (v.length > 1) {
      this.$mtd.message.error('数据集版本只能选择一个,请重新选择');
      this.$set(nodeParam, 'specialDatasets', []);
      return;
    } else {
      this.$set(nodeParam, 'specialDatasets', cloneDeep(v));
    }
  }

  change(v) {
    this.$emit('change', v);
  }

  defVersions: any = null;
  isVersionDef: boolean = false;
  getDefVersion() {
    const { nodeParam } = this.context;
    this.isVersionDef = true;
    if (this.defVersions) {
      this.$set(nodeParam, 'specialDatasets', cloneDeep(this.defVersions));
      return;
    }
    this.action$getDataVersionTrainLatestMilestone({ type: 'sft' }).then((res) => {
      if (res) {
        this.defVersions = [
          {
            datasetId: res.datasetId,
            datasetVersionId: res.id,
          },
        ];
        this.$set(nodeParam, 'specialDatasets', cloneDeep(this.defVersions));
      }
    });
  }

  created() {
    const { nodeParam } = this.context;
    this.type = this.model ? 'path' : 'version';
    if (nodeParam.specialData) {
      this.lastBaselineData = nodeParam.specialData;
    }
    if (!nodeParam.specialDatasets?.length) {
      this.$set(nodeParam, 'specialDatasets', []);
    }
    if (this.type === 'version' && !nodeParam.specialDatasets?.length && !this.item?.noDefault) {
      this.getDefVersion();
    }
  }
}
</script>
<style lang="scss" scoped></style>
