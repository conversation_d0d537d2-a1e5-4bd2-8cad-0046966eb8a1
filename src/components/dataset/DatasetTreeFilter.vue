<template>
  <div class="dataset-tree-filter-container">
    <mtd-form ref="formRef" :model="form" class="tree-form-container" inline :label-width="86">
      <FormItems :form="form" :config="{ itemInfoList: formHeader<PERSON>eys }">
        <template slot="expandBefore">
          <slot name="formItem" />
        </template>
        <template slot="relevantSubsetListFormItem">
          <ModelMultipleRunUnitSelectEEunit
            ref="modelMultipleRunUnitSelectEEunit"
            style="width: 260px"
            v-model="form.relevantSubsetList"
            :multiple="true"
            :reportForms="true"
            :isStatNameFilter="true"
            @submit="emitChange"
            title="评测数据子集选择"
          ></ModelMultipleRunUnitSelectEEunit>
        </template>
      </FormItems>
    </mtd-form>
    <div class="search-btns">
      <slot name="btns">
        <mtd-button @click="resetForm"> 重置 </mtd-button>
      </slot>
    </div>
  </div>
</template>

<script lang="ts">
import FormItems from '@/components/dataset/FormItems.vue';
import { Component, Prop, Ref, Spectra, Weaver } from '@/decorators';
import datasetManage from '@/store/modules/datasetManage';
import ModelMultipleRunUnitSelectEEunit from '@/components/model/ModelMultipleRunUnitSelectEEunit.vue';

export interface ICategory {
  [k: string]: unknown;
  id: number;
  name: string;
}

@Component({ components: { FormItems, ModelMultipleRunUnitSelectEEunit } })
@Spectra
export default class DatasetTreeFilter extends Weaver(datasetManage) {
  @Prop({ default: () => ({}) })
  form;
  @Prop({ default: () => [] })
  showKeys;
  @Ref('formRef') formRef;

  stages: ICategory[] = [];
  types: ICategory[] = [];
  labels = [] as any;
  formHeaderKeys = [
    {
      key: 'stage',
      label: '阶段',
      type: 'select',
      clearable: true,
      options: this.stages,
      opLabel: 'name',
      opValue: 'id',
      affectKeys: ['type'],
      change: this.changeStage,
    },
    {
      key: 'type',
      label: '类型',
      type: 'select',
      clearable: true,
      options: this.types,
      opLabel: 'name',
      opValue: 'id',
      getDisabled: (form) => !form?.stage,
      change: this.emitChange,
    },
    { key: 'creator', label: '创建人', type: 'input', clearable: true, change: this.emitChange },
    {
      key: 'datasetVersionType',
      label: '标签',
      type: 'select',
      clearable: true,
      options: this.labels,
      opLabel: 'name',
      opValue: 'code',
      change: this.emitChange,
    },
    { key: 'relevantSubsetList', label: '数据子集', type: 'custom', hide: false },
  ];

  async getPretrainStages() {
    const res = await this.action$dataHubCategoryCategories({
      dimId: '2',
      parentId: '',
    });
    res && this.stages.splice(0, this.stages.length, ...res);
  }

  setRelevantSubsetList() {
    if (this.showKeys.length && !this.showKeys.includes('relevantSubsetList')) return;
    const stage = this.stages.find((item) => item.id === this.form.stage)?.name;
    if (stage === 'eval' || typeof this.form.stage !== 'number') {
      this.formHeaderKeys.find((item) => item.key === 'relevantSubsetList')!.hide = false;
    } else {
      this.formHeaderKeys.find((item) => item.key === 'relevantSubsetList')!.hide = true;
      this.form.relevantSubsetList = [];
    }
  }

  changeStage() {
    this.setRelevantSubsetList();
    this.getPretrainTypes();
  }

  async getPretrainTypes() {
    this.emitChange();
    this.form.type = undefined;
    this.types.length = 0;
    if (!this.form.stage) return;
    const res = await this.action$dataHubCategoryCategories({
      dimId: '2',
      parentId: `${this.form.stage}`,
    });
    res && this.types.splice(0, this.types.length, ...res);
  }

  async getVersionLabels() {
    const res = await this.action$dataHubDatasetVersionTypes({});
    res && this.labels.splice(0, this.labels.length, ...res);
  }

  resetForm() {
    this.formRef && this.formRef.resetFields();
    this.setRelevantSubsetList();
    this.emitChange();
  }

  emitChange() {
    this.$emit('change');
  }

  handlerShowKeys() {
    if (this.showKeys.length) {
      this.formHeaderKeys = this.formHeaderKeys.filter((item) => this.showKeys.includes(item.key));
    }
  }

  mounted() {
    this.handlerShowKeys();
    this.getPretrainStages();
    this.getVersionLabels();
  }
}
</script>

<style lang="scss">
.dataset-tree-filter-container {
  padding-top: 18px;
  .search-btns {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
  }
}
</style>
