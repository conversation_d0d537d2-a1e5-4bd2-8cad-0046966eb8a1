<script lang="ts">
import { Component, Spectra } from '@/decorators';
import GpuQueueSelector from './GpuQueueSelector.vue';

@Component
@Spectra
export default class OtherGpuQueueSelector extends GpuQueueSelector {
  isOverride(resource) {
    return resource.resourceType === 'gpuJob' && resource.consumerDefineList?.[0].vertexDefineId !== 2 && resource.queue;
  }
}
</script>
<style lang="scss"></style>
