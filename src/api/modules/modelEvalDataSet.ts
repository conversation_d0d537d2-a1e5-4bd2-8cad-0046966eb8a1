import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1296950, catid=227034, projectId=35802, created=2023-10-13 19:28:02, last-modified=2023-11-21 14:38:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSet/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296950)
 */
export function postModelEvalDataSetList(
  params: model.modelEvalDataSet.IPostModelEvalDataSetListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSet.IPostModelEvalDataSetListResponse>('/webApi/modelEvalDataSet/list', {
    data: pick(params, ['metaVersionId', 'keyword', 'statusFilter', 'limit', 'offset']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296951, catid=227034, projectId=35802, created=2023-10-13 19:28:03, last-modified=2023-12-22 15:54:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSet/simpleList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296951)
 */
export function postModelEvalDataSetSimpleList(
  params: model.modelEvalDataSet.IPostModelEvalDataSetSimpleListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSet.IPostModelEvalDataSetSimpleListResponse>('/webApi/modelEvalDataSet/simpleList', {
    data: pick(params, ['metaVersionId', 'keyword', 'statusFilter', 'limit', 'offset']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296961, catid=227034, projectId=35802, created=2023-10-13 19:28:03, last-modified=2024-03-11 10:26:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSet/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296961)
 */
export function postModelEvalDataSetUpsert(
  params: model.modelEvalDataSet.IPostModelEvalDataSetUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSet.IPostModelEvalDataSetUpsertResponse>('/webApi/modelEvalDataSet/upsert', {
    data: pick(params, ['metaVersionId', 'id', 'name', 'label', 'description', 'runtimeConfig', 'execType', 'ownerList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296970, catid=227034, projectId=35802, created=2023-10-13 19:28:04, last-modified=2023-11-21 14:38:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSet/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296970)
 */
export function postModelEvalDataSetDelete(
  params: model.modelEvalDataSet.IPostModelEvalDataSetDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSet.IPostModelEvalDataSetDeleteResponse>('/webApi/modelEvalDataSet/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297035, catid=227034, projectId=35802, created=2023-10-13 19:28:05, last-modified=2024-03-07 16:37:34
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSet/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297035)
 */
export function postModelEvalDataSetGet(
  params: model.modelEvalDataSet.IPostModelEvalDataSetGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSet.IPostModelEvalDataSetGetResponse>('/webApi/modelEvalDataSet/get', {
    data: pick(params, ['metaVersionId', 'id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297051, catid=227034, projectId=35802, created=2023-10-13 19:28:06, last-modified=2023-11-21 14:38:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSet/setStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297051)
 */
export function postModelEvalDataSetSetStatus(
  params: model.modelEvalDataSet.IPostModelEvalDataSetSetStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSet.IPostModelEvalDataSetSetStatusResponse>('/webApi/modelEvalDataSet/setStatus', {
    data: pick(params, ['id', 'status']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
