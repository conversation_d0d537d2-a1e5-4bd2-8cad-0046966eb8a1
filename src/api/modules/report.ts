import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1400985, catid=243298, projectId=35802, created=2024-04-29 15:39:53, last-modified=2024-07-25 11:45:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/report/create)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1400985)
 */
export function postReportCreate(params: model.report.IPostReportCreateParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.report.IPostReportCreateResponse>('/webApi/report/create', {
    data: pick(params, [
      'evalMetricUrl',
      'reportHeadings',
      'reportName',
      'reportType',
      'tensorboardUrl',
      'trainMetricUrl',
      'trainParamUrl',
      'workSpaceModelEvalMetricsBaseParam',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1448054, catid=243298, projectId=35802, created=2024-07-25 11:45:59, last-modified=2024-07-25 11:45:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/report/getFeConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1448054)
 */
export function getReportGetFeConfig(params: model.report.IGetReportGetFeConfigParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.report.IGetReportGetFeConfigResponse>('/webApi/report/getFeConfig', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1400986, catid=243298, projectId=35802, created=2024-04-29 15:39:56, last-modified=2024-07-25 11:45:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/report/getDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1400986)
 */
export function postReportGetDetail(params: model.report.IPostReportGetDetailParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.report.IPostReportGetDetailResponse>('/webApi/report/getDetail', {
    data: pick(params, ['reportId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1400987, catid=243298, projectId=35802, created=2024-04-29 15:39:56, last-modified=2024-07-25 11:45:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/report/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1400987)
 */
export function postReportList(params: model.report.IPostReportListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.report.IPostReportListResponse>('/webApi/report/list', {
    data: pick(params, ['curPage', 'keyword', 'queryType', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1400982, catid=243298, projectId=35802, created=2024-04-29 15:39:53, last-modified=2024-07-25 11:45:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/report/cancelCollection)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1400982)
 */
export function postReportCancelCollection(params: model.report.IPostReportCancelCollectionParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.report.IPostReportCancelCollectionResponse>('/webApi/report/cancelCollection', {
    data: pick(params, ['reportId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1400977, catid=243298, projectId=35802, created=2024-04-29 15:39:53, last-modified=2024-07-25 11:45:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/report/addCollection)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1400977)
 */
export function postReportAddCollection(params: model.report.IPostReportAddCollectionParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.report.IPostReportAddCollectionResponse>('/webApi/report/addCollection', {
    data: pick(params, ['reportId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
