import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1516969, catid=226914, projectId=35802, created=2025-04-02 11:01:24, last-modified=2025-04-03 11:16:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/listByModelIdAndLabel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516969)
 */
export function postModelEvalLlmModelListByModelIdAndLabel(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelListByModelIdAndLabelParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelListByModelIdAndLabelResponse>(
    '/webApi/modelEvalLlmModel/listByModelIdAndLabel',
    {
      data: pick(params, ['modelId', 'label']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1433311, catid=227021, projectId=35802, created=2024-07-08 14:11:03, last-modified=2024-09-20 16:39:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/simpleListNew)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433311)
 */
export function postModelEvalLlmModelSimpleListNew(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelSimpleListNewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelSimpleListNewResponse>('/webApi/modelEvalLlmModel/simpleListNew', {
    data: pick(params, ['keyword', 'limit', 'offset', 'keywordRegex']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1433269, catid=227021, projectId=35802, created=2024-07-08 14:00:34, last-modified=2024-07-08 14:03:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433269)
 */
export function postModelEvalLlmModelUpsert(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelUpsertResponse>('/webApi/modelEvalLlmModel/upsert', {
    data: pick(params, ['id', 'family', 'name', 'label']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296808, catid=227021, projectId=35802, created=2023-10-13 19:27:55, last-modified=2023-11-21 14:38:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296808)
 */
export function postModelEvalLlmModelDelete(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelDeleteResponse>('/webApi/modelEvalLlmModel/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1433273, catid=227021, projectId=35802, created=2024-07-08 14:10:09, last-modified=2024-09-20 16:40:17
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/listNew)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433273)
 */
export function postModelEvalLlmModelListNew(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelListNewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelListNewResponse>('/webApi/modelEvalLlmModel/listNew', {
    data: pick(params, ['keyword', 'limit', 'offset', 'keywordRegex']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1433271, catid=227021, projectId=35802, created=2024-07-08 14:05:08, last-modified=2024-07-08 14:06:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433271)
 */
export function postModelEvalLlmModelGet(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelGetResponse>('/webApi/modelEvalLlmModel/get', {
    data: pick(params, ['id', 'family', 'name']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1332124, catid=227021, projectId=35802, created=2023-11-23 21:08:55, last-modified=2024-09-20 10:19:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/batchClearStat)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332124)
 */
export function postModelEvalLlmModelBatchClearStat(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelBatchClearStatParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelBatchClearStatResponse>('/webApi/modelEvalLlmModel/batchClearStat', {
    data: pick(params, [
      'modelList',
      'keyword',
      'evalDataSize',
      'dataSetName',
      'dataSubSetName',
      'keywordRegex',
      'preview',
      'previewDetail',
      'auditInfo',
      'source',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296810, catid=227021, projectId=35802, created=2023-10-13 19:27:55, last-modified=2024-09-20 14:41:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296810)
 */
export function postModelEvalLlmModelList(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelListResponse>('/webApi/modelEvalLlmModel/list', {
    data: pick(params, [
      'modelList',
      'keyword',
      'keywordRegex',
      'authTypeFilter',
      'ownerFilter',
      'benchmarkingFilter',
      'tagFilters',
      'limit',
      'offset',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296822, catid=227021, projectId=35802, created=2023-10-13 19:27:56, last-modified=2024-07-08 14:03:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/upsertOld)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296822)
 */
export function postModelEvalLlmModelUpsertOld(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelUpsertOldParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelUpsertOldResponse>('/webApi/modelEvalLlmModel/upsertOld', {
    data: pick(params, [
      'id',
      'family',
      'name',
      'label',
      'authType',
      'benchmarking',
      'autoEvalDataSize',
      'ownerList',
      'viewerMisList',
      'viewerRoleList',
      'tags',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296832, catid=227021, projectId=35802, created=2023-10-13 19:27:56, last-modified=2024-09-20 16:38:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/simpleList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296832)
 */
export function postModelEvalLlmModelSimpleList(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelSimpleListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelSimpleListResponse>('/webApi/modelEvalLlmModel/simpleList', {
    data: pick(params, ['keyword', 'keywordRegex', 'authTypeFilter', 'ownerFilter', 'benchmarkingFilter', 'tagFilters', 'limit', 'offset']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296833, catid=227021, projectId=35802, created=2023-10-13 19:27:57, last-modified=2023-11-21 14:38:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/listTagMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296833)
 */
export function postModelEvalLlmModelListTagMeta(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelListTagMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelListTagMetaResponse>('/webApi/modelEvalLlmModel/listTagMeta', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296839, catid=227021, projectId=35802, created=2023-10-13 19:27:57, last-modified=2023-11-21 14:38:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/clearStat)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296839)
 */
export function postModelEvalLlmModelClearStat(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelClearStatParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelClearStatResponse>('/webApi/modelEvalLlmModel/clearStat', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1321163, catid=227021, projectId=35802, created=2023-11-03 14:45:54, last-modified=2023-11-21 14:38:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/mergeStat)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1321163)
 */
export function postModelEvalLlmModelMergeStat(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelMergeStatParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelMergeStatResponse>('/webApi/modelEvalLlmModel/mergeStat', {
    data: pick(params, ['baseModel', 'mergeModelList', 'createNewModel', 'preview', 'previewDetail']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296844, catid=227021, projectId=35802, created=2023-10-13 19:27:57, last-modified=2024-07-08 14:05:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/getOld)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296844)
 */
export function postModelEvalLlmModelGetOld(
  params: model.modelEvalLlmModel.IPostModelEvalLlmModelGetOldParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelGetOldResponse>('/webApi/modelEvalLlmModel/getOld', {
    data: pick(params, ['id', 'family', 'name']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1309807, catid=227034, projectId=35802, created=2023-10-24 11:15:53, last-modified=2023-11-21 14:38:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmModel/getModelQuerySizeLimit)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1309807)
 */
export function postModelEvalLlmModelGetModelQuerySizeLimit(options?: FlowHttpRequestOptions) {
  return http.post<model.modelEvalLlmModel.IPostModelEvalLlmModelGetModelQuerySizeLimitResponse>(
    '/webApi/modelEvalLlmModel/getModelQuerySizeLimit',
    {
      ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
    }
  );
}
