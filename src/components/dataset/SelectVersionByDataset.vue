<template>
  <div class="version-select-by-dataset">
    <mtd-form-item :label-width="config.dataset?.width || 56" :label="config.dataset?.label || '数据集'" prop="datasetId">
      <mtd-select
        v-model="value.datasetId"
        :append-to-container="false"
        remote
        :remote-method="searchDatasetList"
        :debounce="300"
        filterable
        placeholder="请选择数据集"
        :clearable="config.dataset?.clearable || false"
        @change="updateVersionList"
      >
        <mtd-option v-for="item in dataSet?.list || []" :key="item.id" :label="item.name" :value="item.id" />
      </mtd-select>
    </mtd-form-item>
    <mtd-form-item
      ref="VersionRef"
      :label-width="config.version?.width || 122"
      :label="config.version?.label || '数据集版本'"
      prop="datasetVersionId"
    >
      <mtd-select
        v-model="value.datasetVersionId"
        :disabled="!value.datasetId"
        :append-to-container="false"
        remote
        :remote-method="searchVersionList"
        :debounce="300"
        filterable
        placeholder="请选择版本"
        :multiple="config.version?.multiple || false"
        :clearable="config.version?.clearable || false"
        @change="$emit('change', value)"
      >
        <mtd-option v-for="item in dicDatasetVersionLIst" :key="item.id" :value="item.id">
          <mtd-tooltip :content="item.label" size="small" placement="top">
            <div class="version-label">{{ item.label }}</div>
          </mtd-tooltip>
        </mtd-option>
      </mtd-select>
    </mtd-form-item>
  </div>
</template>

<script lang="ts">
import { Component, Model, Prop, Ref, Spectra, Weaver } from '@/decorators';
import { CatchError } from '@/decorators/catchError';
import dataManage from '@/store/modules/dataManage';
import { transformOptions } from '@/utils/dataManage';

interface configType {
  dataset?: Record<string, number | string | object>;
  version?: Record<string, number | string>;
  [k: string]: unknown;
}

@Component
@Spectra
export default class SelectVersionByDataset extends Weaver(dataManage) {
  @Model('change')
  value!: any;
  @Prop({ default: () => ({}) })
  config!: configType;
  @Ref('VersionRef')
  VersionRef;

  dicDatasetVersionLIst: Record<string, string | number>[] = [];

  mounted() {
    this.init();
  }

  init() {
    this.searchDatasetList('');
  }

  @CatchError
  async searchDatasetList(name) {
    const data = {
      param: {
        name,
        // stage: ['test', 'local'].includes(`${process.env.CONFIG_ENV}`) ? 555 : 556,
        // type: 558,
        ...(typeof this.config.dataset?.param === 'object' ? this.config.dataset?.param : {}),
      },
      pageNumber: 1,
      pageSize: 50,
    };
    await this.action$dataSet(data);
  }

  @CatchError
  async searchVersionList(comment) {
    if (!this.value.datasetId) return;
    const res = await this.action$dataSetVersion({
      param: {
        datasetId: this.value.datasetId,
        status: 0,
        comment,
      },
      pageNumber: 1,
      pageSize: 50,
    });
    this.dicDatasetVersionLIst = transformOptions(res?.list);
  }

  async updateVersionList(v) {
    this.value.datasetVersionId = [];
    if (v) {
      await this.searchVersionList('');
      if (!this.config.version?.disAutoFit) {
        this.value.datasetVersionId = this.dicDatasetVersionLIst[0]?.id;
        this.value.datasetVersionId && this.VersionRef.clearValidate();
      }
    } else {
      this.value.datasetVersionId = undefined;
    }

    this.$emit('change', this.value);
  }
}
</script>

<style lang="scss" scoped>
.version-select-by-dataset {
  ::v-deep {
    .mtd-select-dropdown {
      width: 100%;
      .version-label {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
