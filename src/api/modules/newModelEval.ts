import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1310278, catid=228704, projectId=35802, created=2023-10-24 17:19:55, last-modified=2025-03-10 16:36:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/newModelEval/reportQueryView/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1310278)
 */
export function postNewModelEvalReportQueryViewUpsert(
  params: model.newModelEval.IPostNewModelEvalReportQueryViewUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.newModelEval.IPostNewModelEvalReportQueryViewUpsertResponse>('/webApi/newModelEval/reportQueryView/upsert', {
    data: pick(params, ['description', 'detail', 'expireType', 'id', 'metaVersionId', 'name', 'typeKey']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1310265, catid=228704, projectId=35802, created=2023-10-24 17:19:54, last-modified=2025-03-10 16:36:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/newModelEval/reportQueryView/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1310265)
 */
export function postNewModelEvalReportQueryViewDelete(
  params: model.newModelEval.IPostNewModelEvalReportQueryViewDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.newModelEval.IPostNewModelEvalReportQueryViewDeleteResponse>('/webApi/newModelEval/reportQueryView/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1392983, catid=228704, projectId=35802, created=2024-04-11 14:49:29, last-modified=2025-03-10 16:36:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/newModelEval/reportQueryView/copy)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1392983)
 */
export function postNewModelEvalReportQueryViewCopy(
  params: model.newModelEval.IPostNewModelEvalReportQueryViewCopyParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.newModelEval.IPostNewModelEvalReportQueryViewCopyResponse>('/webApi/newModelEval/reportQueryView/copy', {
    data: pick(params, ['id', 'owner']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1313563, catid=228704, projectId=35802, created=2023-10-31 16:08:52, last-modified=2025-03-10 16:36:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/newModelEval/reportQueryView/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1313563)
 */
export function postNewModelEvalReportQueryViewGet(
  params: model.newModelEval.IPostNewModelEvalReportQueryViewGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.newModelEval.IPostNewModelEvalReportQueryViewGetResponse>('/webApi/newModelEval/reportQueryView/get', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1310273, catid=228704, projectId=35802, created=2023-10-24 17:19:54, last-modified=2025-03-10 16:36:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/newModelEval/reportQueryView/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1310273)
 */
export function postNewModelEvalReportQueryViewList(
  params: model.newModelEval.IPostNewModelEvalReportQueryViewListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.newModelEval.IPostNewModelEvalReportQueryViewListResponse>('/webApi/newModelEval/reportQueryView/list', {
    data: pick(params, ['metaVersionId', 'name', 'typeKey']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
