<template>
  <div class="category-container">
    <div v-if="PARAMS?.isTableHeaderContent !== false" class="table-title">
      <span style="font-size: 12px">{{ tableContentHTML()?.statName }}细分维度 - </span>
      <mtd-tooltip style="font-size: 12px" placement="top" :content="tableContentHTML()?.evalModel?.hoverName || '-'">
        <span>评测模型：{{ tableContentHTML()?.evalModel.modelName }}</span>
      </mtd-tooltip>
      <mtd-tooltip
        v-if="tableContentHTML().baseLineModelFlag"
        style="font-size: 12px"
        placement="top"
        :content="tableContentHTML()?.baseLineModel?.hoverName || '-'"
      >
        <span style="margin-left: 10px">基线模型：{{ tableContentHTML()?.baseLineModel.modelName }}</span>
      </mtd-tooltip>
      <mtd-tooltip style="font-size: 12px" placement="top" :content="tableContentHTML()?.baseModel?.hoverName || '-'">
        <span style="margin-left: 10px">对照模型：{{ tableContentHTML()?.baseModel.modelName }}</span>
      </mtd-tooltip>
      <span style="font-size: 12px">，绝对值 / 能力达成度</span>
    </div>
    <mtd-table
      v-export-table="`模型效果报表-自定义维度`"
      bordered
      :data="getCategoryTable"
      :show-header="false"
      :cellStyle="getCellStyle"
      :loading="LS('modelEvalSummaryModelDiffTableMultiple', 'modelEvalListStatMeta', 'modelEvalIUnifiedModelDetailDiffTable')"
      loading-message=""
    >
      <mtd-table-column v-for="(item, i) in columnMaxLength" :key="i" align="center" min-width="180" :fixed="i === 0">
        <template slot-scope="{ row, $index }">
          <div class="content-container" :class="[['CATEGORY_TAG_STAT', 'CATEGORY_STAT'].includes(row[i]?.type) && 'pointer']">
            <div class="content">
              <template v-if="row[i]">
                <div v-if="row[i].type === 'TEXT'">
                  {{ row[i].data.label }}
                </div>

                <div v-else>
                  <mtd-tooltip class="content-tooltip" v-for="(J, K) in row[i].data?.unitList" :key="K" placement="top">
                    <template #content>
                      <div v-html="tableTooltipHTML(J)" />
                    </template>
                    <div>
                      <div :class="row[i].type === 'TEXT' && 'label'">
                        <div>
                          {{ row[i].data.label }}
                          <span style="font-size: 12px">{{ J.dataSetOpennessStatus }}</span>
                          <span v-if="J.hasStatSubSetCount !== J.totalSubSetCount" style="color: red; position: absolute"> * </span>
                          <EvalDataSizePopover
                            v-if="PARAMS.evalDataSizeConfiguration === true"
                            :modelListItem="getModelListItem(i, $index, K)"
                            style="margin-left: 10px"
                          />
                        </div>
                      </div>
                      <div v-if="row[i].type !== 'TEXT'">
                        <div>
                          {{ J?.evalModelList[idex]?.value === null ? '-' : J?.evalModelList[idex]?.value }}
                          <span
                            v-if="originRequest?.baseLineModel"
                            :style="{ color: getEvalBaseLineDiffValueColor(J?.evalModelList[idex]?.baseLineDiffValue) }"
                          >
                            ({{ J?.evalModelList[idex]?.baseLineDiffValue || '-' }})
                          </span>
                          <span style="margin: 0 5px">/</span>
                          {{ J?.evalModelList[idex]?.basePercentage === null ? '#DIV/0!' : J?.evalModelList[idex]?.basePercentage }}
                        </div>
                      </div>
                    </div>
                  </mtd-tooltip>
                </div>
              </template>
            </div>
          </div>
        </template>
      </mtd-table-column>
    </mtd-table>
    <mtd-announcement style="margin-top: 10px">
      <span style="color: red">*</span> 表示该模型在该评测项中存在评测数据缺失，结果可能存在误差。
    </mtd-announcement>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import ModelEval from '@/store/modules/modelEval';
import ModelEvalSummary from '@/store/modules/modelEvalSummary';
import type { CategoryTableParams } from '@/types/modelCommonComponent';
import { getAutoEvalDataSize, getSimpleListLabel1, transformArray } from '@/views/modelEval/utils';
import { cloneDeep } from 'lodash';
import EvalDataSizePopover from './EvalDataSizePopover.vue';

@Component({ components: { EvalDataSizePopover } })
@Spectra
export default class ThreeInOneCategoryTableItem extends Weaver(ModelEval, ModelEvalSummary) {
  @Prop()
  PARAMS!: CategoryTableParams;
  @Prop()
  idex!: any;
  // @Prop()
  // getModelDashboard?: any;

  originRequest: any = null;
  statNameObj: any = {};
  checkModel = false;
  get statName() {
    if (!this.modelEvalUnVersionedListStatMeta?.statMetaList?.length) return {};
    const obj: any = {};
    for (const value of this.modelEvalUnVersionedListStatMeta?.statMetaList) {
      obj[value.name] = value.label;
    }
    return obj;
  }

  get rowList() {
    return this.PARAMS.tableDataModelEffects.categoryTable?.rowList || [];
  }

  get rowMaxLength() {
    if (this.rowList.length) {
      return Math.max(...this.rowList.map((row) => row.rowIndex)) + 1;
    }

    return 0;
  }

  get columnMaxLength() {
    if (this.rowList.length) {
      return Math.max(...this.rowList.map((row) => row.cellList.map((column) => column.columnIndex)).flat()) + 1;
    }

    return 0;
  }

  get getCategoryTable() {
    return Array.from({ length: this.rowMaxLength }).map((item, index) => {
      const row = Array.from({ length: this.columnMaxLength });
      const targetRow = this.rowList.find(({ rowIndex }) => rowIndex === index);
      if (targetRow) {
        targetRow.cellList.forEach((column) => {
          row[column.columnIndex] = column;
        });
      }

      return row;
    });
  }

  // todo 有问题的
  getModelListItem(rowIndex, index, unitIndex) {
    const rowList = this.PARAMS.tableDataModelEffects?.categoryTable?.rowList || [];
    const cellList = rowList[index]?.cellList || [];
    const cellData = cellList.find((item) => item.columnIndex === rowIndex);
    const evalModel = this.originRequest?.evalModelList[this.idex] || {};
    const modelEvalStatCalculateResultList = cellData?.data?.unitList[unitIndex]?.modelEvalStatCalculateResultList || [];
    const calculateUnit =
      modelEvalStatCalculateResultList?.find((init) => init.name === evalModel.name && init.family === evalModel.family) || {};
    const calculateUnitList = calculateUnit?.calculateUnitList || [];

    return cloneDeep(calculateUnitList)?.sort((a, b) => a.evalDataSize - b.evalDataSize);
  }

  tableContentHTML() {
    try {
      // 自定义报表
      // if (this.PARAMS && typeof this.PARAMS?.tableContentHTML === 'function') {
      //   // 改变this指向仍为解决，采用禁用eslint规则
      //   // eslint-disable-next-line @typescript-eslint/no-this-alias
      //   const aa = this;
      //   const htmlContent = this.PARAMS?.tableContentHTML(aa);
      //   return htmlContent;
      // }

      const baseLineModelFlag = this.originRequest?.baseLineModel;

      return {
        baseLineModelFlag,
        statName: this.statName[this.statNameObj?.statName],
        evalModel: this.getModelName(this.originRequest?.evalModelList[this.idex]),
        baseLineModel: this.getModelName(this.originRequest?.baseLineModel),
        baseModel: this.getModelName(this.originRequest?.baseModel),
      };
    } catch (e) {
      console.error(e);
    }
  }

  tableTooltipHTML(rowData) {
    try {
      if (this.PARAMS && typeof this.PARAMS?.tableHeaderContentHTML === 'function') {
        const htmlContent = this.PARAMS.tableHeaderContentHTML(rowData);
        return htmlContent;
      }

      return `<div>评测子集数量：有效${rowData.hasStatSubSetCount}/总计${rowData.totalSubSetCount}</div>
              <div>评测模型绝对值：${rowData.evalModelList[this.idex]?.value || '-'}</div>
              <div>基线模型绝对值：${rowData.baseLineModel?.value || '-'}</div>
              <div>相较基线模型的提升量：${rowData.evalModelList[this.idex]?.baseLineDiffValue || '-'}</div>
              <div>对照模型绝对值：${rowData.baseModel?.value || '-'}</div>
              <div>参照对照模型的能力达成度：${rowData.evalModelList[this.idex]?.basePercentage || '-'}</div>
            `;
    } catch (e) {
      return `<div>评测子集数量：有效${rowData.hasStatSubSetCount}/总计${rowData.totalSubSetCount}</div>
              <div>评测模型绝对值：${rowData.evalModelList[this.idex]?.value || '-'}</div>
              <div>基线模型绝对值：${rowData.baseLineModel?.value || '-'}</div>
              <div>相较基线模型的提升量：${rowData.evalModelList[this.idex]?.baseLineDiffValue || '-'}</div>
              <div>对照模型绝对值：${rowData.baseModel?.value || '-'}</div>
              <div>参照对照模型的能力达成度：${rowData.evalModelList[this.idex]?.basePercentage || '-'}</div>
            `;
    }
  }

  getEvalBaseLineDiffValueColor(value) {
    if (value) {
      return /\+/.test(value) ? 'green' : 'red';
    }

    return 'black';
  }

  handleClick(item: any) {
    const modelList = [this.originRequest.baseModel, this.originRequest.evalModelList[this.idex]];
    if (this.originRequest.baseLineModel) {
      modelList.push(this.originRequest.baseLineModel);
    }

    const query = {
      modelName: modelList.map((i) => i?.name).join(','),
      modelFamily: modelList.map((i) => i?.family).join(','),
      statName: this.originRequest.statName,
      dataSubsetTagList: JSON.stringify(transformArray(this.originRequest.subSetTags)),
      source: 'model-eval-custom-reports',
      evalDataSize: this.originRequest.evalDataSize,
    };
    const name = 'model-data-sub-set-table';

    if (item.type === 'CATEGORY_STAT') {
      const { categoryId } = item.define;
      window.open(
        this.$router.resolve({
          name,
          query: { ...query, categoryId },
        }).href,
        '_blank'
      );
    } else if (item.type === 'CATEGORY_TAG_STAT') {
      const tag = { name: item.define.categoryTagName, value: item.define.categoryTagValue } as any;
      window.open(
        this.$router.resolve({
          name,
          query: { ...query, tag: JSON.stringify(tag) },
        }).href,
        '_blank'
      );
    }
  }

  getModelName(model) {
    if (!model) return {};
    const benchmarking = model.llmModel.modelInfo?.modelMeta.benchmarking || false;
    const modelName = benchmarking ? model.llmModel.modelInfo?.modelMeta.modelShortName : model.llmModel.modelInfo?.modelMeta.modelName;
    return {
      modelName: `${modelName} / ${model.llmModel.modelInfo?.modelCheckpoint} / ${model.llmModel.label}`,
      hoverName: model.llmModel.modelInfo?.modelMeta.modelName,
    };
  }

  getEvalDataSize(model) {
    const evalDataSize = getAutoEvalDataSize(model, this.PARAMS.tableDataModelEffects.modelList).toString();
    return this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList.find((item) => item.name === evalDataSize)?.label || '';
  }

  getCellStyle({ columnIndex, row }) {
    return { backgroundColor: row[columnIndex]?.data?.style || 'rgb(186, 205, 219)' };
  }

  async mounted() {
    const { requestFilterGroupName, filterGroupConfigList, dataSubSetFilterConfig } = this.PARAMS?.tableDataModelEffects?.originRequest;
    this.statNameObj = filterGroupConfigList?.find((item) => item?.name === requestFilterGroupName);
    if (!this.statNameObj) this.statNameObj = (filterGroupConfigList && filterGroupConfigList[0]) || dataSubSetFilterConfig;

    this.originRequest = this.PARAMS?.tableDataModelEffects?.originRequest;

    if (!this.modelEvalUnVersionedListStatMeta.statMetaList?.length) await this.action$modelEvalUnVersionedListStatMeta({});
  }
}
</script>

<style lang="scss">
.category-container {
  // margin-top: 20px;
  .title {
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.87);
    margin-bottom: 15px;
  }
  .table-title {
    min-height: 47px;
    padding: 5px;
    line-height: 36px;
    text-align: center;
    background: #f5f5f5;
    border: 1px solid rgba(0, 0, 0, 0.06);
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    font-weight: bolder;
    white-space: pre-wrap;
  }
  .mtd-table {
    td {
      padding: 0;
      .mtd-table-cell {
        height: 100%;
        padding: 0;
        .content-container {
          padding: 12px 9px;

          &.pointer {
            cursor: pointer;
            opacity: 0.9;
            transition: opacity 0.2s;
            &:hover {
              opacity: 1;
              // .content {
              //   color: #1c6cdc;
              // }
            }
          }
          // .content-tooltip:hover {
          //   color: #1c6cdc;
          // }
          .content {
            text-align: center;
            flex-direction: column;
            transition: color 0.2s;
          }
          .label {
            font-weight: bold;
          }
        }
        .clickable {
          cursor: pointer;
        }
      }
    }
  }
}
</style>
