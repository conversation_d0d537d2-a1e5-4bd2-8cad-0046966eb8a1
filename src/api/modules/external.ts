import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1312630, catid=229069, projectId=35802, created=2023-10-30 10:07:36, last-modified=2023-11-21 14:37:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/external/sendSyncMafkaMessage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1312630)
 */
export function postExternalSendSyncMafkaMessage(
  params: model.external.IPostExternalSendSyncMafkaMessageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.external.IPostExternalSendSyncMafkaMessageResponse>('/external/sendSyncMafkaMessage', {
    data: pick(params, ['type', 'id', 'specifyPartition', 'messageList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1312849, catid=229069, projectId=35802, created=2023-10-30 11:06:29, last-modified=2023-11-21 14:37:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/external/generateModelEvalLlmTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1312849)
 */
export function postExternalGenerateModelEvalLlmTask(
  params: model.external.IPostExternalGenerateModelEvalLlmTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.external.IPostExternalGenerateModelEvalLlmTaskResponse>('/external/generateModelEvalLlmTask', {
    data: pick(params, ['modelFamily', 'modelName', 'runSpecName', 'ownerList', 'taskParam', 'uploadMetricDetail']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
