<script lang="tsx">
export default {
  functional: true,
  props: {
    columnData: Object,
    column: Object,
    rowSortOrderData: Object,
    isRowSortable: Boolean,
  },
  render(h, ctx: any) {
    const { column, isRowSortable, rowSortOrderData, columnData } = ctx.props;
    const { showBlackTag, description, group, cleanAndDirty } = columnData || {};
    return (
      <div class="model-name-cell-col">
        <div style="padding: 0 6px">
          <div style="max-height: 58px !important;">
            {showBlackTag && <div class="blackTag">黑盒</div>}
            {description ? (
              <mtd-tooltip theme="light">
                <div slot="content">{description}</div>
                <span style="cursor:pointer;">
                  {column.title}
                  <i class="mtdicon mtdicon-question-circle-o"></i>
                </span>
              </mtd-tooltip>
            ) : (
              <span>{column.title}</span>
            )}
            {isRowSortable && !cleanAndDirty && (
              <span
                class={{
                  'mtd-table-sortable': true,
                  [rowSortOrderData.order]: rowSortOrderData.colId === column.id,
                }}
                onClick={() => ctx.listeners.sortClick?.(column.id, 0)}
              >
                <i class="mtd-table-sortable-icon mtd-table-sortable-ascending" />
                <i class="mtd-table-sortable-icon mtd-table-sortable-descending" />
              </span>
            )}
            {group && group?.selectGroup.valueListLength && (
              <span style="margin-left: 8px">
                <i
                  class={{
                    'mtdicon mtdicon-right': true,
                    'display-none': group.type !== 'ON',
                  }}
                  style="cursor: pointer;"
                  onClick={() => ctx.listeners.groupChange?.({ selectGroup: group.selectGroup, type: 'ON' })}
                />
                <i
                  class={{
                    'mtdicon mtdicon-left': true,
                    'display-none': group.type !== 'OFF',
                  }}
                  style="cursor: pointer;"
                  onClick={() => ctx.listeners.groupChange?.({ selectGroup: { firstIndex: -1, secondIndex: 0, title: '' }, type: 'OFF' })}
                />
              </span>
            )}
          </div>

          {cleanAndDirty && (
            <div
              style="display: flex; align-items: center; justify-content: center; margin-top: 3px"
              class={{
                cleanDirtyBox: true,
                'display-none-important': false,
              }}
            >
              <div class="cleanDirty" style="width: 50%; border-right: 1px solid rgb(216 216 216)">
                clean
                <span
                  class={{
                    'mtd-table-sortable': true,
                    [rowSortOrderData.order]: rowSortOrderData.colId === column.id && rowSortOrderData.index === 0,
                  }}
                  onClick={() => ctx.listeners.sortClick?.(column.id, 0)}
                >
                  <i class="mtd-table-sortable-icon mtd-table-sortable-ascending" />
                  <i class="mtd-table-sortable-icon mtd-table-sortable-descending" />
                </span>
              </div>
              <div class="cleanDirty" style=" width: 50%">
                dirty
                <span
                  class={{
                    'mtd-table-sortable': true,
                    [rowSortOrderData.order]: rowSortOrderData.colId === column.id && rowSortOrderData.index === 1,
                  }}
                  onClick={() => ctx.listeners.sortClick?.(column.id, 1)}
                >
                  <i class="mtd-table-sortable-icon mtd-table-sortable-ascending" />
                  <i class="mtd-table-sortable-icon mtd-table-sortable-descending" />
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  },
};
</script>
