import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1399407, catid=228704, projectId=35802, created=2024-04-25 18:26:30, last-modified=2024-08-09 20:25:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/internal/newModelEval/reportQueryView/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1399407)
 */
export function postInternalNewModelEvalReportQueryViewUpsert(
  params: model.internal.IPostInternalNewModelEvalReportQueryViewUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.internal.IPostInternalNewModelEvalReportQueryViewUpsertResponse>('/internal/newModelEval/reportQueryView/upsert', {
    data: pick(params, ['detail', 'id', 'metaVersionId', 'name', 'typeKey']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1399389, catid=228704, projectId=35802, created=2024-04-25 18:26:28, last-modified=2024-08-09 20:25:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/internal/newModelEval/reportQueryView/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1399389)
 */
export function postInternalNewModelEvalReportQueryViewDelete(
  params: model.internal.IPostInternalNewModelEvalReportQueryViewDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.internal.IPostInternalNewModelEvalReportQueryViewDeleteResponse>('/internal/newModelEval/reportQueryView/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1399385, catid=228704, projectId=35802, created=2024-04-25 18:26:26, last-modified=2024-08-09 20:25:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/internal/newModelEval/reportQueryView/copy)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1399385)
 */
export function postInternalNewModelEvalReportQueryViewCopy(
  params: model.internal.IPostInternalNewModelEvalReportQueryViewCopyParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.internal.IPostInternalNewModelEvalReportQueryViewCopyResponse>('/internal/newModelEval/reportQueryView/copy', {
    data: pick(params, ['id', 'owner']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1399393, catid=228704, projectId=35802, created=2024-04-25 18:26:29, last-modified=2024-08-09 20:25:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/internal/newModelEval/reportQueryView/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1399393)
 */
export function postInternalNewModelEvalReportQueryViewGet(
  params: model.internal.IPostInternalNewModelEvalReportQueryViewGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.internal.IPostInternalNewModelEvalReportQueryViewGetResponse>('/internal/newModelEval/reportQueryView/get', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1399401, catid=228704, projectId=35802, created=2024-04-25 18:26:29, last-modified=2024-08-09 20:25:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/internal/newModelEval/reportQueryView/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1399401)
 */
export function postInternalNewModelEvalReportQueryViewList(
  params: model.internal.IPostInternalNewModelEvalReportQueryViewListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.internal.IPostInternalNewModelEvalReportQueryViewListResponse>('/internal/newModelEval/reportQueryView/list', {
    data: pick(params, ['metaVersionId', 'name', 'typeKey']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
