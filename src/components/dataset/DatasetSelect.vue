<template>
  <div class="dataset-select-container">
    <div class="selectTitle">
      <mtd-popover trigger="hover" placement="top">
        <span class="exp-text">已选择{{ ids?.length ?? 0 }}项</span>
        <div slot="content">已选择{{ ids?.length ?? 0 }}项</div>
      </mtd-popover>
    </div>
    <mtd-button class="selectWarpBtn" @click="handleShow">选择数据集</mtd-button>
    <mtd-modal v-model="visible" :append-to-container="false" title="数据集选择" width="70%" @close="modalClose">
      <div class="modelSelectContent">
        <div class="selectHeader">
          <slot name="form" :form="form">
            <mtd-form inline :label-width="100">
              <mtd-form-item label="查询名称">
                <mtd-input v-model="form.name" clearable placeholder="查询名称" />
              </mtd-form-item>
              <mtd-form-item label="业务线ID">
                <mtd-select v-model="form.business_id" clearable>
                  <mtd-option v-for="(item, index) in businessList" :key="index" :label="item.name" :value="item.id" />
                </mtd-select>
              </mtd-form-item>
              <mtd-form-item label="任务类型">
                <mtd-select v-model="form.task_type_id" clearable :filterable="true" @change="changeTaskType">
                  <mtd-option v-for="(item, index) in dataHubDatasetTaskTypes" :key="index" :label="item.name" :value="item.id" />
                </mtd-select>
              </mtd-form-item>
              <mtd-form-item label="任务配置">
                <mtd-select v-model="form.task_config_id" :disabled="!form.task_type_id" clearable :filterable="true">
                  <mtd-option
                    v-for="(item, index) in dataHubDatasetTaskConfigs"
                    :key="index"
                    :label="`${item.id}-${item.name}`"
                    :value="item.id"
                  />
                </mtd-select>
              </mtd-form-item>
              <mtd-form-item label="数据责任人">
                <mtd-input v-model="form.creator" clearable placeholder="数据责任人" />
              </mtd-form-item>
              <mtd-form-item label="标注信息">
                <mtd-select v-model="form.tagging_status" clearable>
                  <mtd-option v-for="item in taggingStatusArr" :key="item.id" :label="item.txt" :value="item.id" />
                </mtd-select>
              </mtd-form-item>
            </mtd-form>
            <div class="reset-btn">
              <mtd-button @click="resetForm">重置</mtd-button>
            </div>
          </slot>
        </div>
        <mtd-loading :loading="loading">
          <div class="selectBody">
            <div class="bodyLeft">
              <div class="bodyInnerTitle mtd-table">
                <span>候选标注数据集</span>
                <span class="number">（{{ leftList.length }}）</span>
                <span class="mtd-table-sortable sort-icon" :class="order ? 'ascending' : 'descending'" @click="order = !order"
                  ><i class="mtd-table-sortable-icon mtd-table-sortable-ascending"></i
                  ><i class="mtd-table-sortable-icon mtd-table-sortable-descending"></i
                ></span>
                <div class="titleBtns">
                  <mtd-button style="padding: 0 5px" size="small" @click="addAllList">全部添加</mtd-button>
                  <mtd-button style="padding: 0 5px" size="small" @click="removeAllList">从已选中去除</mtd-button>
                </div>
              </div>
              <div class="listWarp">
                <recycle-scroller v-slot="{ item }" class="virtual-list" :buffer="1000" :item-size="28" key-field="id" :items="leftList">
                  <div :key="item.id" class="innerItem" :class="selectedList.includes(item.id) ? 'hasSelected' : ''">
                    <mtd-popover trigger="hover" placement="top" popper-class>
                      <div class="labelWarp">
                        <span class="listLabel">{{ `${item.name}` }}</span>
                      </div>
                      <div slot="content" class="modelListPopoverContent">
                        <span>{{ item.name }}</span>
                      </div>
                    </mtd-popover>
                    <mtd-icon-button
                      :disabled="selectedList.includes(item.id)"
                      icon="mtdicon mtdicon-arrow-right"
                      @click="
                        () => {
                          addList(item);
                        }
                      "
                    />
                  </div>
                </recycle-scroller>
              </div>
            </div>
            <div class="bodyRight">
              <div class="bodyInnerTitle">
                <span>已选标注数据集</span>
                <span class="number">（{{ selectedList.length }}）</span>
                <div class="titleBtns">
                  <mtd-button size="small" :disabled="selectedList.length === 0" @click="removeAllSelectedList">全部清除</mtd-button>
                </div>
              </div>
              <div class="listWarp selectedListWarp">
                <recycle-scroller v-slot="{ item }" class="virtual-list" :buffer="1000" :item-size="28" key-field="id" :items="rightList">
                  <div :key="item.id" class="innerItem">
                    <mtd-popover trigger="hover" placement="top" popper-class>
                      <div class="labelWarp">
                        <span class="listLabel">{{ `${item.name}` }}</span>
                      </div>
                      <div slot="content" class="modelListPopoverContent">
                        <span>{{ item.name }}</span>
                      </div>
                    </mtd-popover>
                    <mtd-icon-button
                      icon="mtdicon mtdicon-close"
                      @click="
                        () => {
                          removeList(item);
                        }
                      "
                    />
                  </div>
                </recycle-scroller>
              </div>
            </div>
          </div>
        </mtd-loading>
      </div>
      <div slot="footer" class="modalFooter">
        <mtd-button type="primary" @click="handleSureSelect">确认选择</mtd-button>
      </div>
    </mtd-modal>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Spectra, Watch, Weaver } from '@/decorators';
import { IPostDataHubDatasetPageParameter, IPostDataHubDatasetPageResponse } from '@/model/modules/dataHub';
import dataManage from '@/store/modules/dataManage';
import { businessList, taggingStatusEnum } from '@/utils/dataManage';
import { debounce } from 'lodash';
import { RecycleScroller } from 'vue-virtual-scroller';

type ListType = NonNullable<IPostDataHubDatasetPageResponse['data']>['list'];

@Component({ components: { RecycleScroller } })
@Spectra
export default class DatasetSelect extends Weaver(dataManage) {
  @Prop({ default: () => [] })
  ids!: number[];

  visible = false;
  loading = true;
  form = {
    name: '',
    business_id: '',
    task_type_id: 0,
    task_config_id: 0,
    creator: '',
    tagging_status: 0,
  } as IPostDataHubDatasetPageParameter;
  datasetList: NonNullable<ListType> = [];
  selectedList: number[] = [];
  order = true;
  datasetMap = new Map();
  curDatasetListMap = new Map();
  businessList = Object.freeze(businessList);
  taggingStatusArr = Object.keys(taggingStatusEnum).map((id) => ({
    id: Number(id),
    ...taggingStatusEnum[id],
  }));

  get leftList() {
    return JSON.parse(JSON.stringify(this.datasetList)).sort((a, b) =>
      this.order ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name)
    );
  }

  get rightList() {
    return this.selectedList.map((id) => this.datasetMap.get(id));
  }

  @Watch('form', { immediate: true, deep: true })
  changeForm() {
    this.debounceGetDatasetList();
  }

  resetForm() {
    Object.keys(this.form).forEach((key) => {
      this.form[key] = undefined;
    });
  }

  handleShow() {
    this.ids.length && this.selectedList.push(...this.ids);
    this.visible = true;
  }

  modalClose() {
    this.selectedList = [];
    this.visible = false;
  }

  addAllList() {
    this.leftList.forEach((item) => {
      this.selectedList.includes(item.id) || this.selectedList.push(item.id);
    });
  }

  removeAllList() {
    this.selectedList = this.selectedList.filter((id) => !this.curDatasetListMap.get(id));
  }

  removeAllSelectedList() {
    this.selectedList = [];
  }

  addList(item) {
    this.selectedList.push(item.id);
  }

  removeList(item) {
    const idx = this.selectedList.findIndex((id) => id === item.id);
    if (idx > -1) {
      this.selectedList.splice(idx, 1);
    }
  }

  handleSureSelect() {
    this.ids.length = 0;
    this.ids.push(...this.selectedList);
    this.modalClose();
    this.$emit('change', this.ids);
  }

  debounceGetDatasetList = debounce(this.getDatasetList, 120);
  getDatasetList() {
    this.loading = true;
    try {
      this.action$dataHubDatasetPage({ param: { ...this.form, deprecation_status: 0, status:2 }, pageSize: 99999, pageNumber: 1 })
        .then((res) => {
          this.curDatasetListMap.clear();
          const list = res?.list ?? [];
          list.forEach((item) => {
            this.datasetMap.set(item.id, item);
            this.curDatasetListMap.set(item.id, item);
          });
          this.datasetList = list;
        })
        .finally(() => {
          this.loading = false;
        });
    } catch (error) {
      this.loading = false;
      this.$mtd.message.error('数据集列表获取失败');
    }
  }

  changeTaskType(v) {
    this.form.task_config_id = '';
    v && this.action$dataHubDatasetTaskConfigs({ task_type_id: v });
  }

  mounted() {
    this.resetForm();
    // this.getDatasetList();
    this.action$dataHubDatasetTaskTypes({});
  }
}
</script>

<style lang="scss" scoped>
.dataset-select-container {
  display: flex;
  align-items: center;
  height: 32px;
  line-height: 32px;
  .selectTitle {
    line-height: 20px;
    width: calc(100% - 120px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 15px;
  }
  .selectWarpBtn {
    margin-right: 0;
  }
  .modelSelectContent {
    .selectHeader {
      display: flex;
      justify-content: space-between;
      > form {
        width: calc(100% - 80px);
        .mtd-form-item {
          width: 30%;
          flex: auto;
          margin-bottom: 12px;
        }
      }
      .reset-btn {
        display: flex;
        flex-direction: column;
        justify-content: end;
        .mtd-btn {
          margin-bottom: 12px;
        }
      }
    }
    .selectBody {
      height: 400px;
      padding: 10px 0;
      display: flex;
      justify-content: space-between;
      .bodyLeft,
      .bodyRight {
        flex-grow: 1;
        width: 45%;
        flex-shrink: 0;
        .icons {
          & > span {
            margin-left: 5px;
          }
        }
      }
      .bodyLeft {
        border: 1px solid rgba(0, 0, 0, 0.06);
        margin-right: 30px;
      }
      .bodyRight {
        border: 1px solid rgba(0, 0, 0, 0.06);
      }
      .bodyInnerTitle {
        display: flex;
        align-items: center;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        padding: 10px;
        .titleBtns {
          margin-left: auto;
          button {
            margin-left: 10px;
          }
        }
      }
      .listWarp {
        padding: 10px 0;
        height: calc(100% - 45px);
        .virtual-list {
          height: 100%;
        }
        .innerItem {
          list-style: none;
          // line-height: 28px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 10px;
          &:hover {
            background: rgba(0, 0, 0, 0.06);
          }
          .mtd-popover-rel {
            width: calc(100% - 30px);
          }
          .labelWarp {
            width: 100%;
            height: 28px;
            .listLabel {
              display: inline-block;
              width: calc(100% - 85px);
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              line-height: 28px;
            }
            .icons {
              vertical-align: top;
            }
          }
        }
        .hasSelected {
          background-color: #f0f6ff;
        }
      }
    }
  }
  .sort-icon {
    cursor: pointer;
  }
}
</style>
