/**
 * 图表提示信息处理 Worker
 */
self.addEventListener('message', function (e) {
  // 从主线程接收数据，解构获取参数和标记线数据
  const { params, __markLine__ } = e.data;
  // 处理标记线数据
  if (Array.isArray(__markLine__)) {
    __markLine__.forEach((item) => {
      // 为每个标记线添加HTML标记样式
      item.marker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${
        item.color || ''
      };"></span>`;
      // 更新数据点坐标，使用params中的第一个值作为x坐标
      item.data[0] = params[0].value[0];
      item.value = item.data;
      // 保留偏移设置
      item.offsetSetting = item.offsetSetting;
    });
  }

  // 向主线程发送处理后的数据
  // 合并params和__markLine__，并过滤掉重复的seriesName项
  self.postMessage(
    params.concat(__markLine__).filter((item, index, arr) => arr.findIndex((a) => a.seriesName === item.seriesName) === index)
  );
});
