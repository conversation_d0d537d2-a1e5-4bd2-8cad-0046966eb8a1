<template>
  <div class="ModelMultipleRunUnitSelect">
    <div class="selectTitle">
      <mtd-popover trigger="hover" placement="top">
        <span>已选择{{ value?.length }}项</span>
        <div slot="content">已选择{{ value?.length }}项</div>
      </mtd-popover>
    </div>
    <mtd-button class="selectWarpBtn" @click="handleShow">{{ `${disabled ? '查看' : '选择'}评测子集` }}</mtd-button>
    <mtd-modal class="w-full" v-model="visible" :append-to-container="false" title="评测子集选择" width="70%" @close="modalClose">
      <div class="modelSelectContent">
        <div class="selectHeader">
          <slot name="form" :form="form">
            <mtd-form inline :label-width="100">
              <mtd-form-item label="数据子集搜索">
                <mtd-input
                  v-model="form.keyword"
                  type="text"
                  suffix-icon="mtdicon mtdicon-search"
                  placeholder="数据子集/数据集-名称/显示名称"
                />
              </mtd-form-item>
              <mtd-form-item :label-width="140" label="评测执行单元集合">
                <mtd-select
                  v-model="form.runSpecSetIdFilter"
                  :loading="loading"
                  :remote="true"
                  filterable
                  :remote-method="throttledSearch"
                  :clearable="true"
                >
                  <mtd-option v-for="item in runSpecSetList" :key="item.id" :label="item.name" :value="item.id" />
                </mtd-select>
              </mtd-form-item>
            </mtd-form>
          </slot>
        </div>
        <mtd-loading :loading="bodyLoading">
          <div class="selectBody">
            <div class="bodyLeft">
              <div class="bodyInnerTitle">
                <span>候选数据子集</span>
                <span class="number">（{{ dataSubSetListTotalCount }}）</span>
                <div class="titleBtns">
                  <mtd-tooltip content="需筛选至候选数小于1000" :disabled="dataSubSetListTotalCount <= 1000" size="small" placement="top">
                    <div>
                      <mtd-button size="small" @click="addAllList" :disabled="dataSubSetListTotalCount > 1000">全部添加</mtd-button>
                      <mtd-button size="small" @click="removeAllList" :disabled="dataSubSetListTotalCount > 1000">从已选中去除</mtd-button>
                    </div>
                  </mtd-tooltip>
                </div>
              </div>
              <div class="listWarp">
                <recycle-scroller
                  v-slot="{ item }"
                  class="virtual-list"
                  :buffer="1000"
                  :item-size="28"
                  key-field="id"
                  :items="dataSubSetList"
                >
                  <div :key="item.id" class="innerItem" :class="isSelected(item) ? 'hasSelected' : ''">
                    <mtd-popover trigger="hover" placement="top" popper-class>
                      <div class="labelWarp">
                        <span class="listLabel">{{ `${item.dataSetLabel}:${item.label}` }}</span>
                        <span class="icons">
                          <mtd-tag v-if="item.publicStatus === 'BLACK'" theme="">黑盒</mtd-tag>
                        </span>
                      </div>
                      <div slot="content" class="modelListPopoverContent">
                        <span>{{ item.dataSetName }} : {{ item.name }}</span>
                      </div>
                    </mtd-popover>
                    <mtd-icon-button
                      :disabled="disabledAddBtn(item) || disabled"
                      icon="mtdicon mtdicon-arrow-right"
                      @click="
                        () => {
                          addList(item);
                        }
                      "
                    />
                  </div>
                </recycle-scroller>
              </div>
            </div>
            <div class="bodyRight">
              <div class="bodyInnerTitle">
                <span>已选数据子集</span>
                <span class="number">（{{ selectedList?.length }}）</span>
                <div class="titleBtns">
                  <mtd-button size="small" :disabled="selectedList?.length === 0 || disabled" @click="removeAllSelectedList"
                    >全部清除</mtd-button
                  >
                </div>
              </div>
              <div class="listWarp selectedListWarp">
                <recycle-scroller
                  v-slot="{ item }"
                  class="virtual-list"
                  :buffer="1000"
                  :item-size="28"
                  key-field="id"
                  :items="selectedList"
                >
                  <div :key="item.id" class="innerItem">
                    <mtd-popover trigger="hover" placement="top" popper-class>
                      <div class="labelWarp">
                        <span class="listLabel">{{ `${item.dataSetLabel}:${item.label}` }}</span>
                        <span class="icons">
                          <mtd-tag v-if="item.publicStatus === 'BLACK'" theme="">黑盒</mtd-tag>
                        </span>
                      </div>
                      <div slot="content" class="modelListPopoverContent">
                        <span>{{ item.dataSetName }} : {{ item.name }}</span>
                      </div>
                    </mtd-popover>
                    <mtd-icon-button
                      icon="mtdicon mtdicon-close"
                      :disabled="disabled"
                      @click="
                        () => {
                          removeList(item);
                        }
                      "
                    />
                  </div>
                </recycle-scroller>
              </div>
            </div>
          </div>
        </mtd-loading>
      </div>
      <div v-if="!disabled" slot="footer" class="modalFooter">
        <mtd-button type="primary" @click="handleSureSelect">确认</mtd-button>
      </div>
    </mtd-modal>
  </div>
</template>
<script lang="ts">
import { Component, Model, Prop, Spectra, Watch, Weaver } from '@/decorators';
import ModelEvalDataSet from '@/store/modules/modelEvalDataSet';
import ModelEvalDataSubSet from '@/store/modules/modelEvalDataSubSet';
import ModelEvalDataSubSetUnVersioned from '@/store/modules/modelEvalDataSubSetUnVersioned';
import RunSpecSet from '@/store/modules/runSpecSet';
import SpecialMultiSelect from '@/views/modelEval/SpecialMultiSelect.vue';
import { cloneDeep, debounce, throttle } from 'lodash';
import { RecycleScroller } from 'vue-virtual-scroller';
const initForm = {
  keyword: '',
  categoryIdsFilter: [],
  statTypeFilter: '',
  publicStatusFilter: '',
  subSetTags: '',
  typeFilter: '',
  runSpecSetIdFilter: '',
};

interface DataManageConf {
  metaVersionId: null | number;
}

@Component({ components: { RecycleScroller, SpecialMultiSelect } })
@Spectra
export default class DataSubSetSelect extends Weaver(ModelEvalDataSubSet, ModelEvalDataSet, RunSpecSet, ModelEvalDataSubSetUnVersioned) {
  @Model('change')
  value!: any;
  @Prop()
  externalMetaVersionId!: string;
  @Prop()
  dataManageConf!: DataManageConf;
  @Prop({ default: false })
  weight!: boolean | string;
  @Prop({ default: false })
  disabled!: boolean;
  @Prop()
  changeShowSelectedList!: any;
  @Prop()
  data!: any;
  @Prop({ default: true })
  jurisdictionAndForm?: boolean;
  @Prop({ default: 'BLACK' })
  publicStatus?: string;

  get publicStatusOptions() {
    if (!this.jurisdictionAndForm) {
      this.form.publicStatusFilter = 'WHITE';
      return this.$C.publicStatusOptions.filter((item) => item.key === 'WHITE');
    }

    return this.$C.publicStatusOptions;
  }

  @Watch('publicStatus')
  publicStatusChange() {
    if (this.publicStatus === 'BLACK') this.form.publicStatusFilter = '';
  }

  visible = false;
  form: Record<string, any> = cloneDeep(initForm);
  dataSubSetId: any = null;
  selectedList: any = [];
  selectedListFromValue: any = [];
  dataSubSetList: any = [];
  bodyLoading = false;
  loading = false;
  loading1 = false;
  order = true;
  dataSubSetListTotalCount = 0;
  get metaVersionId() {
    if (this.dataManageConf?.metaVersionId) {
      return Number(this.dataManageConf.metaVersionId);
    }

    if (this.externalMetaVersionId) {
      const result = JSON.parse(this.externalMetaVersionId);
      return Number(result?.metaVersionId);
    }

    return this.$store.state.metaVersion?.id || parseInt(this.$route.params.metaVersionId, 10);
  }

  get selectedListMap() {
    return this.selectedList.reduce((obj, item) => {
      if (typeof item.id !== 'undefined') {
        obj[item.id] = item;
      }

      return obj;
    }, {});
  }

  async getDataSubSetListEntities() {
    const idList = this.value.map((item) => (item?.id ? item?.id : item));
    if (!idList?.length) return [];
    const res = await this.action$versionedSimpleList({
      keyword: '',
      statTypeFilter: '',
      publicStatusFilter: '',
      categoryIdsListFilter: [],
      subSetTags: [],
      typeFilter: '',
      categoryTags: [],
      runSpecSetIdFilter: 0,
      limit: 1000,
      offset: 0,
      statNameFilter: '',
      batchKeywordList: [],
      ownerList: [],
      idList,
    });
    return res.dataSubSetList;
  }

  disabledAddBtn(item) {
    return this.hasItemInSelectedList(item);
  }

  @Watch('value', { immediate: true })
  async selectChange(o, n) {
    const selectedList = this.value ? await this.getDataSubSetListEntities() : [];

    this.selectedList = selectedList;
    this.selectedListFromValue = cloneDeep(selectedList);
  }

  @Watch('form', { deep: true })
  formChange() {
    this.dataSubSetId = null;
    this.queryAlternateDataDebounce();
  }
  @Watch('dataSubSetId', { deep: true })
  dataSubSetIdChange() {
    this.queryAlternateDataDebounce();
  }

  queryAlternateDataDebounce = debounce(this.getDataSubSetList, 500);

  hasItemInSelectedList(listItem) {
    return Boolean(this.selectedListMap[listItem.id]);
  }
  get isSelected() {
    return this.hasItemInSelectedList;
  }

  throttledSearch = throttle(this.remoteMethod, 500);
  runSpecSetList: any = [];
  requestID = 0;
  async remoteMethod(query) {
    try {
      this.loading = true;
      this.runSpecSetList = [];
      const currentRequestID = ++this.requestID;
      this.updateOptions(query, currentRequestID);
      this.loading = false;
    } catch (e) {
      this.loading = false;
      console.log(e);
    }
  }

  async updateOptions(query, requestID) {
    try {
      this.cancelRequest('runSpecSetUnVersionedSimpleList');
      await this.action$runSpecSetUnVersionedSimpleList({
        keywordFilter: query,
        limit: 1000000,
        offset: 0,
        statusListFilter: [],
        execTypeFilter: '',
        benchmarkFilter: '',
        requestScene: '',
      });
      if (this.requestID === requestID) {
        this.runSpecSetList = Object.freeze(this.runSpecSetUnVersionedSimpleList?.runSpecSetList) || [];
      }

      this.loading = false;
    } catch (error) {
      this.loading = false;
      console.error(error);
    }
  }

  handleShow() {
    this.visible = true;
  }
  handleClose() {
    this.visible = false;
  }
  modalClose() {
    this.handleClose();
    this.selectedList = this.selectedListFromValue;
  }

  addList(item) {
    if (this.hasItemInSelectedList(item)) {
      this.selectedList = [...this.selectedList];
      return;
    }

    this.selectedList.push(item);
  }
  addAllList() {
    const needAddList = this.dataSubSetList.filter((item) => !this.hasItemInSelectedList(item));
    this.selectedList = this.selectedList.concat(needAddList);
  }
  removeAllList() {
    this.selectedList = this.getMatchingItems(this.selectedList, this.versionedSimpleList.dataSubSetList);
  }

  getMatchingItems(arr1: any, arr2: any) {
    const map = new Map();
    arr2.forEach((item) => map.set(item.id, item));
    return arr1.filter((item) => !map.has(item.id));
  }

  removeAllSelectedList() {
    this.selectedList = [];
  }

  removeList(listItem) {
    this.selectedList = this.selectedList.filter((item) => item.id !== listItem.id);
  }

  convertToObj(arr) {
    const arrCopy = JSON.parse(JSON.stringify(arr));
    return arrCopy.reduce((accumulator, currentObject) => {
      const { bindingRunSpecList } = currentObject;

      const flattenedList = bindingRunSpecList?.map((item) => ({
        ...currentObject,
        ...item,
      }));

      return accumulator.concat(flattenedList);
    }, []);
  }

  handleSureSelect() {
    this.handleClose();
    const mergeAndDeduplicate = (valueArray = [] as any, selectedArray = [] as any) => {
      const resultMap = new Map();
      selectedArray.forEach((item) => resultMap.set(item.id, item));
      valueArray.forEach((item) => {
        if (resultMap.has(item.id)) {
          resultMap.set(item.id, item);
        }
      });

      return Array.from(resultMap.values());
    };

    if (this.weight) {
      this.selectedList = mergeAndDeduplicate(this.data, this.selectedList);
      this.changeShowSelectedList(this.selectedList, 'id');
    }

    this.$emit(
      'change',
      this.selectedList.map((i) => i.id)
    );
    this.$emit(
      'confirm',
      this.selectedList.map((i) => i.id)
    );
  }

  filterById(arr, value) {
    const filteredArray: any = [];

    value.forEach((val) => {
      const foundItem = arr.find((item) => item.id === (val?.id || val));
      if (foundItem) {
        filteredArray.push(foundItem);
      }
    });

    return filteredArray;
  }

  async getRunSpecList() {
    await this.action$runSpecList({
      categoryTags: [],
      statTypeFilter: '',
      typeFilter: '',
      subSetTags: [],
      keyword: '',
      limit: 1000000,
      offset: 0,
      metaVersionId: this.metaVersionId,
      statusFilter: '',
      publicStatusFilter: '',
    });

    const selectedList = this.value ? this.filterById(this.runSpecList?.runSpecList, this.value) : [];
    this.selectedList = selectedList;
    this.selectedListFromValue = cloneDeep(selectedList);
  }

  async getDataSubSetList() {
    this.bodyLoading = true;
    try {
      const categoryIdsFilter = this.form.categoryIdsFilter.filter((item) => item?.length).reduce((pre, val) => pre.concat(val[1]), []);
      const subSetTags = this.form.subSetTags === '' ? [] : [{ name: 'DATA_SUB_SET_SOURCE', values: [this.form.subSetTags] }];
      const res = await this.action$versionedSimpleList({
        keyword: this.form.keyword,
        statTypeFilter: this.form.statTypeFilter,
        publicStatusFilter: this.form.publicStatusFilter,
        categoryIdsListFilter: categoryIdsFilter,
        subSetTags,
        typeFilter: '',
        categoryTags: [],
        runSpecSetIdFilter: this.form.runSpecSetIdFilter || 0,
        limit: 1000,
        offset: 0,
      } as any);

      this.dataSubSetListTotalCount = res?.totalCount;
      this.dataSubSetList = res?.dataSubSetList || [];
      this.bodyLoading = false;
    } catch (e) {
      console.log(e);
    }
  }

  async mounted() {
    await Promise.all([this.getDataSubSetList(), this.getRunSpecList(), this.action$unVersionedListTagMeta({})]);

    this.$nextTick(() => {
      this.$emit('change', cloneDeep(this.value));
    });
  }
}
</script>
<style lang="scss" scoped>
.ModelMultipleRunUnitSelect {
  height: 32px;
  line-height: 32px;
  .selectTitle {
    margin-top: 6px;
    float: left;
    line-height: 20px;
    width: calc(100% - 180px);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 15px;
  }
  .selectWarpBtn {
    margin-right: 0;
    float: right;
  }
}

.modelSelectContent {
  .selectHeader {
    .filterBtns {
      margin-top: 10px;
      text-align: right;
    }
  }
  .selectBody {
    height: 400px;
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    .bodyLeft,
    .bodyRight {
      flex-grow: 1;
      width: 45%;
      flex-shrink: 0;
      .icons {
        & > span {
          margin-left: 5px;
        }
      }
    }
    .bodyLeft {
      border: 1px solid rgba(0, 0, 0, 0.06);
      margin-right: 30px;
    }
    .bodyRight {
      border: 1px solid rgba(0, 0, 0, 0.06);
    }
    .bodyInnerTitle {
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
      padding: 10px;
      .titleBtns {
        margin-left: auto;
        button {
          margin-left: 10px;
        }
      }
    }
    .listWarp {
      padding: 10px 0;
      height: calc(100% - 45px);
      .virtual-list {
        height: 100%;
      }
      .innerItem {
        list-style: none;
        // line-height: 28px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10px;
        &:hover {
          background: rgba(0, 0, 0, 0.06);
        }
        .mtd-popover-rel {
          width: calc(100% - 30px);
        }
        .labelWarp {
          width: 100%;
          height: 28px;
          .listLabel {
            display: inline-block;
            width: calc(100% - 85px);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            line-height: 28px;
          }
          .icons {
            vertical-align: top;
          }
        }
      }
      .hasSelected {
        background-color: #f0f6ff;
      }
    }
  }
}
</style>
