<template>
  <div class="config">
    <div
      style="margin-left: 100px"
      v-if="!context?.nodeParam?.benchmarkList?.length || !context?.nodeParam?.gpuJobQueue || !context?.nodeParam?.cpuJobQueue"
    >
      必须先选择评估数据集与资源队列
    </div>
    <div v-else-if="loading" style="height: 80px">
      <mtd-loading />
    </div>
    <h3 style="color: red" v-else-if="errMsg && errMsg !== 'Cancel'">{{ errMsg }}</h3>
    <template v-else>
      <span>
        评测配置版本：<span style="color: #9152f5; font-weight: bold">{{ new Date(evalFlowJobPreview.versionTs).toLocaleString() }}</span>
      </span>
      <mtd-button type="text-primary" @click="initData(model || [], false)" v-if="!$route.params.taskId">
        读取最新模型配置
        <mtd-icon name="refresh-o" />
      </mtd-button>
      <div style="margin-left: -100px; width: 100%">
        <global-flow-job-card
          ref="globalCard"
          :isEdit="isEdit"
          :foundBenchmark="foundBenchmark"
          :context="context"
          :sourceMetaId="sourceMetaId"
          :sourceConfigList="sourceConfigList"
          :nodeInfo="nodeInfo"
          :saveRunspecSetConfig="saveRunspecSetConfig"
          :clusterUniqueKeyMap="clusterUniqueKeyMap"
        ></global-flow-job-card>
        <div class="flow-config-grid">
          <flow-job-card
            v-for="(flowConfig, idx) of sourceConfigList"
            :key="flowConfig.flowId"
            :isEdit="isEdit"
            :foundBenchmark="foundBenchmark"
            :flowConfig="flowConfig"
            :context="context"
            :sourceMetaId="sourceMetaId"
            :saveRunspecSetConfig="(config) => saveRunspecSetConfig(idx, config)"
            :clusterUniqueKeyMap="clusterUniqueKeyMap"
            :index="idx"
            :ref="`FlowJob_${idx}`"
            @uniqueMapUpdate="uniqueMapUpdate"
            @configChanged="configChanged"
          />
        </div>
      </div>
    </template>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Ref, Spectra } from '@/decorators';
import { ContextParam } from '@/model/customEval';
import { EvalSource } from '@/utils';
import RunSpecSetConfigListBase from './Base';
import FlowJobCard from './FlowJobCard.vue';
import GlobalAliasParams from './GlobalAliasParams.vue';
import GlobalFlowJobCard from './GlobalFlowJobCard.vue';

@Component({
  components: {
    FlowJobCard,
    GlobalAliasParams,
    GlobalFlowJobCard,
  },
})
@Spectra
export default class RunSpecSetConfigList extends RunSpecSetConfigListBase {
  @Prop()
  isEdit!: boolean;

  @Ref()
  globalCard!: GlobalFlowJobCard;

  clusterUniqueKeyMap = new Map();

  runspecSetContext: (typeof this.evalFlowJobPreview.evalFlowJobPreviewList)[0] & { globalAliasParams: Array<ContextParam> } & {
    resourceConfigList: any[];
  } = {
    runSpecFlowConfigList: [],
    defaultUsedEnvGlobalAliasParams: [],
    envGlobalAliasParams: [],
    globalAliasParams: [],
    benchmarkList: [],
    resourceConfigList: [],
    globalPluginBindList: [],
    flowId: 0,
    flowVersion: 0,
  };
  sourceMetaId = 0;

  saveRunspecSetConfig(tmpIdx, changedConfig) {
    this.sourceConfigList.splice(tmpIdx, 1, changedConfig);

    // 输出值则需要滤出当前层级的
    this.exportConfigList.splice(tmpIdx, 1, {
      ...this.exportConfigList[tmpIdx],
      resourceConfigList: changedConfig.resourceConfigList,
      globalAliasParams: changedConfig.globalAliasParams.filter(
        (x) => x.source === EvalSource.GLOBAL_ALIAS_JOB && x.value !== undefined && x.value !== null
      ),
      envGlobalAliasParams: changedConfig.envGlobalAliasParams.filter(
        (x) => x.source === EvalSource.GLOBAL_ALIAS_JOB && x.value !== undefined && x.value !== null
      ),
      // @ts-ignore
      globalAnchorBindMapJson: changedConfig.globalAnchorBindMapJson,
      runSpecAnchorBindMapJson: changedConfig.runSpecAnchorBindMapJson,
    });

    // 补上json 不然后端存不住
    this.exportConfigList[tmpIdx].globalAliasParamsJson = this.exportConfigList[tmpIdx].globalAliasParams;
    this.exportConfigList[tmpIdx].envGlobalAliasParamsJson = this.exportConfigList[tmpIdx].envGlobalAliasParams;
    this.exportConfigList[tmpIdx].resourceConfigListJson = this.exportConfigList[tmpIdx].resourceConfigList;

    this.$emit('change', this.exportConfigList);
    this.sourceConfigList.forEach((v, i) => (this.$refs[`FlowJob_${i}`]?.[0] as any)?.paramsConfigModal?.resourceConfigList?.initData());
  }

  foundBenchmark(flowId) {
    const nodeInfo = (this.taskStatLists || this.experimentConfigDetail?.status || this.taskDetail?.status)?.nodeStatus?.find(
      (x) => x.nodeId === Number(this.context.id)
    );
    nodeInfo && this.updateJobList();
    const res = nodeInfo?.extraInfo?.evaluateFlowExtraInfoVos?.find((x: any) => x.flowId === flowId);
    return res;
  }

  updateEvalFlowJobList() {
    if (!this.nodeInfo?.taskDetailId) {
      this.mutation$evalFlowJobList({ evalFlowJobList: [], totalCount: 0 });
      return;
    }
    const params = {
      offset: 0,
      limit: 999,
      expTaskDetailId: this.nodeInfo?.taskDetailId,
    };
    this.action$evalFlowJobList(params as any);
  }

  updateJobListInterval: any = null;
  initJobList = false;

  updateJobList() {
    if (this.nodeInfo?.nodeStatus === 1 && !this.updateJobListInterval) {
      this.mutation$evalFlowJobList({ evalFlowJobList: [], totalCount: 0 });
      this.updateJobListInterval = setInterval(this.updateEvalFlowJobList, 10000);
    }
    if (this.initJobList) return;
    this.initJobList = true;
    this.updateEvalFlowJobList();
  }

  beforeDestroy() {
    // 清除定时器
    if (this.updateJobListInterval) {
      clearInterval(this.updateJobListInterval);
      this.updateJobListInterval = null;
    }
  }

  uniqueMapUpdate() {
    this.globalCard.genGlobalGpuList();
  }

  configChanged() {
    this.$set(this.context.nodeParam, 'nodeHasChanged', true);
  }
}
</script>
<style lang="less" scoped>
.config {
  overflow: visible;
  &-item {
    white-space: nowrap;
  }
  &-modal {
    overflow: scroll;
  }
}

.status-text {
  max-width: 230px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flow-config-grid {
  padding-top: 6px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 6px;
}
</style>
