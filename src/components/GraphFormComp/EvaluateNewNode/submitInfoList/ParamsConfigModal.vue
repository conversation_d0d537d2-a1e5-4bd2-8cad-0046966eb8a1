<template>
  <mtd-modal v-model="modal" :closable="false" class="config-modal" width="1500px">
    <div slot="title" class="flex justify-between">
      <div class="flex items-center">
        <span style="font-size: 18px; font-weight: bold"> {{ runspecSetContext.flowName }}</span
        >&nbsp; <span style="color: #3888ff"> {{ `数据集 ${runspecSetContext.benchmarkList?.length}` }} </span>&nbsp;
        <span style="color: grey">
          {{ `执行单元 ${runspecSetContext.runSpecFlowConfigList?.length}` }}
        </span>

        <mtd-radio-group v-model="activeTab" class="ml-4" :disabled="false">
          <mtd-radio-button value="params">参数设置</mtd-radio-button>
          <mtd-radio-button value="resource">资源设置</mtd-radio-button>
        </mtd-radio-group>
      </div>
      <div v-if="isEdit">
        <mtd-button style="width: 80px" @click="cancel">取消</mtd-button>&nbsp;&nbsp;&nbsp;<mtd-button
          type="primary"
          style="width: 80px"
          @click="confirm"
          >确定</mtd-button
        >
      </div>
      <div v-else><mtd-button @click="modal = false">关闭</mtd-button></div>
    </div>

    <!-- 参数设置内容 -->
    <div v-if="isLoad && modal" v-show="activeTab === 'params'">
      <h4>插件绑定预览</h4>
      <plugin-bind-preview
        ref="pluginBindPreview"
        :context="runspecSetContext"
        :isEdit="isEdit"
        :getBindsByRunSpecId="getBindsByRunSpecId"
        @change="setPluginBind"
      />
      <h4>全局环境变量列表</h4>
      <global-alias-params
        :context="runspecSetContext"
        @change="setEnvGlobalAliasParams"
        globalAliasType="ENV"
        :initParams="runspecSetContext.envGlobalAliasParams"
        :isEdit="isEdit"
      />
      <h4>全局别名参数列表</h4>
      <global-alias-params
        :context="runspecSetContext"
        @change="setGlobalAliasParams"
        globalAliasType="USER_INPUT"
        :initParams="runspecSetContext.globalAliasParams"
        :isEdit="isEdit"
      />
    </div>

    <!-- 资源设置内容 -->
    <div v-if="isLoad" v-show="activeTab === 'resource'">
      <mtd-tag v-if="resourceCoveredCount" type="pure" theme="red" style="background-color: #ff000080"
        >非默认配置:{{ resourceCoveredCount }}
      </mtd-tag>
      <mtd-button type="text-primary" v-if="isEdit && resourceCoveredCount" @click.stop="resetResourceConfig">全部配置恢复默认</mtd-button>
      <resource-config-list
        ref="resourceConfigList"
        :isEdit="isEdit"
        :context="runspecSetContext"
        :nodeContext="context"
        @change="setResourceConfigList"
      />
    </div>
  </mtd-modal>
</template>
<script lang="ts">
import { postEvalFlowJobPreview } from '@/api/modules/evalFlowJob';
import { Component, Prop, PropSync, Ref, Spectra, Watch, Weaver } from '@/decorators';
import StatusCountTag from '@/views/modelEval/FlowChartJobExecute/components/StatusCountTag.vue';
import StatusTag from '@/views/modelEval/FlowChartJobExecute/components/StatusTag.vue';
import { useGraphStore } from '@/views/modelManage/components/use-graph-store';
import { cloneDeep } from 'lodash';
import GlobalAliasParams from './GlobalAliasParams.vue';
import PluginBindPreview from './PluginBindPreview.vue';
import ResourceConfigList from './ResourceConfigList.vue';

@Component({
  components: {
    GlobalAliasParams,
    ResourceConfigList,
    PluginBindPreview,
    StatusTag,
    StatusCountTag,
  },
})
@Spectra
export default class ParamsConfigModal extends Weaver() {
  @Prop()
  isEdit!: boolean;
  @Prop()
  initConfig;
  @Prop()
  context;
  @Prop()
  sourceMetaId;
  @Prop()
  saveRunspecSetConfig;
  @PropSync('openModal')
  modal;
  @Prop({ default: () => false })
  isBulk;

  @Ref()
  resourceConfigList!: ResourceConfigList;
  @Ref()
  pluginBindPreview?: PluginBindPreview;

  activeTab = 'params';
  resourceCoveredCount = 0;

  collapse = ['1', '2', '3'];
  useGraphStore = useGraphStore();

  runspecSetContext: any = {};
  isLoad = true;

  @Watch('initConfig', { deep: true, immediate: true })
  initConfigChanged() {
    if (this.modal) return;
    this.runspecSetContext = cloneDeep(this.initConfig);
  }

  setGlobalAliasParams(val) {
    this.$set(this.runspecSetContext, 'globalAliasParams', val);
  }
  setEnvGlobalAliasParams(val) {
    this.$set(this.runspecSetContext, 'envGlobalAliasParams', val);
  }

  async getBindsByRunSpecId(runSpecSetId, runSpecId) {
    const { modelId } = this.useGraphStore.modelInfo;
    const res = await postEvalFlowJobPreview({
      benchmarkList: [
        { ...this.context.nodeParam.benchmarkList.find((x) => x.runSpecSetId === runSpecSetId), runSpecIdsFilter: [runSpecId] },
      ],
      modelId,
      modelMetaId: Number(this.sourceMetaId),
    });
    return res.data.evalFlowJobPreviewList[0].globalPluginBindList;
  }

  cancel() {
    this.modal = false;
    this.reloadComponent();
  }
  reloadComponent() {
    this.isLoad = false;
    this.$nextTick(() => {
      this.isLoad = true;
    });
  }

  confirm() {
    this.modal = false;

    this.saveRunspecSetConfig(
      cloneDeep({
        ...this.runspecSetContext,
        globalAnchorBindMapJson: JSON.stringify([...this.pluginBindPreview!.globalAnchorBindMap.entries()]),
        runSpecAnchorBindMapJson: JSON.stringify([...this.pluginBindPreview!.runSpecAnchorBindMap.entries()]),
      })
    );
  }

  setPluginBind(val) {
    this.$set(this.runspecSetContext, 'globalAnchorBindMapJson', val.globalAnchorBindMap);
    this.$set(this.runspecSetContext, 'runSpecAnchorBindMapJson', val.runSpecAnchorBindMap);
  }

  setResourceConfigList(val, defaultVal) {
    this.updateCoveredCount(val);
    this.$set(this.runspecSetContext, 'resourceConfigList', val);
    this.$set(this.runspecSetContext, 'defaultResourceConfigList', defaultVal);
    this.$emit('init', val, defaultVal);
  }

  resetResourceConfig() {
    this.$set(
      this.runspecSetContext,
      'resourceConfigList',
      this.runspecSetContext.resourceConfigList.map(
        ({ queue, workerCpuCore, workerCpuMem, workerGpuCore, workerInstanceCount, ...ext }) => ({ ...ext })
      )
    );
    this.resourceConfigList.initData();
  }

  updateCoveredCount(newResource) {
    const checkParamList = ['queue', 'workerCpuCore', 'workerCpuMem', 'workerGpuCore', 'workerInstanceCount'];
    this.resourceCoveredCount = newResource.reduce((prevCount, curResource) => {
      let curCount = prevCount;
      checkParamList.forEach((key) => curResource[key] && curCount++);
      return curCount;
    }, 0);
  }
}
</script>
<style lang="less" scoped></style>
