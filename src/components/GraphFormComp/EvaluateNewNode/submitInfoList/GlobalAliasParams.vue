<template>
  <div>
    <template v-if="coveredParams.length">
      <mtd-tag type="pure" theme="red" style="background-color: #ff000080">非默认配置:{{ coveredParams.length }}</mtd-tag>
      <mtd-button v-if="isEdit" type="text-primary" @click="setAllDefault">全部变量恢复默认</mtd-button>
    </template>
    <mtd-loading v-if="loading" />
    <context-param-definition
      v-else
      :needSource="false"
      :value="contextParamsDefine"
      :curSource="source"
      :canEditGlobalAlias="true"
      :disabled="!isEdit"
      :jobHighlighted="true"
    ></context-param-definition>
  </div>
</template>
<script lang="ts">
import { evalCustomPluginGlobalAlias } from '@/api';
import ContextParamDefinition from '@/components/Eval/ContextParamDefinition.vue';
import { Component, Prop, ProvideReactive, <PERSON>pectra, <PERSON>, Weaver } from '@/decorators';
import { BindType, ContextParam, PluginBind } from '@/model/customEval';
import evalCustomPlugin from '@/store/modules/evalCustomPlugin';
import evalFlowJob from '@/store/modules/evalFlowJob';
import { EvalSource } from '@/utils';
import { flatten } from 'lodash';
import { mergeParamDefinition } from '../utils';

@Component({
  components: { ContextParamDefinition },
})
@Spectra
export default class GlobalAliasParams extends Weaver(evalCustomPlugin, evalFlowJob) {
  @Prop()
  context;
  @Prop()
  globalAliasType!: string;
  @Prop()
  initParams!: ContextParam[];
  @Prop()
  isEdit!: boolean;
  @ProvideReactive('modelMetaSize')
  modelMetaSize = '';

  source = EvalSource.GLOBAL_ALIAS_JOB;
  coveredParams: ContextParam[] = [];

  contextParamsDefine: ContextParam[] = [];

  globalParamsDefineMap: Map<string, ContextParam> = new Map();

  async created() {
    await this.getGlobalAliasDefineMap();
    this.initData();
  }
  loading = true;

  async getGlobalAliasDefineMap() {
    const res = await evalCustomPluginGlobalAlias.postEvalCustomPluginGlobalAliasList({});
    this.globalParamsDefineMap = new Map(
      res.data.evalCustomPluginGlobalAliasList.map((define) => [
        define.name,
        {
          ...define,
          source: EvalSource.GLOBAL_ALIAS_DEFINE,
          defaultSource: EvalSource.GLOBAL_ALIAS_DEFINE,
          defaultValue: define.value,
        },
      ])
    );
  }

  @Watch('context.globalAnchorBindMapJson', { immediate: true })
  @Watch('context.runSpecAnchorBindMapJson', { immediate: true })
  async initData() {
    // 生成每个runSpec的所有extended bindList后，打平
    const globalAnchorBindMap: Map<string, PluginBind[]> = new Map(JSON.parse(this.context.globalAnchorBindMapJson || '[]'));
    const runSpecAnchorBindMap: Map<string, PluginBind[]> = new Map(JSON.parse(this.context.runSpecAnchorBindMapJson || '[]'));
    const pluginBindList = flatten([...globalAnchorBindMap.values(), ...runSpecAnchorBindMap.values()]);
    // 获取所有全局变量
    const valueMap = new Map();
    for (const bind of pluginBindList) {
      if (bind.bindType === BindType.COVER && bind.source === EvalSource.JOB) {
        try {
          const define = await this.action$evalCustomPluginImplementDefine({
            interfaceName: bind.pluginInterface!,
            implementName: bind.pluginImplement!,
          });
          define.evalCustomPluginImplement.contextParamsDefine.forEach((param) => {
            const name = param.globalAliasName;
            if (name && param.globalAliasType === this.globalAliasType) {
              valueMap.set(name, param);
            }
          });
        } catch (e) {
          console.log(e);
        }
      }
      bind.contextParams.forEach((param) => {
        const name = param.globalAliasName;
        if (name && param.globalAliasType === this.globalAliasType) {
          valueMap.set(name, param);
        }
      });
    }

    // 按照定义排序
    const oriParams = [
      ...(this.globalAliasType === 'ENV' ? this.context.previewEnvGlobalAliasParams : this.context.previewGlobalAliasParams),
      ...valueMap.values(),
    ]
      .filter((param) => this.context.sourceEnvGlobalAliasParams.some((x) => x.name === param.name))
      .map((param) => ({
        ...param,
        defaultSource: param.source,
        defaultValue: param.value,
        value: null,
      }));
    this.contextParamsDefine = mergeParamDefinition(oriParams, this.initParams);
    this.loading = false;
  }

  @Watch('contextParamsDefine', { deep: true })
  contextParamsDefineChanged(v) {
    this.coveredParams = v.filter((x) => x.value !== undefined && x.value !== null);
    this.$emit('change', this.contextParamsDefine);
  }

  setAllDefault() {
    this.coveredParams.forEach((x) => (x.value = null));
  }
}
</script>
<style lang="scss"></style>
