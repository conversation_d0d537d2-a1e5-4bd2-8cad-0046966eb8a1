import { FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1426469, catid=246786, projectId=35802, created=2024-06-24 19:05:51, last-modified=2024-09-09 16:37:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalCustomPluginGlobalAlias/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426469)
 */
export function postEvalCustomPluginGlobalAliasList(
  params: model.evalCustomPluginGlobalAlias.IPostEvalCustomPluginGlobalAliasListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalCustomPluginGlobalAlias.IPostEvalCustomPluginGlobalAliasListResponse>(
    '/webApi/evalCustomPluginGlobalAlias/list',
    {
      data: params,
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
