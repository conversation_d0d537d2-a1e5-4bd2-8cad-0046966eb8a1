import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1396199, catid=242528, projectId=35802, created=2024-04-17 15:46:31, last-modified=2024-04-19 14:50:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource-dashboard/listModelExpTeam)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1396199)
 */
export function getResourceDashboardListModelExpTeam(options?: FlowHttpRequestOptions) {
  return http.get<model.resourceDashboard.IGetResourceDashboardListModelExpTeamResponse>('/webApi/resource-dashboard/listModelExpTeam', {
    ...options,
  });
}

/**
 * @desc id=1396227, catid=242528, projectId=35802, created=2024-04-17 16:44:33, last-modified=2024-04-19 11:08:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource-dashboard/listGpuSpecConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1396227)
 */
export function getResourceDashboardListGpuSpecConfig(options?: FlowHttpRequestOptions) {
  return http.get<model.resourceDashboard.IGetResourceDashboardListGpuSpecConfigResponse>('/webApi/resource-dashboard/listGpuSpecConfig', {
    ...options,
  });
}

/**
 * @desc id=1396238, catid=242528, projectId=35802, created=2024-04-17 16:54:15, last-modified=2024-04-19 11:07:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource-dashboard/searchJobTypeConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1396238)
 */
export function postResourceDashboardSearchJobTypeConfig(
  params: model.resourceDashboard.IPostResourceDashboardSearchJobTypeConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resourceDashboard.IPostResourceDashboardSearchJobTypeConfigResponse>(
    '/webApi/resource-dashboard/searchJobTypeConfig',
    {
      data: pick(params, ['keyWord']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1398599, catid=242528, projectId=35802, created=2024-04-23 10:46:11, last-modified=2024-05-06 14:55:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource-dashboard/searchQueue)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1398599)
 */
export function postResourceDashboardSearchQueue(
  params: model.resourceDashboard.IPostResourceDashboardSearchQueueParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resourceDashboard.IPostResourceDashboardSearchQueueResponse>('/webApi/resource-dashboard/searchQueue', {
    data: pick(params, ['keyWord']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1402000, catid=242528, projectId=35802, created=2024-05-06 15:45:55, last-modified=2024-05-13 11:17:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource-dashboard/searchModelExp)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1402000)
 */
export function postResourceDashboardSearchModelExp(
  params: model.resourceDashboard.IPostResourceDashboardSearchModelExpParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resourceDashboard.IPostResourceDashboardSearchModelExpResponse>('/webApi/resource-dashboard/searchModelExp', {
    data: pick(params, ['keyWord', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1396243, catid=242528, projectId=35802, created=2024-04-17 16:58:04, last-modified=2024-05-06 19:55:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource-dashboard/dashboard)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1396243)
 */
export function postResourceDashboardDashboard(
  params: model.resourceDashboard.IPostResourceDashboardDashboardParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resourceDashboard.IPostResourceDashboardDashboardResponse>('/webApi/resource-dashboard/dashboard', {
    data: pick(params, [
      'timeFilter',
      'teamFilter',
      'ownerFilter',
      'unitSetFilter',
      'queueFilter',
      'gpuSpecFilter',
      'jobTypeFilter',
      'sortConfig',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1398608, catid=242528, projectId=35802, created=2024-04-23 10:49:01, last-modified=2024-09-18 11:35:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource-dashboard/workDashboard)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1398608)
 */
export function postResourceDashboardWorkDashboard(
  params: model.resourceDashboard.IPostResourceDashboardWorkDashboardParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resourceDashboard.IPostResourceDashboardWorkDashboardResponse>('/webApi/resource-dashboard/workDashboard', {
    data: pick(params, [
      'timeFilter',
      'ownerFilter',
      'evalModelFilter',
      'evalUnitSetFilter',
      'runSpecSetNonVersionFilter',
      'expFilter',
      'runAttemptFilter',
      'flowConsumeClusterFilter',
      'flowConsumerFilter',
      'sortConfig',
      'childFilters',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1398617, catid=242528, projectId=35802, created=2024-04-23 10:54:52, last-modified=2024-05-09 14:56:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource-dashboard/workDetailDashboard)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1398617)
 */
export function postResourceDashboardWorkDetailDashboard(
  params: model.resourceDashboard.IPostResourceDashboardWorkDetailDashboardParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resourceDashboard.IPostResourceDashboardWorkDetailDashboardResponse>(
    '/webApi/resource-dashboard/workDetailDashboard',
    {
      data: pick(params, ['runAttemptFilter', 'mlpJobFilter', 'sortConfig']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1398630, catid=242528, projectId=35802, created=2024-04-23 10:55:34, last-modified=2024-04-26 17:29:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource-dashboard/getAttemptEventInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1398630)
 */
export function postResourceDashboardGetAttemptEventInfo(
  params: model.resourceDashboard.IPostResourceDashboardGetAttemptEventInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resourceDashboard.IPostResourceDashboardGetAttemptEventInfoResponse>(
    '/webApi/resource-dashboard/getAttemptEventInfo',
    {
      data: pick(params, ['mlpJobId', 'mlpWorkerId', 'mlpAttemptId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
