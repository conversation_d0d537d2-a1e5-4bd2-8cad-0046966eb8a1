<template>
  <div class="fork-experiment-container">
    <mtd-modal v-bind="modalProps" v-model="config.visible" title="Fork实验" width="auto" destroy-on-close @open="openModal">
      <mtd-form ref="formRef" :model="form">
        <!-- <mtd-form-item label="数据集" prop="resourceId">
          <mtd-select filterable v-model="form.resourceId" style="width: 260px">
            <mtd-option v-for="item in dataSet?.list" :key="item.id" :label="item.name" :value="item.id" />
          </mtd-select>
        </mtd-form-item> -->
        <mtd-form-item label="实验名称" prop="experimentName">
          <mtd-input v-model="form.experimentName" type="text" style="width: 260px" @input="checkVal" />
        </mtd-form-item>
        <mtd-form-item label="实验描述" prop="experimentDescription">
          <mtd-input v-model="form.experimentDescription" type="text" style="width: 260px" />
        </mtd-form-item>
      </mtd-form>
      <div slot="footer">
        <mtd-button :loading="LS('batchFork')" @click="closeModal1">取消</mtd-button>
        <mtd-button type="primary" :loading="LS('batchFork')" @click="confirm">确定</mtd-button>
      </div>
    </mtd-modal>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import dataManage from '@/store/modules/dataManage';
import modelManage from '@/store/modules/modelManage';
import { useGraphStore } from '@/views/modelManage/components/use-graph-store';
import { cloneDeep } from 'lodash';
interface configType {
  visible?: boolean;
  [k: string]: unknown;
}

const formData = {
  experimentName: '',
  experimentDescription: '',
};

@Component({ components: {} })
@Spectra
export default class ForkExperiment extends Weaver(dataManage, modelManage) {
  @Prop({ default: () => ({ visible: false }) })
  config!: configType;
  @Prop({ default: () => ({}) }) modalProps;

  form = cloneDeep(formData);
  useGraphStore = useGraphStore();
  get experimentConfig() {
    return this.experimentBaseInfo?.experimentConfig || {};
  }

  resetForm(data = {}) {
    this.form = Object.assign(cloneDeep(formData), data);
  }

  openModal() {
    this.init();
  }

  closeModal1() {
    this.config.visible = false;
    this.$emit('close');
  }

  async confirm() {
    const { experimentId, resourceType } = this.experimentConfig;
    const data = {
      ...this.form,
      baseExperimentId: Number(experimentId),
      resourceType: Number(resourceType),
      batchFork: false,
      taskId: this.$route.params.taskId,
      version: this.$route.params.version,
      feDisplayConfig: JSON.stringify(this.useGraphStore.feDisplayConfig),
    };
    console.log('data', data);
    const res = await this.action$batchFork(data as any);
    if (res) {
      this.$mtd.message({ message: '创建实验成功', type: 'success' });
      const routeMap = {
        // 模型侧页面的历史
        'experimental-experiment-task-detail-version': 'experimental-experiment-task-detail',
        // 模型侧页面
        'experimental-experiment-task-detail': 'experimental-experiment-task-detail',
        // 数据侧页面的历史
        'experimental-detail-task-version-data-manage': 'experimental-experiment-task-detail-data-manage',
        // 数据侧页面
        'experimental-experiment-task-detail-data-manage': 'experimental-experiment-task-detail-data-manage',
      };
      window.open(
        this.$router.resolve({
          name: routeMap[this.$route.name!],
          params: {
            experimentId: res.experimentId.toString(),
          },
        }).href,
        '_blank'
      );
    }

    this.closeModal1();
  }
  checkVal(v) {
    if (this.form.experimentName) {
      this.form.experimentName = v.replaceAll(/[ //]/g, '_').replaceAll(/[:~：～]/g, '-');
    }
  }

  init() {
    const { experimentName } = this.experimentConfig;
    this.resetForm({
      experimentName: `${experimentName.replaceAll(/[ //]/g, '_').replaceAll(/[:~：～]/g, '-')}-copy`,
    });
    // this.action$dataSet({ param: { name: '' }, pageNumber: 1, pageSize: 9999 });
  }
}
</script>

<style lang="scss">
.fork-experiment-container {
}
</style>
