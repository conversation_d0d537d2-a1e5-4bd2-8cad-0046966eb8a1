<template>
  <div>
    <mtd-button v-if="failedClusterList.length" type="text" style="color: red; font-weight: bold; font-size: 15" @click.stop="open"
      >[失败MLP任务<mtd-icon name="code-off" />]</mtd-button
    >
    <mtd-modal v-model="modal" :title="flowConfig.flowName" class="config-modal" width="1500px" :mount-on-create="false">
      <div slot="title" style="font-weight: bold; font-size: 18px">
        {{ flowConfig.flowName }}
        <mtd-button style="font-weight: bold; font-size: 18px" type="text-primary" @click="$emit('jump', failedClusterList[0]?.jobId)">
          执行详情#{{ failedClusterList[0]?.jobId }}
        </mtd-button>
      </div>
      <div>
        <div style="font-weight: bold; margin-bottom: 10px">
          失败MLP任务
          <span style="color: grey">（{{ failedClusterList.length }}个）</span>
        </div>
        <mtd-table :data="failedClusterList">
          <mtd-table-column label="集群" width="200">
            <template #default="{ row }">
              {{ row.clusterName }}
            </template>
          </mtd-table-column>
          <mtd-table-column label="MLP任务" width="120">
            <template #default="{ row }">
              <mtd-button type="text-primary" :href="row.linkHref" target="_blank">{{ row.linkLabel }}</mtd-button>
            </template>
          </mtd-table-column>
          <mtd-table-column label="失败原因">
            <template #default="{ row }">
              <div class="w-full">
                <pre style="color: red; font-weight: bold; white-space: pre-wrap">{{ row.message }}</pre>
              </div>
            </template>
          </mtd-table-column>
        </mtd-table>
      </div>
    </mtd-modal>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import evalFlowJob from '@/store/modules/evalFlowJob';
import evalFlowVersion from '@/store/modules/evalFlowVersion';
import { consumerEndStatusList } from '@/views/modelEval/EvalFlowJob/components/data';

interface failedConsumer {
  clusterName: string;
  linkLabel: string;
  linkHref: string;
  message: string;
  jobId: number;
}
@Component({
  components: {},
})
@Spectra
export default class FailedClusterModal extends Weaver(evalFlowJob, evalFlowVersion) {
  @Prop()
  flowConfig!: any;

  modal = false;

  get failedClusterList() {
    const res: failedConsumer[] = [];

    this.evalFlowJobList?.evalFlowJobList.forEach((x) => {
      if (x.flowId === this.flowConfig.flowId) {
        const failedClusters = x.consumeClusterFlowStatusList.filter((x) => x.status === 'ERROR');
        failedClusters.forEach((cluster) => {
          cluster.consumerList
            .filter((consumer) => consumer.status === 'END' && consumerEndStatusList.find((x) => x.status === consumer.endReason)?.theme)
            .forEach((consumer) => {
              res.push({
                clusterName: this.getClusterVertexLabels(cluster),
                linkLabel: `${consumer.id}${consumer.mlpJobId ? '#' + consumer.mlpJobId : ''}`,
                linkHref: consumer.mlpJobId
                  ? `https://mlp.sankuai.com/ml/#/run/detail?jobId=${consumer.mlpJobId}`
                  : this.$router.resolve({
                      name: 'flow-chart-job-execute-detail',
                      params: {
                        id: String(cluster.jobId),
                      },
                      query: {
                        openLog: 'true',
                        clusterName: cluster.clusterName,
                        consumerId: String(consumer.id),
                      },
                    }).href,
                message: `${consumerEndStatusList.find((x) => x.status === consumer.endReason)?.label} | ${consumer.message}`,
                jobId: Number(cluster.jobId),
              });
            });
        });
      }
    });
    return res;
  }

  async open() {
    await this.action$evalFlowVersion({
      flowId: this.flowConfig.flowId,
      version: this.flowConfig.flowVersion,
    });
    this.modal = true;
  }

  getClusterVertexLabels(clusterInfo) {
    const { capableDefineList } = clusterInfo;
    const uniqueDefinesMap = new Map(capableDefineList.map((x) => [x.vertexDefineId, x]));
    if (!this.evalFlowVersion) return '';
    const labels = [...uniqueDefinesMap.values()].map((define: any) => {
      const res = this.evalFlowVersion.evalFlowVersion.vertexList.find((x) => x.vertexDefine.id === define.vertexDefineId)!;
      if (!res) return;
      let consumerLabel = '';
      if (res.vertexDefine.type === 'SPLIT') {
        const foundConsumer = res.vertexDefine.consumerDefineList.find((x) => x.rootAnchor === define.consumeDefineRootAnchor);
        consumerLabel += `/${foundConsumer?.label}`;
      }
      return res.vertexDefine?.label + consumerLabel;
    });
    return [...new Set(labels.filter((x) => x))].join(',');
  }
}
</script>
<style lang="less" scoped></style>
