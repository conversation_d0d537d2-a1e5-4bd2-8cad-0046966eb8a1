<template>
  <div class="form-items-container">
    <slot name="expandBefore" />
    <mtd-form-item
      v-for="(item, index) in config.itemInfoList"
      v-show="!item.hide"
      :key="index + item.label"
      :label="item.label"
      :prop="item.key"
      :rules="[{ required: item.require || false, message: `${item.label}不能为空` }, ...(item.rules || [])]"
    >
      <template slot="label">
        <span>{{ item.label }}:</span>
        <mtd-tooltip v-if="item?.tip" :content="item.tip" theme="light" placement="bottom">
          <i class="mtdicon mtdicon-question-circle-o" style="font-size: 15px; color: grey" />
        </mtd-tooltip>
        <i v-if="item?.tipUrl" style="color: #0a70f5" class="mtdicon mtdicon-question-circle-o" @click="openUrl(item?.tipUrl)"></i>
      </template>
      <mtd-input
        v-if="item.type === 'input'"
        v-model="form[item.key]"
        style="width: 260px"
        :clearable="item.clearable || false"
        :placeholder="item.placeholder || `请输入${item.label}`"
        @blur="(v) => item.change && item.change(v)"
        @enter="(v) => item.change && item.change(v)"
        @input="(v) => item.input && item.input(v)"
      >
      </mtd-input>

      <mtd-select
        v-if="item.type === 'select' && freshKeys[item.key]"
        v-model="form[item.key]"
        v-dom-inserted="() => handlerRemoteData(item)"
        style="width: 260px"
        :filterable="item.filterable || false"
        :clearable="item.clearable || false"
        :multiple="item.multiple || false"
        :remote="item.remote || false"
        :placeholder="item.placeholder || `请选择${item.label}`"
        :disabled="(item.getDisabled && item.getDisabled(form)) || item.disabled || false"
        :remote-method="(v) => handlerRemoteData(item, v)"
        :append-to-container="config.selectAppendToContainer || false"
        @change="(v) => changeSelect(v, item)"
      >
        <mtd-option
          v-for="(v, i) in item.options || options.list[item.key] || []"
          :key="i"
          :label="typeof v === 'object' ? v[item.opLabel || 'label'] : v"
          :value="typeof v === 'object' ? v[item.opValue || 'value'] : v"
        ></mtd-option>
      </mtd-select>

      <mtd-input-group v-if="item.type === 'num'" style="width: 260px">
        <mtd-input-number
          v-model="form[item.key]"
          :clearable="item.clearable || false"
          :placeholder="item.placeholder || `请输入${item.label}`"
          :controls="!item.unit"
          controls-position="right"
        />
        <template v-if="item.unit" slot="append">{{ item.unit }}</template>
      </mtd-input-group>

      <mtd-textarea
        v-if="item.type === 'textarea'"
        v-model="form[item.key]"
        :clearable="item.clearable || false"
        :placeholder="item.placeholder || `请输入${item.label}`"
      >
      </mtd-textarea>

      <mtd-radio-group v-if="item.type === 'radio'" v-model="form[item.key]" style="width: 260px">
        <mtd-radio v-for="r in item.radioList || defaultRadioList" :key="r.value" :value="r.value">{{ r.label }}</mtd-radio>
      </mtd-radio-group>

      <SqlEdit v-if="item.type === 'sql'" v-model="form[item.key]" style="width: 260px" />

      <template v-if="item.type === 'custom'">
        <slot :name="`${item.key}FormItem`" :formData="form" :itemConf="item" />
      </template>
      <slot v-if="item.FormItemAfter" :name="`${item.key}FormItemAfter`" :formData="form" :itemConf="item" />
    </mtd-form-item>
    <slot name="expandAfter" />
  </div>
</template>

<script lang="ts">
import SqlEdit from '@/components/SqlEdit.vue';
import { CatchError, Component, Prop, Spectra, Watch, Weaver } from '@/decorators';

interface RadioItem {
  value: number | string;
  label: string;
  [k: string]: unknown;
}

interface Options {
  list: {
    [k: string]: RadioItem[] | string[];
  };
  map: {
    [k: string]: Map<string, object>;
  };
}

@Component({
  components: { SqlEdit },
})
@Spectra
export default class FormIitems extends Weaver() {
  @Prop({ default: () => ({}) })
  form;
  @Prop({
    default: () => ({
      itemInfoList: [],
      selectAppendToContainer: false,
    }),
  })
  config;

  defaultRadioList: RadioItem[] = [
    { value: 1, label: '是' },
    { value: 0, label: '否' },
  ];

  options: Options = {
    list: {},
    map: {},
  };

  freshKeys: {
    [k: string]: boolean;
  } = {};

  @Watch('config.itemInfoList')
  changeHeaders() {
    this.initFreshKeys();
  }

  openUrl(url: string) {
    window.open(url);
  }

  @CatchError
  async handlerRemoteData(item, inputValue = '') {
    const { key, remoteMethod } = item;
    if (!remoteMethod) return;
    const list = await remoteMethod(inputValue, this.form);
    this.$set(this.options.list, key, list);
    this.options.map[key] = list.reduce((acc, ele) => acc.set(ele.value, ele), new Map());
  }

  changeSelect(v, item) {
    const { key, affectKeys, change } = item;
    const { form, options } = this;
    affectKeys &&
      affectKeys.forEach((k) => {
        form[k] = undefined;
        this.refreshKeyRef(k);
      });
    change && change(options.map[key]?.get(v), form);
  }

  refreshKeyRef(key) {
    this.freshKeys[key] = false;
    this.$nextTick(() => {
      this.freshKeys[key] = true;
    });
  }

  initFreshKeys() {
    this.config.itemInfoList?.forEach((item) => {
      this.$set(this.freshKeys, item.key, true);
    });
  }

  created() {
    this.initFreshKeys();
  }
}
</script>

<style lang="scss">
.mtd-form-item .mtd-form-item {
  margin-bottom: 16px;
  &.mtd-form-item-error-bottom {
    margin-bottom: 0;
  }
}
</style>
