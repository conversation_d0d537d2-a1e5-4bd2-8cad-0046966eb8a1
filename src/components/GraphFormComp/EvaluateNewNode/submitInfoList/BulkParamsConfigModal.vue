<template>
  <mtd-modal v-model="modal" :destroy-on-close="true" :mount-on-create="false" class="config-modal" width="1500px" :closable="false">
    <div slot="title" class="flex justify-between">
      <div>
        <span style="font-size: 18px; font-weight: bold"> ==参数批量配置== </span>&nbsp;
        <span style="color: #3888ff"> {{ `数据集 ${runspecSetContext.benchmarkList?.length}` }} </span>&nbsp;
        <span style="color: grey">
          {{ `执行单元 ${runspecSetContext.runSpecFlowConfigList?.length}` }}
        </span>
      </div>
      <div v-if="isEdit">
        <mtd-button style="width: 80px" @click="modal = false">取消</mtd-button>&nbsp;&nbsp;&nbsp;
        <mtd-button style="width: 80px" type="primary" @click="save">确定</mtd-button>
      </div>
      <div v-else><mtd-button @click="modal = false">关闭</mtd-button></div>
    </div>
    <div>
      <h4>插件绑定预览</h4>
      <plugin-bind-preview
        :context="runspecSetContext"
        @change="setPluginBind"
        :isEdit="isEdit"
        :getBindsByRunSpecId="getBindsByRunSpecId"
        :isBulk="true"
      />
      <h4>全局环境变量列表</h4>
      <context-param-definition
        :needSource="false"
        :value="runspecSetContext.envGlobalAliasParams"
        :curSource="source"
        :canEditGlobalAlias="true"
        :disabled="!isEdit"
        :bulkEdit="true"
      ></context-param-definition>
      <h4>全局别名参数列表</h4>
      <context-param-definition
        :needSource="false"
        :value="runspecSetContext.globalAliasParams"
        :curSource="source"
        :canEditGlobalAlias="true"
        :disabled="!isEdit"
        :bulkEdit="true"
      ></context-param-definition>
    </div>
  </mtd-modal>
</template>
<script lang="ts">
import { postEvalFlowJobPreview } from '@/api/modules/evalFlowJob';
import ContextParamDefinition from '@/components/Eval/ContextParamDefinition.vue';
import { Component, Prop, PropSync, Spectra, Weaver } from '@/decorators';
import { EvalSource } from '@/utils';
import StatusCountTag from '@/views/modelEval/FlowChartJobExecute/components/StatusCountTag.vue';
import StatusTag from '@/views/modelEval/FlowChartJobExecute/components/StatusTag.vue';
import { useGraphStore } from '@/views/modelManage/components/use-graph-store';
import GlobalAliasParams from './GlobalAliasParams.vue';
import PluginBindPreview from './PluginBindPreview.vue';
import ResourceConfigList from './ResourceConfigList.vue';

@Component({
  components: {
    GlobalAliasParams,
    ResourceConfigList,
    PluginBindPreview,
    StatusTag,
    StatusCountTag,
    ContextParamDefinition,
  },
})
@Spectra
export default class ParamsConfigModal extends Weaver() {
  @Prop()
  isEdit!: boolean;
  @Prop()
  runspecSetContext;
  @Prop()
  context;
  @Prop()
  sourceMetaId;
  @PropSync('openModal')
  modal;
  @Prop({ default: () => false })
  isBulk;

  collapse = ['1', '2', '3'];
  useGraphStore = useGraphStore();

  source = EvalSource.GLOBAL_ALIAS_JOB;

  setPluginBind(val) {
    this.$set(this.runspecSetContext, 'globalAnchorBindMapJson', val.globalAnchorBindMap);
    this.$set(this.runspecSetContext, 'runSpecAnchorBindMapJson', val.runSpecAnchorBindMap);
  }

  async getBindsByRunSpecId(runSpecSetId, runSpecId) {
    const { modelId } = this.useGraphStore.modelInfo;
    const res = await postEvalFlowJobPreview({
      benchmarkList: [
        { ...this.context.nodeParam.benchmarkList.find((x) => x.runSpecSetId === runSpecSetId), runSpecIdsFilter: [runSpecId] },
      ],
      modelId,
      modelMetaId: Number(this.sourceMetaId),
    });
    return res.data.evalFlowJobPreviewList[0].globalPluginBindList;
  }

  save() {
    this.modal = false;
    this.$emit('save', this.runspecSetContext);
  }
}
</script>
<style lang="less" scoped></style>
