<template>
  <div>
    <div class="showFilter" v-if="showChart">
      <mtd-button class="btn-demo-item" ghost type="primary">{{ filterGroup.selectGroup.title }}</mtd-button>
      <div class="filterOptionContent-name" v-if="stateOption?.length">选择展示内容</div>
      <mtd-select v-model="conditionValue" v-if="stateOption?.length" style="width: 180px" :append-to-container="false">
        <mtd-option v-for="item in stateOption" :key="item.value" :label="item.value" :value="item.key" />
      </mtd-select>
    </div>
    <div v-if="showChart" ref="chart" style="height: 400px; margin-top: 20px" />
  </div>
</template>

<script lang="ts">
import type ModelColumnTableWorker from '@/components/model/ModelColumnTableWorker';
import type { ColumnLineChartParams } from '@/types/modelCommonComponent';
import * as echarts from 'echarts';
import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { INIT_TOOLTIP_EXTRA_CSS_TEXT, TOOLTIP_BODY, TOOLTIP_COL, TOOLTIP_HEADER, TOOLTIP_ROW, TOOLTIP_ROW_VAL } from '@ss/mtdv-charts-core';

@Component
export default class ModelDataSubSetScatterChart extends Vue {
  @Ref('chart')
  chart: HTMLDivElement | undefined;
  @Prop()
  worker!: ModelColumnTableWorker;

  @Prop()
  PARAMS!: ColumnLineChartParams;
  @Prop()
  title!: string;
  @Prop({
    default: () => ({
      selectGroup: {
        firstIndex: -1,
        secondIndex: 0,
        title: '',
      },
      type: 'OFF',
    }),
  })
  filterGroup!: any;

  GET_SCATTER_CHART_DATA = 'GET_SCATTER_CHART_DATA';
  TRANSFORM_TABLE_DATA = 'TRANSFORM_TABLE_DATA';

  $chartsInstance: echarts.EChartsType | null = null;
  showChart = false;
  baseOption: echarts.EChartsOption = {
    tooltip: {
      //   trigger: 'axis',
      trigger: 'item',
      formatter(params) {
        const { data, marker } = params;
        const { index, modelName, value, percent, tip } = data[2];
        return `<div class="${TOOLTIP_HEADER}">${marker.replace('width:10px;height:10px;', 'width:3px;height:10px;')}${modelName}</div>
                    <div class="${TOOLTIP_BODY}">
                      <div class="${TOOLTIP_COL}">
                        <div class="${TOOLTIP_ROW}__name-con ${TOOLTIP_ROW}__name">指标绝对值</div>
                        <div class="${TOOLTIP_ROW}__name-con ${TOOLTIP_ROW}__name">排名</div>
                        <div class="${TOOLTIP_ROW}__name-con ${TOOLTIP_ROW}__name">Top百分数</div>
                        <div class="${TOOLTIP_ROW}__name-con ${TOOLTIP_ROW}__name">${tip}</div>
                      </div>
                      <div style="flex:1" class="${TOOLTIP_COL}">
                        <div class="${TOOLTIP_ROW_VAL}">${value}</div>
                        <div class="${TOOLTIP_ROW_VAL}">${index}</div>
                        <div class="${TOOLTIP_ROW_VAL}">${percent}</div>
                      </div>
                    </div>`;
      },
      extraCssText: INIT_TOOLTIP_EXTRA_CSS_TEXT,
    },
    xAxis: { type: 'category' },
    yAxis: { max: 1, min: 0 },
    toolbox: {
      show: true,
      feature: {
        saveAsImage: {},
      },
    },
  };

  stateOption1 = [
    { value: '仅clean', key: 'CLEAN' },
    { value: '仅dirty', key: 'DIRTY' },
    { value: 'clean和dirty同时显示', key: 'CLEAN&DIRTY' },
  ];
  conditionValue = '';
  stateOption: any = [];
  seriesClean: any = [];
  seriesDirty: any = [];

  valueList: any = [];
  mergedSeries: any[] = [];

  @Watch('filterGroup', { deep: true })
  @Watch('PARAMS.tableData', { immediate: true, deep: false })
  handleTableDataChange() {
    const firstIndex = this.filterGroup.selectGroup.firstIndex;
    if (firstIndex > -1) {
      this.showChart = true;
      this.$nextTick(() => this.handleTable());
    } else if (this.$chartsInstance) {
      this.$chartsInstance.dispose();
      this.$chartsInstance = null;
      this.showChart = false;
    }
  }

  handleTable() {
    this.stateOption = [];
    this.seriesDirty = [];
    this.seriesClean = [];
    const seriesDirty: any = [];
    const seriesClean: any = [];
    const firstIndex = this.filterGroup.selectGroup.firstIndex;
    const secondIndex = this.filterGroup.selectGroup.secondIndex;
    const tableData = this.PARAMS.tableDataDataSubSet;
    if (!tableData || tableData.evalModelList.length === 0) return;
    const evalModelList = tableData.evalModelList;
    if (evalModelList[0]?.filterGroupResultList) {
      this.valueList = evalModelList[0].filterGroupResultList[firstIndex].groupList[secondIndex].valueList;
    } else {
      this.valueList = evalModelList[0].dataSubSetFilterResult.valueList;
    }

    const rows: any[] = [];
    this.conditionValue = '';
    evalModelList.map((model, dataIndex) => {
      let valueList: any = [];
      if (model.filterGroupResultList) {
        valueList = model.filterGroupResultList[firstIndex]?.groupList[secondIndex].valueList;
      } else {
        valueList = model.dataSubSetFilterResult.valueList;
      }

      valueList.forEach((item) => {
        if (item.unitList?.length === 2) {
          this.stateOption = this.stateOption1;
          this.conditionValue = 'CLEAN&DIRTY';
        }
        item.unitList.forEach((key, index) => {
          if (key.dataSetOpennessStatus === 'CLEAN' || key.dataSetOpennessStatus === '') {
            const row = {
              value: key.value,
              modelList: {
                // modelLabel: `${model.llmModel.modelInfo?.modelMeta.modelName}/${model.llmModel.modelInfo?.modelCheckpoint}/${model.llmModel.label}_${key.dataSetOpennessStatus}`,
                modelLabel: key.dataSetOpennessStatus ? `${model.llmModel.label}_${key.dataSetOpennessStatus}` : `${model.llmModel.label}`,
                label: model.label,
                modelCheckpoint: model.llmModel.modelInfo.modelCheckpoint,
                modelName: model.llmModel.modelInfo.modelMeta.modelName,
                evalDataSize: model.evalDataSize || this.PARAMS.evalDataSizeLineChart,
                dataSubSetId: item.dataSubSetId,
                dataStatus: key.dataSetOpennessStatus,
              },
            };
            seriesClean.push(row);
          }
          if (key.dataSetOpennessStatus === 'DIRTY') {
            const row = {
              value: key.value,
              modelList: {
                // modelLabel: `${model.llmModel.modelInfo?.modelMeta.modelName}/${model.llmModel.modelInfo?.modelCheckpoint}/${model.llmModel.label}_${key.dataSetOpennessStatus}`,
                modelLabel: key.dataSetOpennessStatus ? `${model.llmModel.label}_${key.dataSetOpennessStatus}` : `${model.llmModel.label}`,
                label: model.label,
                modelCheckpoint: model.llmModel.modelInfo.modelCheckpoint,
                modelName: model.llmModel.modelInfo.modelMeta.modelName,
                evalDataSize: model.evalDataSize || this.PARAMS.evalDataSizeLineChart,
                dataSubSetId: item.dataSubSetId,
                dataStatus: key.dataSetOpennessStatus,
              },
            };
            seriesDirty.push(row);
          }
        });
      });
    });

    this.seriesClean = this.processData(seriesClean);
    this.seriesDirty = this.processData(seriesDirty);
    if (!this.$chartsInstance) {
      this.$chartsInstance = echarts.init(this.chart, this.$C.ChartTheme);
    }
    this.$chartsInstance.setOption({
      ...this.baseOption,
      title: {
        // title: `${this.valueList[0].label} 上各模型的得分排名`,
        text: `${this.valueList[0].label} 上各模型的得分排名`,
        textStyle: { fontSize: 14, color: '#000' },
        left: 20,
      },
      series: [
        { symbolSize: 4, data: this.seriesClean, type: 'scatter' },
        { symbolSize: 4, data: this.seriesDirty, type: 'scatter' },
      ],
    });
  }

  processData(data) {
    const filteredData = data.filter((item) => item.value);
    const sortedData = filteredData.sort((a, b) => parseFloat(b.value) - parseFloat(a.value));
    const chartData = sortedData.map((item, index) => {
      const toolTipData = {
        index: index + 1,
        modelName: item.modelList.modelLabel,
        value: item.value,
        percent: `${Math.round(((index + 1) / sortedData.length) * 100)}%`,
        tip: `${data.length - sortedData.length}个模型未参与排名`,
      };
      return [index + 1, parseFloat(item.value), toolTipData];
    });
    return chartData;
  }

  beforeDestroy() {
    this.$chartsInstance?.dispose();
    this.$chartsInstance = null;
    this.showChart = false;
  }
}
</script>
<style scoped lang="less">
.showFilter {
  text-align: left;
  margin-left: 30px;
  display: flex;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
}
.filterOptionContent-name {
  margin-left: 20px;
  margin-right: 10px;
}
</style>
