<template>
  <div>
    <mtd-table :data="tableData">
      <mtd-table-column prop="dimension" label="生效范围" width="200">
        <template #default="{ row }">
          <div v-if="row.dimension === '全局'">全局</div>
          <template v-else>
            {{ findDataSubSetName(row.dimension.split('-')[1]) }}<br />
            <mtd-tag>{{ row.dimension.split('-')[0] }}-{{ row.dimension.split('-')[1] }}</mtd-tag>
            <mtd-tag theme="green">{{ getBenchmarkEvalDataSizeLabel(row.dimension.split('-')[2]) }}</mtd-tag>
          </template>
        </template>
      </mtd-table-column>
      <mtd-table-column prop="anchorPath" label="锚点路径"> </mtd-table-column>
      <mtd-table-column prop="bindType" width="150" label="绑定类型"> </mtd-table-column>
      <mtd-table-column prop="pluginBindInfo" width="300" label="插件/参数绑定">
        <template #default="{ row }">
          【<mtd-button type="text-primary" @click="handleVertexJump(row)"> {{ row.bindInfo.label }}</mtd-button
          >】
          <mtd-tag v-if="row.bindInfo.coveredCount" type="pure" theme="red" style="background-color: #ff000080"
            >非默认配置:{{ row.bindInfo.coveredCount }}</mtd-tag
          >
        </template>
      </mtd-table-column>
      <mtd-table-column width="50">
        <template #default="{ row }">
          <mtd-button :disabled="!isEdit" type="text-primary" @click="delBind(row)">x</mtd-button>
        </template>
      </mtd-table-column>
    </mtd-table>
  </div>
</template>
<script lang="ts">
import { BindType, PluginBind } from '@/model/customEval';
import { EvalSource } from '@/utils';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

const bindTypeLabelMap = {
  [BindType.COVER]: '覆盖',
  [BindType.PARAM]: '参数',
};

interface BindTableData {
  anchorPath: string;
  bindType: BindType;
  bindInfo: {
    label: string;
  };
  dimension: string;
  mapKey: string;
}

@Component
export default class PluginBindPreviewTable extends Vue {
  tableData: BindTableData[] = [];

  @Prop()
  globalAnchorBindMap!: Map<string, PluginBind[]>;
  @Prop()
  runSpecAnchorBindMap!: Map<string, PluginBind[]>;
  @Prop()
  isEdit!: Boolean;
  @Prop()
  context!: any;
  @Prop()
  getBenchmarkEvalDataSizeLabel!: any;
  @Prop()
  handleVertexJump!: any;

  @Watch('globalAnchorBindMap', { immediate: true, deep: true })
  @Watch('runSpecAnchorBindMap', { immediate: true, deep: true })
  updateTableData() {
    const processBinds = (entries: [string, PluginBind[]][], isDimensional = false) =>
      entries.flatMap(([mapKey, pluginBindList]) => {
        if (!pluginBindList.length) return [];

        const firstBind = pluginBindList[0]!;
        const dimension = isDimensional ? mapKey.split('&')[0] : '全局';
        const path = isDimensional ? mapKey.split('&')[1] : mapKey;

        if (firstBind.bindType !== BindType.PARAM) {
          return [
            {
              anchorPath: path,
              bindType: bindTypeLabelMap[firstBind.bindType!],
              bindInfo: {
                label:
                  pluginBindList.length > 1
                    ? `${pluginBindList.length}个绑定`
                    : firstBind.pluginImplementLabel || firstBind.pluginImplement!,
                coveredCount: pluginBindList.reduce((count, bind) => {
                  if (bind.bindType === BindType.COVER) count++;
                  bind.contextParams.forEach((param) => {
                    // 特殊逻辑：EvalModelInfer/model 的“计算资源类型”参数在此项目上不计数。
                    if (bind.anchorPath === 'EvalModelInfer/model' && param.name === '_RESOURCE_TYPE') return;
                    if (param.source === EvalSource.JOB) count++;
                  });
                  return count;
                }, 0),
              },
              dimension,
              mapKey,
            },
          ];
        } else {
          return pluginBindList.map((bind) => ({
            anchorPath: `${path}@${bind.pluginImplement}`,
            bindType: bindTypeLabelMap[BindType.PARAM],
            bindInfo: {
              label: bind.pluginImplementLabel || bind.pluginImplement!,
              coveredCount: bind.contextParams.reduce((count, param) => {
                // 特殊逻辑：EvalModelInfer/model 的“计算资源类型”参数在此项目上不计数。
                if (bind.anchorPath === 'EvalModelInfer/model' && param.name === '_RESOURCE_TYPE') return count;
                if (param.source === EvalSource.JOB) count++;
                return count;
              }, 0),
            },
            dimension,
            mapKey,
          }));
        }
      });

    this.tableData = [
      ...processBinds([...this.globalAnchorBindMap.entries()]),
      ...processBinds([...this.runSpecAnchorBindMap.entries()], true),
    ];
  }

  delBind(row) {
    if (row.dimension === '全局') {
      this.delGlobalBind(row);
    } else this.delRunSpecBind(row);
    this.updateTableData();
  }

  delGlobalBind(row) {
    this.globalAnchorBindMap.delete(row.mapKey);
    this.$emit('saveGlobalPluginBindList');
  }

  delRunSpecBind(row) {
    this.runSpecAnchorBindMap.delete(row.mapKey);
    this.$emit('saveGlobalPluginBindList');
  }

  findDataSubSetName(runSpecId) {
    return this.context.runSpecFlowConfigList.find((x) => String(x.runSpecId) === runSpecId)?.dataSubSetName;
  }
}
</script>
<style lang="scss"></style>
