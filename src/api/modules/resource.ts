import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1333480, catid=232606, projectId=35802, created=2023-11-27 20:02:15, last-modified=2024-06-19 11:13:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource/download/stopTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1333480)
 */
export function postResourceDownloadStopTask(
  params: model.resource.IPostResourceDownloadStopTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resource.IPostResourceDownloadStopTaskResponse>('/webApi/resource/download/stopTask', {
    params: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1333458, catid=232606, projectId=35802, created=2023-11-27 20:02:15, last-modified=2024-07-02 16:33:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource/download/createTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1333458)
 */
export function postResourceDownloadCreateTask(
  params: model.resource.IPostResourceDownloadCreateTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resource.IPostResourceDownloadCreateTaskResponse>('/webApi/resource/download/createTask', {
    data: pick(params, [
      'downloadType',
      'modelFamily',
      'modelSeries',
      'modelSize',
      'resourceName',
      'resourceSource',
      'revision',
      'slaTask',
      'userToken',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1333460, catid=232606, projectId=35802, created=2023-11-27 20:02:15, last-modified=2024-06-19 11:13:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource/download/deleteTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1333460)
 */
export function postResourceDownloadDeleteTask(
  params: model.resource.IPostResourceDownloadDeleteTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resource.IPostResourceDownloadDeleteTaskResponse>('/webApi/resource/download/deleteTask', {
    params: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1333489, catid=232606, projectId=35802, created=2023-11-27 20:02:15, last-modified=2024-06-19 11:13:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource/download/updateTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1333489)
 */
export function postResourceDownloadUpdateTask(
  params: model.resource.IPostResourceDownloadUpdateTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resource.IPostResourceDownloadUpdateTaskResponse>('/webApi/resource/download/updateTask', {
    data: pick(params, [
      'downloadType',
      'modelFamily',
      'modelSeries',
      'modelSize',
      'resourceName',
      'resourceSource',
      'revision',
      'slaTask',
      'taskId',
      'userToken',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1333474, catid=232606, projectId=35802, created=2023-11-27 20:02:15, last-modified=2024-06-19 11:13:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource/download/listTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1333474)
 */
export function postResourceDownloadListTask(
  params: model.resource.IPostResourceDownloadListTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resource.IPostResourceDownloadListTaskResponse>('/webApi/resource/download/listTask', {
    data: pick(params, ['curPage', 'keyword', 'showCount', 'slaTask']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1335902, catid=232606, projectId=35802, created=2023-12-04 17:32:22, last-modified=2024-06-19 11:13:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource/download/getTaskLog)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1335902)
 */
export function postResourceDownloadGetTaskLog(
  params: model.resource.IPostResourceDownloadGetTaskLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resource.IPostResourceDownloadGetTaskLogResponse>('/webApi/resource/download/getTaskLog', {
    data: pick(params, ['curPage', 'showCount', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1333475, catid=232606, projectId=35802, created=2023-11-27 20:02:15, last-modified=2024-06-19 11:13:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/resource/download/runTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1333475)
 */
export function postResourceDownloadRunTask(
  params: model.resource.IPostResourceDownloadRunTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.resource.IPostResourceDownloadRunTaskResponse>('/webApi/resource/download/runTask', {
    params: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
