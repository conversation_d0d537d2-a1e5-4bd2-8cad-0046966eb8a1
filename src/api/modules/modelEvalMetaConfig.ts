import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1296984, catid=227034, projectId=35802, created=2023-10-13 19:28:04, last-modified=2023-11-21 14:38:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalMetaConfig/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296984)
 */
export function postModelEvalMetaConfigList(
  params: model.modelEvalMetaConfig.IPostModelEvalMetaConfigListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalMetaConfig.IPostModelEvalMetaConfigListResponse>('/webApi/modelEvalMetaConfig/list', {
    data: pick(params, ['metaVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296997, catid=227034, projectId=35802, created=2023-10-13 19:28:04, last-modified=2023-11-21 14:38:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalMetaConfig/batchUpsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296997)
 */
export function postModelEvalMetaConfigBatchUpsert(
  params: model.modelEvalMetaConfig.IPostModelEvalMetaConfigBatchUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalMetaConfig.IPostModelEvalMetaConfigBatchUpsertResponse>('/webApi/modelEvalMetaConfig/batchUpsert', {
    data: pick(params, ['metaVersionId', 'valueList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
