import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1522769, catid=246537, projectId=35802, created=2025-06-12 15:18:19, last-modified=2025-06-16 15:27:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/updateProject)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522769)
 */
export function postTrainingLineUpdateProject(
  params: model.training.IPostTrainingLineUpdateProjectParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineUpdateProjectResponse>('/webApi/training/line/updateProject', {
    data: pick(params, [
      'createTime',
      'finishedTime',
      'projectDescription',
      'projectId',
      'projectName',
      'projectOwners',
      'projectStatus',
      'updateTime',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522729, catid=246537, projectId=35802, created=2025-06-12 15:18:17, last-modified=2025-06-16 15:27:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/deleteTrainingLineRecord)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522729)
 */
export function postTrainingLineDeleteTrainingLineRecord(
  params: model.training.IPostTrainingLineDeleteTrainingLineRecordParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineDeleteTrainingLineRecordResponse>('/webApi/training/line/deleteTrainingLineRecord', {
    data: pick(params, ['recordId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522727, catid=246537, projectId=35802, created=2025-06-12 15:18:17, last-modified=2025-06-16 15:27:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/deleteProject)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522727)
 */
export function postTrainingLineDeleteProject(
  params: model.training.IPostTrainingLineDeleteProjectParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineDeleteProjectResponse>('/webApi/training/line/deleteProject', {
    data: pick(params, ['projectId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522760, catid=246537, projectId=35802, created=2025-06-12 15:18:19, last-modified=2025-06-16 15:27:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/runEvalExperiment)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522760)
 */
export function postTrainingLineRunEvalExperiment(
  params: model.training.IPostTrainingLineRunEvalExperimentParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineRunEvalExperimentResponse>('/webApi/training/line/runEvalExperiment', {
    data: pick(params, ['releaseNodeId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522724, catid=246537, projectId=35802, created=2025-06-12 15:18:17, last-modified=2025-06-16 15:27:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/createTrainingLineRecord)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522724)
 */
export function postTrainingLineCreateTrainingLineRecord(
  params: model.training.IPostTrainingLineCreateTrainingLineRecordParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineCreateTrainingLineRecordResponse>('/webApi/training/line/createTrainingLineRecord', {
    data: pick(params, ['edges', 'nodeBaseInfoVos', 'nodes', 'originRecordId', 'trainingLineReleaseRecordVo']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522900, catid=246537, projectId=35802, created=2025-06-12 18:22:12, last-modified=2025-06-16 15:27:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/createTrainingLineReleaseNode)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522900)
 */
export function postTrainingLineCreateTrainingLineReleaseNode(
  params: model.training.IPostTrainingLineCreateTrainingLineReleaseNodeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineCreateTrainingLineReleaseNodeResponse>(
    '/webApi/training/line/createTrainingLineReleaseNode',
    {
      data: pick(params, [
        'actualEndTime',
        'actualStartTime',
        'cts',
        'expectedEndTime',
        'expectedStartTime',
        'nodeId',
        'nodeName',
        'nodeSequence',
        'nodeStatus',
        'nodeType',
        'owners',
        'releaseRecordId',
        'remark',
        'uts',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1522718, catid=246537, projectId=35802, created=2025-06-12 15:18:16, last-modified=2025-06-16 15:27:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/createProject)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522718)
 */
export function postTrainingLineCreateProject(
  params: model.training.IPostTrainingLineCreateProjectParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineCreateProjectResponse>('/webApi/training/line/createProject', {
    data: pick(params, [
      'createTime',
      'finishedTime',
      'projectDescription',
      'projectId',
      'projectName',
      'projectOwners',
      'projectStatus',
      'updateTime',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522779, catid=246537, projectId=35802, created=2025-06-12 15:18:19, last-modified=2025-06-16 15:27:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/updateTrainingLineReleaseNode)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522779)
 */
export function postTrainingLineUpdateTrainingLineReleaseNode(
  params: model.training.IPostTrainingLineUpdateTrainingLineReleaseNodeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineUpdateTrainingLineReleaseNodeResponse>(
    '/webApi/training/line/updateTrainingLineReleaseNode',
    {
      data: pick(params, [
        'actualEndTime',
        'actualStartTime',
        'cts',
        'expectedEndTime',
        'expectedStartTime',
        'nodeId',
        'nodeName',
        'nodeSequence',
        'nodeStatus',
        'nodeType',
        'owners',
        'releaseRecordId',
        'remark',
        'uts',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1523058, catid=246537, projectId=35802, created=2025-06-13 17:56:21, last-modified=2025-06-16 15:39:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/updateTrainingLineReleaseNodeSequence)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523058)
 */
export function postTrainingLineUpdateTrainingLineReleaseNodeSequence(
  params: model.training.IPostTrainingLineUpdateTrainingLineReleaseNodeSequenceParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineUpdateTrainingLineReleaseNodeSequenceResponse>(
    '/webApi/training/line/updateTrainingLineReleaseNodeSequence',
    {
      data: pick(params, ['stageInfoVoList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1522903, catid=246537, projectId=35802, created=2025-06-12 18:22:13, last-modified=2025-06-16 15:27:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/updateTrainingLineReleaseNodeExtraInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522903)
 */
export function postTrainingLineUpdateTrainingLineReleaseNodeExtraInfo(
  params: model.training.IPostTrainingLineUpdateTrainingLineReleaseNodeExtraInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineUpdateTrainingLineReleaseNodeExtraInfoResponse>(
    '/webApi/training/line/updateTrainingLineReleaseNodeExtraInfo',
    {
      data: pick(params, ['extraInfoVos']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1523046, catid=246537, projectId=35802, created=2025-06-13 17:56:20, last-modified=2025-06-16 15:27:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/updateReleaseNodeCustomTrainParam)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523046)
 */
export function postTrainingLineUpdateReleaseNodeCustomTrainParam(
  params: model.training.IPostTrainingLineUpdateReleaseNodeCustomTrainParamParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineUpdateReleaseNodeCustomTrainParamResponse>(
    '/webApi/training/line/updateReleaseNodeCustomTrainParam',
    {
      data: pick(params, ['autoParseParam', 'customKeyParam', 'releaseNodeId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1523051, catid=246537, projectId=35802, created=2025-06-13 17:56:21, last-modified=2025-06-16 15:27:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/updateReleaseNodeTrainDataAnalysis)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523051)
 */
export function postTrainingLineUpdateReleaseNodeTrainDataAnalysis(
  params: model.training.IPostTrainingLineUpdateReleaseNodeTrainDataAnalysisParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineUpdateReleaseNodeTrainDataAnalysisResponse>(
    '/webApi/training/line/updateReleaseNodeTrainDataAnalysis',
    {
      data: pick(params, ['analysisUrl', 'releaseNodeId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1522776, catid=246537, projectId=35802, created=2025-06-12 15:18:19, last-modified=2025-06-16 15:27:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/updateTrainingLineRecordConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522776)
 */
export function postTrainingLineUpdateTrainingLineRecordConfig(
  params: model.training.IPostTrainingLineUpdateTrainingLineRecordConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineUpdateTrainingLineRecordConfigResponse>(
    '/webApi/training/line/updateTrainingLineRecordConfig',
    {
      data: pick(params, [
        'baseModelInfo',
        'baseModelMetaBaseVo',
        'comparisonModelList',
        'createTime',
        'description',
        'modelFeatureList',
        'modelIntroduction',
        'owners',
        'projectId',
        'recordId',
        'recordName',
        'releaseModelInfo',
        'releaseModelMetaBaseVo',
        'releaseTime',
        'runningNodeType',
        'status',
        'updateTime',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1522732, catid=246537, projectId=35802, created=2025-06-12 15:18:17, last-modified=2025-06-16 15:27:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/deleteTrainingLineReleaseNode)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522732)
 */
export function postTrainingLineDeleteTrainingLineReleaseNode(
  params: model.training.IPostTrainingLineDeleteTrainingLineReleaseNodeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineDeleteTrainingLineReleaseNodeResponse>(
    '/webApi/training/line/deleteTrainingLineReleaseNode',
    {
      data: pick(params, ['releaseNodeId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1523040, catid=246537, projectId=35802, created=2025-06-13 17:56:20, last-modified=2025-06-16 15:27:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/overviewModelPerformanceForCapability)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523040)
 */
export function postTrainingLineOverviewModelPerformanceForCapability(
  params: model.training.IPostTrainingLineOverviewModelPerformanceForCapabilityParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineOverviewModelPerformanceForCapabilityResponse>(
    '/webApi/training/line/overviewModelPerformanceForCapability',
    {
      data: pick(params, ['comparisonModelMetaIds', 'recordId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1523162, catid=246537, projectId=35802, created=2025-06-16 15:27:51, last-modified=2025-06-16 15:27:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/getReleaseModelStructure)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523162)
 */
export function postTrainingLineGetReleaseModelStructure(
  params: model.training.IPostTrainingLineGetReleaseModelStructureParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineGetReleaseModelStructureResponse>('/webApi/training/line/getReleaseModelStructure', {
    data: pick(params, ['recordId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1523000, catid=246537, projectId=35802, created=2025-06-13 17:56:18, last-modified=2025-06-16 15:27:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/getReleaseNodeStageInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523000)
 */
export function postTrainingLineGetReleaseNodeStageInfo(
  params: model.training.IPostTrainingLineGetReleaseNodeStageInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineGetReleaseNodeStageInfoResponse>('/webApi/training/line/getReleaseNodeStageInfo', {
    data: pick(params, ['recordId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1523027, catid=246537, projectId=35802, created=2025-06-13 17:56:19, last-modified=2025-06-16 15:27:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/getReleaseNodeTrainReport)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523027)
 */
export function postTrainingLineGetReleaseNodeTrainReport(
  params: model.training.IPostTrainingLineGetReleaseNodeTrainReportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineGetReleaseNodeTrainReportResponse>('/webApi/training/line/getReleaseNodeTrainReport', {
    data: pick(params, ['releaseNodeId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1523015, catid=246537, projectId=35802, created=2025-06-13 17:56:18, last-modified=2025-06-16 15:27:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/getReleaseNodeTrainMetrics)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523015)
 */
export function postTrainingLineGetReleaseNodeTrainMetrics(
  params: model.training.IPostTrainingLineGetReleaseNodeTrainMetricsParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineGetReleaseNodeTrainMetricsResponse>('/webApi/training/line/getReleaseNodeTrainMetrics', {
    data: pick(params, ['releaseNodeId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1523009, catid=246537, projectId=35802, created=2025-06-13 17:56:18, last-modified=2025-06-16 15:27:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/getReleaseNodeTrainDataAnalysis)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523009)
 */
export function postTrainingLineGetReleaseNodeTrainDataAnalysis(
  params: model.training.IPostTrainingLineGetReleaseNodeTrainDataAnalysisParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineGetReleaseNodeTrainDataAnalysisResponse>(
    '/webApi/training/line/getReleaseNodeTrainDataAnalysis',
    {
      data: pick(params, ['releaseNodeId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1523020, catid=246537, projectId=35802, created=2025-06-13 17:56:19, last-modified=2025-06-16 15:27:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/getReleaseNodeTrainParam)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523020)
 */
export function postTrainingLineGetReleaseNodeTrainParam(
  params: model.training.IPostTrainingLineGetReleaseNodeTrainParamParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineGetReleaseNodeTrainParamResponse>('/webApi/training/line/getReleaseNodeTrainParam', {
    data: pick(params, ['releaseNodeId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522755, catid=246537, projectId=35802, created=2025-06-12 15:18:18, last-modified=2025-06-16 15:46:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/listTrainingLineRecord)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522755)
 */
export function postTrainingLineListTrainingLineRecord(
  params: model.training.IPostTrainingLineListTrainingLineRecordParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineListTrainingLineRecordResponse>('/webApi/training/line/listTrainingLineRecord', {
    data: pick(params, ['curPage', 'owner', 'projectId', 'recordName', 'recordStatus', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522743, catid=246537, projectId=35802, created=2025-06-12 15:18:18, last-modified=2025-06-16 15:27:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/getTrainingLineRecordInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522743)
 */
export function postTrainingLineGetTrainingLineRecordInfo(
  params: model.training.IPostTrainingLineGetTrainingLineRecordInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineGetTrainingLineRecordInfoResponse>('/webApi/training/line/getTrainingLineRecordInfo', {
    data: pick(params, ['recordId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522738, catid=246537, projectId=35802, created=2025-06-12 15:18:18, last-modified=2025-06-16 15:27:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/getTrainingLineRecordDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522738)
 */
export function postTrainingLineGetTrainingLineRecordDetail(
  params: model.training.IPostTrainingLineGetTrainingLineRecordDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineGetTrainingLineRecordDetailResponse>(
    '/webApi/training/line/getTrainingLineRecordDetail',
    {
      data: pick(params, ['recordId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1523031, catid=246537, projectId=35802, created=2025-06-13 17:56:20, last-modified=2025-06-16 15:27:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/overviewModelPerformance)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1523031)
 */
export function postTrainingLineOverviewModelPerformance(
  params: model.training.IPostTrainingLineOverviewModelPerformanceParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineOverviewModelPerformanceResponse>('/webApi/training/line/overviewModelPerformance', {
    data: pick(params, ['releaseNodeId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522746, catid=246537, projectId=35802, created=2025-06-12 15:18:18, last-modified=2025-06-16 15:27:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/listProject)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522746)
 */
export function postTrainingLineListProject(
  params: model.training.IPostTrainingLineListProjectParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.training.IPostTrainingLineListProjectResponse>('/webApi/training/line/listProject', {
    data: pick(params, ['curPage', 'projectName', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1522733, catid=246537, projectId=35802, created=2025-06-12 15:18:18, last-modified=2025-06-16 15:27:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/training/line/getProject)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1522733)
 */
export function getTrainingLineGetProject(params: model.training.IGetTrainingLineGetProjectParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.training.IGetTrainingLineGetProjectResponse>('/webApi/training/line/getProject', {
    params: pick(params, ['projectId']),
    ...options,
  });
}
