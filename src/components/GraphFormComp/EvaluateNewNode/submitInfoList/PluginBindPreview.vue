<template>
  <div class="plugin-bind-preview">
    <div class="flex justify-between">
      <div>
        <mtd-tag v-if="coveredCount()" type="pure" theme="red" style="background-color: #ff000080">非默认配置:{{ coveredCount() }}</mtd-tag>
      </div>
      <mtd-button type="primary" :loading="buttonLoading" @click="handleButton" style="margin-bottom: 10px" v-if="!isBulk && isEdit"
        >新增绑定</mtd-button
      >
    </div>
    <plugin-bind-preview-table
      :globalAnchorBindMap="globalAnchorBindMap"
      :runSpecAnchorBindMap="runSpecAnchorBindMap"
      :isEdit="isEdit"
      :context="context"
      :getBenchmarkEvalDataSizeLabel="getBenchmarkEvalDataSizeLabel"
      :handleVertexJump="handleVertexJump"
      @saveGlobalPluginBindList="saveGlobalPluginBindList"
    />
    <mtd-modal
      v-model="modal"
      :width="1500"
      class="plugin-bind-preview-modal"
      :destroy-on-close="true"
      :mount-on-create="false"
      @close="save"
    >
      <mtd-tabs v-model="activeTab" style="margin-bottom: 10px">
        <mtd-tab-pane label="全局视图" value="global"></mtd-tab-pane>
        <mtd-tab-pane label="评测执行单元视图" value="runSpec" v-if="!isBulk"></mtd-tab-pane>
      </mtd-tabs>

      <div class="header">
        <span>
          <mtd-tag v-if="evalFlowVersion?.evalFlowVersion?.status === 'ONLINE'" theme="green" type="pure">上线</mtd-tag>
          <mtd-tag v-else theme="gray" type="pure">下线</mtd-tag>
        </span>
        <span class="header-title">
          {{ evalFlow.evalFlow.name }}
        </span>
        <span class="header-version">- 版本 {{ evalFlowVersion.evalFlowVersion.version }} </span>
        <mtd-select
          v-if="activeTab === 'runSpec'"
          v-model="runSpecJobId"
          filterable
          :filter-method="runSpecFilterMethod"
          placeholder="请选择runSpec"
          :disabled="false"
          :formatter="getRunSpecJobLabel"
          style="width: 400px"
        >
          <mtd-option v-for="runSpec of context.runSpecFlowConfigList" :key="getRunSpecJobId(runSpec)" :value="getRunSpecJobId(runSpec)">
            {{ runSpec.dataSubSetName }}
            <mtd-tag>{{ runSpec.runSpecSetId }}-{{ runSpec.runSpecId }}</mtd-tag>
            <mtd-tag theme="green">{{ getBenchmarkEvalDataSizeLabel(runSpec.evalDataSize) }}</mtd-tag>
          </mtd-option>
        </mtd-select>
      </div>
      <div ref="graph" class="graph-tab">
        <h1 v-if="activeTab === 'runSpec' && !runSpecJobId">请选择runSpec</h1>
        <mtd-loading v-else-if="graphLoading" class="graph-loading"></mtd-loading>
        <custom-node-flow
          v-else
          :editable="false"
          :nodeComponent="nodeComponent"
          :data="flowData"
          :defaultSelectedNode="defaultSelectedNode"
          @select="updateSelectedNode"
          @unselect="unselectNode"
        />
        <mtd-drawer
          v-model="openDrawer"
          :width="500"
          :mask="false"
          title="节点详情"
          :mask-closable="false"
          :get-popup-container="getGraphContainer"
          :append-to-container="false"
          :closable="false"
          :destroy-on-close="true"
        >
          <mtd-form v-if="hasSelectedNode">
            <mtd-form-item label="节点名称">
              {{ selectedNodeData.label }}
            </mtd-form-item>
            <mtd-form-item label="节点定义">
              {{ selectedNodeVertexDefine()?.label }}
            </mtd-form-item>
            <mtd-form-item label="锚点名">
              {{ selectedNodeVertexDefine()?.anchor }}
            </mtd-form-item>
            <mtd-form-item label="插件配置">
              <node-bind-definition
                ref="nodeBindDefinition"
                :initData="selectedNodeVertexDefine()"
                :source="EvalSource.JOB"
                :disabled="!isEdit"
                :defaultExpandPath="defaultExpandPath"
                :globalDisabledPath="globalDisabledPath"
                @nodeUpdated="(e) => addPluginBindList(e.anchor, e.currBindType, e.changedBindType)"
              />
            </mtd-form-item>
          </mtd-form>
        </mtd-drawer>
      </div>
    </mtd-modal>
  </div>
</template>
<script lang="ts">
import NodeBindDefinition from '@/components/Eval/NodeBindDefinition.vue';
import CustomNodeFlow from '@/components/Eval/X6GraphFlow/CustomNodeFlow.vue';
import { Component, Prop, ProvideReactive, Ref, Spectra, Watch, Weaver } from '@/decorators';
import { AnchorDefine, BindType, FlowVertexNodeData, PluginBind } from '@/model/customEval';
import evalFlow from '@/store/modules/evalFlow';
import evalFlowJob from '@/store/modules/evalFlowJob';
import evalFlowVersion from '@/store/modules/evalFlowVersion';
import evalFlowVertexDefine from '@/store/modules/evalFlowVertexDefine';
import modelEvalDataSubSetUnVersioned from '@/store/modules/modelEvalDataSubSetUnVersioned';
import modelManage from '@/store/modules/modelManage';
import { EvalSource } from '@/utils';
import EvalFlowNode from '@/views/modelEval/EvalFlow/EvalFlowNode.vue';
import { generateAnchorBindMap } from '@/views/modelEval/utils';
import { cloneDeep, flatten } from 'lodash';
import PluginBindPreviewTable from './PluginBindPreviewTable.vue';

const bindTypeLabelMap = {
  [BindType.COVER]: '覆盖',
  [BindType.PARAM]: '参数',
};
@Component({
  components: {
    NodeBindDefinition,
    CustomNodeFlow,
    PluginBindPreviewTable,
  },
})
@Spectra
export default class PluginBindPreview extends Weaver(
  modelManage,
  modelEvalDataSubSetUnVersioned,
  evalFlowVersion,
  evalFlow,
  evalFlowJob,
  evalFlowVertexDefine
) {
  nodeComponent = EvalFlowNode;
  @Prop()
  context: any;

  @Ref('nodeBindDefinition')
  nodeBindDefinition!: NodeBindDefinition;

  @Prop()
  isEdit!: boolean;

  @Prop()
  getBindsByRunSpecId;

  @Prop()
  isBulk?: boolean;

  flowId = -1;
  flowVersion = -1;
  modal = false;
  activeTab = 'global';
  buttonLoading = false;
  graphLoading = false;
  openDrawer = false;
  hasSelectedNode = false;
  EvalSource = EvalSource;
  selectedNodeData: FlowVertexNodeData = {
    id: -1,
    label: '',
    vertexDefineId: -1,
    pluginBindList: [],
  };
  flowData: any = {
    nodes: [],
    edges: [],
  };
  runSpecJobId: string = '';

  anchorBindMap: Map<string, PluginBind[]> = new Map();
  globalAnchorBindMap: Map<string, PluginBind[]> = new Map();
  runSpecAnchorBindMap: Map<string, PluginBind[]> = new Map();
  prevSourceBindList: PluginBind[] = [];
  curSourceBindList: PluginBind[] = [];
  defaultSelectedNode = '';
  defaultExpandPath = '';

  get globalDisabledPath() {
    return this.activeTab === 'runSpec' ? [...this.globalAnchorBindMap.keys()] : [];
  }

  coveredCount() {
    let count = 0;

    const globalBindList: PluginBind[] = flatten([...this.globalAnchorBindMap.entries()].map((x) => x[1]));
    const runSpecBindList: PluginBind[] = flatten([...this.runSpecAnchorBindMap.entries()].map((x) => x[1]));
    [...globalBindList, ...runSpecBindList].forEach((bind) => {
      if (bind.bindType === BindType.COVER) count++;
      bind.contextParams.forEach((param) => {
        // 特殊逻辑：EvalModelInfer/model 的“计算资源类型”参数在此项目上不计数。
        if (bind.anchorPath === 'EvalModelInfer/model' && param.name === '_RESOURCE_TYPE') return;
        if (param.source === EvalSource.JOB) count++;
      });
    });
    return count;
  }
  handleButton() {
    this.defaultSelectedNode = '';
    this.defaultExpandPath = '';
    this.openModal();
  }

  mounted() {
    this.globalAnchorBindMap = new Map(JSON.parse(this.context.globalAnchorBindMapJson || '[]'));
    this.runSpecAnchorBindMap = new Map(JSON.parse(this.context.runSpecAnchorBindMapJson || '[]'));
  }

  getRunSpecJobId(runSpec) {
    return `${runSpec.runSpecSetId}-${runSpec.runSpecId}-${runSpec.evalDataSize}`;
  }

  getRunSpecJobLabel({ value }) {
    if (!value) return '';
    const [runSpecSetId, runSpecId, evalDataSize] = value.split('-');
    const runSpecLabel = this.context.runSpecFlowConfigList.find((x) => x.runSpecId === Number(runSpecId))?.dataSubSetName;
    return `${runSpecLabel}-${this.getBenchmarkEvalDataSizeLabel(evalDataSize)}`;
  }

  getBenchmarkEvalDataSizeLabel(dataSize) {
    return this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList.find((x) => x.name === String(dataSize))?.label || '-';
  }

  selectedNodeVertexDefine() {
    return {
      ...this.selectedNodeData!.vertexDefine,
      pluginBindList:
        this.activeTab === 'global'
          ? flatten([...this.globalAnchorBindMap.values(), this.context.globalPluginBindList])
          : this.getRunSpecPluginBindList(),
    };
  }

  @ProvideReactive('paramsMap')
  globalParamsMap: Map<string, any> = new Map();

  @Watch('activeTab')
  saveGlobalData(val, oldVal) {
    this.openDrawer = false;
    if (oldVal === 'global') {
      this.saveGlobalPluginBindList();
      this.initRunspecBind();
    } else this.saveRunSpecPluginBindList(this.runSpecJobId);
  }

  @Watch('runSpecJobId')
  saveRunSpecData(_, oldVal) {
    oldVal && this.saveRunSpecPluginBindList(oldVal);
    this.insertBenchmarkEnvParams();
    this.initRunspecBind();
  }

  @Watch('context', { deep: true })
  onContextChange() {
    this.insertGlobalParams();
  }

  insertGlobalParams() {
    const paramsList = this.context.globalAliasParams
      .filter((param) => param.value !== '__MULTIPLE__')
      .map((param) => [param.name, { ...param, value: param.value || param.defaultValue }]);
    const envParamsList = this.context.envGlobalAliasParams.map((param) => [
      param.name,
      { ...param, value: param.value || param.defaultValue, source: EvalSource.GLOBAL_ALIAS_JOB },
    ]);
    this.globalParamsMap = new Map([...paramsList, ...envParamsList]);
  }

  insertBenchmarkEnvParams() {
    const res = this.context.runSpecFlowConfigList.find((runSpec) => this.getRunSpecJobId(runSpec) === this.runSpecJobId).runSpecSetId;
    if (!res) return;
    const benchmark = this.context.benchmarkList.find((benchmark) => benchmark.runSpecSetId === res);
    if (!benchmark) return;
    benchmark.envGlobalAliasParams.forEach((param) => this.globalParamsMap.set(param.name, param));
  }

  openModal() {
    this.modal = true;
    this.insertGlobalParams();
    this.generateFlowData();
  }

  async initRunspecBind() {
    if (!this.runSpecJobId) return;
    const res = this.context.runSpecFlowConfigList.find((runSpec) => this.getRunSpecJobId(runSpec) === this.runSpecJobId);
    const [runSpecSetId, runSpecId] = this.runSpecJobId.split('-').map((x) => Number(x));
    const pluginBindList = await this.getBindsByRunSpecId(res.jobBenchmark.rootRunSpecSetId || res.jobBenchmark.runSpecSetId, runSpecId);
    if (!res) return;
    // runspec层的env变量merge进去
    res.envGlobalAliasParams?.forEach((param) => this.globalParamsMap.set(param.name, param));
    this.prevSourceBindList = pluginBindList;
    const curRunSpecBindList: PluginBind[] = [];
    [...this.runSpecAnchorBindMap.entries()].forEach(([k, v]) => {
      if (k.startsWith(this.runSpecJobId)) curRunSpecBindList.push(...v);
    });
    this.anchorBindMap = generateAnchorBindMap(curRunSpecBindList);
  }

  async generateFlowData() {
    this.graphLoading = true;
    const { vertexList, edgeList } = this.evalFlowVersion.evalFlowVersion;

    // 节点定义存入cache
    // @ts-ignore
    this.action$batchSetEvalFlowVertexDefine(vertexList.map((vertex) => vertex.vertexDefine));

    const nodes = vertexList.map((vertex) => ({
      id: String(vertex.id),
      shape: 'custom-vue-node',
      data: {
        ...vertex,
      },
    }));
    const edges = edgeList.map((edge) => ({
      source: String(edge.sourceVertexId),
      target: String(edge.targetVertexId),
      shape: 'data-processing-curve',
    }));
    this.flowData = { nodes, edges };
    this.$nextTick(() => (this.graphLoading = false));
  }
  unselectNode() {
    this.hasSelectedNode = false;
    setTimeout(() => {
      if (!this.hasSelectedNode) this.openDrawer = false;
    }, 50);
  }

  updateSelectedNode(val) {
    this.hasSelectedNode = true;
    this.openDrawer = true;
    this.selectedNodeData = val.data;
  }

  getGraphContainer() {
    return this.$refs.graph;
  }
  getRunSpecPluginBindList() {
    const res = [
      ...this.prevSourceBindList.filter((bind) => bind.anchorPath?.startsWith(this.selectedNodeData.vertexDefine!.anchor)), // 仅过滤出所选节点的绑定
      ...[...this.anchorBindMap.values()].flat().filter((bind) => bind.anchorPath?.startsWith(this.selectedNodeData.vertexDefine!.anchor)),
    ];
    return res;
  }

  unitAnchorPluginBindListMapCache: Map<string, PluginBind[]> = new Map();

  saveGlobalPluginBindList() {
    this.$emit('change', {
      runSpecFlowConfigList: cloneDeep(this.context.runSpecFlowConfigList),
      globalAnchorBindMap: JSON.stringify([...this.globalAnchorBindMap.entries()]),
      runSpecAnchorBindMap: JSON.stringify([...this.runSpecAnchorBindMap.entries()]),
    });
    this.$forceUpdate();
  }

  saveRunSpecPluginBindList(id) {
    const curRunSpec = this.context.runSpecFlowConfigList.find((runSpec) => this.getRunSpecJobId(runSpec) === id);
    if (!curRunSpec) return;
    // @ts-ignore
    curRunSpec.pluginBindList = [...this.prevSourceBindList, ...[...this.anchorBindMap.values()].flat()];
  }

  addPluginBindList(anchor: AnchorDefine, currBindType: BindType, changedBindType: Boolean) {
    const { anchorPath } = anchor;
    const jobPluginBindList = cloneDeep(anchor)
      .pluginBindList.map((anchorBind) => {
        // 如果是参数绑定且在当前层级有一个参数，那么就需要把它设置成当前层级
        if (anchorBind.bindType === BindType.PARAM && anchorBind.contextParams.find((param) => param.source === EvalSource.JOB)) {
          anchorBind.source = this.EvalSource.JOB;
        }
        return anchorBind;
      })
      .filter((x) => x.source === EvalSource.JOB);

    if (this.activeTab === 'runSpec') {
      this.anchorBindMap.set(anchorPath!, jobPluginBindList);
      this.runSpecAnchorBindMap.set(`${this.runSpecJobId}&${anchorPath}`, jobPluginBindList);
      if (currBindType === BindType.EXTEND && changedBindType) this.anchorBindMap.delete(anchorPath!);
    } else {
      this.globalAnchorBindMap.set(anchorPath!, jobPluginBindList);
    }
    this.$forceUpdate();
  }

  save() {
    this.openDrawer = false;
    this.saveGlobalData('', this.activeTab);
    this.$emit('change', {
      runSpecFlowConfigList: cloneDeep(this.context.runSpecFlowConfigList),
      globalAnchorBindMap: JSON.stringify([...this.globalAnchorBindMap.entries()]),
      runSpecAnchorBindMap: JSON.stringify([...this.runSpecAnchorBindMap.entries()]),
    });
    this.globalAnchorBindMap = new Map(this.globalAnchorBindMap);
    this.runSpecAnchorBindMap = new Map(this.runSpecAnchorBindMap);
  }

  handleVertexJump(row) {
    this.activeTab = row.dimension === '全局' ? 'global' : 'runSpec';
    if (row.dimension !== '全局') {
      this.runSpecJobId = row.dimension;
    }

    const { vertexList } = this.evalFlowVersion.evalFlowVersion;
    const anchor = row.anchorPath.split('/')[0];
    const vertexId = String(vertexList.find((x) => x.vertexDefine.anchor === anchor)?.id || '');
    this.defaultSelectedNode = vertexId;
    if (row.bindType === bindTypeLabelMap[BindType.PARAM]) {
      this.defaultExpandPath = row.anchorPath.replace(/@[^@]*$/, '');
    } else this.defaultExpandPath = row.anchorPath;
    this.$nextTick(this.openModal);
  }

  runSpecFilterMethod(query, value) {
    const runSpecId = value.split('-')[1];
    const label = this.context.runSpecFlowConfigList.find((x) => x.runSpecId === Number(runSpecId)).dataSubSetName;
    return (label + value).includes(query);
  }
}
</script>
<style lang="scss">
.plugin-bind-preview-modal {
  .header {
    display: flex;
    align-items: center;
    flex-direction: row;
    &-info {
      display: flex;
      align-items: center;
    }
    &-title {
      margin: 0 10px;
      font-size: 20px;
      font-weight: bold;
    }
    &-version {
      margin-right: 10px;
    }
  }
  .graph-tab {
    position: relative;
    height: 800px;
    .mtd-drawer {
      position: absolute;
    }
    .mtd-drawer-wrapper {
      pointer-events: none;
    }
  }
}
</style>
