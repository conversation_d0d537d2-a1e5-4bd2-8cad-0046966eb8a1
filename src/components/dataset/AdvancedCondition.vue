<script lang="ts">
import { type IPostDataHubGlanceSearchParameter } from '@/model/modules/dataHub';
import { cloneDeep } from 'lodash';
import dataManage from '@/store/modules/dataManage';
import { Component, Prop, PropSync } from 'vue-property-decorator';
import { <PERSON><PERSON><PERSON>, <PERSON> } from '@/decorators';

@Component({})
@Spectra
export default class AdvancedCondition extends Weaver(dataManage) {
  @PropSync('value', { default: false }) visible;
  @Prop() expression: IPostDataHubGlanceSearchParameter['expression'];
  innerExpression: IPostDataHubGlanceSearchParameter['expression'] = {
    expressionItemList: [],
    logicExpression: '',
  };

  calList = ['=', '<>'];

  open() {
    if (this.innerExpression) {
      this.$set(this.innerExpression, 'expressionItemList', cloneDeep(this.expression?.expressionItemList) || []);
      this.$set(this.innerExpression, 'logicExpression', this.expression?.logicExpression || '');
      this.innerExpression.expressionItemList?.length || this.addCondition();
    }
  }

  closeModal() {
    this.$emit('input', false);
  }

  addCondition(index = -1) {
    this.innerExpression?.expressionItemList?.splice(index + 1, 0, {
      code: '',
      key: 'text',
      cal: '=',
      val: '',
    });
  }

  deleteCondition(index: number) {
    this.innerExpression?.expressionItemList?.splice(index, 1);
  }

  setCode() {
    this.innerExpression?.expressionItemList?.forEach((e, i) => {
      e.code = String.fromCharCode(65 + i);
    });
  }

  clear() {
    if (this.expression) {
      this.$set(this.expression, 'expressionItemList', []);
      this.$set(this.expression, 'logicExpression', '');
    }

    this.open();
  }

  chackData() {
    if (this.innerExpression) {
      const { expressionItemList, logicExpression } = this.innerExpression;
      if (!logicExpression) {
        this.$mtd.message.warning('条件间关系不填写筛选不生效');
        return false;
      }

      const idx = expressionItemList?.findIndex((e) => !e.val) ?? -1;
      if (idx > -1) {
        this.$mtd.message.warning(`筛选条件值为必填项，请填写条件${String.fromCharCode(65 + idx)}的值`);
        return false;
      }
    }

    return true;
  }

  confirm() {
    if (!this.chackData()) return;
    if (this.expression) {
      this.setCode();
      this.$set(this.expression, 'expressionItemList', cloneDeep(this.innerExpression?.expressionItemList) || []);
      this.$set(this.expression, 'logicExpression', this.innerExpression?.logicExpression || '');
    }

    this.$emit('change', this.expression);
    this.closeModal();
  }

  mounted() {
    this.action$getGlanceFields({});
  }
}
</script>

<template>
  <mtd-modal v-model="visible" title="高级筛选" width="800px" @open="open" @close="closeModal">
    <div>
      <div style="margin-bottom: 10px; margin-left: 30px">
        筛选条件
        <span style="font-size: 12px; color: red; margin-left: 10px">条件间关系不填写筛选不生效</span>
      </div>
      <mtd-form :model="innerExpression">
        <mtd-form-item v-for="(item, index) in innerExpression.expressionItemList" :key="index" :label="String.fromCharCode(65 + index)">
          <div style="display: flex; width: 600px">
            <mtd-select v-model="item.key" style="width: 150px; margin-right: 10px" placeholder="字段">
              <mtd-option v-for="keyName in getGlanceFields ?? []" :key="keyName" :label="keyName" :value="keyName" />
            </mtd-select>
            <mtd-select v-model="item.cal" style="width: 150px" placeholder="符号">
              <mtd-option v-for="calName in calList" :key="calName" :label="calName" :value="calName" />
            </mtd-select>
            <mtd-input v-model="item.val" clearable placeholder="值" type="text" style="width: 150px; margin-left: 10px" />
            <mtd-icon-button
              :disabled="innerExpression.expressionItemList.length < 2"
              style="margin-left: 12px"
              icon="mtdicon mtdicon-minus"
              @click="deleteCondition(index)"
            />
            <mtd-icon-button
              :disabled="innerExpression.expressionItemList.length > 4"
              icon="mtdicon mtdicon-add"
              @click="addCondition(index)"
            />
          </div>
        </mtd-form-item>
        <mtd-form-item label="条件间关系" prop="logicExpression">
          <mtd-input
            v-model="innerExpression.logicExpression"
            type="text"
            style="width: 500px"
            clearable
            placeholder="不填写筛选不生效，填写示例：（A or B）and C"
          />
        </mtd-form-item>
      </mtd-form>
    </div>
    <div slot="footer" class="demo-modal-footer">
      <mtd-button @click="clear">清空</mtd-button>
      <mtd-button type="primary" @click="confirm">确认筛选</mtd-button>
    </div>
  </mtd-modal>
</template>

<style scoped lang="scss"></style>
