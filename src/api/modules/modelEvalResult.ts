import { FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1332351, catid=227021, projectId=35802, created=2023-11-24 14:22:49, last-modified=2023-11-24 18:03:38
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalResult/listDataSet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332351)
 */
export function postModelEvalResultListDataSet(
  params: model.modelEvalResult.IPostModelEvalResultListDataSetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalResult.IPostModelEvalResultListDataSetResponse>('/webApi/modelEvalResult/listDataSet', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
