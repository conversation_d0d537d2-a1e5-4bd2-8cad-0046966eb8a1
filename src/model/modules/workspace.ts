/**
 * 模型信息列表请求入参
 */
export interface IPostWorkspaceModelListCandidateModelParameter {
  /**
   * 模型创建人
   */
  creator?: string;
  /**
   * 页码
   */
  curPage: number;
  /**
   * 动态字段展示值
   */
  customTagValue?: {
    /**
     * 唯一标识, 前后端交互用
     */
    identification: string;
    /**
     * 字段值
     */
    values?: {
      /**
       * 可选值展示名
       */
      label?: string;
      /**
       * 可选值
       */
      name?: string;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  /**
   * 模型家族
   */
  modelFamily?: string;
  /**
   * 模型格式
   */
  modelFormat?: string;
  /**
   * 模型名
   */
  modelName?: string;
  /**
   * 模型地址
   */
  modelPath?: string;
  /**
   * 产出阶段
   */
  modelStage?: string;
  /**
   * 模型地址是否为正则模式
   */
  regularMode?: boolean;
  /**
   * 每页显示数
   */
  showCount: number;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelListCandidateModelResponse {
  /**
   * 模型信息列表请求返回值
   */
  data: {
    /**
     * 模型列表
     */
    modelList: {
      /**
       * 模型列表是否是全部Checkpoint
       */
      allInstances: boolean;
      /**
       * 是否为标杆模型
       */
      benchmarking: boolean;
      /**
       * 模型信息
       */
      bestCheckpointModelInfo?: {
        /**
         * 是否为标杆模型
         */
        benchmarking?: boolean;
        /**
         * 模型创建人
         */
        creator: string;
        /**
         * 创建时间
         */
        cts: string;
        /**
         * 实验任务详情id
         */
        experimentTaskDetailId?: number;
        /**
         * 扩展词表地址
         */
        extTokenizerPath?: string;
        /**
         * 扩展字段集合
         */
        extraInfoFields?: {
          /**
           * 扩展字段值类型
           */
          fieldType?: string;
          /**
           * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
           */
          filedKey?:
            | 'CONTEXT_LENGTH'
            | 'REQUEST_BATCH_SIZE'
            | 'MAX_NEW_TOKENS'
            | 'FOLLOW_DATA_SUB_SET'
            | 'TEMPERATURE'
            | 'REPETITION_PENALTY'
            | 'TOP_K'
            | 'TOP_P'
            | 'PROMPT_PREFIX'
            | 'PROMPT_SUFFIX'
            | 'SYSTEM_PROMPT'
            | 'STOP_SEQUENCES'
            | 'MODEL_SCORE_EXTRACT_PREFIX'
            | 'MODEL_SCORE_EXTRACT_SUFFIX'
            | 'AUTO_EVAL_DATA_SIZE'
            | 'CALCULATE_TOKEN'
            | 'CALCULATE_TOKEN_PARAM'
            | 'AUTO_EVAL'
            | 'AUTO_EVAL_PARAM'
            | 'TRAIN_TASK_ID'
            | 'MODEL_CONFIG_DIRECTORY'
            | 'COMPARISON_MODEL_META_ID'
            | 'COMPARISON_BENCHMARK_ID'
            | 'TP'
            | 'PP'
            | 'AUTO_TOKEN'
            | 'TOKEN'
            | 'FLOPS';
          /**
           * 扩展字段key
           */
          filedName?: string;
          /**
           * 扩展字段值
           */
          filedValue?: string;
          /**
           * 是否系统创建
           */
          systemCreate?: boolean;
          [k: string]: unknown;
        }[];
        /**
         * 模型训练每秒浮点计算次数
         */
        flops?: string;
        /**
         * 模型checkpoint
         */
        modelCheckpoint: string;
        /**
         * 模型格式
         */
        modelFormat: string;
        /**
         * 模型id
         */
        modelId: number;
        /**
         * 模型meta信息id
         */
        modelMetaId?: number;
        /**
         * 模型名
         */
        modelName?: string;
        /**
         * 模型地址
         */
        modelPath: string;
        /**
         * 模型别名
         */
        modelShortName?: string;
        /**
         * 产出阶段
         */
        modelStage?: string;
        /**
         * 模型状态
         */
        modelStatus: boolean;
        /**
         * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
         */
        modelType: number;
        /**
         * 模型mp
         */
        pp?: number;
        /**
         * 注册渠道
         */
        registerChannel: string;
        /**
         * 模型step
         */
        step?: string;
        /**
         * 模型标签
         */
        tagValues?: {
          /**
           * 唯一标识, 前后端交互用
           */
          identification: string;
          /**
           * 字段值
           */
          values?: {
            /**
             * 可选值展示名
             */
            label?: string;
            /**
             * 可选值
             */
            name?: string;
            [k: string]: unknown;
          }[];
          [k: string]: unknown;
        }[];
        /**
         * 模型训练使用token数
         */
        token?: string;
        /**
         * 模型tp
         */
        tp?: number;
        [k: string]: unknown;
      };
      /**
       * 模型列表
       */
      modelInstances: {
        /**
         * 评测数据集规模，评测指标查询使用的默认规模
         */
        evalDataSetSize?: string;
        /**
         * 产出模型的任务id
         */
        experimentTaskDetailId?: number;
        /**
         * 模型checkpoint
         */
        modelCheckpoint: string;
        /**
         * 模型格式
         */
        modelFormat: string;
        /**
         * 模型id
         */
        modelId: number;
        /**
         * 模型地址
         */
        modelPath: string;
        [k: string]: unknown;
      }[];
      /**
       * 模型metaId
       */
      modelMetaId: number;
      /**
       * 模型名
       */
      modelName: string;
      /**
       * 模型别名
       */
      modelShortName: string;
      /**
       * 产出阶段
       */
      modelStage?: string;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型MASTER指标合入审批结果
 */
export interface IPostWorkspaceModelMergeMasterApproveResultParameter {
  /**
   * 批次号
   */
  batchId: string;
  /**
   * 驳回原因
   */
  message?: string;
  /**
   * 模型metaId
   */
  modelMetaIds: number[];
  /**
   * 审批结果, 1: 通过, 2: 驳回, 3: 撤销, 4: 确认
   */
  result: number;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelMergeMasterApproveResultResponse {
  /**
   * 响应数据
   */
  data: {
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型评估指标请求入参
 */
export interface IPostWorkspaceModelListEvalModelNameV3Parameter {
  /**
   * 是否为自定义评测tab
   */
  customChart?: boolean;
  /**
   * 评估数据集
   */
  evalDataSet: string;
  /**
   * 评估数据集规模
   */
  evalDataSetSize: string;
  /**
   * 评测版本
   */
  evalMetaId?: number;
  /**
   * 评估实验
   */
  evalModelName: string;
  /**
   * 模型ID列表请求入参
   */
  modelIdRequest: {
    /**
     * 模型Id
     */
    modelAndMetaIds: {
      /**
       * 模型Id
       */
      modelIds?: number[];
      /**
       * 模型metaId
       */
      modelMetaId: number;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelListEvalModelNameV3Response {
  /**
   * 响应数据
   */
  data: {
    /**
     * 默认选中的评估模型名格式规则
     */
    defaultEvalModelNames: string[];
    /**
     * 模型meta Id
     */
    metaId: number;
    /**
     * 模型model与评测任务关系
     */
    modelAndEvalList: {
      /**
       * ckpt值
       */
      checkpoint: string;
      /**
       * 所属全部评测任务列表(名称ckpt段经过处理)
       */
      evalModelNames: string[];
      /**
       * modelId
       */
      modelId: number;
      [k: string]: unknown;
    }[];
    /**
     * 模型名
     */
    modelName?: string;
    [k: string]: unknown;
  }[];
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

export interface IPostWorkspaceModelCancelCollectViewParameter {
  /**
   * viewId
   */
  viewId: string;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelCancelCollectViewResponse {
  /**
   * 响应数据
   */
  data: {
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型评估指标请求入参
 */
export interface IPostWorkspaceModelDiffMetricsChartParameter {
  /**
   * 折线调整参数
   */
  curveModifier: {
    /**
     * 划定ckpt阈值: 过滤时仅考虑大于阈值的ckpt
     */
    labelThreshold: string;
    /**
     * 置信区间, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
     */
    topCIPercentage: number;
    /**
     * 困难的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
     */
    topDiffPercentage: number;
    /**
     * 简单的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
     */
    topEasyPercentage: number;
    /**
     * 波动的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
     */
    topVariancePercentage: number;
    [k: string]: unknown;
  };
  /**
   * 自定义图表
   */
  customChart?: boolean;
  /**
   * 数据子集图表标识
   */
  dataSubSetChart?: boolean;
  /**
   * 数据子集id列表, 自定义图表必填
   */
  dataSubSetIds?: number[];
  /**
   * 评估数据集
   */
  evalDataSet: string;
  /**
   * 评估数据集规模
   */
  evalDataSetSize: string;
  /**
   * 评测版本
   */
  evalMetaId?: number;
  /**
   * 评估指标维度
   */
  evalModelMetricsDimension: string;
  /**
   * 评估指标名
   */
  evalModelMetricsNames?: string[];
  /**
   * 评估实验
   */
  evalModelName: string;
  /**
   * 根据模型查询评估模型名的过滤规则
   */
  evalModelNameRules: {
    /**
     * 全部的评估模型名格式规则
     */
    allEvalModelNameRules?: string[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    /**
     * 模型名
     */
    modelName?: string;
    /**
     * 已选中的评估模型名格式规则
     */
    specifyEvalModelNameRules: string[];
    [k: string]: unknown;
  }[];
  /**
   * 心跳超时时间, 单位秒
   */
  heartbeatTtl: number;
  /**
   * 忽略无效数据点
   */
  ignoreInvalidData: boolean;
  /**
   * 模型ID列表请求入参
   */
  modelIdRequest: {
    /**
     * 模型Id
     */
    modelAndMetaIds: {
      /**
       * 模型Id
       */
      modelIds?: number[];
      /**
       * 模型metaId
       */
      modelMetaId: number;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  /**
   * 自定义图表必填, true: 无版本数据子集, false:  有版本数据子集
   */
  nonversionDataSubset?: boolean;
  /**
   * uuid, 请求的唯一标识
   */
  queryUuid: string;
  /**
   * MASTER指标合并请求发起页面的标识, 不需要传值
   */
  rightActiveTab?: string;
  /**
   * 指标维度, 自定义图表必填
   */
  statValue?: string;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelDiffMetricsChartResponse {
  /**
   * 模型评估指标返回值(图表形式)
   */
  data: {
    /**
     * 模型评估指标
     */
    metricsCharts?: {
      /**
       * 图表曲线
       */
      metricsChartCurves?: {
        /**
         * 模型评估指标
         */
        metricsList?: {
          /**
           * 评测模型名family
           */
          family?: string;
          /**
           * 有结果的数据子集数
           */
          hasStatSubSetCount?: number;
          modelEvalModelStat?: {
            evalDataSize?: string;
            family?: string;
            groupValue?: {
              confidenceInterval?: string;
              hasStatSubSetCount?: number;
              macroMean?: string;
              mean?: string;
              result?: {
                binNames?: string[];
                binResultList?: {
                  calculateUnitList?: {
                    aggrateType?: string;
                    autoEvalDataSizeList?: string[];
                    commonSubSetEvalInstanceCount?: number;
                    complete?: boolean;
                    confidenceInterval?: number;
                    configTotalInstanceCount?: number;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    evalDataSize?: string;
                    evalKeyCount?: number;
                    instanceCount?: number;
                    instanceStatCalculateUnitList?: {
                      evalKey?: string;
                      instanceId?: string;
                      statValue?: number;
                      [k: string]: unknown;
                    }[];
                    invalidReason?: string;
                    modelFamily?: string;
                    modelName?: string;
                    runSpecSetId?: number;
                    statType?: string;
                    statValue?: number;
                    subSetId?: number;
                    textLenOutputCount?: number;
                    tiktokenOutputCount?: number;
                    tokenInstanceCount?: number;
                    tokenOutputCount?: number;
                    totalInstanceCount?: number;
                    valueIsValid?: boolean;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  categoryIds?: number[];
                  dataSubSetIds?: number[];
                  dataSubSetWeightSum?: number;
                  evalDataSize?: string;
                  family?: string;
                  hasStatDataSubSetIds?: number[];
                  name?: string;
                  notCalculateDataSubSetList?: {
                    autoEvalDataSizeOrder?: string;
                    autoEvalDataSizeOrderList?: string[];
                    bindingRunSpecList?: {
                      cts?: string;
                      dataSetLabel?: string;
                      dataSetName?: string;
                      dataSubSetId?: number;
                      dataSubSetLabel?: string;
                      dataSubSetName?: string;
                      dataSubSetPublicStatus?: string;
                      description?: string;
                      extraParam?: string;
                      id?: number;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    categoryList?: unknown[];
                    cts?: string;
                    dataSet?: {
                      cts?: string;
                      description?: string;
                      execType?: string;
                      id?: number;
                      label?: string;
                      metaVersionId?: number;
                      name?: string;
                      ownerList?: string[];
                      runtimeConfig?: string;
                      status?: string;
                      subSetCount?: number;
                      uts?: string;
                      [k: string]: unknown;
                    };
                    dataSetLabel?: string;
                    dataSetName?: string;
                    description?: string;
                    evalType?: string;
                    files?: {
                      cts?: string;
                      dataKey?: string;
                      detail?: {
                        bucket?: string;
                        dataSetName?: string;
                        dataSetVersionName?: string;
                        errorMsg?: string;
                        index?: string;
                        labelType?: number;
                        path?: string;
                        [k: string]: unknown;
                      };
                      detailJson?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      storageType?: string;
                      uploadTs?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    id?: number;
                    instanceCount?: number;
                    instanceDetail?: {
                      instanceList?: {
                        list?: {
                          categoryList?: string;
                          correctAnswer?: string;
                          cts?: string;
                          id?: number;
                          instanceId?: string;
                          modelEvalDataSubSetId?: number;
                          modelPrompt?: string;
                          originInput?: string;
                          uts?: string;
                          [k: string]: unknown;
                        }[];
                        totalCount?: number;
                        [k: string]: unknown;
                      };
                      instanceOriginContent?: string;
                      [k: string]: unknown;
                    };
                    label?: string;
                    metaVersionId?: number;
                    modelEvalDataSetId?: number;
                    name?: string;
                    publicStatus?: string;
                    statType?: string;
                    stats?: {
                      cts?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    tags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    uts?: string;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  value?: number;
                  [k: string]: unknown;
                }[];
                categoryId?: number;
                children?: {
                  binNames?: string[];
                  children?: unknown[];
                  confidenceIntervalName?: string;
                  itemDisplayType?: string;
                  label?: string;
                  name?: string;
                  objectiveBaseValueConfig?: {
                    dataSubSetTags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    evalDataSize?: string;
                    modelFamily?: string;
                    modelMetaIdForMaster?: number;
                    modelName?: string;
                    statName?: string;
                    [k: string]: unknown;
                  };
                  objectiveEvalValueConfig?: {
                    dataSubSetTags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    evalDataSize?: string;
                    modelFamily?: string;
                    modelMetaIdForMaster?: number;
                    modelName?: string;
                    statName?: string;
                    [k: string]: unknown;
                  };
                  objectiveValueConfigList?: {
                    dataSubSetTags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    evalDataSize?: string;
                    modelFamily?: string;
                    modelMetaIdForMaster?: number;
                    modelName?: string;
                    statName?: string;
                    [k: string]: unknown;
                  }[];
                  relationTagNameList?: string[];
                  secondStatName?: string;
                  showCapabilityReportFlag?: boolean;
                  subjectiveValueConfigList?: {
                    dataSubSetTags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    evalDataSize?: string;
                    statName?: string;
                    [k: string]: unknown;
                  }[];
                  winRateFlag?: boolean;
                  [k: string]: unknown;
                }[];
                confidenceInterval?: string;
                confidenceIntervalName?: string;
                confidenceResult?: {
                  calculateUnitList?: {
                    aggrateType?: string;
                    autoEvalDataSizeList?: string[];
                    commonSubSetEvalInstanceCount?: number;
                    complete?: boolean;
                    confidenceInterval?: number;
                    configTotalInstanceCount?: number;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    evalDataSize?: string;
                    evalKeyCount?: number;
                    instanceCount?: number;
                    instanceStatCalculateUnitList?: {
                      evalKey?: string;
                      instanceId?: string;
                      statValue?: number;
                      [k: string]: unknown;
                    }[];
                    invalidReason?: string;
                    modelFamily?: string;
                    modelName?: string;
                    runSpecSetId?: number;
                    statType?: string;
                    statValue?: number;
                    subSetId?: number;
                    textLenOutputCount?: number;
                    tiktokenOutputCount?: number;
                    tokenInstanceCount?: number;
                    tokenOutputCount?: number;
                    totalInstanceCount?: number;
                    valueIsValid?: boolean;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  categoryIds?: number[];
                  dataSubSetIds?: number[];
                  dataSubSetWeightSum?: number;
                  evalDataSize?: string;
                  family?: string;
                  hasStatDataSubSetIds?: number[];
                  name?: string;
                  notCalculateDataSubSetList?: {
                    autoEvalDataSizeOrder?: string;
                    autoEvalDataSizeOrderList?: string[];
                    bindingRunSpecList?: {
                      cts?: string;
                      dataSetLabel?: string;
                      dataSetName?: string;
                      dataSubSetId?: number;
                      dataSubSetLabel?: string;
                      dataSubSetName?: string;
                      dataSubSetPublicStatus?: string;
                      description?: string;
                      extraParam?: string;
                      id?: number;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    categoryList?: unknown[];
                    cts?: string;
                    dataSet?: {
                      cts?: string;
                      description?: string;
                      execType?: string;
                      id?: number;
                      label?: string;
                      metaVersionId?: number;
                      name?: string;
                      ownerList?: string[];
                      runtimeConfig?: string;
                      status?: string;
                      subSetCount?: number;
                      uts?: string;
                      [k: string]: unknown;
                    };
                    dataSetLabel?: string;
                    dataSetName?: string;
                    description?: string;
                    evalType?: string;
                    files?: {
                      cts?: string;
                      dataKey?: string;
                      detail?: {
                        bucket?: string;
                        dataSetName?: string;
                        dataSetVersionName?: string;
                        errorMsg?: string;
                        index?: string;
                        labelType?: number;
                        path?: string;
                        [k: string]: unknown;
                      };
                      detailJson?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      storageType?: string;
                      uploadTs?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    id?: number;
                    instanceCount?: number;
                    instanceDetail?: {
                      instanceList?: {
                        list?: {
                          categoryList?: string;
                          correctAnswer?: string;
                          cts?: string;
                          id?: number;
                          instanceId?: string;
                          modelEvalDataSubSetId?: number;
                          modelPrompt?: string;
                          originInput?: string;
                          uts?: string;
                          [k: string]: unknown;
                        }[];
                        totalCount?: number;
                        [k: string]: unknown;
                      };
                      instanceOriginContent?: string;
                      [k: string]: unknown;
                    };
                    label?: string;
                    metaVersionId?: number;
                    modelEvalDataSetId?: number;
                    name?: string;
                    publicStatus?: string;
                    statType?: string;
                    stats?: {
                      cts?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    tags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    uts?: string;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  value?: number;
                  [k: string]: unknown;
                };
                dataSetId?: number;
                dataSubSetId?: number;
                hasStatSubSetCount?: number;
                itemDisplayType?: string;
                label?: string;
                name?: string;
                objectiveBaseValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveEvalValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                relationTagNameList?: string[];
                result?: {
                  calculateUnitList?: {
                    aggrateType?: string;
                    autoEvalDataSizeList?: string[];
                    commonSubSetEvalInstanceCount?: number;
                    complete?: boolean;
                    confidenceInterval?: number;
                    configTotalInstanceCount?: number;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    evalDataSize?: string;
                    evalKeyCount?: number;
                    instanceCount?: number;
                    instanceStatCalculateUnitList?: {
                      evalKey?: string;
                      instanceId?: string;
                      statValue?: number;
                      [k: string]: unknown;
                    }[];
                    invalidReason?: string;
                    modelFamily?: string;
                    modelName?: string;
                    runSpecSetId?: number;
                    statType?: string;
                    statValue?: number;
                    subSetId?: number;
                    textLenOutputCount?: number;
                    tiktokenOutputCount?: number;
                    tokenInstanceCount?: number;
                    tokenOutputCount?: number;
                    totalInstanceCount?: number;
                    valueIsValid?: boolean;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  categoryIds?: number[];
                  dataSubSetIds?: number[];
                  dataSubSetWeightSum?: number;
                  evalDataSize?: string;
                  family?: string;
                  hasStatDataSubSetIds?: number[];
                  name?: string;
                  notCalculateDataSubSetList?: {
                    autoEvalDataSizeOrder?: string;
                    autoEvalDataSizeOrderList?: string[];
                    bindingRunSpecList?: {
                      cts?: string;
                      dataSetLabel?: string;
                      dataSetName?: string;
                      dataSubSetId?: number;
                      dataSubSetLabel?: string;
                      dataSubSetName?: string;
                      dataSubSetPublicStatus?: string;
                      description?: string;
                      extraParam?: string;
                      id?: number;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    categoryList?: unknown[];
                    cts?: string;
                    dataSet?: {
                      cts?: string;
                      description?: string;
                      execType?: string;
                      id?: number;
                      label?: string;
                      metaVersionId?: number;
                      name?: string;
                      ownerList?: string[];
                      runtimeConfig?: string;
                      status?: string;
                      subSetCount?: number;
                      uts?: string;
                      [k: string]: unknown;
                    };
                    dataSetLabel?: string;
                    dataSetName?: string;
                    description?: string;
                    evalType?: string;
                    files?: {
                      cts?: string;
                      dataKey?: string;
                      detail?: {
                        bucket?: string;
                        dataSetName?: string;
                        dataSetVersionName?: string;
                        errorMsg?: string;
                        index?: string;
                        labelType?: number;
                        path?: string;
                        [k: string]: unknown;
                      };
                      detailJson?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      storageType?: string;
                      uploadTs?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    id?: number;
                    instanceCount?: number;
                    instanceDetail?: {
                      instanceList?: {
                        list?: {
                          categoryList?: string;
                          correctAnswer?: string;
                          cts?: string;
                          id?: number;
                          instanceId?: string;
                          modelEvalDataSubSetId?: number;
                          modelPrompt?: string;
                          originInput?: string;
                          uts?: string;
                          [k: string]: unknown;
                        }[];
                        totalCount?: number;
                        [k: string]: unknown;
                      };
                      instanceOriginContent?: string;
                      [k: string]: unknown;
                    };
                    label?: string;
                    metaVersionId?: number;
                    modelEvalDataSetId?: number;
                    name?: string;
                    publicStatus?: string;
                    statType?: string;
                    stats?: {
                      cts?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    tags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    uts?: string;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  value?: number;
                  [k: string]: unknown;
                };
                secondStatName?: string;
                secondValue?: string;
                showCapabilityReportFlag?: boolean;
                subjectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                totalSubSetCount?: number;
                type?: string;
                value?: string;
                winRateFlag?: boolean;
                [k: string]: unknown;
              };
              secondValue?: string;
              totalSubSetCount?: number;
              [k: string]: unknown;
            };
            label?: string;
            llmModel?: {
              authType?: string;
              autoEvalDataSize?: string;
              benchmarking?: string;
              cts?: string;
              family?: string;
              id?: number;
              label?: string;
              modelId?: number;
              modelMetaId?: number;
              modelPath?: string;
              name?: string;
              nlpOpenRunSpecSetId?: number;
              ownerList?: string[];
              simpleTags?: string[];
              status?: string;
              tags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              uts?: string;
              viewerMisList?: string[];
              viewerRoleList?: string[];
              [k: string]: unknown;
            };
            /**
             * 模型信息
             */
            modelInfoVo?: {
              /**
               * 是否为标杆模型
               */
              benchmarking?: boolean;
              /**
               * 模型创建人
               */
              creator: string;
              /**
               * 创建时间
               */
              cts: string;
              /**
               * 实验任务详情id
               */
              experimentTaskDetailId?: number;
              /**
               * 扩展词表地址
               */
              extTokenizerPath?: string;
              /**
               * 扩展字段集合
               */
              extraInfoFields?: {
                /**
                 * 扩展字段值类型
                 */
                fieldType?: string;
                /**
                 * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
                 */
                filedKey?:
                  | 'CONTEXT_LENGTH'
                  | 'REQUEST_BATCH_SIZE'
                  | 'MAX_NEW_TOKENS'
                  | 'FOLLOW_DATA_SUB_SET'
                  | 'TEMPERATURE'
                  | 'REPETITION_PENALTY'
                  | 'TOP_K'
                  | 'TOP_P'
                  | 'PROMPT_PREFIX'
                  | 'PROMPT_SUFFIX'
                  | 'SYSTEM_PROMPT'
                  | 'STOP_SEQUENCES'
                  | 'MODEL_SCORE_EXTRACT_PREFIX'
                  | 'MODEL_SCORE_EXTRACT_SUFFIX'
                  | 'AUTO_EVAL_DATA_SIZE'
                  | 'CALCULATE_TOKEN'
                  | 'CALCULATE_TOKEN_PARAM'
                  | 'AUTO_EVAL'
                  | 'AUTO_EVAL_PARAM'
                  | 'TRAIN_TASK_ID'
                  | 'MODEL_CONFIG_DIRECTORY'
                  | 'COMPARISON_MODEL_META_ID'
                  | 'COMPARISON_BENCHMARK_ID'
                  | 'TP'
                  | 'PP'
                  | 'AUTO_TOKEN'
                  | 'TOKEN'
                  | 'FLOPS';
                /**
                 * 扩展字段key
                 */
                filedName?: string;
                /**
                 * 扩展字段值
                 */
                filedValue?: string;
                /**
                 * 是否系统创建
                 */
                systemCreate?: boolean;
                [k: string]: unknown;
              }[];
              /**
               * 模型训练每秒浮点计算次数
               */
              flops?: string;
              /**
               * 模型checkpoint
               */
              modelCheckpoint: string;
              /**
               * 模型格式
               */
              modelFormat: string;
              /**
               * 模型id
               */
              modelId: number;
              /**
               * 模型meta信息id
               */
              modelMetaId?: number;
              /**
               * 模型名
               */
              modelName?: string;
              /**
               * 模型地址
               */
              modelPath: string;
              /**
               * 模型别名
               */
              modelShortName?: string;
              /**
               * 产出阶段
               */
              modelStage?: string;
              /**
               * 模型状态
               */
              modelStatus: boolean;
              /**
               * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
               */
              modelType: number;
              /**
               * 模型mp
               */
              pp?: number;
              /**
               * 注册渠道
               */
              registerChannel: string;
              /**
               * 模型step
               */
              step?: string;
              /**
               * 模型标签
               */
              tagValues?: {
                /**
                 * 唯一标识, 前后端交互用
                 */
                identification: string;
                /**
                 * 字段值
                 */
                values?: {
                  /**
                   * 可选值展示名
                   */
                  label?: string;
                  /**
                   * 可选值
                   */
                  name?: string;
                  [k: string]: unknown;
                }[];
                [k: string]: unknown;
              }[];
              /**
               * 模型训练使用token数
               */
              token?: string;
              /**
               * 模型tp
               */
              tp?: number;
              [k: string]: unknown;
            };
            name?: string;
            nonversionValueList?: {
              categoryId?: number;
              dataSubSetId?: number;
              description?: string;
              label?: string;
              name?: string;
              publicStatus?: string;
              unitList?: {
                confidenceInterval?: string;
                dataSetOpennessStatus?: string;
                hasStatSubSetCount?: number;
                objectivityIntegrationMean?: string;
                result?: {
                  calculateUnitList?: {
                    aggrateType?: string;
                    autoEvalDataSizeList?: string[];
                    commonSubSetEvalInstanceCount?: number;
                    complete?: boolean;
                    confidenceInterval?: number;
                    configTotalInstanceCount?: number;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    evalDataSize?: string;
                    evalKeyCount?: number;
                    instanceCount?: number;
                    instanceStatCalculateUnitList?: {
                      evalKey?: string;
                      instanceId?: string;
                      statValue?: number;
                      [k: string]: unknown;
                    }[];
                    invalidReason?: string;
                    modelFamily?: string;
                    modelName?: string;
                    runSpecSetId?: number;
                    statType?: string;
                    statValue?: number;
                    subSetId?: number;
                    textLenOutputCount?: number;
                    tiktokenOutputCount?: number;
                    tokenInstanceCount?: number;
                    tokenOutputCount?: number;
                    totalInstanceCount?: number;
                    valueIsValid?: boolean;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  categoryIds?: number[];
                  dataSubSetIds?: number[];
                  dataSubSetWeightSum?: number;
                  evalDataSize?: string;
                  family?: string;
                  hasStatDataSubSetIds?: number[];
                  instanceCountRatio?: number;
                  instanceIncompleteList?: {
                    dataSetName?: string;
                    id?: number;
                    invalidReason?: string;
                    name?: string;
                    [k: string]: unknown;
                  }[];
                  name?: string;
                  notCalculateDataSubSetList?: {
                    dataSetName?: string;
                    id?: number;
                    invalidReason?: string;
                    name?: string;
                    [k: string]: unknown;
                  }[];
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  value?: number;
                  [k: string]: unknown;
                };
                secondValue?: string;
                subjectivityIsFromModelEval?: boolean;
                textLenOutputAvg?: number;
                tikTokenOutputAvg?: number;
                tokenOutputAvg?: number;
                totalSubSetCount?: number;
                value?: string;
                [k: string]: unknown;
              }[];
              [k: string]: unknown;
            }[];
            searchEvalDataSize?: string;
            valueList?: {
              binNames?: string[];
              binResultList?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              }[];
              categoryId?: number;
              children?: {
                binNames?: string[];
                children?: unknown[];
                confidenceIntervalName?: string;
                itemDisplayType?: string;
                label?: string;
                name?: string;
                objectiveBaseValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveEvalValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                relationTagNameList?: string[];
                secondStatName?: string;
                showCapabilityReportFlag?: boolean;
                subjectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                winRateFlag?: boolean;
                [k: string]: unknown;
              }[];
              confidenceInterval?: string;
              confidenceIntervalName?: string;
              confidenceResult?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              };
              dataSetId?: number;
              dataSubSetId?: number;
              hasStatSubSetCount?: number;
              itemDisplayType?: string;
              label?: string;
              name?: string;
              objectiveBaseValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveEvalValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              relationTagNameList?: string[];
              result?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              };
              secondStatName?: string;
              secondValue?: string;
              showCapabilityReportFlag?: boolean;
              subjectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              totalSubSetCount?: number;
              type?: string;
              value?: string;
              winRateFlag?: boolean;
              [k: string]: unknown;
            }[];
            winRateValue?: {
              comprehensiveValue?: string;
              flag?: boolean;
              objectiveValue?: string;
              objectiveValueEfficient?: number;
              objectiveValueTotal?: number;
              subjectiveValue?: string;
              subjectiveValueEfficient?: number;
              subjectiveValueTotal?: number;
              [k: string]: unknown;
            };
            [k: string]: unknown;
          };
          /**
           * 模型Id
           */
          modelId?: number;
          /**
           * 模型信息
           */
          modelInfoVo?: {
            /**
             * 是否为标杆模型
             */
            benchmarking?: boolean;
            /**
             * 模型创建人
             */
            creator: string;
            /**
             * 创建时间
             */
            cts: string;
            /**
             * 实验任务详情id
             */
            experimentTaskDetailId?: number;
            /**
             * 扩展词表地址
             */
            extTokenizerPath?: string;
            /**
             * 扩展字段集合
             */
            extraInfoFields?: {
              /**
               * 扩展字段值类型
               */
              fieldType?: string;
              /**
               * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
               */
              filedKey?:
                | 'CONTEXT_LENGTH'
                | 'REQUEST_BATCH_SIZE'
                | 'MAX_NEW_TOKENS'
                | 'FOLLOW_DATA_SUB_SET'
                | 'TEMPERATURE'
                | 'REPETITION_PENALTY'
                | 'TOP_K'
                | 'TOP_P'
                | 'PROMPT_PREFIX'
                | 'PROMPT_SUFFIX'
                | 'SYSTEM_PROMPT'
                | 'STOP_SEQUENCES'
                | 'MODEL_SCORE_EXTRACT_PREFIX'
                | 'MODEL_SCORE_EXTRACT_SUFFIX'
                | 'AUTO_EVAL_DATA_SIZE'
                | 'CALCULATE_TOKEN'
                | 'CALCULATE_TOKEN_PARAM'
                | 'AUTO_EVAL'
                | 'AUTO_EVAL_PARAM'
                | 'TRAIN_TASK_ID'
                | 'MODEL_CONFIG_DIRECTORY'
                | 'COMPARISON_MODEL_META_ID'
                | 'COMPARISON_BENCHMARK_ID'
                | 'TP'
                | 'PP'
                | 'AUTO_TOKEN'
                | 'TOKEN'
                | 'FLOPS';
              /**
               * 扩展字段key
               */
              filedName?: string;
              /**
               * 扩展字段值
               */
              filedValue?: string;
              /**
               * 是否系统创建
               */
              systemCreate?: boolean;
              [k: string]: unknown;
            }[];
            /**
             * 模型训练每秒浮点计算次数
             */
            flops?: string;
            /**
             * 模型checkpoint
             */
            modelCheckpoint: string;
            /**
             * 模型格式
             */
            modelFormat: string;
            /**
             * 模型id
             */
            modelId: number;
            /**
             * 模型meta信息id
             */
            modelMetaId?: number;
            /**
             * 模型名
             */
            modelName?: string;
            /**
             * 模型地址
             */
            modelPath: string;
            /**
             * 模型别名
             */
            modelShortName?: string;
            /**
             * 产出阶段
             */
            modelStage?: string;
            /**
             * 模型状态
             */
            modelStatus: boolean;
            /**
             * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
             */
            modelType: number;
            /**
             * 模型mp
             */
            pp?: number;
            /**
             * 注册渠道
             */
            registerChannel: string;
            /**
             * 模型step
             */
            step?: string;
            /**
             * 模型标签
             */
            tagValues?: {
              /**
               * 唯一标识, 前后端交互用
               */
              identification: string;
              /**
               * 字段值
               */
              values?: {
                /**
                 * 可选值展示名
                 */
                label?: string;
                /**
                 * 可选值
                 */
                name?: string;
                [k: string]: unknown;
              }[];
              [k: string]: unknown;
            }[];
            /**
             * 模型训练使用token数
             */
            token?: string;
            /**
             * 模型tp
             */
            tp?: number;
            [k: string]: unknown;
          };
          /**
           * 评测模型名name
           */
          name?: string;
          nonversionValueList?: {
            categoryId?: number;
            dataSubSetId?: number;
            description?: string;
            label?: string;
            name?: string;
            publicStatus?: string;
            unitList?: {
              confidenceInterval?: string;
              dataSetOpennessStatus?: string;
              hasStatSubSetCount?: number;
              objectivityIntegrationMean?: string;
              result?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                instanceCountRatio?: number;
                instanceIncompleteList?: {
                  dataSetName?: string;
                  id?: number;
                  invalidReason?: string;
                  name?: string;
                  [k: string]: unknown;
                }[];
                name?: string;
                notCalculateDataSubSetList?: {
                  dataSetName?: string;
                  id?: number;
                  invalidReason?: string;
                  name?: string;
                  [k: string]: unknown;
                }[];
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                value?: number;
                [k: string]: unknown;
              };
              secondValue?: string;
              subjectivityIsFromModelEval?: boolean;
              textLenOutputAvg?: number;
              tikTokenOutputAvg?: number;
              tokenOutputAvg?: number;
              totalSubSetCount?: number;
              value?: string;
              [k: string]: unknown;
            }[];
            [k: string]: unknown;
          }[];
          /**
           * 实际评测规模
           */
          realEvalSize?: string;
          /**
           * 数据子集总数
           */
          totalSubSetCount?: number;
          /**
           * 指标值
           */
          value?: string;
          [k: string]: unknown;
        }[];
        /**
         * 模型评估指标名称
         */
        metricsName?: string;
        /**
         * 模型格式
         */
        modelFormat?: string;
        /**
         * 模型meta信息
         */
        modelMeta?: {
          /**
           * 是否开启自动评测
           */
          autoEval?: boolean;
          /**
           * 自动评测实验画布id
           */
          autoEvalBaseExperimentId?: number;
          /**
           * 自动评测ckpt正则表达式
           */
          autoEvalCkptRegex?: string;
          /**
           * 评测集数据规模
           */
          autoEvalDataSize?: string;
          /**
           * 自动评测评测实验名
           */
          autoEvalEvaluateModelName?: string;
          /**
           * 自动评测实验画布名
           */
          autoEvalExperimentName?: string;
          /**
           * 自动评测ckpt格式
           */
          autoEvalModelFormat?: string;
          /**
           * 标杆模型
           */
          benchmarking?: boolean;
          /**
           * 最佳ckpt
           */
          bestCheckpoint?: number;
          /**
           * 模型信息
           */
          bestCheckpointModelInfo?: {
            /**
             * 是否为标杆模型
             */
            benchmarking?: boolean;
            /**
             * 模型创建人
             */
            creator: string;
            /**
             * 创建时间
             */
            cts: string;
            /**
             * 实验任务详情id
             */
            experimentTaskDetailId?: number;
            /**
             * 扩展词表地址
             */
            extTokenizerPath?: string;
            /**
             * 扩展字段集合
             */
            extraInfoFields?: {
              /**
               * 扩展字段值类型
               */
              fieldType?: string;
              /**
               * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
               */
              filedKey?:
                | 'CONTEXT_LENGTH'
                | 'REQUEST_BATCH_SIZE'
                | 'MAX_NEW_TOKENS'
                | 'FOLLOW_DATA_SUB_SET'
                | 'TEMPERATURE'
                | 'REPETITION_PENALTY'
                | 'TOP_K'
                | 'TOP_P'
                | 'PROMPT_PREFIX'
                | 'PROMPT_SUFFIX'
                | 'SYSTEM_PROMPT'
                | 'STOP_SEQUENCES'
                | 'MODEL_SCORE_EXTRACT_PREFIX'
                | 'MODEL_SCORE_EXTRACT_SUFFIX'
                | 'AUTO_EVAL_DATA_SIZE'
                | 'CALCULATE_TOKEN'
                | 'CALCULATE_TOKEN_PARAM'
                | 'AUTO_EVAL'
                | 'AUTO_EVAL_PARAM'
                | 'TRAIN_TASK_ID'
                | 'MODEL_CONFIG_DIRECTORY'
                | 'COMPARISON_MODEL_META_ID'
                | 'COMPARISON_BENCHMARK_ID'
                | 'TP'
                | 'PP'
                | 'AUTO_TOKEN'
                | 'TOKEN'
                | 'FLOPS';
              /**
               * 扩展字段key
               */
              filedName?: string;
              /**
               * 扩展字段值
               */
              filedValue?: string;
              /**
               * 是否系统创建
               */
              systemCreate?: boolean;
              [k: string]: unknown;
            }[];
            /**
             * 模型训练每秒浮点计算次数
             */
            flops?: string;
            /**
             * 模型checkpoint
             */
            modelCheckpoint: string;
            /**
             * 模型格式
             */
            modelFormat: string;
            /**
             * 模型id
             */
            modelId: number;
            /**
             * 模型meta信息id
             */
            modelMetaId?: number;
            /**
             * 模型名
             */
            modelName?: string;
            /**
             * 模型地址
             */
            modelPath: string;
            /**
             * 模型别名
             */
            modelShortName?: string;
            /**
             * 产出阶段
             */
            modelStage?: string;
            /**
             * 模型状态
             */
            modelStatus: boolean;
            /**
             * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
             */
            modelType: number;
            /**
             * 模型mp
             */
            pp?: number;
            /**
             * 注册渠道
             */
            registerChannel: string;
            /**
             * 模型step
             */
            step?: string;
            /**
             * 模型标签
             */
            tagValues?: {
              /**
               * 唯一标识, 前后端交互用
               */
              identification: string;
              /**
               * 字段值
               */
              values?: {
                /**
                 * 可选值展示名
                 */
                label?: string;
                /**
                 * 可选值
                 */
                name?: string;
                [k: string]: unknown;
              }[];
              [k: string]: unknown;
            }[];
            /**
             * 模型训练使用token数
             */
            token?: string;
            /**
             * 模型tp
             */
            tp?: number;
            [k: string]: unknown;
          };
          /**
           * 是否收藏
           */
          collected?: boolean;
          /**
           * 模型支持的上下文窗口大小
           */
          contextLength?: number;
          /**
           * 创建时间
           */
          cts?: string;
          /**
           * 模型描述
           */
          description?: string;
          /**
           * 扩展词表地址
           */
          extTokenizerPath?: string;
          /**
           * 模型meta扩展环境变量
           */
          extendEnvParams?: {
            /**
             * maxNewTokens
             */
            maxNewTokens?: number;
            /**
             * modelScoreExtractPrefix
             */
            modelScoreExtractPrefix?: string;
            /**
             * modelScoreExtractSuffix
             */
            modelScoreExtractSuffix?: string;
            /**
             * requestBatchSize
             */
            requestBatchSize?: number;
            /**
             * systemPrompt
             */
            systemPrompt?: string;
            [k: string]: unknown;
          };
          /**
           * 模型meta扩展信息
           */
          extraInfo?: {
            /**
             * batchSizeConfig
             */
            batchSizeConfigs?: string;
            /**
             * flopsPerToken
             */
            flopsPerToken?: string;
            /**
             * global_batch_size
             */
            globalBatchSize?: string;
            /**
             * rampup_batch_size样本数
             */
            rampUpBatchSizeSampleNum?: string;
            /**
             * rampup_batch_size起始值
             */
            rampUpBatchSizeStart?: string;
            /**
             * rampup_batch_size步长
             */
            rampUpBatchSizeStep?: string;
            /**
             * seq_length
             */
            seqLength?: string;
            [k: string]: unknown;
          };
          /**
           * 扩展字段集合
           */
          extraInfoFields?: {
            /**
             * 扩展字段值类型
             */
            fieldType?: string;
            /**
             * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
             */
            filedKey?:
              | 'CONTEXT_LENGTH'
              | 'REQUEST_BATCH_SIZE'
              | 'MAX_NEW_TOKENS'
              | 'FOLLOW_DATA_SUB_SET'
              | 'TEMPERATURE'
              | 'REPETITION_PENALTY'
              | 'TOP_K'
              | 'TOP_P'
              | 'PROMPT_PREFIX'
              | 'PROMPT_SUFFIX'
              | 'SYSTEM_PROMPT'
              | 'STOP_SEQUENCES'
              | 'MODEL_SCORE_EXTRACT_PREFIX'
              | 'MODEL_SCORE_EXTRACT_SUFFIX'
              | 'AUTO_EVAL_DATA_SIZE'
              | 'CALCULATE_TOKEN'
              | 'CALCULATE_TOKEN_PARAM'
              | 'AUTO_EVAL'
              | 'AUTO_EVAL_PARAM'
              | 'TRAIN_TASK_ID'
              | 'MODEL_CONFIG_DIRECTORY'
              | 'COMPARISON_MODEL_META_ID'
              | 'COMPARISON_BENCHMARK_ID'
              | 'TP'
              | 'PP'
              | 'AUTO_TOKEN'
              | 'TOKEN'
              | 'FLOPS';
            /**
             * 扩展字段key
             */
            filedName?: string;
            /**
             * 扩展字段值
             */
            filedValue?: string;
            /**
             * 是否系统创建
             */
            systemCreate?: boolean;
            [k: string]: unknown;
          }[];
          /**
           * 模型meta信息id
           */
          metaId?: number;
          /**
           * 模型Checkpoint
           */
          modelCheckpoint?: string;
          /**
           * 模型适用领域
           */
          modelDomain?: string;
          /**
           * 模型家族
           */
          modelFamily: string;
          /**
           * 模型id
           */
          modelId?: number;
          /**
           * 模型名称
           */
          modelName?: string;
          /**
           * 具体模型数量
           */
          modelNum?: number;
          /**
           * 模型系列
           */
          modelSeries?: string;
          /**
           * 模型展示名
           */
          modelShortName?: string;
          /**
           * 模型尺寸
           */
          modelSize?: string;
          /**
           * 产出阶段
           */
          modelStage: string;
          /**
           * 模型目标语言
           */
          modelTargetLanguage?: string;
          /**
           * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
           */
          modelType: number;
          /**
           * 自定义模型名称
           */
          modelUserSettings?: string;
          /**
           * 发布原始模型的机构名
           */
          orgName?: string;
          overwriteExtraInfoFields?: boolean;
          /**
           * 负责人列表
           */
          ownerList: string[];
          /**
           * 插件绑定列表
           */
          pluginBindList?: {
            /**
             * 锚点完整路径
             */
            anchorPath?: string;
            /**
             * 绑定类型
             */
            bindType?: string;
            /**
             * 上下文参数
             */
            contextParams?: {
              /**
               * 配置
               */
              config?: string;
              /**
               * 说明
               */
              description?: string;
              /**
               * 全局绑定参数名
               */
              globalAliasName?: string;
              /**
               * 全局绑定参数类型
               */
              globalAliasType?: string;
              /**
               * 展示名
               */
              label?: string;
              /**
               * 参数名
               */
              name?: string;
              /**
               * 提交用，覆盖子Benchmark配置
               */
              overrideFlag?: string;
              /**
               * 来源
               */
              source?: string;
              /**
               * 类型
               */
              type?: string;
              /**
               * 默认值
               */
              value?: string;
              [k: string]: unknown;
            }[];
            /**
             * 额外信息
             */
            extra?: {
              [k: string]: {
                [k: string]: unknown;
              };
            };
            /**
             * 插件顺序
             */
            orderIndex?: number;
            /**
             * 插件实现类名
             */
            pluginImplement?: string;
            /**
             * 插件展示实现名
             */
            pluginImplementLabel?: string;
            /**
             * 插件接口名
             */
            pluginInterface?: string;
            /**
             * 来源
             */
            source?: string;
            /**
             * 关联节点定义ID
             */
            vertexDefineId?: number;
            [k: string]: unknown;
          }[];
          /**
           * prompt前缀
           */
          promptPrefix?: string;
          /**
           * prompt后缀
           */
          promptSuffix?: string;
          /**
           * 授权可以使用私有模型的用户组
           */
          roleList?: string[];
          /**
           * 推理终止序列
           */
          stopSequences?: string;
          /**
           * 标签信息
           */
          tagValues?: {
            /**
             * 唯一标识, 前后端交互用
             */
            identification: string;
            /**
             * 字段值
             */
            values?: {
              /**
               * 可选值展示名
               */
              label?: string;
              /**
               * 可选值
               */
              name?: string;
              [k: string]: unknown;
            }[];
            [k: string]: unknown;
          }[];
          /**
           * 训练数据版本
           */
          trainDataVersion?: string;
          /**
           * 唯一标识
           */
          uniqueIdentification?: string;
          /**
           * 授权可以使用私有模型的用户
           */
          userList?: string[];
          /**
           * 修改时间
           */
          uts?: string;
          [k: string]: unknown;
        };
        /**
         * 模型metaId
         */
        modelMetaId?: number;
        /**
         * 模型名
         */
        modelName?: string;
        /**
         * 模型评估指标
         */
        modelStatList?: {
          evalDataSize?: string;
          family?: string;
          groupValue?: {
            confidenceInterval?: string;
            hasStatSubSetCount?: number;
            macroMean?: string;
            mean?: string;
            result?: {
              binNames?: string[];
              binResultList?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              }[];
              categoryId?: number;
              children?: {
                binNames?: string[];
                children?: unknown[];
                confidenceIntervalName?: string;
                itemDisplayType?: string;
                label?: string;
                name?: string;
                objectiveBaseValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveEvalValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                relationTagNameList?: string[];
                secondStatName?: string;
                showCapabilityReportFlag?: boolean;
                subjectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                winRateFlag?: boolean;
                [k: string]: unknown;
              }[];
              confidenceInterval?: string;
              confidenceIntervalName?: string;
              confidenceResult?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              };
              dataSetId?: number;
              dataSubSetId?: number;
              hasStatSubSetCount?: number;
              itemDisplayType?: string;
              label?: string;
              name?: string;
              objectiveBaseValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveEvalValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              relationTagNameList?: string[];
              result?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              };
              secondStatName?: string;
              secondValue?: string;
              showCapabilityReportFlag?: boolean;
              subjectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              totalSubSetCount?: number;
              type?: string;
              value?: string;
              winRateFlag?: boolean;
              [k: string]: unknown;
            };
            secondValue?: string;
            totalSubSetCount?: number;
            [k: string]: unknown;
          };
          label?: string;
          llmModel?: {
            authType?: string;
            autoEvalDataSize?: string;
            benchmarking?: string;
            cts?: string;
            family?: string;
            id?: number;
            label?: string;
            modelId?: number;
            modelMetaId?: number;
            modelPath?: string;
            name?: string;
            nlpOpenRunSpecSetId?: number;
            ownerList?: string[];
            simpleTags?: string[];
            status?: string;
            tags?: {
              name?: string;
              values?: string[];
              [k: string]: unknown;
            }[];
            uts?: string;
            viewerMisList?: string[];
            viewerRoleList?: string[];
            [k: string]: unknown;
          };
          /**
           * 模型信息
           */
          modelInfoVo?: {
            /**
             * 是否为标杆模型
             */
            benchmarking?: boolean;
            /**
             * 模型创建人
             */
            creator: string;
            /**
             * 创建时间
             */
            cts: string;
            /**
             * 实验任务详情id
             */
            experimentTaskDetailId?: number;
            /**
             * 扩展词表地址
             */
            extTokenizerPath?: string;
            /**
             * 扩展字段集合
             */
            extraInfoFields?: {
              /**
               * 扩展字段值类型
               */
              fieldType?: string;
              /**
               * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
               */
              filedKey?:
                | 'CONTEXT_LENGTH'
                | 'REQUEST_BATCH_SIZE'
                | 'MAX_NEW_TOKENS'
                | 'FOLLOW_DATA_SUB_SET'
                | 'TEMPERATURE'
                | 'REPETITION_PENALTY'
                | 'TOP_K'
                | 'TOP_P'
                | 'PROMPT_PREFIX'
                | 'PROMPT_SUFFIX'
                | 'SYSTEM_PROMPT'
                | 'STOP_SEQUENCES'
                | 'MODEL_SCORE_EXTRACT_PREFIX'
                | 'MODEL_SCORE_EXTRACT_SUFFIX'
                | 'AUTO_EVAL_DATA_SIZE'
                | 'CALCULATE_TOKEN'
                | 'CALCULATE_TOKEN_PARAM'
                | 'AUTO_EVAL'
                | 'AUTO_EVAL_PARAM'
                | 'TRAIN_TASK_ID'
                | 'MODEL_CONFIG_DIRECTORY'
                | 'COMPARISON_MODEL_META_ID'
                | 'COMPARISON_BENCHMARK_ID'
                | 'TP'
                | 'PP'
                | 'AUTO_TOKEN'
                | 'TOKEN'
                | 'FLOPS';
              /**
               * 扩展字段key
               */
              filedName?: string;
              /**
               * 扩展字段值
               */
              filedValue?: string;
              /**
               * 是否系统创建
               */
              systemCreate?: boolean;
              [k: string]: unknown;
            }[];
            /**
             * 模型训练每秒浮点计算次数
             */
            flops?: string;
            /**
             * 模型checkpoint
             */
            modelCheckpoint: string;
            /**
             * 模型格式
             */
            modelFormat: string;
            /**
             * 模型id
             */
            modelId: number;
            /**
             * 模型meta信息id
             */
            modelMetaId?: number;
            /**
             * 模型名
             */
            modelName?: string;
            /**
             * 模型地址
             */
            modelPath: string;
            /**
             * 模型别名
             */
            modelShortName?: string;
            /**
             * 产出阶段
             */
            modelStage?: string;
            /**
             * 模型状态
             */
            modelStatus: boolean;
            /**
             * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
             */
            modelType: number;
            /**
             * 模型mp
             */
            pp?: number;
            /**
             * 注册渠道
             */
            registerChannel: string;
            /**
             * 模型step
             */
            step?: string;
            /**
             * 模型标签
             */
            tagValues?: {
              /**
               * 唯一标识, 前后端交互用
               */
              identification: string;
              /**
               * 字段值
               */
              values?: {
                /**
                 * 可选值展示名
                 */
                label?: string;
                /**
                 * 可选值
                 */
                name?: string;
                [k: string]: unknown;
              }[];
              [k: string]: unknown;
            }[];
            /**
             * 模型训练使用token数
             */
            token?: string;
            /**
             * 模型tp
             */
            tp?: number;
            [k: string]: unknown;
          };
          name?: string;
          nonversionValueList?: {
            categoryId?: number;
            dataSubSetId?: number;
            description?: string;
            label?: string;
            name?: string;
            publicStatus?: string;
            unitList?: {
              confidenceInterval?: string;
              dataSetOpennessStatus?: string;
              hasStatSubSetCount?: number;
              objectivityIntegrationMean?: string;
              result?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                instanceCountRatio?: number;
                instanceIncompleteList?: {
                  dataSetName?: string;
                  id?: number;
                  invalidReason?: string;
                  name?: string;
                  [k: string]: unknown;
                }[];
                name?: string;
                notCalculateDataSubSetList?: {
                  dataSetName?: string;
                  id?: number;
                  invalidReason?: string;
                  name?: string;
                  [k: string]: unknown;
                }[];
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                value?: number;
                [k: string]: unknown;
              };
              secondValue?: string;
              subjectivityIsFromModelEval?: boolean;
              textLenOutputAvg?: number;
              tikTokenOutputAvg?: number;
              tokenOutputAvg?: number;
              totalSubSetCount?: number;
              value?: string;
              [k: string]: unknown;
            }[];
            [k: string]: unknown;
          }[];
          searchEvalDataSize?: string;
          valueList?: {
            binNames?: string[];
            binResultList?: {
              calculateUnitList?: {
                aggrateType?: string;
                autoEvalDataSizeList?: string[];
                commonSubSetEvalInstanceCount?: number;
                complete?: boolean;
                confidenceInterval?: number;
                configTotalInstanceCount?: number;
                dataSetLabel?: string;
                dataSetName?: string;
                dataSubSetLabel?: string;
                dataSubSetName?: string;
                evalDataSize?: string;
                evalKeyCount?: number;
                instanceCount?: number;
                instanceStatCalculateUnitList?: {
                  evalKey?: string;
                  instanceId?: string;
                  statValue?: number;
                  [k: string]: unknown;
                }[];
                invalidReason?: string;
                modelFamily?: string;
                modelName?: string;
                runSpecSetId?: number;
                statType?: string;
                statValue?: number;
                subSetId?: number;
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                totalInstanceCount?: number;
                valueIsValid?: boolean;
                weight?: number;
                [k: string]: unknown;
              }[];
              categoryIds?: number[];
              dataSubSetIds?: number[];
              dataSubSetWeightSum?: number;
              evalDataSize?: string;
              family?: string;
              hasStatDataSubSetIds?: number[];
              name?: string;
              notCalculateDataSubSetList?: {
                autoEvalDataSizeOrder?: string;
                autoEvalDataSizeOrderList?: string[];
                bindingRunSpecList?: {
                  cts?: string;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetId?: number;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  dataSubSetPublicStatus?: string;
                  description?: string;
                  extraParam?: string;
                  id?: number;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                categoryList?: unknown[];
                cts?: string;
                dataSet?: {
                  cts?: string;
                  description?: string;
                  execType?: string;
                  id?: number;
                  label?: string;
                  metaVersionId?: number;
                  name?: string;
                  ownerList?: string[];
                  runtimeConfig?: string;
                  status?: string;
                  subSetCount?: number;
                  uts?: string;
                  [k: string]: unknown;
                };
                dataSetLabel?: string;
                dataSetName?: string;
                description?: string;
                evalType?: string;
                files?: {
                  cts?: string;
                  dataKey?: string;
                  detail?: {
                    bucket?: string;
                    dataSetName?: string;
                    dataSetVersionName?: string;
                    errorMsg?: string;
                    index?: string;
                    labelType?: number;
                    path?: string;
                    [k: string]: unknown;
                  };
                  detailJson?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  storageType?: string;
                  uploadTs?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                id?: number;
                instanceCount?: number;
                instanceDetail?: {
                  instanceList?: {
                    list?: {
                      categoryList?: string;
                      correctAnswer?: string;
                      cts?: string;
                      id?: number;
                      instanceId?: string;
                      modelEvalDataSubSetId?: number;
                      modelPrompt?: string;
                      originInput?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    totalCount?: number;
                    [k: string]: unknown;
                  };
                  instanceOriginContent?: string;
                  [k: string]: unknown;
                };
                label?: string;
                metaVersionId?: number;
                modelEvalDataSetId?: number;
                name?: string;
                publicStatus?: string;
                statType?: string;
                stats?: {
                  cts?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                tags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                uts?: string;
                weight?: number;
                [k: string]: unknown;
              }[];
              value?: number;
              [k: string]: unknown;
            }[];
            categoryId?: number;
            children?: {
              binNames?: string[];
              children?: unknown[];
              confidenceIntervalName?: string;
              itemDisplayType?: string;
              label?: string;
              name?: string;
              objectiveBaseValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveEvalValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              relationTagNameList?: string[];
              secondStatName?: string;
              showCapabilityReportFlag?: boolean;
              subjectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              winRateFlag?: boolean;
              [k: string]: unknown;
            }[];
            confidenceInterval?: string;
            confidenceIntervalName?: string;
            confidenceResult?: {
              calculateUnitList?: {
                aggrateType?: string;
                autoEvalDataSizeList?: string[];
                commonSubSetEvalInstanceCount?: number;
                complete?: boolean;
                confidenceInterval?: number;
                configTotalInstanceCount?: number;
                dataSetLabel?: string;
                dataSetName?: string;
                dataSubSetLabel?: string;
                dataSubSetName?: string;
                evalDataSize?: string;
                evalKeyCount?: number;
                instanceCount?: number;
                instanceStatCalculateUnitList?: {
                  evalKey?: string;
                  instanceId?: string;
                  statValue?: number;
                  [k: string]: unknown;
                }[];
                invalidReason?: string;
                modelFamily?: string;
                modelName?: string;
                runSpecSetId?: number;
                statType?: string;
                statValue?: number;
                subSetId?: number;
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                totalInstanceCount?: number;
                valueIsValid?: boolean;
                weight?: number;
                [k: string]: unknown;
              }[];
              categoryIds?: number[];
              dataSubSetIds?: number[];
              dataSubSetWeightSum?: number;
              evalDataSize?: string;
              family?: string;
              hasStatDataSubSetIds?: number[];
              name?: string;
              notCalculateDataSubSetList?: {
                autoEvalDataSizeOrder?: string;
                autoEvalDataSizeOrderList?: string[];
                bindingRunSpecList?: {
                  cts?: string;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetId?: number;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  dataSubSetPublicStatus?: string;
                  description?: string;
                  extraParam?: string;
                  id?: number;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                categoryList?: unknown[];
                cts?: string;
                dataSet?: {
                  cts?: string;
                  description?: string;
                  execType?: string;
                  id?: number;
                  label?: string;
                  metaVersionId?: number;
                  name?: string;
                  ownerList?: string[];
                  runtimeConfig?: string;
                  status?: string;
                  subSetCount?: number;
                  uts?: string;
                  [k: string]: unknown;
                };
                dataSetLabel?: string;
                dataSetName?: string;
                description?: string;
                evalType?: string;
                files?: {
                  cts?: string;
                  dataKey?: string;
                  detail?: {
                    bucket?: string;
                    dataSetName?: string;
                    dataSetVersionName?: string;
                    errorMsg?: string;
                    index?: string;
                    labelType?: number;
                    path?: string;
                    [k: string]: unknown;
                  };
                  detailJson?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  storageType?: string;
                  uploadTs?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                id?: number;
                instanceCount?: number;
                instanceDetail?: {
                  instanceList?: {
                    list?: {
                      categoryList?: string;
                      correctAnswer?: string;
                      cts?: string;
                      id?: number;
                      instanceId?: string;
                      modelEvalDataSubSetId?: number;
                      modelPrompt?: string;
                      originInput?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    totalCount?: number;
                    [k: string]: unknown;
                  };
                  instanceOriginContent?: string;
                  [k: string]: unknown;
                };
                label?: string;
                metaVersionId?: number;
                modelEvalDataSetId?: number;
                name?: string;
                publicStatus?: string;
                statType?: string;
                stats?: {
                  cts?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                tags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                uts?: string;
                weight?: number;
                [k: string]: unknown;
              }[];
              value?: number;
              [k: string]: unknown;
            };
            dataSetId?: number;
            dataSubSetId?: number;
            hasStatSubSetCount?: number;
            itemDisplayType?: string;
            label?: string;
            name?: string;
            objectiveBaseValueConfig?: {
              dataSubSetTags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              evalDataSize?: string;
              modelFamily?: string;
              modelMetaIdForMaster?: number;
              modelName?: string;
              statName?: string;
              [k: string]: unknown;
            };
            objectiveEvalValueConfig?: {
              dataSubSetTags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              evalDataSize?: string;
              modelFamily?: string;
              modelMetaIdForMaster?: number;
              modelName?: string;
              statName?: string;
              [k: string]: unknown;
            };
            objectiveValueConfigList?: {
              dataSubSetTags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              evalDataSize?: string;
              modelFamily?: string;
              modelMetaIdForMaster?: number;
              modelName?: string;
              statName?: string;
              [k: string]: unknown;
            }[];
            relationTagNameList?: string[];
            result?: {
              calculateUnitList?: {
                aggrateType?: string;
                autoEvalDataSizeList?: string[];
                commonSubSetEvalInstanceCount?: number;
                complete?: boolean;
                confidenceInterval?: number;
                configTotalInstanceCount?: number;
                dataSetLabel?: string;
                dataSetName?: string;
                dataSubSetLabel?: string;
                dataSubSetName?: string;
                evalDataSize?: string;
                evalKeyCount?: number;
                instanceCount?: number;
                instanceStatCalculateUnitList?: {
                  evalKey?: string;
                  instanceId?: string;
                  statValue?: number;
                  [k: string]: unknown;
                }[];
                invalidReason?: string;
                modelFamily?: string;
                modelName?: string;
                runSpecSetId?: number;
                statType?: string;
                statValue?: number;
                subSetId?: number;
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                totalInstanceCount?: number;
                valueIsValid?: boolean;
                weight?: number;
                [k: string]: unknown;
              }[];
              categoryIds?: number[];
              dataSubSetIds?: number[];
              dataSubSetWeightSum?: number;
              evalDataSize?: string;
              family?: string;
              hasStatDataSubSetIds?: number[];
              name?: string;
              notCalculateDataSubSetList?: {
                autoEvalDataSizeOrder?: string;
                autoEvalDataSizeOrderList?: string[];
                bindingRunSpecList?: {
                  cts?: string;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetId?: number;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  dataSubSetPublicStatus?: string;
                  description?: string;
                  extraParam?: string;
                  id?: number;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                categoryList?: unknown[];
                cts?: string;
                dataSet?: {
                  cts?: string;
                  description?: string;
                  execType?: string;
                  id?: number;
                  label?: string;
                  metaVersionId?: number;
                  name?: string;
                  ownerList?: string[];
                  runtimeConfig?: string;
                  status?: string;
                  subSetCount?: number;
                  uts?: string;
                  [k: string]: unknown;
                };
                dataSetLabel?: string;
                dataSetName?: string;
                description?: string;
                evalType?: string;
                files?: {
                  cts?: string;
                  dataKey?: string;
                  detail?: {
                    bucket?: string;
                    dataSetName?: string;
                    dataSetVersionName?: string;
                    errorMsg?: string;
                    index?: string;
                    labelType?: number;
                    path?: string;
                    [k: string]: unknown;
                  };
                  detailJson?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  storageType?: string;
                  uploadTs?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                id?: number;
                instanceCount?: number;
                instanceDetail?: {
                  instanceList?: {
                    list?: {
                      categoryList?: string;
                      correctAnswer?: string;
                      cts?: string;
                      id?: number;
                      instanceId?: string;
                      modelEvalDataSubSetId?: number;
                      modelPrompt?: string;
                      originInput?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    totalCount?: number;
                    [k: string]: unknown;
                  };
                  instanceOriginContent?: string;
                  [k: string]: unknown;
                };
                label?: string;
                metaVersionId?: number;
                modelEvalDataSetId?: number;
                name?: string;
                publicStatus?: string;
                statType?: string;
                stats?: {
                  cts?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                tags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                uts?: string;
                weight?: number;
                [k: string]: unknown;
              }[];
              value?: number;
              [k: string]: unknown;
            };
            secondStatName?: string;
            secondValue?: string;
            showCapabilityReportFlag?: boolean;
            subjectiveValueConfigList?: {
              dataSubSetTags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              evalDataSize?: string;
              statName?: string;
              [k: string]: unknown;
            }[];
            totalSubSetCount?: number;
            type?: string;
            value?: string;
            winRateFlag?: boolean;
            [k: string]: unknown;
          }[];
          winRateValue?: {
            comprehensiveValue?: string;
            flag?: boolean;
            objectiveValue?: string;
            objectiveValueEfficient?: number;
            objectiveValueTotal?: number;
            subjectiveValue?: string;
            subjectiveValueEfficient?: number;
            subjectiveValueTotal?: number;
            [k: string]: unknown;
          };
          [k: string]: unknown;
        }[];
        [k: string]: unknown;
      }[];
      /**
       * 模型评估指标名称
       */
      metricsName?: string;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

export interface IGetWorkspaceModelGetDetailParameter {
  /**
   * viewId
   */
  viewId: string;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IGetWorkspaceModelGetDetailResponse {
  /**
   * 视图基本信息
   */
  data: {
    /**
     * 收藏数量
     */
    collectNum?: number;
    /**
     * 是否被当前访问用户收藏
     */
    collected?: boolean;
    /**
     * 创建人
     */
    creator?: string;
    /**
     * 创建时间
     */
    cts?: string;
    /**
     * 视图描述
     */
    description?: string;
    /**
     * 视图保存的详细参数
     */
    detail?: string;
    /**
     * 视图保留时长(枚举值, 可选如下(值-展示名): 1-长期; 2-30天)
     */
    expireType?: number;
    /**
     * 视图id
     */
    id?: number;
    /**
     * 视图名称
     */
    name?: string;
    /**
     * 更新时间
     */
    uts?: string;
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型MASTER指标合入请求
 */
export interface IPostWorkspaceModelSubmitMergeMasterInfoParameter {
  /**
   * 指标查询请求
   */
  metricsRequests: {
    /**
     * 折线调整参数
     */
    curveModifier: {
      /**
       * 划定ckpt阈值: 过滤时仅考虑大于阈值的ckpt
       */
      labelThreshold: string;
      /**
       * 置信区间, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
       */
      topCIPercentage: number;
      /**
       * 困难的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
       */
      topDiffPercentage: number;
      /**
       * 简单的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
       */
      topEasyPercentage: number;
      /**
       * 波动的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
       */
      topVariancePercentage: number;
      [k: string]: unknown;
    };
    /**
     * 自定义图表
     */
    customChart?: boolean;
    /**
     * 数据子集图表标识
     */
    dataSubSetChart?: boolean;
    /**
     * 数据子集id列表, 自定义图表必填
     */
    dataSubSetIds?: number[];
    /**
     * 评估数据集
     */
    evalDataSet: string;
    /**
     * 评估数据集规模
     */
    evalDataSetSize: string;
    /**
     * 评测版本
     */
    evalMetaId?: number;
    /**
     * 评估指标维度
     */
    evalModelMetricsDimension: string;
    /**
     * 评估指标名
     */
    evalModelMetricsNames?: string[];
    /**
     * 评估实验
     */
    evalModelName: string;
    /**
     * 根据模型查询评估模型名的过滤规则
     */
    evalModelNameRules: {
      /**
       * 全部的评估模型名格式规则
       */
      allEvalModelNameRules?: string[];
      /**
       * 模型metaId
       */
      modelMetaId: number;
      /**
       * 模型名
       */
      modelName?: string;
      /**
       * 已选中的评估模型名格式规则
       */
      specifyEvalModelNameRules: string[];
      [k: string]: unknown;
    }[];
    /**
     * 心跳超时时间, 单位秒
     */
    heartbeatTtl: number;
    /**
     * 忽略无效数据点
     */
    ignoreInvalidData: boolean;
    /**
     * 模型ID列表请求入参
     */
    modelIdRequest: {
      /**
       * 模型Id
       */
      modelAndMetaIds: {
        /**
         * 模型Id
         */
        modelIds?: number[];
        /**
         * 模型metaId
         */
        modelMetaId: number;
        [k: string]: unknown;
      }[];
      [k: string]: unknown;
    };
    /**
     * 自定义图表必填, true: 无版本数据子集, false:  有版本数据子集
     */
    nonversionDataSubset?: boolean;
    /**
     * uuid, 请求的唯一标识
     */
    queryUuid: string;
    /**
     * MASTER指标合并请求发起页面的标识, 不需要传值
     */
    rightActiveTab?: string;
    /**
     * 指标维度, 自定义图表必填
     */
    statValue?: string;
    [k: string]: unknown;
  }[];
  /**
   * 模型ID列表请求入参
   */
  modelIdRequest?: {
    /**
     * 模型Id
     */
    modelAndMetaIds: {
      /**
       * 模型Id
       */
      modelIds?: number[];
      /**
       * 模型metaId
       */
      modelMetaId: number;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  /**
   * 发起合并请求的页面
   */
  rightActiveTab: string;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelSubmitMergeMasterInfoResponse {
  /**
   * 模型MASTER指标合入任务信息
   */
  data: {
    /**
     * 提示信息
     */
    message?: string;
    /**
     * 指标查询请求
     */
    metricsRequests: {
      /**
       * 折线调整参数
       */
      curveModifier: {
        /**
         * 划定ckpt阈值: 过滤时仅考虑大于阈值的ckpt
         */
        labelThreshold: string;
        /**
         * 置信区间, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topCIPercentage: number;
        /**
         * 困难的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topDiffPercentage: number;
        /**
         * 简单的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topEasyPercentage: number;
        /**
         * 波动的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topVariancePercentage: number;
        [k: string]: unknown;
      };
      /**
       * 自定义图表
       */
      customChart?: boolean;
      /**
       * 数据子集图表标识
       */
      dataSubSetChart?: boolean;
      /**
       * 数据子集id列表, 自定义图表必填
       */
      dataSubSetIds?: number[];
      /**
       * 评估数据集
       */
      evalDataSet: string;
      /**
       * 评估数据集规模
       */
      evalDataSetSize: string;
      /**
       * 评测版本
       */
      evalMetaId?: number;
      /**
       * 评估指标维度
       */
      evalModelMetricsDimension: string;
      /**
       * 评估指标名
       */
      evalModelMetricsNames?: string[];
      /**
       * 评估实验
       */
      evalModelName: string;
      /**
       * 根据模型查询评估模型名的过滤规则
       */
      evalModelNameRules: {
        /**
         * 全部的评估模型名格式规则
         */
        allEvalModelNameRules?: string[];
        /**
         * 模型metaId
         */
        modelMetaId: number;
        /**
         * 模型名
         */
        modelName?: string;
        /**
         * 已选中的评估模型名格式规则
         */
        specifyEvalModelNameRules: string[];
        [k: string]: unknown;
      }[];
      /**
       * 心跳超时时间, 单位秒
       */
      heartbeatTtl: number;
      /**
       * 忽略无效数据点
       */
      ignoreInvalidData: boolean;
      /**
       * 模型ID列表请求入参
       */
      modelIdRequest: {
        /**
         * 模型Id
         */
        modelAndMetaIds: {
          /**
           * 模型Id
           */
          modelIds?: number[];
          /**
           * 模型metaId
           */
          modelMetaId: number;
          [k: string]: unknown;
        }[];
        [k: string]: unknown;
      };
      /**
       * 自定义图表必填, true: 无版本数据子集, false:  有版本数据子集
       */
      nonversionDataSubset?: boolean;
      /**
       * uuid, 请求的唯一标识
       */
      queryUuid: string;
      /**
       * MASTER指标合并请求发起页面的标识, 不需要传值
       */
      rightActiveTab?: string;
      /**
       * 指标维度, 自定义图表必填
       */
      statValue?: string;
      [k: string]: unknown;
    }[];
    /**
     * 模型ID列表请求入参
     */
    modelIdRequest: {
      /**
       * 模型Id
       */
      modelAndMetaIds: {
        /**
         * 模型Id
         */
        modelIds?: number[];
        /**
         * 模型metaId
         */
        modelMetaId: number;
        [k: string]: unknown;
      }[];
      [k: string]: unknown;
    };
    /**
     * 任务详情
     */
    modelMasterMergeDetailInfos?: {
      /**
       * 审批人
       */
      approveCandidates?: string[];
      /**
       * modelId合并结果, key: modelId, value: 合并结果（0-未提交 1-排队中 2-执行中 3-失败 4-成功）
       */
      modelIdMergeResultMap?: {
        [k: string]: number;
      };
      /**
       * 模型metaId
       */
      modelMetaId?: number;
      /**
       * 驳回原因
       */
      rejectReason?: string;
      /**
       * 状态, 1-已提交 2-审批中 3-审批驳回 4-执行中 5-结束
       */
      status?: number;
      [k: string]: unknown;
    }[];
    /**
     * 模型评估指标请求入参
     */
    request?: {
      /**
       * 折线调整参数
       */
      curveModifier: {
        /**
         * 划定ckpt阈值: 过滤时仅考虑大于阈值的ckpt
         */
        labelThreshold: string;
        /**
         * 置信区间, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topCIPercentage: number;
        /**
         * 困难的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topDiffPercentage: number;
        /**
         * 简单的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topEasyPercentage: number;
        /**
         * 波动的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topVariancePercentage: number;
        [k: string]: unknown;
      };
      /**
       * 自定义图表
       */
      customChart?: boolean;
      /**
       * 数据子集图表标识
       */
      dataSubSetChart?: boolean;
      /**
       * 数据子集id列表, 自定义图表必填
       */
      dataSubSetIds?: number[];
      /**
       * 评估数据集
       */
      evalDataSet: string;
      /**
       * 评估数据集规模
       */
      evalDataSetSize: string;
      /**
       * 评测版本
       */
      evalMetaId?: number;
      /**
       * 评估指标维度
       */
      evalModelMetricsDimension: string;
      /**
       * 评估指标名
       */
      evalModelMetricsNames?: string[];
      /**
       * 评估实验
       */
      evalModelName: string;
      /**
       * 根据模型查询评估模型名的过滤规则
       */
      evalModelNameRules: {
        /**
         * 全部的评估模型名格式规则
         */
        allEvalModelNameRules?: string[];
        /**
         * 模型metaId
         */
        modelMetaId: number;
        /**
         * 模型名
         */
        modelName?: string;
        /**
         * 已选中的评估模型名格式规则
         */
        specifyEvalModelNameRules: string[];
        [k: string]: unknown;
      }[];
      /**
       * 心跳超时时间, 单位秒
       */
      heartbeatTtl: number;
      /**
       * 忽略无效数据点
       */
      ignoreInvalidData: boolean;
      /**
       * 模型ID列表请求入参
       */
      modelIdRequest: {
        /**
         * 模型Id
         */
        modelAndMetaIds: {
          /**
           * 模型Id
           */
          modelIds?: number[];
          /**
           * 模型metaId
           */
          modelMetaId: number;
          [k: string]: unknown;
        }[];
        [k: string]: unknown;
      };
      /**
       * 自定义图表必填, true: 无版本数据子集, false:  有版本数据子集
       */
      nonversionDataSubset?: boolean;
      /**
       * uuid, 请求的唯一标识
       */
      queryUuid: string;
      /**
       * MASTER指标合并请求发起页面的标识, 不需要传值
       */
      rightActiveTab?: string;
      /**
       * 指标维度, 自定义图表必填
       */
      statValue?: string;
      [k: string]: unknown;
    };
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

export interface IPostWorkspaceModelCollectViewParameter {
  /**
   * viewId
   */
  viewId: string;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelCollectViewResponse {
  /**
   * 响应数据
   */
  data: {
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

export interface IGetWorkspaceModelListDataSubSetAboutCategoryByRunSpecSetParameter {
  /**
   * 数据集名
   */
  runSpecSetName: string;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IGetWorkspaceModelListDataSubSetAboutCategoryByRunSpecSetResponse {
  /**
   * 数据子集关联的评测能力
   */
  data: {
    /**
     * 数据子集关联的评测能力
     */
    dataSubSetAboutCategoryVos?: {
      /**
       * 数据子集关联的评测能力
       */
      categoryList?: string[];
      /**
       * 数据子集id
       */
      dataSubSetId?: number;
      /**
       * 数据子集名
       */
      dataSubSetName?: string;
      [k: string]: unknown;
    }[];
    /**
     * 评测项类目体系版本
     */
    metaVersionId?: number;
    /**
     * 数据集id
     */
    runSpecSetId?: number;
    /**
     * 数据集名
     */
    runSpecSetName?: string;
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

export interface IGetWorkspaceModelGetMergeMasterApproveContentParameter {
  /**
   * 批次号
   */
  batchId: string;
  /**
   * modelMetaId
   */
  modelMetaId: string;
  /**
   * 页面类型, 1: 申请进度展示, 2: 审批, 3: 二次确认, 4: 查看MASTER变更
   */
  page: string;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IGetWorkspaceModelGetMergeMasterApproveContentResponse {
  /**
   * 模型MASTER指标合入任务信息
   */
  data: {
    /**
     * 提示信息
     */
    message?: string;
    /**
     * 指标查询请求
     */
    metricsRequests: {
      /**
       * 折线调整参数
       */
      curveModifier: {
        /**
         * 划定ckpt阈值: 过滤时仅考虑大于阈值的ckpt
         */
        labelThreshold: string;
        /**
         * 置信区间, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topCIPercentage: number;
        /**
         * 困难的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topDiffPercentage: number;
        /**
         * 简单的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topEasyPercentage: number;
        /**
         * 波动的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topVariancePercentage: number;
        [k: string]: unknown;
      };
      /**
       * 自定义图表
       */
      customChart?: boolean;
      /**
       * 数据子集图表标识
       */
      dataSubSetChart?: boolean;
      /**
       * 数据子集id列表, 自定义图表必填
       */
      dataSubSetIds?: number[];
      /**
       * 评估数据集
       */
      evalDataSet: string;
      /**
       * 评估数据集规模
       */
      evalDataSetSize: string;
      /**
       * 评测版本
       */
      evalMetaId?: number;
      /**
       * 评估指标维度
       */
      evalModelMetricsDimension: string;
      /**
       * 评估指标名
       */
      evalModelMetricsNames?: string[];
      /**
       * 评估实验
       */
      evalModelName: string;
      /**
       * 根据模型查询评估模型名的过滤规则
       */
      evalModelNameRules: {
        /**
         * 全部的评估模型名格式规则
         */
        allEvalModelNameRules?: string[];
        /**
         * 模型metaId
         */
        modelMetaId: number;
        /**
         * 模型名
         */
        modelName?: string;
        /**
         * 已选中的评估模型名格式规则
         */
        specifyEvalModelNameRules: string[];
        [k: string]: unknown;
      }[];
      /**
       * 心跳超时时间, 单位秒
       */
      heartbeatTtl: number;
      /**
       * 忽略无效数据点
       */
      ignoreInvalidData: boolean;
      /**
       * 模型ID列表请求入参
       */
      modelIdRequest: {
        /**
         * 模型Id
         */
        modelAndMetaIds: {
          /**
           * 模型Id
           */
          modelIds?: number[];
          /**
           * 模型metaId
           */
          modelMetaId: number;
          [k: string]: unknown;
        }[];
        [k: string]: unknown;
      };
      /**
       * 自定义图表必填, true: 无版本数据子集, false:  有版本数据子集
       */
      nonversionDataSubset?: boolean;
      /**
       * uuid, 请求的唯一标识
       */
      queryUuid: string;
      /**
       * MASTER指标合并请求发起页面的标识, 不需要传值
       */
      rightActiveTab?: string;
      /**
       * 指标维度, 自定义图表必填
       */
      statValue?: string;
      [k: string]: unknown;
    }[];
    /**
     * 模型ID列表请求入参
     */
    modelIdRequest: {
      /**
       * 模型Id
       */
      modelAndMetaIds: {
        /**
         * 模型Id
         */
        modelIds?: number[];
        /**
         * 模型metaId
         */
        modelMetaId: number;
        [k: string]: unknown;
      }[];
      [k: string]: unknown;
    };
    /**
     * 任务详情
     */
    modelMasterMergeDetailInfos?: {
      /**
       * 审批人
       */
      approveCandidates?: string[];
      /**
       * modelId合并结果, key: modelId, value: 合并结果（0-未提交 1-排队中 2-执行中 3-失败 4-成功）
       */
      modelIdMergeResultMap?: {
        [k: string]: number;
      };
      /**
       * 模型metaId
       */
      modelMetaId?: number;
      /**
       * 驳回原因
       */
      rejectReason?: string;
      /**
       * 状态, 1-已提交 2-审批中 3-审批驳回 4-执行中 5-结束
       */
      status?: number;
      [k: string]: unknown;
    }[];
    /**
     * 模型评估指标请求入参
     */
    request?: {
      /**
       * 折线调整参数
       */
      curveModifier: {
        /**
         * 划定ckpt阈值: 过滤时仅考虑大于阈值的ckpt
         */
        labelThreshold: string;
        /**
         * 置信区间, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topCIPercentage: number;
        /**
         * 困难的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topDiffPercentage: number;
        /**
         * 简单的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topEasyPercentage: number;
        /**
         * 波动的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
         */
        topVariancePercentage: number;
        [k: string]: unknown;
      };
      /**
       * 自定义图表
       */
      customChart?: boolean;
      /**
       * 数据子集图表标识
       */
      dataSubSetChart?: boolean;
      /**
       * 数据子集id列表, 自定义图表必填
       */
      dataSubSetIds?: number[];
      /**
       * 评估数据集
       */
      evalDataSet: string;
      /**
       * 评估数据集规模
       */
      evalDataSetSize: string;
      /**
       * 评测版本
       */
      evalMetaId?: number;
      /**
       * 评估指标维度
       */
      evalModelMetricsDimension: string;
      /**
       * 评估指标名
       */
      evalModelMetricsNames?: string[];
      /**
       * 评估实验
       */
      evalModelName: string;
      /**
       * 根据模型查询评估模型名的过滤规则
       */
      evalModelNameRules: {
        /**
         * 全部的评估模型名格式规则
         */
        allEvalModelNameRules?: string[];
        /**
         * 模型metaId
         */
        modelMetaId: number;
        /**
         * 模型名
         */
        modelName?: string;
        /**
         * 已选中的评估模型名格式规则
         */
        specifyEvalModelNameRules: string[];
        [k: string]: unknown;
      }[];
      /**
       * 心跳超时时间, 单位秒
       */
      heartbeatTtl: number;
      /**
       * 忽略无效数据点
       */
      ignoreInvalidData: boolean;
      /**
       * 模型ID列表请求入参
       */
      modelIdRequest: {
        /**
         * 模型Id
         */
        modelAndMetaIds: {
          /**
           * 模型Id
           */
          modelIds?: number[];
          /**
           * 模型metaId
           */
          modelMetaId: number;
          [k: string]: unknown;
        }[];
        [k: string]: unknown;
      };
      /**
       * 自定义图表必填, true: 无版本数据子集, false:  有版本数据子集
       */
      nonversionDataSubset?: boolean;
      /**
       * uuid, 请求的唯一标识
       */
      queryUuid: string;
      /**
       * MASTER指标合并请求发起页面的标识, 不需要传值
       */
      rightActiveTab?: string;
      /**
       * 指标维度, 自定义图表必填
       */
      statValue?: string;
      [k: string]: unknown;
    };
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型ID列表请求入参
 */
export interface IPostWorkspaceModelDiffTensorBoardParameter {
  /**
   * 模型Id
   */
  modelAndMetaIds: {
    /**
     * 模型Id
     */
    modelIds?: number[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    [k: string]: unknown;
  }[];
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelDiffTensorBoardResponse {
  /**
   * Tensorboard对比结果
   */
  data: {
    /**
     * 模型metaId颜色映射
     */
    modelMetaIdColorMap?: {
      [k: string]: string;
    };
    /**
     * tensorboardUrl
     */
    tensorboardUrl?: string;
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型ID列表请求入参
 */
export interface IPostWorkspaceModelGetModelListParameter {
  /**
   * 模型Id
   */
  modelAndMetaIds: {
    /**
     * 模型Id
     */
    modelIds?: number[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    [k: string]: unknown;
  }[];
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelGetModelListResponse {
  /**
   * 模型信息列表请求返回值
   */
  data: {
    /**
     * 模型列表
     */
    modelList: {
      /**
       * 模型列表是否是全部Checkpoint
       */
      allInstances: boolean;
      /**
       * 是否为标杆模型
       */
      benchmarking: boolean;
      /**
       * 模型信息
       */
      bestCheckpointModelInfo?: {
        /**
         * 是否为标杆模型
         */
        benchmarking?: boolean;
        /**
         * 模型创建人
         */
        creator: string;
        /**
         * 创建时间
         */
        cts: string;
        /**
         * 实验任务详情id
         */
        experimentTaskDetailId?: number;
        /**
         * 扩展词表地址
         */
        extTokenizerPath?: string;
        /**
         * 扩展字段集合
         */
        extraInfoFields?: {
          /**
           * 扩展字段值类型
           */
          fieldType?: string;
          /**
           * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
           */
          filedKey?:
            | 'CONTEXT_LENGTH'
            | 'REQUEST_BATCH_SIZE'
            | 'MAX_NEW_TOKENS'
            | 'FOLLOW_DATA_SUB_SET'
            | 'TEMPERATURE'
            | 'REPETITION_PENALTY'
            | 'TOP_K'
            | 'TOP_P'
            | 'PROMPT_PREFIX'
            | 'PROMPT_SUFFIX'
            | 'SYSTEM_PROMPT'
            | 'STOP_SEQUENCES'
            | 'MODEL_SCORE_EXTRACT_PREFIX'
            | 'MODEL_SCORE_EXTRACT_SUFFIX'
            | 'AUTO_EVAL_DATA_SIZE'
            | 'CALCULATE_TOKEN'
            | 'CALCULATE_TOKEN_PARAM'
            | 'AUTO_EVAL'
            | 'AUTO_EVAL_PARAM'
            | 'TRAIN_TASK_ID'
            | 'MODEL_CONFIG_DIRECTORY'
            | 'COMPARISON_MODEL_META_ID'
            | 'COMPARISON_BENCHMARK_ID'
            | 'TP'
            | 'PP'
            | 'AUTO_TOKEN'
            | 'TOKEN'
            | 'FLOPS';
          /**
           * 扩展字段key
           */
          filedName?: string;
          /**
           * 扩展字段值
           */
          filedValue?: string;
          /**
           * 是否系统创建
           */
          systemCreate?: boolean;
          [k: string]: unknown;
        }[];
        /**
         * 模型训练每秒浮点计算次数
         */
        flops?: string;
        /**
         * 模型checkpoint
         */
        modelCheckpoint: string;
        /**
         * 模型格式
         */
        modelFormat: string;
        /**
         * 模型id
         */
        modelId: number;
        /**
         * 模型meta信息id
         */
        modelMetaId?: number;
        /**
         * 模型名
         */
        modelName?: string;
        /**
         * 模型地址
         */
        modelPath: string;
        /**
         * 模型别名
         */
        modelShortName?: string;
        /**
         * 产出阶段
         */
        modelStage?: string;
        /**
         * 模型状态
         */
        modelStatus: boolean;
        /**
         * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
         */
        modelType: number;
        /**
         * 模型mp
         */
        pp?: number;
        /**
         * 注册渠道
         */
        registerChannel: string;
        /**
         * 模型step
         */
        step?: string;
        /**
         * 模型标签
         */
        tagValues?: {
          /**
           * 唯一标识, 前后端交互用
           */
          identification: string;
          /**
           * 字段值
           */
          values?: {
            /**
             * 可选值展示名
             */
            label?: string;
            /**
             * 可选值
             */
            name?: string;
            [k: string]: unknown;
          }[];
          [k: string]: unknown;
        }[];
        /**
         * 模型训练使用token数
         */
        token?: string;
        /**
         * 模型tp
         */
        tp?: number;
        [k: string]: unknown;
      };
      /**
       * 模型列表
       */
      modelInstances: {
        /**
         * 评测数据集规模，评测指标查询使用的默认规模
         */
        evalDataSetSize?: string;
        /**
         * 产出模型的任务id
         */
        experimentTaskDetailId?: number;
        /**
         * 模型checkpoint
         */
        modelCheckpoint: string;
        /**
         * 模型格式
         */
        modelFormat: string;
        /**
         * 模型id
         */
        modelId: number;
        /**
         * 模型地址
         */
        modelPath: string;
        [k: string]: unknown;
      }[];
      /**
       * 模型metaId
       */
      modelMetaId: number;
      /**
       * 模型名
       */
      modelName: string;
      /**
       * 模型别名
       */
      modelShortName: string;
      /**
       * 产出阶段
       */
      modelStage?: string;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

export interface IGetWorkspaceModelGetLatestByUserParameter {
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IGetWorkspaceModelGetLatestByUserResponse {
  /**
   * 视图基本信息
   */
  data: {
    /**
     * 收藏数量
     */
    collectNum?: number;
    /**
     * 是否被当前访问用户收藏
     */
    collected?: boolean;
    /**
     * 创建人
     */
    creator?: string;
    /**
     * 创建时间
     */
    cts?: string;
    /**
     * 视图描述
     */
    description?: string;
    /**
     * 视图保存的详细参数
     */
    detail?: string;
    /**
     * 视图保留时长(枚举值, 可选如下(值-展示名): 1-长期; 2-30天)
     */
    expireType?: number;
    /**
     * 视图id
     */
    id?: number;
    /**
     * 视图名称
     */
    name?: string;
    /**
     * 更新时间
     */
    uts?: string;
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 视图列表请求参数
 */
export interface IPostWorkspaceModelListViewParameter {
  /**
   * 与我有关
   */
  aboutMe: boolean;
  /**
   * 页码
   */
  curPage: number;
  /**
   * 是否为长期视图
   */
  longTermView: boolean;
  /**
   * 我的收藏
   */
  myCollection: boolean;
  /**
   * 搜索关键词(视图名称，描述，创建人mis，模型名)
   */
  queryKeyword?: string;
  /**
   * 每页显示数
   */
  showCount: number;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelListViewResponse {
  /**
   * 响应数据
   */
  data: {
    /**
     * 视图总数量
     */
    totalNum?: number;
    /**
     * 视图基本信息列表
     */
    viewList?: {
      /**
       * 收藏数量
       */
      collectNum?: number;
      /**
       * 是否被当前访问用户收藏
       */
      collected?: boolean;
      /**
       * 创建人
       */
      creator?: string;
      /**
       * 创建时间
       */
      cts?: string;
      /**
       * 视图描述
       */
      description?: string;
      /**
       * 视图保存的详细参数
       */
      detail?: string;
      /**
       * 视图保留时长(枚举值, 可选如下(值-展示名): 1-长期; 2-30天)
       */
      expireType?: number;
      /**
       * 视图id
       */
      id?: number;
      /**
       * 视图名称
       */
      name?: string;
      /**
       * 更新时间
       */
      uts?: string;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  }[];
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型ID列表请求入参
 */
export interface IPostWorkspaceModelDiffTrainParamsParameter {
  /**
   * 模型Id
   */
  modelAndMetaIds: {
    /**
     * 模型Id
     */
    modelIds?: number[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    [k: string]: unknown;
  }[];
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelDiffTrainParamsResponse {
  /**
   * 训练参数对比结果
   */
  data: {
    /**
     * 参数类型集合
     */
    diffParamItems?: {
      /**
       * 参数分组key
       */
      groupKey?: string;
      /**
       * 参数集合
       */
      items?: {
        /**
         * 参数名
         */
        key?: string;
        /**
         * 是否一致
         */
        same?: boolean;
        /**
         * 参数集合
         */
        values?: {
          /**
           * 实验名称
           */
          experimentName?: string;
          /**
           * 参数key
           */
          key?: string;
          /**
           * 参数值
           */
          value?: string;
          [k: string]: unknown;
        }[];
        [k: string]: unknown;
      }[];
      [k: string]: unknown;
    }[];
    /**
     * 实验名称集合
     */
    experimentNames?: string[];
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型ID列表请求入参
 */
export interface IPostWorkspaceModelDiffTrainMetricsParameter {
  /**
   * 模型Id
   */
  modelAndMetaIds: {
    /**
     * 模型Id
     */
    modelIds?: number[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    [k: string]: unknown;
  }[];
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelDiffTrainMetricsResponse {
  /**
   * 训练指标对比结果
   */
  data: {
    /**
     * 模型metaId颜色映射
     */
    modelMetaIdColorMap?: {
      [k: string]: string;
    };
    /**
     * 训练指标列表
     */
    trainMetricsList?: {
      /**
       * 训练指标url
       */
      metricsUrlList?: {
        /**
         * 训练指标名
         */
        metricsName?: string;
        /**
         * 训练指标URL
         */
        metricsUrl?: string;
        [k: string]: unknown;
      }[];
      /**
       * 训练指标Section名称
       */
      sectionName?: string;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型ID列表请求入参
 */
export interface IPostWorkspaceModelListEvalModelNameParameter {
  /**
   * 模型Id
   */
  modelAndMetaIds: {
    /**
     * 模型Id
     */
    modelIds?: number[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    [k: string]: unknown;
  }[];
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelListEvalModelNameResponse {
  /**
   * 响应数据
   */
  data: {
    /**
     * 全部的评估模型名格式规则
     */
    allEvalModelNameRules?: string[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    /**
     * 模型名
     */
    modelName?: string;
    /**
     * 已选中的评估模型名格式规则
     */
    specifyEvalModelNameRules: string[];
    [k: string]: unknown;
  }[];
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型评估指标请求入参
 */
export interface IPostWorkspaceModelListEvalModelNameV2Parameter {
  /**
   * 是否为自定义评测tab
   */
  customChart?: boolean;
  /**
   * 评估数据集
   */
  evalDataSet: string;
  /**
   * 评估数据集规模
   */
  evalDataSetSize: string;
  /**
   * 评测版本
   */
  evalMetaId?: number;
  /**
   * 评估实验
   */
  evalModelName: string;
  /**
   * 模型ID列表请求入参
   */
  modelIdRequest: {
    /**
     * 模型Id
     */
    modelAndMetaIds: {
      /**
       * 模型Id
       */
      modelIds?: number[];
      /**
       * 模型metaId
       */
      modelMetaId: number;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelListEvalModelNameV2Response {
  /**
   * 响应数据
   */
  data: {
    /**
     * 全部的评估模型名格式规则
     */
    allEvalModelNameRules?: string[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    /**
     * 模型名
     */
    modelName?: string;
    /**
     * 已选中的评估模型名格式规则
     */
    specifyEvalModelNameRules: string[];
    [k: string]: unknown;
  }[];
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型评估指标请求入参
 */
export interface IPostWorkspaceModelListEvalMetricsDimensionParameter {
  /**
   * 是否为自定义评测tab
   */
  customChart?: boolean;
  /**
   * 评估数据集
   */
  evalDataSet: string;
  /**
   * 评估数据集规模
   */
  evalDataSetSize: string;
  /**
   * 评测版本
   */
  evalMetaId?: number;
  /**
   * 评估实验
   */
  evalModelName: string;
  /**
   * 模型ID列表请求入参
   */
  modelIdRequest: {
    /**
     * 模型Id
     */
    modelAndMetaIds: {
      /**
       * 模型Id
       */
      modelIds?: number[];
      /**
       * 模型metaId
       */
      modelMetaId: number;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelListEvalMetricsDimensionResponse {
  /**
   * 评估指标维度
   */
  data: {
    /**
     * 评估指标维度
     */
    dimensionVos?: {
      /**
       * 评估指标名
       */
      chartName?: string[];
      /**
       * 评估指标维度
       */
      sectionName?: string;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型ID列表请求入参
 */
export interface IPostWorkspaceModelListEvalDataSetAndSizeParameter {
  /**
   * 模型Id
   */
  modelAndMetaIds: {
    /**
     * 模型Id
     */
    modelIds?: number[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    [k: string]: unknown;
  }[];
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelListEvalDataSetAndSizeResponse {
  /**
   * 响应数据
   */
  data: {
    /**
     * 评估数据集
     */
    dataSetName?: string;
    /**
     * 评估数据集规模
     */
    dataSetSizes?: string[];
    [k: string]: unknown;
  }[];
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}

/**
 * 模型评估指标请求入参
 */
export interface IPostWorkspaceModelDiffModelEvalMetricsChartParameter {
  /**
   * 折线调整参数
   */
  curveModifier: {
    /**
     * 划定ckpt阈值: 过滤时仅考虑大于阈值的ckpt
     */
    labelThreshold: string;
    /**
     * 置信区间, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
     */
    topCIPercentage: number;
    /**
     * 困难的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
     */
    topDiffPercentage: number;
    /**
     * 简单的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
     */
    topEasyPercentage: number;
    /**
     * 波动的百分比, 不过滤时为0, 数值范围0-1(大于1默认/100处理)
     */
    topVariancePercentage: number;
    [k: string]: unknown;
  };
  /**
   * 自定义图表
   */
  customChart?: boolean;
  /**
   * 数据子集图表标识
   */
  dataSubSetChart?: boolean;
  /**
   * 数据子集id列表, 自定义图表必填
   */
  dataSubSetIds?: number[];
  /**
   * 评估数据集
   */
  evalDataSet: string;
  /**
   * 评估数据集规模
   */
  evalDataSetSize: string;
  /**
   * 评测版本
   */
  evalMetaId?: number;
  /**
   * 评估指标维度
   */
  evalModelMetricsDimension: string;
  /**
   * 评估指标名
   */
  evalModelMetricsNames?: string[];
  /**
   * 评估实验
   */
  evalModelName: string;
  /**
   * 根据模型查询评估模型名的过滤规则
   */
  evalModelNameRules: {
    /**
     * 全部的评估模型名格式规则
     */
    allEvalModelNameRules?: string[];
    /**
     * 模型metaId
     */
    modelMetaId: number;
    /**
     * 模型名
     */
    modelName?: string;
    /**
     * 已选中的评估模型名格式规则
     */
    specifyEvalModelNameRules: string[];
    [k: string]: unknown;
  }[];
  /**
   * 心跳超时时间, 单位秒
   */
  heartbeatTtl: number;
  /**
   * 忽略无效数据点
   */
  ignoreInvalidData: boolean;
  /**
   * 模型ID列表请求入参
   */
  modelIdRequest: {
    /**
     * 模型Id
     */
    modelAndMetaIds: {
      /**
       * 模型Id
       */
      modelIds?: number[];
      /**
       * 模型metaId
       */
      modelMetaId: number;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  /**
   * 自定义图表必填, true: 无版本数据子集, false:  有版本数据子集
   */
  nonversionDataSubset?: boolean;
  /**
   * uuid, 请求的唯一标识
   */
  queryUuid: string;
  /**
   * MASTER指标合并请求发起页面的标识, 不需要传值
   */
  rightActiveTab?: string;
  /**
   * 指标维度, 自定义图表必填
   */
  statValue?: string;
  [k: string]: unknown;
}

/**
 * 通用响应结构体
 */
export interface IPostWorkspaceModelDiffModelEvalMetricsChartResponse {
  /**
   * 模型评估指标返回值(图表形式)
   */
  data: {
    /**
     * 模型评估指标
     */
    metricsCharts?: {
      /**
       * 图表曲线
       */
      metricsChartCurves?: {
        /**
         * 模型评估指标
         */
        metricsList?: {
          /**
           * 评测模型名family
           */
          family?: string;
          /**
           * 有结果的数据子集数
           */
          hasStatSubSetCount?: number;
          modelEvalModelStat?: {
            evalDataSize?: string;
            family?: string;
            groupValue?: {
              confidenceInterval?: string;
              hasStatSubSetCount?: number;
              macroMean?: string;
              mean?: string;
              result?: {
                binNames?: string[];
                binResultList?: {
                  calculateUnitList?: {
                    aggrateType?: string;
                    autoEvalDataSizeList?: string[];
                    commonSubSetEvalInstanceCount?: number;
                    complete?: boolean;
                    confidenceInterval?: number;
                    configTotalInstanceCount?: number;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    evalDataSize?: string;
                    evalKeyCount?: number;
                    instanceCount?: number;
                    instanceStatCalculateUnitList?: {
                      evalKey?: string;
                      instanceId?: string;
                      statValue?: number;
                      [k: string]: unknown;
                    }[];
                    invalidReason?: string;
                    modelFamily?: string;
                    modelName?: string;
                    runSpecSetId?: number;
                    statType?: string;
                    statValue?: number;
                    subSetId?: number;
                    textLenOutputCount?: number;
                    tiktokenOutputCount?: number;
                    tokenInstanceCount?: number;
                    tokenOutputCount?: number;
                    totalInstanceCount?: number;
                    valueIsValid?: boolean;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  categoryIds?: number[];
                  dataSubSetIds?: number[];
                  dataSubSetWeightSum?: number;
                  evalDataSize?: string;
                  family?: string;
                  hasStatDataSubSetIds?: number[];
                  name?: string;
                  notCalculateDataSubSetList?: {
                    autoEvalDataSizeOrder?: string;
                    autoEvalDataSizeOrderList?: string[];
                    bindingRunSpecList?: {
                      cts?: string;
                      dataSetLabel?: string;
                      dataSetName?: string;
                      dataSubSetId?: number;
                      dataSubSetLabel?: string;
                      dataSubSetName?: string;
                      dataSubSetPublicStatus?: string;
                      description?: string;
                      extraParam?: string;
                      id?: number;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    categoryList?: unknown[];
                    cts?: string;
                    dataSet?: {
                      cts?: string;
                      description?: string;
                      execType?: string;
                      id?: number;
                      label?: string;
                      metaVersionId?: number;
                      name?: string;
                      ownerList?: string[];
                      runtimeConfig?: string;
                      status?: string;
                      subSetCount?: number;
                      uts?: string;
                      [k: string]: unknown;
                    };
                    dataSetLabel?: string;
                    dataSetName?: string;
                    description?: string;
                    evalType?: string;
                    files?: {
                      cts?: string;
                      dataKey?: string;
                      detail?: {
                        bucket?: string;
                        dataSetName?: string;
                        dataSetVersionName?: string;
                        errorMsg?: string;
                        index?: string;
                        labelType?: number;
                        path?: string;
                        [k: string]: unknown;
                      };
                      detailJson?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      storageType?: string;
                      uploadTs?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    id?: number;
                    instanceCount?: number;
                    instanceDetail?: {
                      instanceList?: {
                        list?: {
                          categoryList?: string;
                          correctAnswer?: string;
                          cts?: string;
                          id?: number;
                          instanceId?: string;
                          modelEvalDataSubSetId?: number;
                          modelPrompt?: string;
                          originInput?: string;
                          uts?: string;
                          [k: string]: unknown;
                        }[];
                        totalCount?: number;
                        [k: string]: unknown;
                      };
                      instanceOriginContent?: string;
                      [k: string]: unknown;
                    };
                    label?: string;
                    metaVersionId?: number;
                    modelEvalDataSetId?: number;
                    name?: string;
                    publicStatus?: string;
                    statType?: string;
                    stats?: {
                      cts?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    tags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    uts?: string;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  value?: number;
                  [k: string]: unknown;
                }[];
                categoryId?: number;
                children?: {
                  binNames?: string[];
                  children?: unknown[];
                  confidenceIntervalName?: string;
                  itemDisplayType?: string;
                  label?: string;
                  name?: string;
                  objectiveBaseValueConfig?: {
                    dataSubSetTags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    evalDataSize?: string;
                    modelFamily?: string;
                    modelMetaIdForMaster?: number;
                    modelName?: string;
                    statName?: string;
                    [k: string]: unknown;
                  };
                  objectiveEvalValueConfig?: {
                    dataSubSetTags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    evalDataSize?: string;
                    modelFamily?: string;
                    modelMetaIdForMaster?: number;
                    modelName?: string;
                    statName?: string;
                    [k: string]: unknown;
                  };
                  objectiveValueConfigList?: {
                    dataSubSetTags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    evalDataSize?: string;
                    modelFamily?: string;
                    modelMetaIdForMaster?: number;
                    modelName?: string;
                    statName?: string;
                    [k: string]: unknown;
                  }[];
                  relationTagNameList?: string[];
                  secondStatName?: string;
                  showCapabilityReportFlag?: boolean;
                  subjectiveValueConfigList?: {
                    dataSubSetTags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    evalDataSize?: string;
                    statName?: string;
                    [k: string]: unknown;
                  }[];
                  winRateFlag?: boolean;
                  [k: string]: unknown;
                }[];
                confidenceInterval?: string;
                confidenceIntervalName?: string;
                confidenceResult?: {
                  calculateUnitList?: {
                    aggrateType?: string;
                    autoEvalDataSizeList?: string[];
                    commonSubSetEvalInstanceCount?: number;
                    complete?: boolean;
                    confidenceInterval?: number;
                    configTotalInstanceCount?: number;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    evalDataSize?: string;
                    evalKeyCount?: number;
                    instanceCount?: number;
                    instanceStatCalculateUnitList?: {
                      evalKey?: string;
                      instanceId?: string;
                      statValue?: number;
                      [k: string]: unknown;
                    }[];
                    invalidReason?: string;
                    modelFamily?: string;
                    modelName?: string;
                    runSpecSetId?: number;
                    statType?: string;
                    statValue?: number;
                    subSetId?: number;
                    textLenOutputCount?: number;
                    tiktokenOutputCount?: number;
                    tokenInstanceCount?: number;
                    tokenOutputCount?: number;
                    totalInstanceCount?: number;
                    valueIsValid?: boolean;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  categoryIds?: number[];
                  dataSubSetIds?: number[];
                  dataSubSetWeightSum?: number;
                  evalDataSize?: string;
                  family?: string;
                  hasStatDataSubSetIds?: number[];
                  name?: string;
                  notCalculateDataSubSetList?: {
                    autoEvalDataSizeOrder?: string;
                    autoEvalDataSizeOrderList?: string[];
                    bindingRunSpecList?: {
                      cts?: string;
                      dataSetLabel?: string;
                      dataSetName?: string;
                      dataSubSetId?: number;
                      dataSubSetLabel?: string;
                      dataSubSetName?: string;
                      dataSubSetPublicStatus?: string;
                      description?: string;
                      extraParam?: string;
                      id?: number;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    categoryList?: unknown[];
                    cts?: string;
                    dataSet?: {
                      cts?: string;
                      description?: string;
                      execType?: string;
                      id?: number;
                      label?: string;
                      metaVersionId?: number;
                      name?: string;
                      ownerList?: string[];
                      runtimeConfig?: string;
                      status?: string;
                      subSetCount?: number;
                      uts?: string;
                      [k: string]: unknown;
                    };
                    dataSetLabel?: string;
                    dataSetName?: string;
                    description?: string;
                    evalType?: string;
                    files?: {
                      cts?: string;
                      dataKey?: string;
                      detail?: {
                        bucket?: string;
                        dataSetName?: string;
                        dataSetVersionName?: string;
                        errorMsg?: string;
                        index?: string;
                        labelType?: number;
                        path?: string;
                        [k: string]: unknown;
                      };
                      detailJson?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      storageType?: string;
                      uploadTs?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    id?: number;
                    instanceCount?: number;
                    instanceDetail?: {
                      instanceList?: {
                        list?: {
                          categoryList?: string;
                          correctAnswer?: string;
                          cts?: string;
                          id?: number;
                          instanceId?: string;
                          modelEvalDataSubSetId?: number;
                          modelPrompt?: string;
                          originInput?: string;
                          uts?: string;
                          [k: string]: unknown;
                        }[];
                        totalCount?: number;
                        [k: string]: unknown;
                      };
                      instanceOriginContent?: string;
                      [k: string]: unknown;
                    };
                    label?: string;
                    metaVersionId?: number;
                    modelEvalDataSetId?: number;
                    name?: string;
                    publicStatus?: string;
                    statType?: string;
                    stats?: {
                      cts?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    tags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    uts?: string;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  value?: number;
                  [k: string]: unknown;
                };
                dataSetId?: number;
                dataSubSetId?: number;
                hasStatSubSetCount?: number;
                itemDisplayType?: string;
                label?: string;
                name?: string;
                objectiveBaseValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveEvalValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                relationTagNameList?: string[];
                result?: {
                  calculateUnitList?: {
                    aggrateType?: string;
                    autoEvalDataSizeList?: string[];
                    commonSubSetEvalInstanceCount?: number;
                    complete?: boolean;
                    confidenceInterval?: number;
                    configTotalInstanceCount?: number;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    evalDataSize?: string;
                    evalKeyCount?: number;
                    instanceCount?: number;
                    instanceStatCalculateUnitList?: {
                      evalKey?: string;
                      instanceId?: string;
                      statValue?: number;
                      [k: string]: unknown;
                    }[];
                    invalidReason?: string;
                    modelFamily?: string;
                    modelName?: string;
                    runSpecSetId?: number;
                    statType?: string;
                    statValue?: number;
                    subSetId?: number;
                    textLenOutputCount?: number;
                    tiktokenOutputCount?: number;
                    tokenInstanceCount?: number;
                    tokenOutputCount?: number;
                    totalInstanceCount?: number;
                    valueIsValid?: boolean;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  categoryIds?: number[];
                  dataSubSetIds?: number[];
                  dataSubSetWeightSum?: number;
                  evalDataSize?: string;
                  family?: string;
                  hasStatDataSubSetIds?: number[];
                  name?: string;
                  notCalculateDataSubSetList?: {
                    autoEvalDataSizeOrder?: string;
                    autoEvalDataSizeOrderList?: string[];
                    bindingRunSpecList?: {
                      cts?: string;
                      dataSetLabel?: string;
                      dataSetName?: string;
                      dataSubSetId?: number;
                      dataSubSetLabel?: string;
                      dataSubSetName?: string;
                      dataSubSetPublicStatus?: string;
                      description?: string;
                      extraParam?: string;
                      id?: number;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    categoryList?: unknown[];
                    cts?: string;
                    dataSet?: {
                      cts?: string;
                      description?: string;
                      execType?: string;
                      id?: number;
                      label?: string;
                      metaVersionId?: number;
                      name?: string;
                      ownerList?: string[];
                      runtimeConfig?: string;
                      status?: string;
                      subSetCount?: number;
                      uts?: string;
                      [k: string]: unknown;
                    };
                    dataSetLabel?: string;
                    dataSetName?: string;
                    description?: string;
                    evalType?: string;
                    files?: {
                      cts?: string;
                      dataKey?: string;
                      detail?: {
                        bucket?: string;
                        dataSetName?: string;
                        dataSetVersionName?: string;
                        errorMsg?: string;
                        index?: string;
                        labelType?: number;
                        path?: string;
                        [k: string]: unknown;
                      };
                      detailJson?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      storageType?: string;
                      uploadTs?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    id?: number;
                    instanceCount?: number;
                    instanceDetail?: {
                      instanceList?: {
                        list?: {
                          categoryList?: string;
                          correctAnswer?: string;
                          cts?: string;
                          id?: number;
                          instanceId?: string;
                          modelEvalDataSubSetId?: number;
                          modelPrompt?: string;
                          originInput?: string;
                          uts?: string;
                          [k: string]: unknown;
                        }[];
                        totalCount?: number;
                        [k: string]: unknown;
                      };
                      instanceOriginContent?: string;
                      [k: string]: unknown;
                    };
                    label?: string;
                    metaVersionId?: number;
                    modelEvalDataSetId?: number;
                    name?: string;
                    publicStatus?: string;
                    statType?: string;
                    stats?: {
                      cts?: string;
                      id?: number;
                      modelEvalDataSubSetId?: number;
                      name?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    tags?: {
                      name?: string;
                      values?: string[];
                      [k: string]: unknown;
                    }[];
                    uts?: string;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  value?: number;
                  [k: string]: unknown;
                };
                secondStatName?: string;
                secondValue?: string;
                showCapabilityReportFlag?: boolean;
                subjectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                totalSubSetCount?: number;
                type?: string;
                value?: string;
                winRateFlag?: boolean;
                [k: string]: unknown;
              };
              secondValue?: string;
              totalSubSetCount?: number;
              [k: string]: unknown;
            };
            label?: string;
            llmModel?: {
              authType?: string;
              autoEvalDataSize?: string;
              benchmarking?: string;
              cts?: string;
              family?: string;
              id?: number;
              label?: string;
              modelId?: number;
              modelMetaId?: number;
              modelPath?: string;
              name?: string;
              nlpOpenRunSpecSetId?: number;
              ownerList?: string[];
              simpleTags?: string[];
              status?: string;
              tags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              uts?: string;
              viewerMisList?: string[];
              viewerRoleList?: string[];
              [k: string]: unknown;
            };
            /**
             * 模型信息
             */
            modelInfoVo?: {
              /**
               * 是否为标杆模型
               */
              benchmarking?: boolean;
              /**
               * 模型创建人
               */
              creator: string;
              /**
               * 创建时间
               */
              cts: string;
              /**
               * 实验任务详情id
               */
              experimentTaskDetailId?: number;
              /**
               * 扩展词表地址
               */
              extTokenizerPath?: string;
              /**
               * 扩展字段集合
               */
              extraInfoFields?: {
                /**
                 * 扩展字段值类型
                 */
                fieldType?: string;
                /**
                 * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
                 */
                filedKey?:
                  | 'CONTEXT_LENGTH'
                  | 'REQUEST_BATCH_SIZE'
                  | 'MAX_NEW_TOKENS'
                  | 'FOLLOW_DATA_SUB_SET'
                  | 'TEMPERATURE'
                  | 'REPETITION_PENALTY'
                  | 'TOP_K'
                  | 'TOP_P'
                  | 'PROMPT_PREFIX'
                  | 'PROMPT_SUFFIX'
                  | 'SYSTEM_PROMPT'
                  | 'STOP_SEQUENCES'
                  | 'MODEL_SCORE_EXTRACT_PREFIX'
                  | 'MODEL_SCORE_EXTRACT_SUFFIX'
                  | 'AUTO_EVAL_DATA_SIZE'
                  | 'CALCULATE_TOKEN'
                  | 'CALCULATE_TOKEN_PARAM'
                  | 'AUTO_EVAL'
                  | 'AUTO_EVAL_PARAM'
                  | 'TRAIN_TASK_ID'
                  | 'MODEL_CONFIG_DIRECTORY'
                  | 'COMPARISON_MODEL_META_ID'
                  | 'COMPARISON_BENCHMARK_ID'
                  | 'TP'
                  | 'PP'
                  | 'AUTO_TOKEN'
                  | 'TOKEN'
                  | 'FLOPS';
                /**
                 * 扩展字段key
                 */
                filedName?: string;
                /**
                 * 扩展字段值
                 */
                filedValue?: string;
                /**
                 * 是否系统创建
                 */
                systemCreate?: boolean;
                [k: string]: unknown;
              }[];
              /**
               * 模型训练每秒浮点计算次数
               */
              flops?: string;
              /**
               * 模型checkpoint
               */
              modelCheckpoint: string;
              /**
               * 模型格式
               */
              modelFormat: string;
              /**
               * 模型id
               */
              modelId: number;
              /**
               * 模型meta信息id
               */
              modelMetaId?: number;
              /**
               * 模型名
               */
              modelName?: string;
              /**
               * 模型地址
               */
              modelPath: string;
              /**
               * 模型别名
               */
              modelShortName?: string;
              /**
               * 产出阶段
               */
              modelStage?: string;
              /**
               * 模型状态
               */
              modelStatus: boolean;
              /**
               * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
               */
              modelType: number;
              /**
               * 模型mp
               */
              pp?: number;
              /**
               * 注册渠道
               */
              registerChannel: string;
              /**
               * 模型step
               */
              step?: string;
              /**
               * 模型标签
               */
              tagValues?: {
                /**
                 * 唯一标识, 前后端交互用
                 */
                identification: string;
                /**
                 * 字段值
                 */
                values?: {
                  /**
                   * 可选值展示名
                   */
                  label?: string;
                  /**
                   * 可选值
                   */
                  name?: string;
                  [k: string]: unknown;
                }[];
                [k: string]: unknown;
              }[];
              /**
               * 模型训练使用token数
               */
              token?: string;
              /**
               * 模型tp
               */
              tp?: number;
              [k: string]: unknown;
            };
            name?: string;
            nonversionValueList?: {
              categoryId?: number;
              dataSubSetId?: number;
              description?: string;
              label?: string;
              name?: string;
              publicStatus?: string;
              unitList?: {
                confidenceInterval?: string;
                dataSetOpennessStatus?: string;
                hasStatSubSetCount?: number;
                objectivityIntegrationMean?: string;
                result?: {
                  calculateUnitList?: {
                    aggrateType?: string;
                    autoEvalDataSizeList?: string[];
                    commonSubSetEvalInstanceCount?: number;
                    complete?: boolean;
                    confidenceInterval?: number;
                    configTotalInstanceCount?: number;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    evalDataSize?: string;
                    evalKeyCount?: number;
                    instanceCount?: number;
                    instanceStatCalculateUnitList?: {
                      evalKey?: string;
                      instanceId?: string;
                      statValue?: number;
                      [k: string]: unknown;
                    }[];
                    invalidReason?: string;
                    modelFamily?: string;
                    modelName?: string;
                    runSpecSetId?: number;
                    statType?: string;
                    statValue?: number;
                    subSetId?: number;
                    textLenOutputCount?: number;
                    tiktokenOutputCount?: number;
                    tokenInstanceCount?: number;
                    tokenOutputCount?: number;
                    totalInstanceCount?: number;
                    valueIsValid?: boolean;
                    weight?: number;
                    [k: string]: unknown;
                  }[];
                  categoryIds?: number[];
                  dataSubSetIds?: number[];
                  dataSubSetWeightSum?: number;
                  evalDataSize?: string;
                  family?: string;
                  hasStatDataSubSetIds?: number[];
                  instanceCountRatio?: number;
                  instanceIncompleteList?: {
                    dataSetName?: string;
                    id?: number;
                    invalidReason?: string;
                    name?: string;
                    [k: string]: unknown;
                  }[];
                  name?: string;
                  notCalculateDataSubSetList?: {
                    dataSetName?: string;
                    id?: number;
                    invalidReason?: string;
                    name?: string;
                    [k: string]: unknown;
                  }[];
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  value?: number;
                  [k: string]: unknown;
                };
                secondValue?: string;
                subjectivityIsFromModelEval?: boolean;
                textLenOutputAvg?: number;
                tikTokenOutputAvg?: number;
                tokenOutputAvg?: number;
                totalSubSetCount?: number;
                value?: string;
                [k: string]: unknown;
              }[];
              [k: string]: unknown;
            }[];
            searchEvalDataSize?: string;
            valueList?: {
              binNames?: string[];
              binResultList?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              }[];
              categoryId?: number;
              children?: {
                binNames?: string[];
                children?: unknown[];
                confidenceIntervalName?: string;
                itemDisplayType?: string;
                label?: string;
                name?: string;
                objectiveBaseValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveEvalValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                relationTagNameList?: string[];
                secondStatName?: string;
                showCapabilityReportFlag?: boolean;
                subjectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                winRateFlag?: boolean;
                [k: string]: unknown;
              }[];
              confidenceInterval?: string;
              confidenceIntervalName?: string;
              confidenceResult?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              };
              dataSetId?: number;
              dataSubSetId?: number;
              hasStatSubSetCount?: number;
              itemDisplayType?: string;
              label?: string;
              name?: string;
              objectiveBaseValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveEvalValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              relationTagNameList?: string[];
              result?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              };
              secondStatName?: string;
              secondValue?: string;
              showCapabilityReportFlag?: boolean;
              subjectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              totalSubSetCount?: number;
              type?: string;
              value?: string;
              winRateFlag?: boolean;
              [k: string]: unknown;
            }[];
            winRateValue?: {
              comprehensiveValue?: string;
              flag?: boolean;
              objectiveValue?: string;
              objectiveValueEfficient?: number;
              objectiveValueTotal?: number;
              subjectiveValue?: string;
              subjectiveValueEfficient?: number;
              subjectiveValueTotal?: number;
              [k: string]: unknown;
            };
            [k: string]: unknown;
          };
          /**
           * 模型Id
           */
          modelId?: number;
          /**
           * 模型信息
           */
          modelInfoVo?: {
            /**
             * 是否为标杆模型
             */
            benchmarking?: boolean;
            /**
             * 模型创建人
             */
            creator: string;
            /**
             * 创建时间
             */
            cts: string;
            /**
             * 实验任务详情id
             */
            experimentTaskDetailId?: number;
            /**
             * 扩展词表地址
             */
            extTokenizerPath?: string;
            /**
             * 扩展字段集合
             */
            extraInfoFields?: {
              /**
               * 扩展字段值类型
               */
              fieldType?: string;
              /**
               * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
               */
              filedKey?:
                | 'CONTEXT_LENGTH'
                | 'REQUEST_BATCH_SIZE'
                | 'MAX_NEW_TOKENS'
                | 'FOLLOW_DATA_SUB_SET'
                | 'TEMPERATURE'
                | 'REPETITION_PENALTY'
                | 'TOP_K'
                | 'TOP_P'
                | 'PROMPT_PREFIX'
                | 'PROMPT_SUFFIX'
                | 'SYSTEM_PROMPT'
                | 'STOP_SEQUENCES'
                | 'MODEL_SCORE_EXTRACT_PREFIX'
                | 'MODEL_SCORE_EXTRACT_SUFFIX'
                | 'AUTO_EVAL_DATA_SIZE'
                | 'CALCULATE_TOKEN'
                | 'CALCULATE_TOKEN_PARAM'
                | 'AUTO_EVAL'
                | 'AUTO_EVAL_PARAM'
                | 'TRAIN_TASK_ID'
                | 'MODEL_CONFIG_DIRECTORY'
                | 'COMPARISON_MODEL_META_ID'
                | 'COMPARISON_BENCHMARK_ID'
                | 'TP'
                | 'PP'
                | 'AUTO_TOKEN'
                | 'TOKEN'
                | 'FLOPS';
              /**
               * 扩展字段key
               */
              filedName?: string;
              /**
               * 扩展字段值
               */
              filedValue?: string;
              /**
               * 是否系统创建
               */
              systemCreate?: boolean;
              [k: string]: unknown;
            }[];
            /**
             * 模型训练每秒浮点计算次数
             */
            flops?: string;
            /**
             * 模型checkpoint
             */
            modelCheckpoint: string;
            /**
             * 模型格式
             */
            modelFormat: string;
            /**
             * 模型id
             */
            modelId: number;
            /**
             * 模型meta信息id
             */
            modelMetaId?: number;
            /**
             * 模型名
             */
            modelName?: string;
            /**
             * 模型地址
             */
            modelPath: string;
            /**
             * 模型别名
             */
            modelShortName?: string;
            /**
             * 产出阶段
             */
            modelStage?: string;
            /**
             * 模型状态
             */
            modelStatus: boolean;
            /**
             * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
             */
            modelType: number;
            /**
             * 模型mp
             */
            pp?: number;
            /**
             * 注册渠道
             */
            registerChannel: string;
            /**
             * 模型step
             */
            step?: string;
            /**
             * 模型标签
             */
            tagValues?: {
              /**
               * 唯一标识, 前后端交互用
               */
              identification: string;
              /**
               * 字段值
               */
              values?: {
                /**
                 * 可选值展示名
                 */
                label?: string;
                /**
                 * 可选值
                 */
                name?: string;
                [k: string]: unknown;
              }[];
              [k: string]: unknown;
            }[];
            /**
             * 模型训练使用token数
             */
            token?: string;
            /**
             * 模型tp
             */
            tp?: number;
            [k: string]: unknown;
          };
          /**
           * 评测模型名name
           */
          name?: string;
          nonversionValueList?: {
            categoryId?: number;
            dataSubSetId?: number;
            description?: string;
            label?: string;
            name?: string;
            publicStatus?: string;
            unitList?: {
              confidenceInterval?: string;
              dataSetOpennessStatus?: string;
              hasStatSubSetCount?: number;
              objectivityIntegrationMean?: string;
              result?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                instanceCountRatio?: number;
                instanceIncompleteList?: {
                  dataSetName?: string;
                  id?: number;
                  invalidReason?: string;
                  name?: string;
                  [k: string]: unknown;
                }[];
                name?: string;
                notCalculateDataSubSetList?: {
                  dataSetName?: string;
                  id?: number;
                  invalidReason?: string;
                  name?: string;
                  [k: string]: unknown;
                }[];
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                value?: number;
                [k: string]: unknown;
              };
              secondValue?: string;
              subjectivityIsFromModelEval?: boolean;
              textLenOutputAvg?: number;
              tikTokenOutputAvg?: number;
              tokenOutputAvg?: number;
              totalSubSetCount?: number;
              value?: string;
              [k: string]: unknown;
            }[];
            [k: string]: unknown;
          }[];
          /**
           * 实际评测规模
           */
          realEvalSize?: string;
          /**
           * 数据子集总数
           */
          totalSubSetCount?: number;
          /**
           * 指标值
           */
          value?: string;
          [k: string]: unknown;
        }[];
        /**
         * 模型评估指标名称
         */
        metricsName?: string;
        /**
         * 模型格式
         */
        modelFormat?: string;
        /**
         * 模型meta信息
         */
        modelMeta?: {
          /**
           * 是否开启自动评测
           */
          autoEval?: boolean;
          /**
           * 自动评测实验画布id
           */
          autoEvalBaseExperimentId?: number;
          /**
           * 自动评测ckpt正则表达式
           */
          autoEvalCkptRegex?: string;
          /**
           * 评测集数据规模
           */
          autoEvalDataSize?: string;
          /**
           * 自动评测评测实验名
           */
          autoEvalEvaluateModelName?: string;
          /**
           * 自动评测实验画布名
           */
          autoEvalExperimentName?: string;
          /**
           * 自动评测ckpt格式
           */
          autoEvalModelFormat?: string;
          /**
           * 标杆模型
           */
          benchmarking?: boolean;
          /**
           * 最佳ckpt
           */
          bestCheckpoint?: number;
          /**
           * 模型信息
           */
          bestCheckpointModelInfo?: {
            /**
             * 是否为标杆模型
             */
            benchmarking?: boolean;
            /**
             * 模型创建人
             */
            creator: string;
            /**
             * 创建时间
             */
            cts: string;
            /**
             * 实验任务详情id
             */
            experimentTaskDetailId?: number;
            /**
             * 扩展词表地址
             */
            extTokenizerPath?: string;
            /**
             * 扩展字段集合
             */
            extraInfoFields?: {
              /**
               * 扩展字段值类型
               */
              fieldType?: string;
              /**
               * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
               */
              filedKey?:
                | 'CONTEXT_LENGTH'
                | 'REQUEST_BATCH_SIZE'
                | 'MAX_NEW_TOKENS'
                | 'FOLLOW_DATA_SUB_SET'
                | 'TEMPERATURE'
                | 'REPETITION_PENALTY'
                | 'TOP_K'
                | 'TOP_P'
                | 'PROMPT_PREFIX'
                | 'PROMPT_SUFFIX'
                | 'SYSTEM_PROMPT'
                | 'STOP_SEQUENCES'
                | 'MODEL_SCORE_EXTRACT_PREFIX'
                | 'MODEL_SCORE_EXTRACT_SUFFIX'
                | 'AUTO_EVAL_DATA_SIZE'
                | 'CALCULATE_TOKEN'
                | 'CALCULATE_TOKEN_PARAM'
                | 'AUTO_EVAL'
                | 'AUTO_EVAL_PARAM'
                | 'TRAIN_TASK_ID'
                | 'MODEL_CONFIG_DIRECTORY'
                | 'COMPARISON_MODEL_META_ID'
                | 'COMPARISON_BENCHMARK_ID'
                | 'TP'
                | 'PP'
                | 'AUTO_TOKEN'
                | 'TOKEN'
                | 'FLOPS';
              /**
               * 扩展字段key
               */
              filedName?: string;
              /**
               * 扩展字段值
               */
              filedValue?: string;
              /**
               * 是否系统创建
               */
              systemCreate?: boolean;
              [k: string]: unknown;
            }[];
            /**
             * 模型训练每秒浮点计算次数
             */
            flops?: string;
            /**
             * 模型checkpoint
             */
            modelCheckpoint: string;
            /**
             * 模型格式
             */
            modelFormat: string;
            /**
             * 模型id
             */
            modelId: number;
            /**
             * 模型meta信息id
             */
            modelMetaId?: number;
            /**
             * 模型名
             */
            modelName?: string;
            /**
             * 模型地址
             */
            modelPath: string;
            /**
             * 模型别名
             */
            modelShortName?: string;
            /**
             * 产出阶段
             */
            modelStage?: string;
            /**
             * 模型状态
             */
            modelStatus: boolean;
            /**
             * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
             */
            modelType: number;
            /**
             * 模型mp
             */
            pp?: number;
            /**
             * 注册渠道
             */
            registerChannel: string;
            /**
             * 模型step
             */
            step?: string;
            /**
             * 模型标签
             */
            tagValues?: {
              /**
               * 唯一标识, 前后端交互用
               */
              identification: string;
              /**
               * 字段值
               */
              values?: {
                /**
                 * 可选值展示名
                 */
                label?: string;
                /**
                 * 可选值
                 */
                name?: string;
                [k: string]: unknown;
              }[];
              [k: string]: unknown;
            }[];
            /**
             * 模型训练使用token数
             */
            token?: string;
            /**
             * 模型tp
             */
            tp?: number;
            [k: string]: unknown;
          };
          /**
           * 是否收藏
           */
          collected?: boolean;
          /**
           * 模型支持的上下文窗口大小
           */
          contextLength?: number;
          /**
           * 创建时间
           */
          cts?: string;
          /**
           * 模型描述
           */
          description?: string;
          /**
           * 扩展词表地址
           */
          extTokenizerPath?: string;
          /**
           * 模型meta扩展环境变量
           */
          extendEnvParams?: {
            /**
             * maxNewTokens
             */
            maxNewTokens?: number;
            /**
             * modelScoreExtractPrefix
             */
            modelScoreExtractPrefix?: string;
            /**
             * modelScoreExtractSuffix
             */
            modelScoreExtractSuffix?: string;
            /**
             * requestBatchSize
             */
            requestBatchSize?: number;
            /**
             * systemPrompt
             */
            systemPrompt?: string;
            [k: string]: unknown;
          };
          /**
           * 模型meta扩展信息
           */
          extraInfo?: {
            /**
             * batchSizeConfig
             */
            batchSizeConfigs?: string;
            /**
             * flopsPerToken
             */
            flopsPerToken?: string;
            /**
             * global_batch_size
             */
            globalBatchSize?: string;
            /**
             * rampup_batch_size样本数
             */
            rampUpBatchSizeSampleNum?: string;
            /**
             * rampup_batch_size起始值
             */
            rampUpBatchSizeStart?: string;
            /**
             * rampup_batch_size步长
             */
            rampUpBatchSizeStep?: string;
            /**
             * seq_length
             */
            seqLength?: string;
            [k: string]: unknown;
          };
          /**
           * 扩展字段集合
           */
          extraInfoFields?: {
            /**
             * 扩展字段值类型
             */
            fieldType?: string;
            /**
             * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
             */
            filedKey?:
              | 'CONTEXT_LENGTH'
              | 'REQUEST_BATCH_SIZE'
              | 'MAX_NEW_TOKENS'
              | 'FOLLOW_DATA_SUB_SET'
              | 'TEMPERATURE'
              | 'REPETITION_PENALTY'
              | 'TOP_K'
              | 'TOP_P'
              | 'PROMPT_PREFIX'
              | 'PROMPT_SUFFIX'
              | 'SYSTEM_PROMPT'
              | 'STOP_SEQUENCES'
              | 'MODEL_SCORE_EXTRACT_PREFIX'
              | 'MODEL_SCORE_EXTRACT_SUFFIX'
              | 'AUTO_EVAL_DATA_SIZE'
              | 'CALCULATE_TOKEN'
              | 'CALCULATE_TOKEN_PARAM'
              | 'AUTO_EVAL'
              | 'AUTO_EVAL_PARAM'
              | 'TRAIN_TASK_ID'
              | 'MODEL_CONFIG_DIRECTORY'
              | 'COMPARISON_MODEL_META_ID'
              | 'COMPARISON_BENCHMARK_ID'
              | 'TP'
              | 'PP'
              | 'AUTO_TOKEN'
              | 'TOKEN'
              | 'FLOPS';
            /**
             * 扩展字段key
             */
            filedName?: string;
            /**
             * 扩展字段值
             */
            filedValue?: string;
            /**
             * 是否系统创建
             */
            systemCreate?: boolean;
            [k: string]: unknown;
          }[];
          /**
           * 模型meta信息id
           */
          metaId?: number;
          /**
           * 模型Checkpoint
           */
          modelCheckpoint?: string;
          /**
           * 模型适用领域
           */
          modelDomain?: string;
          /**
           * 模型家族
           */
          modelFamily: string;
          /**
           * 模型id
           */
          modelId?: number;
          /**
           * 模型名称
           */
          modelName?: string;
          /**
           * 具体模型数量
           */
          modelNum?: number;
          /**
           * 模型系列
           */
          modelSeries?: string;
          /**
           * 模型展示名
           */
          modelShortName?: string;
          /**
           * 模型尺寸
           */
          modelSize?: string;
          /**
           * 产出阶段
           */
          modelStage: string;
          /**
           * 模型目标语言
           */
          modelTargetLanguage?: string;
          /**
           * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
           */
          modelType: number;
          /**
           * 自定义模型名称
           */
          modelUserSettings?: string;
          /**
           * 发布原始模型的机构名
           */
          orgName?: string;
          overwriteExtraInfoFields?: boolean;
          /**
           * 负责人列表
           */
          ownerList: string[];
          /**
           * 插件绑定列表
           */
          pluginBindList?: {
            /**
             * 锚点完整路径
             */
            anchorPath?: string;
            /**
             * 绑定类型
             */
            bindType?: string;
            /**
             * 上下文参数
             */
            contextParams?: {
              /**
               * 配置
               */
              config?: string;
              /**
               * 说明
               */
              description?: string;
              /**
               * 全局绑定参数名
               */
              globalAliasName?: string;
              /**
               * 全局绑定参数类型
               */
              globalAliasType?: string;
              /**
               * 展示名
               */
              label?: string;
              /**
               * 参数名
               */
              name?: string;
              /**
               * 提交用，覆盖子Benchmark配置
               */
              overrideFlag?: string;
              /**
               * 来源
               */
              source?: string;
              /**
               * 类型
               */
              type?: string;
              /**
               * 默认值
               */
              value?: string;
              [k: string]: unknown;
            }[];
            /**
             * 额外信息
             */
            extra?: {
              [k: string]: {
                [k: string]: unknown;
              };
            };
            /**
             * 插件顺序
             */
            orderIndex?: number;
            /**
             * 插件实现类名
             */
            pluginImplement?: string;
            /**
             * 插件展示实现名
             */
            pluginImplementLabel?: string;
            /**
             * 插件接口名
             */
            pluginInterface?: string;
            /**
             * 来源
             */
            source?: string;
            /**
             * 关联节点定义ID
             */
            vertexDefineId?: number;
            [k: string]: unknown;
          }[];
          /**
           * prompt前缀
           */
          promptPrefix?: string;
          /**
           * prompt后缀
           */
          promptSuffix?: string;
          /**
           * 授权可以使用私有模型的用户组
           */
          roleList?: string[];
          /**
           * 推理终止序列
           */
          stopSequences?: string;
          /**
           * 标签信息
           */
          tagValues?: {
            /**
             * 唯一标识, 前后端交互用
             */
            identification: string;
            /**
             * 字段值
             */
            values?: {
              /**
               * 可选值展示名
               */
              label?: string;
              /**
               * 可选值
               */
              name?: string;
              [k: string]: unknown;
            }[];
            [k: string]: unknown;
          }[];
          /**
           * 训练数据版本
           */
          trainDataVersion?: string;
          /**
           * 唯一标识
           */
          uniqueIdentification?: string;
          /**
           * 授权可以使用私有模型的用户
           */
          userList?: string[];
          /**
           * 修改时间
           */
          uts?: string;
          [k: string]: unknown;
        };
        /**
         * 模型metaId
         */
        modelMetaId?: number;
        /**
         * 模型名
         */
        modelName?: string;
        /**
         * 模型评估指标
         */
        modelStatList?: {
          evalDataSize?: string;
          family?: string;
          groupValue?: {
            confidenceInterval?: string;
            hasStatSubSetCount?: number;
            macroMean?: string;
            mean?: string;
            result?: {
              binNames?: string[];
              binResultList?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              }[];
              categoryId?: number;
              children?: {
                binNames?: string[];
                children?: unknown[];
                confidenceIntervalName?: string;
                itemDisplayType?: string;
                label?: string;
                name?: string;
                objectiveBaseValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveEvalValueConfig?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                };
                objectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  modelFamily?: string;
                  modelMetaIdForMaster?: number;
                  modelName?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                relationTagNameList?: string[];
                secondStatName?: string;
                showCapabilityReportFlag?: boolean;
                subjectiveValueConfigList?: {
                  dataSubSetTags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  evalDataSize?: string;
                  statName?: string;
                  [k: string]: unknown;
                }[];
                winRateFlag?: boolean;
                [k: string]: unknown;
              }[];
              confidenceInterval?: string;
              confidenceIntervalName?: string;
              confidenceResult?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              };
              dataSetId?: number;
              dataSubSetId?: number;
              hasStatSubSetCount?: number;
              itemDisplayType?: string;
              label?: string;
              name?: string;
              objectiveBaseValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveEvalValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              relationTagNameList?: string[];
              result?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                name?: string;
                notCalculateDataSubSetList?: {
                  autoEvalDataSizeOrder?: string;
                  autoEvalDataSizeOrderList?: string[];
                  bindingRunSpecList?: {
                    cts?: string;
                    dataSetLabel?: string;
                    dataSetName?: string;
                    dataSubSetId?: number;
                    dataSubSetLabel?: string;
                    dataSubSetName?: string;
                    dataSubSetPublicStatus?: string;
                    description?: string;
                    extraParam?: string;
                    id?: number;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  categoryList?: unknown[];
                  cts?: string;
                  dataSet?: {
                    cts?: string;
                    description?: string;
                    execType?: string;
                    id?: number;
                    label?: string;
                    metaVersionId?: number;
                    name?: string;
                    ownerList?: string[];
                    runtimeConfig?: string;
                    status?: string;
                    subSetCount?: number;
                    uts?: string;
                    [k: string]: unknown;
                  };
                  dataSetLabel?: string;
                  dataSetName?: string;
                  description?: string;
                  evalType?: string;
                  files?: {
                    cts?: string;
                    dataKey?: string;
                    detail?: {
                      bucket?: string;
                      dataSetName?: string;
                      dataSetVersionName?: string;
                      errorMsg?: string;
                      index?: string;
                      labelType?: number;
                      path?: string;
                      [k: string]: unknown;
                    };
                    detailJson?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    storageType?: string;
                    uploadTs?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  id?: number;
                  instanceCount?: number;
                  instanceDetail?: {
                    instanceList?: {
                      list?: {
                        categoryList?: string;
                        correctAnswer?: string;
                        cts?: string;
                        id?: number;
                        instanceId?: string;
                        modelEvalDataSubSetId?: number;
                        modelPrompt?: string;
                        originInput?: string;
                        uts?: string;
                        [k: string]: unknown;
                      }[];
                      totalCount?: number;
                      [k: string]: unknown;
                    };
                    instanceOriginContent?: string;
                    [k: string]: unknown;
                  };
                  label?: string;
                  metaVersionId?: number;
                  modelEvalDataSetId?: number;
                  name?: string;
                  publicStatus?: string;
                  statType?: string;
                  stats?: {
                    cts?: string;
                    id?: number;
                    modelEvalDataSubSetId?: number;
                    name?: string;
                    uts?: string;
                    [k: string]: unknown;
                  }[];
                  tags?: {
                    name?: string;
                    values?: string[];
                    [k: string]: unknown;
                  }[];
                  uts?: string;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                value?: number;
                [k: string]: unknown;
              };
              secondStatName?: string;
              secondValue?: string;
              showCapabilityReportFlag?: boolean;
              subjectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              totalSubSetCount?: number;
              type?: string;
              value?: string;
              winRateFlag?: boolean;
              [k: string]: unknown;
            };
            secondValue?: string;
            totalSubSetCount?: number;
            [k: string]: unknown;
          };
          label?: string;
          llmModel?: {
            authType?: string;
            autoEvalDataSize?: string;
            benchmarking?: string;
            cts?: string;
            family?: string;
            id?: number;
            label?: string;
            modelId?: number;
            modelMetaId?: number;
            modelPath?: string;
            name?: string;
            nlpOpenRunSpecSetId?: number;
            ownerList?: string[];
            simpleTags?: string[];
            status?: string;
            tags?: {
              name?: string;
              values?: string[];
              [k: string]: unknown;
            }[];
            uts?: string;
            viewerMisList?: string[];
            viewerRoleList?: string[];
            [k: string]: unknown;
          };
          /**
           * 模型信息
           */
          modelInfoVo?: {
            /**
             * 是否为标杆模型
             */
            benchmarking?: boolean;
            /**
             * 模型创建人
             */
            creator: string;
            /**
             * 创建时间
             */
            cts: string;
            /**
             * 实验任务详情id
             */
            experimentTaskDetailId?: number;
            /**
             * 扩展词表地址
             */
            extTokenizerPath?: string;
            /**
             * 扩展字段集合
             */
            extraInfoFields?: {
              /**
               * 扩展字段值类型
               */
              fieldType?: string;
              /**
               * 扩展字段展示名(枚举值, 可选如下(值-展示名): CONTEXT_LENGTH-上下文窗口大小; REQUEST_BATCH_SIZE-请求实际BatchSize; MAX_NEW_TOKENS-max_new_tokens; FOLLOW_DATA_SUB_SET-遵循数据子集配置; TEMPERATURE-Temperature; REPETITION_PENALTY-重复惩罚系数; TOP_K-Top_K; TOP_P-Top_P; PROMPT_PREFIX-Prompt前缀; PROMPT_SUFFIX-Prompt后缀; SYSTEM_PROMPT-系统Prompt; STOP_SEQUENCES-推理终止序列; MODEL_SCORE_EXTRACT_PREFIX-模型打分推理结果抽取前缀; MODEL_SCORE_EXTRACT_SUFFIX-模型打分推理结果抽取后缀; AUTO_EVAL_DATA_SIZE-评测集auto规模; CALCULATE_TOKEN-是否计算token; CALCULATE_TOKEN_PARAM-计算token参数; AUTO_EVAL-是否开启自动评测; AUTO_EVAL_PARAM-自动评测参数; TRAIN_TASK_ID-训练任务id; MODEL_CONFIG_DIRECTORY-模型配置目录; COMPARISON_MODEL_META_ID-对比模型; COMPARISON_BENCHMARK_ID-对比评测数据集; TP-tp; PP-pp; AUTO_TOKEN-是否为自动上报; TOKEN-token; FLOPS-flops)
               */
              filedKey?:
                | 'CONTEXT_LENGTH'
                | 'REQUEST_BATCH_SIZE'
                | 'MAX_NEW_TOKENS'
                | 'FOLLOW_DATA_SUB_SET'
                | 'TEMPERATURE'
                | 'REPETITION_PENALTY'
                | 'TOP_K'
                | 'TOP_P'
                | 'PROMPT_PREFIX'
                | 'PROMPT_SUFFIX'
                | 'SYSTEM_PROMPT'
                | 'STOP_SEQUENCES'
                | 'MODEL_SCORE_EXTRACT_PREFIX'
                | 'MODEL_SCORE_EXTRACT_SUFFIX'
                | 'AUTO_EVAL_DATA_SIZE'
                | 'CALCULATE_TOKEN'
                | 'CALCULATE_TOKEN_PARAM'
                | 'AUTO_EVAL'
                | 'AUTO_EVAL_PARAM'
                | 'TRAIN_TASK_ID'
                | 'MODEL_CONFIG_DIRECTORY'
                | 'COMPARISON_MODEL_META_ID'
                | 'COMPARISON_BENCHMARK_ID'
                | 'TP'
                | 'PP'
                | 'AUTO_TOKEN'
                | 'TOKEN'
                | 'FLOPS';
              /**
               * 扩展字段key
               */
              filedName?: string;
              /**
               * 扩展字段值
               */
              filedValue?: string;
              /**
               * 是否系统创建
               */
              systemCreate?: boolean;
              [k: string]: unknown;
            }[];
            /**
             * 模型训练每秒浮点计算次数
             */
            flops?: string;
            /**
             * 模型checkpoint
             */
            modelCheckpoint: string;
            /**
             * 模型格式
             */
            modelFormat: string;
            /**
             * 模型id
             */
            modelId: number;
            /**
             * 模型meta信息id
             */
            modelMetaId?: number;
            /**
             * 模型名
             */
            modelName?: string;
            /**
             * 模型地址
             */
            modelPath: string;
            /**
             * 模型别名
             */
            modelShortName?: string;
            /**
             * 产出阶段
             */
            modelStage?: string;
            /**
             * 模型状态
             */
            modelStatus: boolean;
            /**
             * 模型类型(枚举值, 可选如下(值-展示名): 0-本地模型; 1-Friday模型; 2-API模型)
             */
            modelType: number;
            /**
             * 模型mp
             */
            pp?: number;
            /**
             * 注册渠道
             */
            registerChannel: string;
            /**
             * 模型step
             */
            step?: string;
            /**
             * 模型标签
             */
            tagValues?: {
              /**
               * 唯一标识, 前后端交互用
               */
              identification: string;
              /**
               * 字段值
               */
              values?: {
                /**
                 * 可选值展示名
                 */
                label?: string;
                /**
                 * 可选值
                 */
                name?: string;
                [k: string]: unknown;
              }[];
              [k: string]: unknown;
            }[];
            /**
             * 模型训练使用token数
             */
            token?: string;
            /**
             * 模型tp
             */
            tp?: number;
            [k: string]: unknown;
          };
          name?: string;
          nonversionValueList?: {
            categoryId?: number;
            dataSubSetId?: number;
            description?: string;
            label?: string;
            name?: string;
            publicStatus?: string;
            unitList?: {
              confidenceInterval?: string;
              dataSetOpennessStatus?: string;
              hasStatSubSetCount?: number;
              objectivityIntegrationMean?: string;
              result?: {
                calculateUnitList?: {
                  aggrateType?: string;
                  autoEvalDataSizeList?: string[];
                  commonSubSetEvalInstanceCount?: number;
                  complete?: boolean;
                  confidenceInterval?: number;
                  configTotalInstanceCount?: number;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  evalDataSize?: string;
                  evalKeyCount?: number;
                  instanceCount?: number;
                  instanceStatCalculateUnitList?: {
                    evalKey?: string;
                    instanceId?: string;
                    statValue?: number;
                    [k: string]: unknown;
                  }[];
                  invalidReason?: string;
                  modelFamily?: string;
                  modelName?: string;
                  runSpecSetId?: number;
                  statType?: string;
                  statValue?: number;
                  subSetId?: number;
                  textLenOutputCount?: number;
                  tiktokenOutputCount?: number;
                  tokenInstanceCount?: number;
                  tokenOutputCount?: number;
                  totalInstanceCount?: number;
                  valueIsValid?: boolean;
                  weight?: number;
                  [k: string]: unknown;
                }[];
                categoryIds?: number[];
                dataSubSetIds?: number[];
                dataSubSetWeightSum?: number;
                evalDataSize?: string;
                family?: string;
                hasStatDataSubSetIds?: number[];
                instanceCountRatio?: number;
                instanceIncompleteList?: {
                  dataSetName?: string;
                  id?: number;
                  invalidReason?: string;
                  name?: string;
                  [k: string]: unknown;
                }[];
                name?: string;
                notCalculateDataSubSetList?: {
                  dataSetName?: string;
                  id?: number;
                  invalidReason?: string;
                  name?: string;
                  [k: string]: unknown;
                }[];
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                value?: number;
                [k: string]: unknown;
              };
              secondValue?: string;
              subjectivityIsFromModelEval?: boolean;
              textLenOutputAvg?: number;
              tikTokenOutputAvg?: number;
              tokenOutputAvg?: number;
              totalSubSetCount?: number;
              value?: string;
              [k: string]: unknown;
            }[];
            [k: string]: unknown;
          }[];
          searchEvalDataSize?: string;
          valueList?: {
            binNames?: string[];
            binResultList?: {
              calculateUnitList?: {
                aggrateType?: string;
                autoEvalDataSizeList?: string[];
                commonSubSetEvalInstanceCount?: number;
                complete?: boolean;
                confidenceInterval?: number;
                configTotalInstanceCount?: number;
                dataSetLabel?: string;
                dataSetName?: string;
                dataSubSetLabel?: string;
                dataSubSetName?: string;
                evalDataSize?: string;
                evalKeyCount?: number;
                instanceCount?: number;
                instanceStatCalculateUnitList?: {
                  evalKey?: string;
                  instanceId?: string;
                  statValue?: number;
                  [k: string]: unknown;
                }[];
                invalidReason?: string;
                modelFamily?: string;
                modelName?: string;
                runSpecSetId?: number;
                statType?: string;
                statValue?: number;
                subSetId?: number;
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                totalInstanceCount?: number;
                valueIsValid?: boolean;
                weight?: number;
                [k: string]: unknown;
              }[];
              categoryIds?: number[];
              dataSubSetIds?: number[];
              dataSubSetWeightSum?: number;
              evalDataSize?: string;
              family?: string;
              hasStatDataSubSetIds?: number[];
              name?: string;
              notCalculateDataSubSetList?: {
                autoEvalDataSizeOrder?: string;
                autoEvalDataSizeOrderList?: string[];
                bindingRunSpecList?: {
                  cts?: string;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetId?: number;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  dataSubSetPublicStatus?: string;
                  description?: string;
                  extraParam?: string;
                  id?: number;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                categoryList?: unknown[];
                cts?: string;
                dataSet?: {
                  cts?: string;
                  description?: string;
                  execType?: string;
                  id?: number;
                  label?: string;
                  metaVersionId?: number;
                  name?: string;
                  ownerList?: string[];
                  runtimeConfig?: string;
                  status?: string;
                  subSetCount?: number;
                  uts?: string;
                  [k: string]: unknown;
                };
                dataSetLabel?: string;
                dataSetName?: string;
                description?: string;
                evalType?: string;
                files?: {
                  cts?: string;
                  dataKey?: string;
                  detail?: {
                    bucket?: string;
                    dataSetName?: string;
                    dataSetVersionName?: string;
                    errorMsg?: string;
                    index?: string;
                    labelType?: number;
                    path?: string;
                    [k: string]: unknown;
                  };
                  detailJson?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  storageType?: string;
                  uploadTs?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                id?: number;
                instanceCount?: number;
                instanceDetail?: {
                  instanceList?: {
                    list?: {
                      categoryList?: string;
                      correctAnswer?: string;
                      cts?: string;
                      id?: number;
                      instanceId?: string;
                      modelEvalDataSubSetId?: number;
                      modelPrompt?: string;
                      originInput?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    totalCount?: number;
                    [k: string]: unknown;
                  };
                  instanceOriginContent?: string;
                  [k: string]: unknown;
                };
                label?: string;
                metaVersionId?: number;
                modelEvalDataSetId?: number;
                name?: string;
                publicStatus?: string;
                statType?: string;
                stats?: {
                  cts?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                tags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                uts?: string;
                weight?: number;
                [k: string]: unknown;
              }[];
              value?: number;
              [k: string]: unknown;
            }[];
            categoryId?: number;
            children?: {
              binNames?: string[];
              children?: unknown[];
              confidenceIntervalName?: string;
              itemDisplayType?: string;
              label?: string;
              name?: string;
              objectiveBaseValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveEvalValueConfig?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              };
              objectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                modelFamily?: string;
                modelMetaIdForMaster?: number;
                modelName?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              relationTagNameList?: string[];
              secondStatName?: string;
              showCapabilityReportFlag?: boolean;
              subjectiveValueConfigList?: {
                dataSubSetTags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                evalDataSize?: string;
                statName?: string;
                [k: string]: unknown;
              }[];
              winRateFlag?: boolean;
              [k: string]: unknown;
            }[];
            confidenceInterval?: string;
            confidenceIntervalName?: string;
            confidenceResult?: {
              calculateUnitList?: {
                aggrateType?: string;
                autoEvalDataSizeList?: string[];
                commonSubSetEvalInstanceCount?: number;
                complete?: boolean;
                confidenceInterval?: number;
                configTotalInstanceCount?: number;
                dataSetLabel?: string;
                dataSetName?: string;
                dataSubSetLabel?: string;
                dataSubSetName?: string;
                evalDataSize?: string;
                evalKeyCount?: number;
                instanceCount?: number;
                instanceStatCalculateUnitList?: {
                  evalKey?: string;
                  instanceId?: string;
                  statValue?: number;
                  [k: string]: unknown;
                }[];
                invalidReason?: string;
                modelFamily?: string;
                modelName?: string;
                runSpecSetId?: number;
                statType?: string;
                statValue?: number;
                subSetId?: number;
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                totalInstanceCount?: number;
                valueIsValid?: boolean;
                weight?: number;
                [k: string]: unknown;
              }[];
              categoryIds?: number[];
              dataSubSetIds?: number[];
              dataSubSetWeightSum?: number;
              evalDataSize?: string;
              family?: string;
              hasStatDataSubSetIds?: number[];
              name?: string;
              notCalculateDataSubSetList?: {
                autoEvalDataSizeOrder?: string;
                autoEvalDataSizeOrderList?: string[];
                bindingRunSpecList?: {
                  cts?: string;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetId?: number;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  dataSubSetPublicStatus?: string;
                  description?: string;
                  extraParam?: string;
                  id?: number;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                categoryList?: unknown[];
                cts?: string;
                dataSet?: {
                  cts?: string;
                  description?: string;
                  execType?: string;
                  id?: number;
                  label?: string;
                  metaVersionId?: number;
                  name?: string;
                  ownerList?: string[];
                  runtimeConfig?: string;
                  status?: string;
                  subSetCount?: number;
                  uts?: string;
                  [k: string]: unknown;
                };
                dataSetLabel?: string;
                dataSetName?: string;
                description?: string;
                evalType?: string;
                files?: {
                  cts?: string;
                  dataKey?: string;
                  detail?: {
                    bucket?: string;
                    dataSetName?: string;
                    dataSetVersionName?: string;
                    errorMsg?: string;
                    index?: string;
                    labelType?: number;
                    path?: string;
                    [k: string]: unknown;
                  };
                  detailJson?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  storageType?: string;
                  uploadTs?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                id?: number;
                instanceCount?: number;
                instanceDetail?: {
                  instanceList?: {
                    list?: {
                      categoryList?: string;
                      correctAnswer?: string;
                      cts?: string;
                      id?: number;
                      instanceId?: string;
                      modelEvalDataSubSetId?: number;
                      modelPrompt?: string;
                      originInput?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    totalCount?: number;
                    [k: string]: unknown;
                  };
                  instanceOriginContent?: string;
                  [k: string]: unknown;
                };
                label?: string;
                metaVersionId?: number;
                modelEvalDataSetId?: number;
                name?: string;
                publicStatus?: string;
                statType?: string;
                stats?: {
                  cts?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                tags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                uts?: string;
                weight?: number;
                [k: string]: unknown;
              }[];
              value?: number;
              [k: string]: unknown;
            };
            dataSetId?: number;
            dataSubSetId?: number;
            hasStatSubSetCount?: number;
            itemDisplayType?: string;
            label?: string;
            name?: string;
            objectiveBaseValueConfig?: {
              dataSubSetTags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              evalDataSize?: string;
              modelFamily?: string;
              modelMetaIdForMaster?: number;
              modelName?: string;
              statName?: string;
              [k: string]: unknown;
            };
            objectiveEvalValueConfig?: {
              dataSubSetTags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              evalDataSize?: string;
              modelFamily?: string;
              modelMetaIdForMaster?: number;
              modelName?: string;
              statName?: string;
              [k: string]: unknown;
            };
            objectiveValueConfigList?: {
              dataSubSetTags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              evalDataSize?: string;
              modelFamily?: string;
              modelMetaIdForMaster?: number;
              modelName?: string;
              statName?: string;
              [k: string]: unknown;
            }[];
            relationTagNameList?: string[];
            result?: {
              calculateUnitList?: {
                aggrateType?: string;
                autoEvalDataSizeList?: string[];
                commonSubSetEvalInstanceCount?: number;
                complete?: boolean;
                confidenceInterval?: number;
                configTotalInstanceCount?: number;
                dataSetLabel?: string;
                dataSetName?: string;
                dataSubSetLabel?: string;
                dataSubSetName?: string;
                evalDataSize?: string;
                evalKeyCount?: number;
                instanceCount?: number;
                instanceStatCalculateUnitList?: {
                  evalKey?: string;
                  instanceId?: string;
                  statValue?: number;
                  [k: string]: unknown;
                }[];
                invalidReason?: string;
                modelFamily?: string;
                modelName?: string;
                runSpecSetId?: number;
                statType?: string;
                statValue?: number;
                subSetId?: number;
                textLenOutputCount?: number;
                tiktokenOutputCount?: number;
                tokenInstanceCount?: number;
                tokenOutputCount?: number;
                totalInstanceCount?: number;
                valueIsValid?: boolean;
                weight?: number;
                [k: string]: unknown;
              }[];
              categoryIds?: number[];
              dataSubSetIds?: number[];
              dataSubSetWeightSum?: number;
              evalDataSize?: string;
              family?: string;
              hasStatDataSubSetIds?: number[];
              name?: string;
              notCalculateDataSubSetList?: {
                autoEvalDataSizeOrder?: string;
                autoEvalDataSizeOrderList?: string[];
                bindingRunSpecList?: {
                  cts?: string;
                  dataSetLabel?: string;
                  dataSetName?: string;
                  dataSubSetId?: number;
                  dataSubSetLabel?: string;
                  dataSubSetName?: string;
                  dataSubSetPublicStatus?: string;
                  description?: string;
                  extraParam?: string;
                  id?: number;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                categoryList?: unknown[];
                cts?: string;
                dataSet?: {
                  cts?: string;
                  description?: string;
                  execType?: string;
                  id?: number;
                  label?: string;
                  metaVersionId?: number;
                  name?: string;
                  ownerList?: string[];
                  runtimeConfig?: string;
                  status?: string;
                  subSetCount?: number;
                  uts?: string;
                  [k: string]: unknown;
                };
                dataSetLabel?: string;
                dataSetName?: string;
                description?: string;
                evalType?: string;
                files?: {
                  cts?: string;
                  dataKey?: string;
                  detail?: {
                    bucket?: string;
                    dataSetName?: string;
                    dataSetVersionName?: string;
                    errorMsg?: string;
                    index?: string;
                    labelType?: number;
                    path?: string;
                    [k: string]: unknown;
                  };
                  detailJson?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  storageType?: string;
                  uploadTs?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                id?: number;
                instanceCount?: number;
                instanceDetail?: {
                  instanceList?: {
                    list?: {
                      categoryList?: string;
                      correctAnswer?: string;
                      cts?: string;
                      id?: number;
                      instanceId?: string;
                      modelEvalDataSubSetId?: number;
                      modelPrompt?: string;
                      originInput?: string;
                      uts?: string;
                      [k: string]: unknown;
                    }[];
                    totalCount?: number;
                    [k: string]: unknown;
                  };
                  instanceOriginContent?: string;
                  [k: string]: unknown;
                };
                label?: string;
                metaVersionId?: number;
                modelEvalDataSetId?: number;
                name?: string;
                publicStatus?: string;
                statType?: string;
                stats?: {
                  cts?: string;
                  id?: number;
                  modelEvalDataSubSetId?: number;
                  name?: string;
                  uts?: string;
                  [k: string]: unknown;
                }[];
                tags?: {
                  name?: string;
                  values?: string[];
                  [k: string]: unknown;
                }[];
                uts?: string;
                weight?: number;
                [k: string]: unknown;
              }[];
              value?: number;
              [k: string]: unknown;
            };
            secondStatName?: string;
            secondValue?: string;
            showCapabilityReportFlag?: boolean;
            subjectiveValueConfigList?: {
              dataSubSetTags?: {
                name?: string;
                values?: string[];
                [k: string]: unknown;
              }[];
              evalDataSize?: string;
              statName?: string;
              [k: string]: unknown;
            }[];
            totalSubSetCount?: number;
            type?: string;
            value?: string;
            winRateFlag?: boolean;
            [k: string]: unknown;
          }[];
          winRateValue?: {
            comprehensiveValue?: string;
            flag?: boolean;
            objectiveValue?: string;
            objectiveValueEfficient?: number;
            objectiveValueTotal?: number;
            subjectiveValue?: string;
            subjectiveValueEfficient?: number;
            subjectiveValueTotal?: number;
            [k: string]: unknown;
          };
          [k: string]: unknown;
        }[];
        [k: string]: unknown;
      }[];
      /**
       * 模型评估指标名称
       */
      metricsName?: string;
      [k: string]: unknown;
    }[];
    [k: string]: unknown;
  };
  exception?: string;
  /**
   * 响应消息
   */
  msg?: string;
  /**
   * 响应状态码
   */
  rescode: number;
  [k: string]: unknown;
}
