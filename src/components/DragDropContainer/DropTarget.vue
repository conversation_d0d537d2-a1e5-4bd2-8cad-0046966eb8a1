<script lang="tsx">
import { defineComponent } from 'vue';
import { DragDropContext } from './DragDropContextProvider';

const DropTarget = defineComponent({
  setup(_, { slots, emit }) {
    return {
      slots,
      emit,
    };
  },
  render(h, hack) {
    const context = DragDropContext.useContext();

    return (
      <div onClick={(e) => this.emit('click', e)} onDragstart={(e) => this.emit('onDragstart', e)}>
        {this.slots.default && this.slots.default()}
      </div>
    );
  },
}) as any;

export default DropTarget;
</script>
