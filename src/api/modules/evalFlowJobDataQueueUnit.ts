import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1426077, catid=246786, projectId=35802, created=2024-06-24 14:03:18, last-modified=2024-08-07 17:21:17
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJobDataQueueUnit/dispatch)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426077)
 */
export function postEvalFlowJobDataQueueUnitDispatch(
  params: model.evalFlowJobDataQueueUnit.IPostEvalFlowJobDataQueueUnitDispatchParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJobDataQueueUnit.IPostEvalFlowJobDataQueueUnitDispatchResponse>(
    '/webApi/evalFlowJobDataQueueUnit/dispatch',
    {
      data: pick(params, ['clientConsumerId', 'jobId', 'clusterName', 'batchSize']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1426100, catid=246786, projectId=35802, created=2024-06-24 14:25:41, last-modified=2024-07-03 10:51:17
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJobDataQueueUnit/ack)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426100)
 */
export function postEvalFlowJobDataQueueUnitAck(
  params: model.evalFlowJobDataQueueUnit.IPostEvalFlowJobDataQueueUnitAckParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJobDataQueueUnit.IPostEvalFlowJobDataQueueUnitAckResponse>('/webApi/evalFlowJobDataQueueUnit/ack', {
    data: pick(params, ['evalFlowJobDataQueueUnitList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
