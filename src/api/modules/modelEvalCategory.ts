import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1432925, catid=227034, projectId=35802, created=2024-07-05 17:04:19, last-modified=2024-07-05 17:05:17
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/batchUnbindDataSubSetNoVersioned)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1432925)
 */
export function postModelEvalCategoryBatchUnbindDataSubSetNoVersioned(
  params: model.modelEvalCategory.IPostModelEvalCategoryBatchUnbindDataSubSetNoVersionedParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryBatchUnbindDataSubSetNoVersionedResponse>(
    '/webApi/modelEvalCategory/batchUnbindDataSubSetNoVersioned',
    {
      data: pick(params, ['categoryId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505092, catid=227034, projectId=35802, created=2025-01-16 17:17:46, last-modified=2025-01-17 14:51:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/subList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505092)
 */
export function postModelEvalCategorySubList(
  params: model.modelEvalCategory.IPostModelEvalCategorySubListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategorySubListResponse>('/webApi/modelEvalCategory/subList', {
    data: pick(params, ['metaVersionId', 'parentName', 'type']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507091, catid=227034, projectId=35802, created=2025-02-25 15:21:42, last-modified=2025-03-03 16:49:30
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/getParentsList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507091)
 */
export function postModelEvalCategoryGetParentsList(
  params: model.modelEvalCategory.IPostModelEvalCategoryGetParentsListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryGetParentsListResponse>('/webApi/modelEvalCategory/getParentsList', {
    data: pick(params, ['metaVersionId', 'type', 'ids']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1416792, catid=227034, projectId=35802, created=2024-06-04 17:01:34, last-modified=2024-06-05 10:10:45
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/getDefaultMode)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1416792)
 */
export function postModelEvalCategoryGetDefaultMode(options?: FlowHttpRequestOptions) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryGetDefaultModeResponse>('/webApi/modelEvalCategory/getDefaultMode', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1435267, catid=227034, projectId=35802, created=2024-07-11 10:19:32, last-modified=2024-07-11 10:24:45
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/getTagValueDetailUnVersioned)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1435267)
 */
export function postModelEvalCategoryGetTagValueDetailUnVersioned(
  params: model.modelEvalCategory.IPostModelEvalCategoryGetTagValueDetailUnVersionedParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryGetTagValueDetailUnVersionedResponse>(
    '/webApi/modelEvalCategory/getTagValueDetailUnVersioned',
    {
      data: pick(params, ['metaVersionId', 'type', 'tag', 'subSetTags']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1392997, catid=227034, projectId=35802, created=2024-04-11 15:04:11, last-modified=2024-04-11 15:05:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/updateParentId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1392997)
 */
export function postModelEvalCategoryUpdateParentId(
  params: model.modelEvalCategory.IPostModelEvalCategoryUpdateParentIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryUpdateParentIdResponse>('/webApi/modelEvalCategory/updateParentId', {
    data: pick(params, ['id', 'parentId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1435503, catid=227034, projectId=35802, created=2024-07-11 15:10:10, last-modified=2024-07-11 15:54:16
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/batchStatCountUnVersioned)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1435503)
 */
export function postModelEvalCategoryBatchStatCountUnVersioned(
  params: model.modelEvalCategory.IPostModelEvalCategoryBatchStatCountUnVersionedParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryBatchStatCountUnVersionedResponse>(
    '/webApi/modelEvalCategory/batchStatCountUnVersioned',
    {
      data: pick(params, ['nodes', 'subSetTags', 'metaVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1488473, catid=227034, projectId=35802, created=2024-11-07 14:19:34, last-modified=2024-11-08 11:05:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/upload/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1488473)
 */
export function postModelEvalCategoryUploadUpsert(
  params: model.modelEvalCategory.IPostModelEvalCategoryUploadUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryUploadUpsertResponse>('/webApi/modelEvalCategory/upload/upsert', {
    data: pick(params, ['parentId', 'file', 'metaVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296952, catid=227034, projectId=35802, created=2023-10-13 19:28:03, last-modified=2025-01-16 17:21:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296952)
 */
export function postModelEvalCategoryList(
  params: model.modelEvalCategory.IPostModelEvalCategoryListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryListResponse>('/webApi/modelEvalCategory/list', {
    data: pick(params, ['metaVersionId', 'type', 'limit', 'offset']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296964, catid=227034, projectId=35802, created=2023-10-13 19:28:03, last-modified=2024-06-04 17:19:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296964)
 */
export function postModelEvalCategoryUpsert(
  params: model.modelEvalCategory.IPostModelEvalCategoryUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryUpsertResponse>('/webApi/modelEvalCategory/upsert', {
    data: pick(params, ['metaVersionId', 'id', 'parentId', 'type', 'name', 'description', 'tags', 'associateId', 'categoryId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296974, catid=227034, projectId=35802, created=2023-10-13 19:28:04, last-modified=2023-11-21 14:38:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296974)
 */
export function postModelEvalCategoryDelete(
  params: model.modelEvalCategory.IPostModelEvalCategoryDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryDeleteResponse>('/webApi/modelEvalCategory/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296999, catid=227034, projectId=35802, created=2023-10-13 19:28:05, last-modified=2023-11-21 14:38:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/batchStatCount)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296999)
 */
export function postModelEvalCategoryBatchStatCount(
  params: model.modelEvalCategory.IPostModelEvalCategoryBatchStatCountParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryBatchStatCountResponse>('/webApi/modelEvalCategory/batchStatCount', {
    data: pick(params, ['metaVersionId', 'nodes', 'subSetTags', 'statusFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297027, catid=227034, projectId=35802, created=2023-10-13 19:28:05, last-modified=2023-11-21 14:38:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/listTagMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297027)
 */
export function postModelEvalCategoryListTagMeta(
  params: model.modelEvalCategory.IPostModelEvalCategoryListTagMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryListTagMetaResponse>('/webApi/modelEvalCategory/listTagMeta', {
    data: pick(params, ['metaVersionId', 'type']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297037, catid=227034, projectId=35802, created=2023-10-13 19:28:06, last-modified=2024-08-20 12:46:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297037)
 */
export function postModelEvalCategoryGet(
  params: model.modelEvalCategory.IPostModelEvalCategoryGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryGetResponse>('/webApi/modelEvalCategory/get', {
    data: pick(params, ['metaVersionId', 'id', 'subSetTags', 'statusFilter', 'keyword']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297042, catid=227034, projectId=35802, created=2023-10-13 19:28:06, last-modified=2023-11-21 14:38:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/getTagValueDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297042)
 */
export function postModelEvalCategoryGetTagValueDetail(
  params: model.modelEvalCategory.IPostModelEvalCategoryGetTagValueDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryGetTagValueDetailResponse>('/webApi/modelEvalCategory/getTagValueDetail', {
    data: pick(params, ['metaVersionId', 'type', 'tag', 'subSetTags', 'statusFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1363818, catid=227034, projectId=35802, created=2024-01-30 16:33:56, last-modified=2024-01-30 16:35:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCategory/batchUnbindDataSubSet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1363818)
 */
export function postModelEvalCategoryBatchUnbindDataSubSet(
  params: model.modelEvalCategory.IPostModelEvalCategoryBatchUnbindDataSubSetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCategory.IPostModelEvalCategoryBatchUnbindDataSubSetResponse>(
    '/webApi/modelEvalCategory/batchUnbindDataSubSet',
    {
      data: pick(params, ['categoryId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
