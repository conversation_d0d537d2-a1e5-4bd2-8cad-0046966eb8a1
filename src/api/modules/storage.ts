import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1505874, catid=256839, projectId=35802, created=2025-02-10 17:14:49, last-modified=2025-02-12 11:03:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/storage/getTree)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505874)
 */
export function getStorageGetTree(params: model.storage.IGetStorageGetTreeParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.storage.IGetStorageGetTreeResponse>('/webApi/storage/getTree', {
    params: pick(params, ['path']),
    ...options,
  });
}

/**
 * @desc id=1521664, catid=256839, projectId=35802, created=2025-05-30 10:10:21, last-modified=2025-05-30 10:10:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/storage/versions)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1521664)
 */
export function getStorageVersions(options?: FlowHttpRequestOptions) {
  return http.get<model.storage.IGetStorageVersionsResponse>('/webApi/storage/versions', {
    ...options,
  });
}

/**
 * @desc id=1505870, catid=256839, projectId=35802, created=2025-02-10 17:13:22, last-modified=2025-02-10 17:14:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/storage/getLastestVersion)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505870)
 */
export function getStorageGetLastestVersion(options?: FlowHttpRequestOptions) {
  return http.get<model.storage.IGetStorageGetLastestVersionResponse>('/webApi/storage/getLastestVersion', {
    ...options,
  });
}

/**
 * @desc id=1505880, catid=256839, projectId=35802, created=2025-02-10 17:19:24, last-modified=2025-02-11 16:17:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/storage/getStorageDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505880)
 */
export function getStorageGetStorageDetail(params: model.storage.IGetStorageGetStorageDetailParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.storage.IGetStorageGetStorageDetailResponse>('/webApi/storage/getStorageDetail', {
    params: pick(params, ['path']),
    ...options,
  });
}

/**
 * @desc id=1521667, catid=256839, projectId=35802, created=2025-05-30 10:11:33, last-modified=2025-05-30 11:13:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/storage/get_path)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1521667)
 */
export function postStorageGetPath(params: model.storage.IPostStorageGetPathParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.storage.IPostStorageGetPathResponse>('/webApi/storage/get_path', {
    data: pick(params, ['path', 'dt']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
