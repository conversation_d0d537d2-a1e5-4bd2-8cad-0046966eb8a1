import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1426812, catid=246786, projectId=35802, created=2024-06-25 16:20:06, last-modified=2024-07-02 16:52:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlow/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426812)
 */
export function postEvalFlowList(params: model.evalFlow.IPostEvalFlowListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlow.IPostEvalFlowListResponse>('/webApi/evalFlow/list', {
    data: pick(params, ['searchFilter', 'ownerFilter', 'categoryFilter', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426844, catid=246786, projectId=35802, created=2024-06-25 16:31:38, last-modified=2024-06-25 16:32:16
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlow/listCategory)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426844)
 */
export function postEvalFlowListCategory(params: model.evalFlow.IPostEvalFlowListCategoryParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlow.IPostEvalFlowListCategoryResponse>('/webApi/evalFlow/listCategory', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426908, catid=246786, projectId=35802, created=2024-06-25 16:52:50, last-modified=2024-06-25 17:00:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlow/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426908)
 */
export function postEvalFlowGet(params: model.evalFlow.IPostEvalFlowGetParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlow.IPostEvalFlowGetResponse>('/webApi/evalFlow/get', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426990, catid=246786, projectId=35802, created=2024-06-25 18:39:41, last-modified=2024-06-25 18:40:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlow/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426990)
 */
export function postEvalFlowUpsert(params: model.evalFlow.IPostEvalFlowUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlow.IPostEvalFlowUpsertResponse>('/webApi/evalFlow/upsert', {
    data: pick(params, ['id', 'name', 'category', 'ownerList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426852, catid=246786, projectId=35802, created=2024-06-25 16:35:34, last-modified=2024-06-25 16:38:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlow/clone)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426852)
 */
export function postEvalFlowClone(params: model.evalFlow.IPostEvalFlowCloneParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlow.IPostEvalFlowCloneResponse>('/webApi/evalFlow/clone', {
    data: pick(params, ['sourceId', 'sourceVersion', 'targetName', 'targetCategory']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426819, catid=246786, projectId=35802, created=2024-06-25 16:22:58, last-modified=2024-06-25 16:23:32
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlow/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426819)
 */
export function postEvalFlowDelete(params: model.evalFlow.IPostEvalFlowDeleteParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlow.IPostEvalFlowDeleteResponse>('/webApi/evalFlow/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
