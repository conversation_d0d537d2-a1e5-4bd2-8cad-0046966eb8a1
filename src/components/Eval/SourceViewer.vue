<template>
  <span class="sourceContainer" :style="`background-color:${evalSourceView[source]?.color || 'transparent'};`">
    {{ label || (source && evalSourceView[source]?.label) || '-' }}
    <template v-if="isCurrentSource">
      <span>&lt;当前&gt;</span>
    </template>
  </span>
</template>
<script lang="ts">
import { EvalSource, evalSourceView } from '@/utils';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class SourceViewer extends Vue {
  @Prop()
  source?: EvalSource;
  @Prop()
  isCurrentSource?: boolean;
  @Prop()
  label?: string;

  evalSourceView = evalSourceView;
}
</script>
<style lang="scss">
.sourceContainer {
  padding: 5px;
  font-weight: bold;
}
</style>
