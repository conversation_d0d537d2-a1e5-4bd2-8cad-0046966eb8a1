<template>
  <div>
    <div v-if="visible" class="help-button" :style="{ top: `${buttonTop}px` }" @click="handleClick" @mousedown="initVerticalDrag">
      <span class="help-text">
        <i class="mtdicon mtdicon-question-circle help-icon"></i>
        <span v-for="(char, index) in text" :key="index">{{ char }}</span>
      </span>
    </div>
    <mtd-drawer v-model="drawerVisible" :width="drawerWidth" @close="visible = true">
      <template #title>
        <div class="flex items-center">
          <div class="text-lg font-bold">帮助文档</div>
          <div class="text-[#2a8efe]">（<a :href="helpUrl" target="_blank">在学城中打开</a>）</div>
        </div>
      </template>
      <div class="h-full">
        <iframe width="100%" height="100%" :src="helpUrl"></iframe>
        <div v-if="isDragging" class="iframe-overlay"></div>
      </div>
      <div @mousedown="initDrag">
        <div class="drag-button" :style="{ right: `${drawerWidth}px` }">
          <i class="mtdicon mtdicon-menus-o"></i>
        </div>
      </div>
    </mtd-drawer>
  </div>
</template>

<script setup lang="ts">
import Horn from '@mtfe/horn-sdk';
import { computed, onMounted, onUnmounted, ref, watchEffect } from 'vue';
import { useRoute } from 'vue-router/composables';

const props = defineProps({
  text: {
    type: String,
    default: '帮助文档',
  },
  bgColor: {
    type: String,
    default: '#fff',
  },
  textColor: {
    type: String,
    default: '#333',
  },
  defaultWidth: {
    type: [Number, String],
    default: 900,
  },
  minWidth: {
    type: [Number, String],
    default: null,
  },
  maxWidth: {
    type: [Number, String],
    default: null,
  },
});
const route = useRoute();
const visible = ref(true);
const drawerVisible = ref(false);
const drawerWidth = ref(props.defaultWidth);
const isDragging = ref(false);
const startX = ref(0);
const startWidth = ref(0);
const helpUrl = ref('');

// 新增：垂直拖拽相关的响应式变量
const buttonTop = ref(getInitialButtonTop()); // 从 localStorage 获取初始位置
const isVerticalDragging = ref(false);
const startY = ref(0);
const startTop = ref(0);
const justFinishedDragging = ref(false); // 新增：标记是否刚完成拖拽
const hasMoved = ref(false); // 新增：标记是否有实际移动

// 获取初始按钮位置
function getInitialButtonTop() {
  const savedTop = localStorage.getItem('help-button-top');
  if (savedTop !== null) {
    const top = parseInt(savedTop);
    // 确保位置在有效范围内
    const buttonHeight = 90;
    const maxTop = window.innerHeight - buttonHeight;
    return Math.max(0, Math.min(maxTop, top));
  }
  // 默认位置：屏幕中央减去按钮高度的一半
  return window.innerHeight / 2 - 45;
}

const helpUrlWithKmCustomOption = computed(() => {
  return `${helpUrl.value}?km_custom_option=${JSON.stringify({})}`;
});

const handleClick = async (e) => {
  // 如果刚完成拖拽，不触发点击事件
  if (justFinishedDragging.value) {
    justFinishedDragging.value = false;
    return;
  }

  visible.value = false;
  drawerVisible.value = true;
};

// 新增：初始化垂直拖拽
const initVerticalDrag = (e) => {
  isVerticalDragging.value = true;
  justFinishedDragging.value = false; // 重置标记
  hasMoved.value = false; // 重置移动标记
  startY.value = e.clientY;
  startTop.value = buttonTop.value;

  document.addEventListener('mousemove', handleVerticalDrag);
  document.addEventListener('mouseup', stopVerticalDrag);

  // 防止选择文本和默认行为
  document.body.style.userSelect = 'none';
  e.preventDefault();
  e.stopPropagation();
};

// 新增：处理垂直拖拽
const handleVerticalDrag = (e) => {
  if (!isVerticalDragging.value) return;

  const delta = e.clientY - startY.value;

  // 如果移动距离超过5px，认为是拖拽
  if (Math.abs(delta) > 5) {
    hasMoved.value = true;
  }

  let newTop = startTop.value + delta;

  // 限制在窗口范围内
  const buttonHeight = 90; // 按钮高度
  const minTop = 0;
  const maxTop = window.innerHeight - buttonHeight;

  newTop = Math.max(minTop, Math.min(maxTop, newTop));
  buttonTop.value = newTop;

  // 保存位置到 localStorage
  localStorage.setItem('help-button-top', newTop.toString());

  e.preventDefault();
  e.stopPropagation();
};

// 新增：停止垂直拖拽
const stopVerticalDrag = (e) => {
  if (e) {
    e.preventDefault();
    e.stopPropagation();
  }

  const wasDragging = isVerticalDragging.value;
  isVerticalDragging.value = false;

  // 只有在实际移动时才设置标记防止立即触发点击
  if (wasDragging && hasMoved.value) {
    justFinishedDragging.value = true;
    // 短暂延迟后重置标记，允许后续正常点击
    setTimeout(() => {
      justFinishedDragging.value = false;
    }, 100);
  }

  document.removeEventListener('mousemove', handleVerticalDrag);
  document.removeEventListener('mouseup', stopVerticalDrag);
  document.body.style.userSelect = '';
};

// 新增：窗口大小改变时调整按钮位置
const handleResize = () => {
  const buttonHeight = 90;
  const maxTop = window.innerHeight - buttonHeight;
  if (buttonTop.value > maxTop) {
    buttonTop.value = Math.max(0, maxTop);
    localStorage.setItem('help-button-top', buttonTop.value.toString());
  }
};

const initDrag = (e) => {
  isDragging.value = true;
  startX.value = e.clientX;
  // 如果当前宽度是字符串（如"400px"），则转换为数字
  startWidth.value = typeof drawerWidth.value === 'string' ? parseInt(drawerWidth.value) : drawerWidth.value;

  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);

  // 防止选择文本
  document.body.style.userSelect = 'none';

  // 防止鼠标事件丢失
  e.preventDefault();
  e.stopPropagation();
};

const handleDrag = (e) => {
  if (!isDragging.value) return;

  const delta = startX.value - e.clientX;
  let newWidth = startWidth.value + delta;

  // 如果设置了最小宽度限制
  if (props.minWidth !== null) {
    const minWidthValue = typeof props.minWidth === 'string' ? parseInt(props.minWidth) : props.minWidth;
    newWidth = Math.max(minWidthValue, newWidth);
  }

  // 如果设置了最大宽度限制
  if (props.maxWidth !== null) {
    const maxWidthValue = typeof props.maxWidth === 'string' ? parseInt(props.maxWidth) : props.maxWidth;
    newWidth = Math.min(maxWidthValue, newWidth);
  }

  drawerWidth.value = newWidth;

  // 防止事件冒泡和默认行为
  e.preventDefault();
  e.stopPropagation();
};

const stopDrag = (e) => {
  if (e) {
    e.preventDefault();
    e.stopPropagation();
  }

  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.body.style.userSelect = '';
};

watchEffect(async () => {
  const routeName = route.name;
  const res = await Horn.fetch('llm_help_document_url_map');
  const { urlMap } = res;
  const url = urlMap?.[routeName as string];
  if (url) {
    visible.value = true;
    helpUrl.value = url.doc_url;
  } else {
    visible.value = false;
  }
});

onMounted(() => {
  // 初始化宽度
  drawerWidth.value = props.defaultWidth;

  // 验证并调整按钮位置（防止窗口大小变化导致位置超出范围）
  const buttonHeight = 90;
  const maxTop = window.innerHeight - buttonHeight;
  if (buttonTop.value > maxTop) {
    buttonTop.value = Math.max(0, maxTop);
    localStorage.setItem('help-button-top', buttonTop.value.toString());
  }

  // 添加窗口大小改变监听
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  // 清理事件监听
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('mousemove', handleVerticalDrag);
  document.removeEventListener('mouseup', stopVerticalDrag);
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss" scoped>
:deep(.mtd-drawer-content) {
  height: 100%;
}
.help-button {
  position: fixed;
  right: 0;
  width: 30px;
  height: 90px;
  background-color: v-bind('props.bgColor');
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer; /* 改为 move 光标表示可拖拽 */
  z-index: 9999;
  border-radius: 8px 0 0 8px;
  user-select: none; /* 防止选择文本 */

  &.left-side {
    right: auto;
    left: 0;
    border-radius: 0 4px 4px 0;
  }

  &:hover {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  }

  .help-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    transform: rotate(180deg);
    font-size: 13px;
    font-weight: 500;
    color: v-bind('props.textColor');
    user-select: none;
    cursor: pointer; /* 文本区域保持点击光标 */

    .help-icon {
      transform: rotate(90deg);
      margin-bottom: 4px;
      font-size: 14px;
    }

    span {
      display: inline-block;
      transform: rotate(90deg);
      margin: 0;
      line-height: 1;
    }
  }
}

.drag-button {
  position: fixed;
  top: 50%;
  transform: translateY(-50%) translateX(6px);
  width: 12px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: ew-resize;
  user-select: none;
  line-height: 30px;
  color: #91918e;
  text-align: center;
  border-radius: 500px;
  background-color: #f2f2f2;
  font-size: 12px;
  .mtdicon {
    transform: rotate(90deg);
  }
}

.iframe-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 1000; /* 确保在iframe上面 */
  cursor: ew-resize;
}
</style>
