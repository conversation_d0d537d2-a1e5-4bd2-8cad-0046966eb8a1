import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1426412, catid=246786, projectId=35802, created=2024-06-24 16:58:27, last-modified=2024-07-02 16:54:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalCustomPluginImplement/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426412)
 */
export function postEvalCustomPluginImplementList(
  params: model.evalCustomPluginImplement.IPostEvalCustomPluginImplementListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalCustomPluginImplement.IPostEvalCustomPluginImplementListResponse>('/webApi/evalCustomPluginImplement/list', {
    data: pick(params, ['searchFilter', 'interfaceNameFilter', 'ownerFilter', 'statusFilter', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426418, catid=246786, projectId=35802, created=2024-06-24 17:05:00, last-modified=2024-06-24 17:09:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalCustomPluginImplement/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426418)
 */
export function postEvalCustomPluginImplementDelete(
  params: model.evalCustomPluginImplement.IPostEvalCustomPluginImplementDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalCustomPluginImplement.IPostEvalCustomPluginImplementDeleteResponse>(
    '/webApi/evalCustomPluginImplement/delete',
    {
      data: pick(params, ['id']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1426421, catid=246786, projectId=35802, created=2024-06-24 17:15:27, last-modified=2024-06-24 17:16:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalCustomPluginImplement/setStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426421)
 */
export function postEvalCustomPluginImplementSetStatus(
  params: model.evalCustomPluginImplement.IPostEvalCustomPluginImplementSetStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalCustomPluginImplement.IPostEvalCustomPluginImplementSetStatusResponse>(
    '/webApi/evalCustomPluginImplement/setStatus',
    {
      data: pick(params, ['id', 'status']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1426423, catid=246786, projectId=35802, created=2024-06-24 17:17:53, last-modified=2024-08-06 19:07:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalCustomPluginImplement/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426423)
 */
export function postEvalCustomPluginImplementGet(
  params: model.evalCustomPluginImplement.IPostEvalCustomPluginImplementGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalCustomPluginImplement.IPostEvalCustomPluginImplementGetResponse>('/webApi/evalCustomPluginImplement/get', {
    data: pick(params, ['id', 'interfaceName', 'implementName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426467, catid=246786, projectId=35802, created=2024-06-24 18:55:45, last-modified=2024-08-06 19:07:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalCustomPluginImplement/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426467)
 */
export function postEvalCustomPluginImplementUpsert(
  params: model.evalCustomPluginImplement.IPostEvalCustomPluginImplementUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalCustomPluginImplement.IPostEvalCustomPluginImplementUpsertResponse>(
    '/webApi/evalCustomPluginImplement/upsert',
    {
      data: pick(params, [
        'id',
        'interfaceName',
        'implementName',
        'implementLabel',
        'ownerList',
        'status',
        'contextParamsDefine',
        'requireAnchorList',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
