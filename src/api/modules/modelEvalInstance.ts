import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1450572, catid=227025, projectId=35802, created=2024-07-31 11:31:28, last-modified=2024-08-01 15:16:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstance/evalKeyList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1450572)
 */
export function postModelEvalInstanceEvalKeyList(
  params: model.modelEvalInstance.IPostModelEvalInstanceEvalKeyListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstance.IPostModelEvalInstanceEvalKeyListResponse>('/webApi/modelEvalInstance/evalKeyList', {
    data: pick(params, ['model', 'compareModeList', 'dataSubSetId', 'parentCategoryIdList', 'evalDataSize', 'statName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1450555, catid=227025, projectId=35802, created=2024-07-31 11:19:54, last-modified=2024-08-02 10:13:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstance/compareList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1450555)
 */
export function postModelEvalInstanceCompareList(
  params: model.modelEvalInstance.IPostModelEvalInstanceCompareListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstance.IPostModelEvalInstanceCompareListResponse>('/webApi/modelEvalInstance/compareList', {
    data: pick(params, [
      'model',
      'compareModeList',
      'dataSubSetId',
      'parentCategoryIdList',
      'evalDataSize',
      'statName',
      'scoreFilter',
      'offset',
      'limit',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1433742, catid=227025, projectId=35802, created=2024-07-09 14:51:30, last-modified=2024-07-09 15:40:24
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstance/listNonnumeric)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433742)
 */
export function postModelEvalInstanceListNonnumeric(
  params: model.modelEvalInstance.IPostModelEvalInstanceListNonnumericParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstance.IPostModelEvalInstanceListNonnumericResponse>('/webApi/modelEvalInstance/listNonnumeric', {
    data: pick(params, ['model', 'dataSubSetId', 'evalDataSize', 'statName', 'jsonKey']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1385616, catid=227025, projectId=35802, created=2024-03-25 14:31:21, last-modified=2024-07-17 11:41:38
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstance/export)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1385616)
 */
export function postModelEvalInstanceExport(
  params: model.modelEvalInstance.IPostModelEvalInstanceExportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstance.IPostModelEvalInstanceExportResponse>('/webApi/modelEvalInstance/export', {
    data: pick(params, ['exportInfoList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296858, catid=227025, projectId=35802, created=2023-10-13 19:27:58, last-modified=2024-08-05 19:49:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstance/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296858)
 */
export function postModelEvalInstanceList(
  params: model.modelEvalInstance.IPostModelEvalInstanceListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstance.IPostModelEvalInstanceListResponse>('/webApi/modelEvalInstance/list', {
    data: pick(params, ['model', 'dataSubSetId', 'parentCategoryIdList', 'evalDataSize', 'statName', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
