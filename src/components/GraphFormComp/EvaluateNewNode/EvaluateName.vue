<template>
  <div class="common-width" style="display: flex">
    <mtd-input style="flex: 1" v-if="isEdit" :value="model" @input="handleInput"></mtd-input>
    <span v-else>{{ model }}</span>
    <mtd-button type="text-primary" v-if="foundBenchmark()" @click="handleViewBenchmarkDetail"
      >查看所有指标
      <mtd-icon name="barschart-o" />
    </mtd-button>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import modelManage from '@/store/modules/modelManage';
import { useGraphStore } from '@/views/modelManage/components/use-graph-store';
@Component
@Spectra
export default class App extends Weaver(modelManage) {
  @Prop()
  model!: string;
  @Prop()
  isEdit!: boolean;
  @Prop()
  context!: any;
  @Prop()
  globalContext!: any;

  useGraphStore = useGraphStore();

  handleInput(e) {
    this.$emit('change', e);
  }

  mounted() {
    if (!this.model) this.$emit('change', this.defaultModel);
  }

  get isFirstCreate() {
    return !this.experimentConfigDetail?.configDetail?.nodes.find((x) => x.nodeType === 1);
  }
  get defaultModel() {
    const modelInfo = this.experimentBaseInfo?.modelInfo ?? this.useGraphStore.modelInfo;
    const experimentName = this.experimentBaseInfo.experimentConfig.experimentName;
    let ckpt = '';
    if (modelInfo?.modelCheckpoint) {
      ckpt = '-' + modelInfo?.modelCheckpoint;
    }
    return 'MT/' + experimentName + (this.isFirstCreate ? ckpt : '');
  }

  foundBenchmark() {
    const data = (this.taskStatLists || this.experimentConfigDetail?.status || this.taskDetail?.status)?.nodeStatus?.find(
      (x) => x.nodeId === Number(this.context.id)
    )?.extraInfo?.evaluateFlowExtraInfoVos;
    return data;
  }

  handleViewBenchmarkDetail() {
    const runSpecSelector = this.globalContext.$refs['benchmarkList@RunSpecSelector'][0];
    runSpecSelector && runSpecSelector.handleViewAllBenchmarkDetail();
  }
}
</script>
<style lang="scss"></style>
