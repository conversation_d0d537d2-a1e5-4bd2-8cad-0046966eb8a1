import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1395870, catid=242469, projectId=35802, created=2024-04-17 10:29:46, last-modified=2024-04-17 11:05:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/commonCache/createCache)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1395870)
 */
export function postCommonCacheCreateCache(
  params: model.commonCache.IPostCommonCacheCreateCacheParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.commonCache.IPostCommonCacheCreateCacheResponse>('/webApi/commonCache/createCache', {
    data: pick(params, ['cacheType', 'cacheKey', 'cacheValue', 'cacheCreateTs']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1395871, catid=242469, projectId=35802, created=2024-04-17 10:39:23, last-modified=2024-04-17 10:40:17
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/commonCache/getValidCache)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1395871)
 */
export function postCommonCacheGetValidCache(
  params: model.commonCache.IPostCommonCacheGetValidCacheParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.commonCache.IPostCommonCacheGetValidCacheResponse>('/webApi/commonCache/getValidCache', {
    data: pick(params, ['cacheType', 'cacheKey']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
