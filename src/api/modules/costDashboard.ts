import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1506027, catid=242528, projectId=35802, created=2025-02-12 14:59:39, last-modified=2025-03-11 14:44:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/cost-dashboard/resourceDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506027)
 */
export function postCostDashboardResourceDetail(
  params: model.costDashboard.IPostCostDashboardResourceDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.costDashboard.IPostCostDashboardResourceDetailResponse>('/webApi/cost-dashboard/resourceDetail', {
    data: pick(params, [
      'timeFilter',
      'teamFilter',
      'ownerFilter',
      'queueFilter',
      'gpuSpecFilter',
      'consumerTypeFilter',
      'benchmarkFilter',
      'benchmarkGroupFilter',
      'evalModelFilter',
      'modelMetaFilter',
      'evalModelGroupFilter',
      'flowSubmitFilter',
      'flowJobFilter',
      'sortConfig',
      'dataSubSetFilter',
      'limit',
      'offset',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506554, catid=242528, projectId=35802, created=2025-02-18 16:22:30, last-modified=2025-02-18 19:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/cost-dashboard/efficiencyAnalysis)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506554)
 */
export function postCostDashboardEfficiencyAnalysis(
  params: model.costDashboard.IPostCostDashboardEfficiencyAnalysisParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.costDashboard.IPostCostDashboardEfficiencyAnalysisResponse>('/webApi/cost-dashboard/efficiencyAnalysis', {
    data: pick(params, [
      'timeFilter',
      'benchmarkFilter',
      'benchmarkGroupFilter',
      'evalModelFilter',
      'modelMetaFilter',
      'evalModelGroupFilter',
      'flowSubmitFilter',
      'flowJobFilter',
      'sortConfig',
      'onlyCompleteModel',
      'limit',
      'offset',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1505995, catid=242528, projectId=35802, created=2025-02-12 14:20:09, last-modified=2025-02-12 14:21:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/cost-dashboard/listGpuSpecConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505995)
 */
export function getCostDashboardListGpuSpecConfig(options?: FlowHttpRequestOptions) {
  return http.get<model.costDashboard.IGetCostDashboardListGpuSpecConfigResponse>('/webApi/cost-dashboard/listGpuSpecConfig', {
    ...options,
  });
}

/**
 * @desc id=1506018, catid=242528, projectId=35802, created=2025-02-12 14:56:18, last-modified=2025-02-12 14:57:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/cost-dashboard/listModelGroupList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506018)
 */
export function postCostDashboardListModelGroupList(
  params: model.costDashboard.IPostCostDashboardListModelGroupListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.costDashboard.IPostCostDashboardListModelGroupListResponse>('/webApi/cost-dashboard/listModelGroupList', {
    data: pick(params, ['name']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506012, catid=242528, projectId=35802, created=2025-02-12 14:43:25, last-modified=2025-02-12 14:46:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/cost-dashboard/listBenchmarkGroupList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506012)
 */
export function postCostDashboardListBenchmarkGroupList(
  params: model.costDashboard.IPostCostDashboardListBenchmarkGroupListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.costDashboard.IPostCostDashboardListBenchmarkGroupListResponse>('/webApi/cost-dashboard/listBenchmarkGroupList', {
    data: pick(params, ['name']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
