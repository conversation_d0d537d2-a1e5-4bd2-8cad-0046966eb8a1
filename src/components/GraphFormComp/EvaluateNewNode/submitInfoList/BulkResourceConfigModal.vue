<template>
  <mtd-modal v-model="modal" class="config-modal" width="1500px" :mount-on-create="false" :closable="false">
    <div slot="title" class="flex justify-between">
      <div>
        <span style="font-size: 18px; font-weight: bold"> ==资源批量配置== </span>&nbsp;
        <span style="color: #3888ff"> {{ `数据集 ${runspecSetContext.benchmarkList?.length}` }} </span>&nbsp;
        <span style="color: grey">
          {{ `执行单元 ${runspecSetContext.runSpecFlowConfigList?.length}` }}
        </span>
      </div>
      <div v-if="isEdit">
        <mtd-button style="width: 80px" @click="modal = false">取消</mtd-button>&nbsp;&nbsp;&nbsp;
        <mtd-button style="width: 80px" type="primary" @click="save">确定</mtd-button>
      </div>
      <div v-else><mtd-button @click="modal = false">关闭</mtd-button></div>
    </div>
    <div style="height: 500px; overflow: auto">
      <mtd-tag v-if="isEdit" class="bulk-background" style="border: 0">即将应用的批量配置:{{ bulkCount }}</mtd-tag>
      <mtd-table :data="tableData" class="resource-table" cell-class="resource-table-ceil">
        <mtd-table-column prop="clusterName" label="消费集群" show-overflow-tooltip :width="100">
          <template #default="{ row }">
            <mtd-tag>{{ row.clusterName.slice(0, 4) }}</mtd-tag>
            <br />
            <mtd-tag v-if="row.resourceType === 'gpuJob'" theme="purple">GPU集群</mtd-tag>
            <mtd-tag v-if="row.resourceType === '多值'" theme="red">资源冲突</mtd-tag>
          </template>
        </mtd-table-column>

        <mtd-table-column prop="referencedConsumer" label="包含消费者" :width="220">
          <template #default="{ row }">
            <mtd-tooltip
              v-for="(define, index) in row.consumerDefineList"
              :key="`${define.vertexDefineId}${define.rootAnchor}`"
              size="small"
              placement="top"
              :content="`${vertexMap.get(define.vertexDefineId)?.anchor}/${define.rootAnchor}`"
            >
              <div class="font-bold">{{ `${vertexMap.get(define.vertexDefineId)?.label}/${define.label}` }}</div>
            </mtd-tooltip>
          </template>
        </mtd-table-column>
        <mtd-table-column prop="queue" label="队列" :width="450" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <mtd-tooltip placement="top-start">
              <div slot="content">
                {{ row.resourceType === '多值' ? '所需资源冲突，请在不同计划中单独配置' : row.queue || getDefaultQueue(row) }}
              </div>
              <div v-if="isEdit && row.resourceType !== '多值'">
                <mtd-select
                  :value="getValue($index, 'queue')"
                  style="width: 400px"
                  @change="(e) => setQueue(e, $index)"
                  filterable
                  :placeholder="`默认(${getDefaultQueue(row)})`"
                  :class="{ 'bulk-background': bulkTableData[$index].queue }"
                >
                  <mtd-option v-if="row.queue" :label="row.queue" :value="row.queue"></mtd-option>
                  <mtd-option
                    v-for="v in row.resourceType === 'gpuJob'
                      ? gpuQueueList.filter((x) => x.queue !== row.queue)
                      : cpuQueueList.filter((x) => x.queue !== row.queue)"
                    :key="v.queue"
                    :label="v.queue"
                    :value="v.queue"
                  />
                </mtd-select>
                <br />
                <mtd-button type="text-primary" v-if="getValue($index, 'queue')" @click="setDefault($index, 'queue')"
                  >恢复默认({{ getDefaultQueue(row) }})</mtd-button
                >
              </div>
              <div v-else-if="row.resourceType === '多值'" class="disabled-row-tip">所需资源冲突，请在不同计划中单独配置</div>
              <div v-else>{{ row.queue || getDefaultQueue(row) }}</div>
            </mtd-tooltip>
          </template>
        </mtd-table-column>

        <!-- 实例数量列 -->
        <mtd-table-column prop="workers" label="实例数量" :width="80">
          <template #default="{ row, $index }" v-if="isEdit">
            <mtd-input-number
              v-if="row.resourceType !== '多值'"
              :value="getValue($index, 'workers')"
              @change="(e) => setBulkData(e, $index, 'workers')"
              :controls="false"
              style="width: 50px"
              :illegal-clear="true"
              :min="1"
              :placeholder="`${row.workers}`"
            />
            <span v-else class="disabled-row-tip">-</span>
          </template>
        </mtd-table-column>

        <!-- 显卡型号列 -->
        <mtd-table-column prop="gpuSpec" label="显卡型号" :width="145">
          <template #default="{ row, $index }" v-if="isEdit">
            <mtd-select
              v-if="row.resourceType === 'gpuJob'"
              :value="getValue($index, 'gpuSpec')"
              style="width: 115px"
              @change="(e) => setBulkData(e, $index, 'gpuSpec')"
              :class="{ 'bulk-background': bulkTableData[$index].gpuSpec }"
              :disabled="row.resourceType === '多值'"
            >
              <!-- 选项保持不变 -->
            </mtd-select>
            <span v-else-if="row.resourceType === '多值'" class="disabled-row-tip">-</span>
            <span v-else>-</span>
          </template>
          <template #default="{ row }" v-else>
            <span v-if="row.resourceType === 'gpuJob'">{{ row.gpuSpec }}</span>
            <span v-else-if="row.resourceType === '多值'" class="disabled-row-tip">-</span>
            <span v-else>-</span>
          </template>
        </mtd-table-column>

        <!-- 动态列 -->
        <template v-for="column in columns">
          <mtd-table-column :prop="column.prop" :label="column.label" :width="110">
            <template #default="{ row, $index }">
              <mtd-input-number
                v-if="(column.prop !== 'workerGpuCore' || row.resourceType === 'gpuJob') && isEdit && row.resourceType !== '多值'"
                :value="getValue($index, column.prop, true)"
                :placeholder="getValue($index, column.prop)?.toString() || `默认(${defaultTableData[$index][column.prop]})`"
                :controls="false"
                :class="{ 'bulk-background': bulkTableData[$index][column.prop] }"
                style="width: 60px"
                :illegal-clear="true"
                :min="1"
                @change="(e) => setBulkData(e, $index, column.prop)"
              />
              <span v-else-if="row.resourceType === '多值'" class="disabled-row-tip">-</span>
              <span v-else>{{ (!isEdit && (getValue($index, column.prop) || defaultTableData[$index][column.prop])) || '-' }}</span>

              <mtd-button
                type="text-primary"
                v-if="bulkTableData[$index][column.prop] && isEdit && row.resourceType !== '多值'"
                @click="setBulkDefault($index, column.prop)"
              >
                取消批量配置
              </mtd-button>
              <mtd-button
                type="text-primary"
                v-if="getValue($index, column.prop) && isEdit && row.resourceType !== '多值'"
                @click="setDefault($index, column.prop)"
              >
                置为默认({{ defaultTableData[$index][column.prop] }})
              </mtd-button>
            </template>
          </mtd-table-column>
        </template>
      </mtd-table>
    </div>
  </mtd-modal>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Spectra, Watch, Weaver } from '@/decorators';
import { VertexDefine } from '@/model/customEval';
import evalFlowVersion from '@/store/modules/evalFlowVersion';
import modelManage from '@/store/modules/modelManage';
import { cloneDeep } from 'lodash';
import ResourceConfigList from './ResourceConfigList.vue';

interface ResourceConfig {
  clusterName: string;
  resourceType: string;
  gpuSpec: string;
  workerGpuCore?: number;
  workerCpuCore?: number;
  workerCpuMem?: number;
  workerInstanceCount?: number;
  workers: number;
  queue?: string | null;
  dockerImage: string;
  consumerDefineList: {
    label: string;
    rootAnchor: string;
    vertexDefineId: number;
  }[];
  useOtherGpuQueue?: boolean;
}

@Component({
  components: {
    ResourceConfigList,
  },
})
@Spectra
export default class ResourceConfigModal extends Weaver(modelManage, evalFlowVersion) {
  @Prop()
  context!: any;
  @Prop()
  isEdit!: boolean;
  @Prop()
  runspecSetContext;
  @PropSync('openModal')
  modal!: boolean;
  @Prop()
  sourceTableData;
  @Prop()
  defaultTableData;

  @Ref()
  resourceConfigList!: ResourceConfigList;

  bulkTableData: any = [];

  getValue(index, prop, handleInvalid = false) {
    if (this.bulkTableData[index][prop] === 'DEFAULT') return null;
    if (!this.bulkTableData[index][prop] && this.tableData[index][prop] === '多值' && handleInvalid) return null;
    return this.bulkTableData[index][prop] || this.tableData[index][prop];
  }

  get bulkCount() {
    return this.bulkTableData.flatMap((x) => Object.entries(x)).filter(([k, v]) => k !== 'clusterName' && v).length;
  }

  get vertexMap() {
    const { vertexList } = this.evalFlowVersion?.evalFlowVersion;
    const vertexMap: Map<number, VertexDefine> = new Map();

    vertexList?.forEach(({ vertexDefine }) => {
      vertexMap.set(vertexDefine.id, vertexDefine as any);
    });
    return vertexMap;
  }

  tableData: ResourceConfig[] = [];
  columns = [
    {
      prop: 'workerGpuCore',
      label: '单机卡数(tp)',
    },
    {
      prop: 'workerCpuCore',
      label: '单机CPU数',
    },
    {
      prop: 'workerCpuMem',
      label: '单机内存(GB)',
    },
    {
      prop: 'workerInstanceCount',
      label: '分片数 (pp)',
    },
  ];

  coveredCount = 0;

  @Watch('sourceTableData', { immediate: true, deep: true })
  generateTableData(v) {
    this.tableData = cloneDeep(v);
    this.bulkTableData = v.map((x) => ({ clusterName: x.clusterName }));
  }

  getDefaultQueue(row: ResourceConfig) {
    const { cpuJobQueue, gpuJobQueue, otherGpuJobQueue } = this.context.nodeParam;
    if (row.resourceType === 'cpuJob') return cpuJobQueue;
    if (row.useOtherGpuQueue) return otherGpuJobQueue || gpuJobQueue;
    return gpuJobQueue;
  }

  gpuList(queue, isOther = false) {
    const { gpuJobQueue, otherGpuJobQueue } = this.context.nodeParam;
    return this.gpuQueueList.find((x) => x.queue === (queue || (isOther ? otherGpuJobQueue || gpuJobQueue : gpuJobQueue)))?.gpuType || [];
  }

  setQueue(value, index) {
    this.setBulkData(value, index, 'queue');
    this.setBulkData(this.gpuList(value, this.defaultTableData[index].useOtherGpuQueue)[0], index, 'gpuSpec');
  }

  setDefault(index, prop) {
    this.$set(this.bulkTableData[index], prop, 'DEFAULT');
  }

  save() {
    this.$emit('save', this.bulkTableData);
    this.modal = false;
  }

  setBulkData(value, index, prop) {
    this.$set(this.bulkTableData[index], prop, value);
  }
  setBulkDefault(index, prop) {
    this.$set(this.bulkTableData[index], prop, null);
  }
}
</script>
<style lang="less" scoped>
.resource-table {
  width: 100%;
  overflow: scroll;
}

::v-deep .resource-table-ceil {
  vertical-align: top !important;
}

.bulk-background {
  background-color: rgb(203, 225, 254);
  ::v-deep .mtd-input-number {
    background-color: rgba(0, 0, 0, 0);
  }
  ::v-deep .mtd-input {
    background-color: rgba(0, 0, 0, 0);
  }
}
</style>
