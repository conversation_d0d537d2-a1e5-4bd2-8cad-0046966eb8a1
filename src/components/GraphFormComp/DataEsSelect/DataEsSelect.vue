<template>
  <mtd-select v-model="arrValue" @change="handleChange" :multiple="true" style="width: 100%">
    <mtd-option v-for="item in options" :key="item.clusterName" :label="item.clusterName" :value="item.clusterName" />
  </mtd-select>
</template>
<script lang="ts">
import { Compo<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Watch } from '@/decorators';
import dataManage from '@/store/modules/dataManage';

@Component({ components: {} })
@Spectra
export default class DataEsSelect extends Weaver(dataManage) {
  @Prop()
  model!: any;
  @Prop()
  context: any;
  @Prop()
  globalContext: any;
  @Prop()
  isEdit!: boolean;

  arrValue: string[] = [];

  @Watch('model', { immediate: true, deep: true })
  modelChange(v) {
    this.arrValue = v && typeof v === 'string' ? v.split(',') : [];
  }

  options = [] as typeof this.dataHubGlanceEsClusterRelation;
  esMap = {} as any;
  defaultEs = [] as any;
  async getList() {
    const nodeParam = this.context.nodeParam;
    const res = await this.action$dataHubGlanceEsClusterRelation();
    if (!res) return;
    this.options = res;
    this.esMap = res.reduce((pre, cur) => {
      cur?.clusterName && (pre[cur.clusterName] = cur);
      cur?.defaultFlag === true && this.defaultEs.push(cur);
      return pre;
    }, {});
    this.model || this.$emit('change', this.defaultEs.map((item) => item.clusterName).join(','));
    nodeParam.indexAlias || this.$set(nodeParam, 'indexAlias', 'llm_pretrain_prepared_data');
  }

  handleChange() {
    const nodeParam = this.context.nodeParam;
    this.$emit('change', this.arrValue.join(','));
    nodeParam.indexAlias || this.$set(nodeParam, 'indexAlias', 'llm_pretrain_prepared_data');
  }

  created() {
    this.getList();
  }
}
</script>
<style lang="scss" scoped></style>
