<template>
  <div class="x6-graph-container">
    <context-menu-tool
      v-if="showContextMenuTool"
      :config="menuToolConfig"
      @addNode="addChildNode"
      @delNode="delNode"
      @linkNode="linkNodeStart"
    />
    <custom-node-flow-toolbar v-if="!loading" :graph="graph" @operation="toolBarOperation" />
    <div id="x6-container"></div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Spectra, Weaver } from '@/decorators';
import { DagreLayout } from '@antv/layout';
import { Edge, Graph, Node, Path } from '@antv/x6';
import { Selection } from '@antv/x6-plugin-selection';
import { register } from '@antv/x6-vue-shape';
import { VueClass } from 'vue-class-component/lib/declarations';
import ContextMenuTool from './ContextMenuTool.vue';
import CustomNodeFlowToolbar from './CustomNodeFlowToolbar.vue';

// 注册连线
Graph.registerConnector(
  'curveConnector',
  (sourcePoint, targetPoint) => {
    const hgap = Math.abs(targetPoint.x - sourcePoint.x);
    const path = new Path();
    path.appendSegment(Path.createSegment('M', sourcePoint.x - 4, sourcePoint.y));
    path.appendSegment(Path.createSegment('L', sourcePoint.x + 12, sourcePoint.y));
    // 水平三阶贝塞尔曲线
    path.appendSegment(
      Path.createSegment(
        'C',
        sourcePoint.x < targetPoint.x ? sourcePoint.x + hgap / 2 : sourcePoint.x - hgap / 2,
        sourcePoint.y,
        sourcePoint.x < targetPoint.x ? targetPoint.x - hgap / 2 : targetPoint.x + hgap / 2,
        targetPoint.y,
        targetPoint.x - 6,
        targetPoint.y
      )
    );
    path.appendSegment(Path.createSegment('L', targetPoint.x + 2, targetPoint.y));

    return path.serialize();
  },
  true
);

Edge.config({
  markup: [
    {
      tagName: 'path',
      selector: 'wrap',
      attrs: {
        fill: 'none',
        cursor: 'pointer',
        stroke: 'transparent',
        strokeLinecap: 'round',
      },
    },
    {
      tagName: 'path',
      selector: 'line',
      attrs: {
        fill: 'none',
        pointerEvents: 'none',
      },
    },
  ],
  connector: { name: 'curveConnector' },
  attrs: {
    wrap: {
      connection: true,
      strokeWidth: 10,
      strokeLinejoin: 'round',
    },
    line: {
      connection: true,
      stroke: '#A2B1C3',
      strokeWidth: 1,
      targetMarker: {
        name: 'classic',
        size: 6,
      },
    },
  },
});

Graph.registerEdge('data-processing-curve', Edge, true);

// 注册布局

const dagreLayout = new DagreLayout({
  type: 'dagre',
  rankdir: 'LR',
  align: 'UL',
  ranksep: 150,
  nodesep: 100,
  controlPoints: true,
});
@Component({
  components: { CustomNodeFlowToolbar, ContextMenuTool },
})
@Spectra
export default class CustomNodeFlow extends Weaver() {
  @Prop()
  nodeComponent!: VueClass<unknown>;

  @PropSync('data')
  model: any;

  @Prop({ default: true })
  editable!: boolean;

  @Prop({ default: () => dagreLayout })
  dagreLayout!: DagreLayout;

  @Prop()
  defaultSelectedNode?: string;

  loading = true;
  graph?: Graph;
  showContextMenuTool = false;
  menuToolConfig: any = {};
  selectedNode?: Node;
  linkStartId?: string | null;

  toolBarOperation(code) {
    this.showContextMenuTool = false;
    switch (code) {
      case 'resize':
        this.model = this.dagreLayout.layout(this.model);
        this.graph?.fromJSON(this.model);
        return;
      case 'refresh':
        this.graph?.fromJSON(this.model);
        return;
      default:
        return;
    }
  }
  /**
   * 根据起点初始下游节点的位置信息
   * @param node 起始节点
   * @param graph
   * @returns
   */
  getDownstreamNodePosition(node: Node, dx = 150, dy = 50) {
    // 找出画布中以该起始节点为起点的相关边的终点id集合
    const downstreamNodeIdList: string[] = [];
    this.graph!.getEdges().forEach((edge) => {
      if (edge.getSourceCellId() === node.id) {
        downstreamNodeIdList.push(edge.getTargetCellId());
      }
    });
    // 获取起点的位置信息
    const position = node.getPosition();
    let minX = Infinity;
    let maxY = -Infinity;
    this.graph!.getNodes().forEach((graphNode) => {
      if (downstreamNodeIdList.indexOf(graphNode.id) > -1) {
        const nodePosition = graphNode.getPosition();
        // 找到所有节点中最左侧的节点的x坐标
        if (nodePosition.x < minX) {
          minX = nodePosition.x;
        }

        // 找到所有节点中最下方的节点的y坐标
        if (nodePosition.y + (graphNode.getProp('size')?.height || 0) > maxY) {
          maxY = nodePosition.y + (graphNode.getProp('size')?.height || 0);
        }
      }
    });

    return {
      x: minX !== Infinity ? minX : position.x + (node.getProp('size')?.width || 0) + dx,
      y: maxY !== -Infinity ? maxY + dy : position.y,
    };
  }

  /**
   * 创建节点并添加到画布
   * @param type 节点类型
   * @param graph
   * @param position 节点位置
   * @returns
   */
  createNode(data: any, position?) {
    const node = {
      id: data.id,
      shape: 'custom-vue-node',
      x: position?.x,
      y: position?.y,
      data: {
        ...data,
      },
    };
    this.model.nodes.push(node);
    this.$emit('changed');
    return this.graph!.addNode(node);
  }
  /**
   * 创建边并添加到画布
   * @param source
   * @param target
   * @param graph
   */
  createEdge(source: string, target: string) {
    const edge = {
      shape: 'data-processing-curve',
      source,
      target,
    };
    this.model.edges.push(edge);
    this.$emit('changed');
    this.graph?.addEdge(edge);
  }

  addChildNode(data) {
    const oriNode = this.menuToolConfig!.node;
    // 获取下游节点的初始位置信息
    const position = this.getDownstreamNodePosition(oriNode);
    // 创建下游节点
    const newNode = this.createNode(data, position);
    const source = oriNode.id;
    const target = newNode.id;
    // 创建该节点出发到下游节点的边
    this.createEdge(source, target);
  }
  delNode() {
    const nodeId = this.menuToolConfig!.node.id;
    this.showContextMenuTool = false;
    this.graph?.removeNode(nodeId);
    const { nodes: oriNodes, edges: oriEdges } = this.model;

    this.$set(
      this.model,
      'nodes',
      oriNodes.filter((node) => node.id !== nodeId)
    );
    this.$set(
      this.model,
      'edges',
      oriEdges.filter((edge) => edge.source !== nodeId && edge.target !== nodeId)
    );
    this.$emit('changed');
  }

  linkNodeStart() {
    const nodeId = this.menuToolConfig!.node.id;
    this.linkStartId = nodeId;
    this.graph?.enableMultipleSelection();
    const targetNodes = this.graph
      ?.getNodes()
      .filter(({ id, data }) => this.model.edges.map((x) => x.target).indexOf(id) === -1 && nodeId !== id && data?.vertexDefineId !== 1);
    if (!targetNodes?.length) this.$mtd.message.error('没有可连接的节点');
    else this.graph?.resetSelection(targetNodes);
  }
  linkNodeEnd() {
    this.graph?.cleanSelection();
    this.graph?.disableMultipleSelection();
    this.linkStartId = null;
  }

  mounted() {
    register({
      shape: 'custom-vue-node',
      component: this.nodeComponent,
      width: 400,
      height: 200,
    });

    const container = document.getElementById('x6-container')!;

    this.graph = new Graph({
      container,
      width: container.offsetWidth,
      height: container.offsetHeight,
      panning: true,
      interacting: false,
      connecting: {
        snap: true,
        allowBlank: false,
        allowLoop: false,
        highlight: true,
        sourceAnchor: {
          name: 'right',
        },
        targetAnchor: {
          name: 'left',
        },
        connectionPoint: 'anchor',
      },
    });

    this.loading = false;
    const newModel = this.dagreLayout.layout(this.model);

    this.graph.fromJSON(newModel);

    // 选择
    this.graph.use(
      new Selection({
        enabled: true,
        multiple: false,
        showNodeSelectionBox: true,
        pointerEvents: 'none',
      })
    );

    // 相关事件

    // 右键菜单
    const rect = container.getBoundingClientRect();
    this.editable &&
      this.graph.on('node:contextmenu', (args) => {
        const { clientX: x, clientY: y } = args.e;
        this.menuToolConfig = { x: x - rect.left, y: y - rect.top, node: args.node };
        this.showContextMenuTool = true;
      });
    this.graph.on('blank:mousedown', () => {
      this.showContextMenuTool = false;
      this.linkNodeEnd();
    });
    this.graph.on('node:mousedown', (args) => {
      this.handleNodeMouseDown(args.node);
    });
    // 节点选择
    this.graph.on('node:selected', ({ node }) => {
      if (!this.linkStartId) {
        this.$emit('select', { data: node.getData(), pos: node.getPosition() });
      }
    });
    this.graph.on('node:unselected', ({ node }) => {
      if (!this.linkStartId) {
        this.$emit('unselect');
      }
    });
    this.graph.on('node:moved', ({ node }) => {
      this.$emit('nodeMoved', node);
    });

    if (this.model.nodes?.length > 3) this.graph!.zoomToFit();
    if (this.defaultSelectedNode) this.graph.select(this.defaultSelectedNode);
  }

  handleNodeMouseDown(node: Node) {
    this.showContextMenuTool = false;
    if (this.linkStartId && this.graph?.isSelected(node)) {
      const edge = {
        source: this.linkStartId,
        target: node.id,
        shape: 'data-processing-curve',
      };
      this.graph.addEdge(edge);
      this.model.edges.push(edge);
    }

    this.linkNodeEnd();
  }
}
</script>
<style lang="scss">
#x6-container {
  width: 100%;
  height: 100%;
  background-color: rgb(252, 252, 255);
}
.x6-graph-container {
  height: 100%;
  position: relative;
}
.x6-widget-selection-box {
  border: 2px dashed #239edd;
}
.x6-widget-selection-inner {
  display: none;
}
</style>
