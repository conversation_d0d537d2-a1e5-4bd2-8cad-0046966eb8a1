import http from '@/api/http';
import { FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';

export function modelDataQuery(params: { [k: string]: any }, options: FlowHttpRequestOptions & { url: string }) {
  return http.post(options.url, {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

export function modelCommonCache(params: { [k: string]: any }, options: FlowHttpRequestOptions & { url: string }) {
  return http.post(options.url, {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

export function chatUpload(params: FormData, options?: FlowHttpRequestOptions) {
  return http.post('/webApi/chat/upload', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'multipart/form-data' }),
  });
}
