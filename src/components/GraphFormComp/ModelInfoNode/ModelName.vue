<template>
  <div>
    <mtd-tag v-if="getModelMeta?.benchmarking" theme="green">标杆</mtd-tag>
    {{ modelInfo?.benchmarking || modelInfo?.modelStage === 'ORIGIN' ? modelInfo.modelShortName : modelInfo?.modelName }}
    <mtd-button type="text-primary" @click="handleModelMetaJump('global')"
      >全局推理参数配置
      <mtd-tag v-if="getModelMeta?.pluginBindList?.length || 0 > 0" theme="green">已配置</mtd-tag>
      <mtd-tag v-else theme="red">未配置</mtd-tag>
    </mtd-button>
    <mtd-button type="text-primary" @click="handleModelMetaJump('promptPrefix')"
      >prompt前缀：
      <mtd-tag v-if="getModelMeta?.promptPrefix" theme="green">{{ getModelMeta?.promptPrefix }}</mtd-tag>
      <mtd-tag v-else theme="red">未配置</mtd-tag>
    </mtd-button>
    <mtd-button type="text-primary" @click="handleModelMetaJump('promptSuffix')"
      >prompt后缀：
      <mtd-tag v-if="getModelMeta?.promptSuffix" theme="green">{{ getModelMeta?.promptSuffix }}</mtd-tag>
      <mtd-tag v-else theme="red">未配置</mtd-tag>
    </mtd-button>
    <mtd-button type="text-primary" @click="handleModelMetaJump('autoEvalDataSize')"
      >评测数据规模：
      <mtd-tag v-if="getModelMeta?.autoEvalDataSize" theme="green">{{ getMetaLabel(getModelMeta?.autoEvalDataSize) }}</mtd-tag>
      <mtd-tag v-else theme="red">未配置</mtd-tag>
    </mtd-button>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import modelManage from '@/store/modules/modelManage';
import { useGraphModelStore } from '@/views/modelManage/graph-components/use-graph-model-store';
@Component
@Spectra
export default class CustomComp extends Weaver(modelManage) {
  @Prop()
  isEdit!: boolean;
  @Prop()
  model!: any;
  @Prop()
  context!: any;

  get modelInfo() {
    return this.experimentBaseInfo?.modelMeta;
  }
  graphModelStore = useGraphModelStore();

  handleModelMetaJump(type: string) {
    window.open(
      this.$router.resolve({
        name: 'model-manage-model-detail',
        params: {
          metaId: String(this.modelInfo?.metaId ?? this.graphModelStore.modelInfo.modelMetaId),
        },
        query: {
          type,
        },
      }).href,
      '_blank',
      'noopener'
    );
  }

  created() {
    let modelMetaId;
    if (this.modelInfo?.metaId) {
      modelMetaId = this.modelInfo.metaId;
    } else if (this.graphModelStore.modelInfo.modelMetaId) {
      modelMetaId = this.graphModelStore.modelInfo.modelMetaId;
    }
    this.action$getModelMeta({
      modelMetaId: modelMetaId ?? 0,
    });
    this.action$modelEvalListEvalDataSizeMeta({});
  }

  getMetaLabel(meta) {
    return this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList.find((x) => x.name === meta)?.label || meta;
  }
}
</script>
<style lang="scss"></style>
