<template>
  <DocumentReader :model="model" :context="context" :globalContext="globalContext" :isEdit="isEdit" @change="change" :isDataType="true" />
</template>
<script lang="ts">
import DocumentReader from '@/components/GraphFormComp/DocumentReaderNode/DocumentReader.vue';
import { Component, Prop, Spectra, Vue } from '@/decorators';

@Component({ components: { DocumentReader } })
@Spectra
export default class DocumentReader2 extends Vue {
  @Prop()
  model!: any;
  @Prop()
  context: any;
  @Prop()
  globalContext: any;
  @Prop()
  isEdit!: boolean;

  change(v) {
    this.$emit('change', v);
  }
}
</script>
<style lang="scss" scoped></style>
