<template>
  <div>
    <div style="margin-bottom: 10px; display: flex">
      <div v-if="PARAMS.modelEffectsBtn && PARAMS.params.filterGroupConfigList">
        <mtd-button
          v-for="(B, I) in PARAMS.params.filterGroupConfigList"
          :key="I"
          ghost
          :type="I === selectGroup ? 'primary' : ''"
          @click="selectGroupBtn(B, I)"
          style="margin-right: 10px"
          :disabled="loading"
          >{{ B.name }}</mtd-button
        >
      </div>
      <div v-if="PARAMS.unUseTemplateBtnFlag" class="flex-center" style="margin-right: 10px; margin-left: auto">
        使用模版
        <mtd-switch style="margin-left: 10px" :value="PARAMS.templateFlag" @change="templateFlagClick" />
      </div>
      <mtd-button
        style="margin-left: 10px"
        type="primary"
        @click="
          () => {
            getModelDashboard && getModelDashboard(false, true, selectGroupName);
          }
        "
        >刷新报表</mtd-button
      >
    </div>
    <div v-if="LS('modelEvalIUnifiedModelDetailDiffTable') && !PARAMS.tableDataModelEffects">
      <mtd-loading />
    </div>
    <div v-if="PARAMS.tableDataModelEffects" :key="keyChange" class="category-container">
      <div v-for="(n, index) in PARAMS?.tableDataModelEffects?.originRequest.evalModelList" :key="index">
        <ThreeInOneCategoryTableItem :PARAMS="PARAMS" :idex="index" style="margin-bottom: 20px; padding-top: 30px" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import ThreeInOneCategoryTableItem from '@/components/model/ThreeInOne/ThreeInOneCategoryTableItem.vue';
import type { CategoryTableParams } from '@/types/modelCommonComponent';
import ModelEval from '@/store/modules/modelEval';

@Component({ components: { ThreeInOneCategoryTableItem } })
@Spectra
export default class ThreeInOneCategoryTable extends Weaver(ModelEval) {
  @Prop()
  PARAMS!: CategoryTableParams;
  @Prop()
  getModelDashboard?: any;
  @Prop({ default: () => () => void 0 })
  templateFlagClick?: any;
  @Prop()
  keyChange?: boolean;

  selectGroup = 0;
  loading = false;
  selectGroupName = '';
  async selectGroupBtn(mas, index) {
    if (this.selectGroup === index) return;
    this.selectGroup = index;
    this.loading = true;
    this.selectGroupName = mas.name;
    await this.getModelDashboard(false, true, mas.name);
    this.loading = false;
  }

  async mounted() {
    if (!this.modelEvalUnVersionedListStatMeta.statMetaList?.length) await this.action$modelEvalUnVersionedListStatMeta({});
  }
}
</script>

<style scoped lang="scss">
::v-deep .export-button {
  transform: translateY(-30px);
  float: right;
}
</style>
