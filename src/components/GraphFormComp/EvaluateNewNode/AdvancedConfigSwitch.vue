<template>
  <div class="common-width" style="display: flex; align-items: center">
    <mtd-switch :disabled="false" size="small" :value="model" @input="$emit('change', $event)" />
    <mtd-button type="text-primary" href="https://km.sankuai.com/collabpage/2621970523" target="_blank"
      >V2评测使用指南
      <mtd-icon name="question-circle-o" />
    </mtd-button>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import modelManage from '@/store/modules/modelManage';
@Component
@Spectra
export default class App extends Weaver(modelManage) {
  @Prop()
  model!: string;
  @Prop()
  isEdit!: boolean;
  @Prop()
  context!: any;
}
</script>
<style lang="scss"></style>
