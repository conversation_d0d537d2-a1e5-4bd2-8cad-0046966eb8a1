import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1418876, catid=227034, projectId=35802, created=2024-06-10 16:59:05, last-modified=2024-06-10 17:26:32
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEval/batchEntityActionUnVersioned)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418876)
 */
export function postModelEvalBatchEntityActionUnVersioned(
  params: model.modelEval.IPostModelEvalBatchEntityActionUnVersionedParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEval.IPostModelEvalBatchEntityActionUnVersionedResponse>('/webApi/modelEval/batchEntityActionUnVersioned', {
    data: pick(params, ['metaVersionId', 'actionList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296993, catid=227034, projectId=35802, created=2023-10-13 19:28:04, last-modified=2023-11-21 14:38:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEval/batchEntityAction)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296993)
 */
export function postModelEvalBatchEntityAction(
  params: model.modelEval.IPostModelEvalBatchEntityActionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEval.IPostModelEvalBatchEntityActionResponse>('/webApi/modelEval/batchEntityAction', {
    data: pick(params, ['metaVersionId', 'actionList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297005, catid=227034, projectId=35802, created=2023-10-13 19:28:05, last-modified=2023-11-30 14:56:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEval/listStatMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297005)
 */
export function postModelEvalListStatMeta(params: model.modelEval.IPostModelEvalListStatMetaParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.modelEval.IPostModelEvalListStatMetaResponse>('/webApi/modelEval/listStatMeta', {
    data: pick(params, ['metaVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297018, catid=227034, projectId=35802, created=2023-10-13 19:28:05, last-modified=2023-11-21 14:38:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEval/listEvalDataSizeMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297018)
 */
export function postModelEvalListEvalDataSizeMeta(
  params: model.modelEval.IPostModelEvalListEvalDataSizeMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEval.IPostModelEvalListEvalDataSizeMetaResponse>('/webApi/modelEval/listEvalDataSizeMeta', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
