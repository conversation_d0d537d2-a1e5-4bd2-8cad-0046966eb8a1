import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1309844, catid=228648, projectId=35802, created=2023-10-24 11:31:39, last-modified=2023-12-11 10:54:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/common/interruptHeartbeat)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1309844)
 */
export function postCommonInterruptHeartbeat(
  params: model.common.IPostCommonInterruptHeartbeatParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.common.IPostCommonInterruptHeartbeatResponse>('/webApi/common/interruptHeartbeat', {
    data: pick(params, ['queryUuid']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1309830, catid=228648, projectId=35802, created=2023-10-24 11:26:01, last-modified=2023-12-11 10:54:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/common/keepHeartbeat)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1309830)
 */
export function postCommonKeepHeartbeat(params: model.common.IPostCommonKeepHeartbeatParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.common.IPostCommonKeepHeartbeatResponse>('/webApi/common/keepHeartbeat', {
    data: pick(params, ['queryUuid', 'heartbeatTtl']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1313290, catid=232386, projectId=35802, created=2023-10-30 20:39:17, last-modified=2025-05-30 15:50:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/common/pullQueueByProjectGroup)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1313290)
 */
export function getCommonPullQueueByProjectGroup(
  params: model.common.IGetCommonPullQueueByProjectGroupParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.common.IGetCommonPullQueueByProjectGroupResponse>('/webApi/common/pullQueueByProjectGroup', {
    params: pick(params, ['projectGroups']),
    ...options,
  });
}

/**
 * @desc id=1521689, catid=232386, projectId=35802, created=2025-05-30 15:50:26, last-modified=2025-05-30 15:50:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/common/getWikiContentTitle)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1521689)
 */
export function getCommonGetWikiContentTitle(
  params: model.common.IGetCommonGetWikiContentTitleParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.common.IGetCommonGetWikiContentTitleResponse>('/webApi/common/getWikiContentTitle', {
    params: pick(params, ['wikiUrl']),
    ...options,
  });
}

/**
 * @desc id=1294837, catid=232386, projectId=35802, created=2023-10-11 17:14:46, last-modified=2025-05-30 15:50:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/common/listProjectGroup)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1294837)
 */
export function getCommonListProjectGroup(options?: FlowHttpRequestOptions) {
  return http.get<model.common.IGetCommonListProjectGroupResponse>('/webApi/common/listProjectGroup', {
    ...options,
  });
}

/**
 * @desc id=1471116, catid=232386, projectId=35802, created=2024-09-13 15:20:02, last-modified=2025-05-30 15:50:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/common/listProjectGroupCpuQueue)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1471116)
 */
export function getCommonListProjectGroupCpuQueue(
  params: model.common.IGetCommonListProjectGroupCpuQueueParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.common.IGetCommonListProjectGroupCpuQueueResponse>('/webApi/common/listProjectGroupCpuQueue', {
    params: pick(params, ['projectGroup']),
    ...options,
  });
}

/**
 * @desc id=1471120, catid=232386, projectId=35802, created=2024-09-13 15:20:02, last-modified=2025-05-30 15:50:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/common/listProjectGroupGpuQueue)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1471120)
 */
export function getCommonListProjectGroupGpuQueue(
  params: model.common.IGetCommonListProjectGroupGpuQueueParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.common.IGetCommonListProjectGroupGpuQueueResponse>('/webApi/common/listProjectGroupGpuQueue', {
    params: pick(params, ['projectGroup']),
    ...options,
  });
}

/**
 * @desc id=1294840, catid=232386, projectId=35802, created=2023-10-11 17:14:46, last-modified=2025-05-30 15:50:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/common/listProjectGroupQueue)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1294840)
 */
export function getCommonListProjectGroupQueue(
  params: model.common.IGetCommonListProjectGroupQueueParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.common.IGetCommonListProjectGroupQueueResponse>('/webApi/common/listProjectGroupQueue', {
    params: pick(params, ['projectGroup']),
    ...options,
  });
}

/**
 * @desc id=1416631, catid=227034, projectId=35802, created=2024-06-04 14:48:13, last-modified=2024-06-05 17:27:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/common/auth)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1416631)
 */
export function postCommonAuth(params: model.common.IPostCommonAuthParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.common.IPostCommonAuthResponse>('/webApi/common/auth', {
    data: pick(params, ['entity', 'action', 'value']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
