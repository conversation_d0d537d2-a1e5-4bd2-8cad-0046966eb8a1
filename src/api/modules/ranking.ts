import * as model from '@/model';
import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import http from '../http';

/**
 * @desc 首页默认查10条
 * @desc id=1402143, catid=243411, projectId=35802, created=2024-05-06 17:24:56, last-modified=2024-05-09 10:21:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/ranking/existRankDateList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1402143)
 */
export function postRankingExistRankDateList(
  params: model.ranking.IPostRankingExistRankDateListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.ranking.IPostRankingExistRankDateListResponse>('/webApi/ranking/existRankDateList', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc 首页默认查10条
 * @desc id=1402114, catid=243411, projectId=35802, created=2024-05-06 17:15:52, last-modified=2024-05-13 14:20:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/ranking/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1402114)
 */
export function postRankingList(params: model.ranking.IPostRankingListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.ranking.IPostRankingListResponse>('/webApi/ranking/list', {
    data: pick(params, ['rankType', 'rankItemName', 'rankDate', 'sort', 'offset', 'limit', 'modelTypeFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1402398, catid=243411, projectId=35802, created=2024-05-07 11:24:14, last-modified=2024-05-15 15:03:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/ranking/configList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1402398)
 */
export function postRankingConfigList(params: model.ranking.IPostRankingConfigListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.ranking.IPostRankingConfigListResponse>('/webApi/ranking/configList', {
    data: pick(params, ['rankType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
