<template>
  <div class="context-param">
    <mtd-tag v-if="bulkEdit" type="pure" theme="blue" style="border: 0"
      >即将应用的批量配置:{{ contextParamsDefine.filter((x) => x.isBulk).length }}</mtd-tag
    >
    <mtd-table :data="contextParamsDefine">
      <mtd-table-column v-if="hasOverrideFlag" label="覆盖子benchmark" :width="150">
        <template #default="{ row }">
          <mtd-switch :disabled="disabled" v-model="row.overrideFlag" true-value="ALL_SUB" :false-value="null" />
        </template>
      </mtd-table-column>
      <mtd-table-column prop="label" label="展示名" :width="250">
        <template #default="{ row }">
          <span>
            {{ row.globalAliasName && '<全局>' }}
            {{ row.label }}
          </span>
          <mtd-tooltip v-if="row.description" :content="row.description" placement="bottom">
            <i class="mtdicon mtdicon-question-circle-o"></i>
          </mtd-tooltip>
        </template>
      </mtd-table-column>
      <mtd-table-column prop="value" label="取值">
        <template #default="{ row, $index }" class="value-box">
          <div v-if="shouldShowGlobalAlias(row)">
            <mtd-tooltip :content="getEnvGlobalParamValue(row)" placement="top-start" :disabled="getEnvGlobalParamValue(row) < 100">
              <span style="white-space: pre-wrap">{{ truncatedValue(getEnvGlobalParamValue(row)) }}</span>
            </mtd-tooltip>
            <mtd-button v-if="isLongValue(getEnvGlobalParamValue(row))" type="text-primary" @click="copyValue(getEnvGlobalParamValue(row))">
              参数较长，点击复制
            </mtd-button>
          </div>
          <div v-else-if="disabled">
            <mtd-tooltip
              :content="row.value || getDefaultValue(row)"
              placement="top-start"
              :disabled="row.value ? row.value.length < 100 : getDefaultValue(row).length < 100"
            >
              <span
                :class="{
                  'job-background': isJobHighlighted(row),
                  'not-refer-background': row.referValue && row.value !== row.referValue && !bulkEdit,
                }"
                style="white-space: pre-wrap"
                >{{ truncatedValue(getDisplayValue(row)) }}</span
              >
            </mtd-tooltip>
            <mtd-button v-if="isLongValue(getDisplayValue(row))" type="text-primary" @click="copyValue(getDisplayValue(row))">
              参数较长，点击复制
            </mtd-button>
          </div>
          <div v-else-if="row.type === 'string'">
            <mtd-tooltip
              :content="row.value || getDefaultValue(row)"
              placement="top-start"
              :disabled="row.value ? row.value.length < 10 : getDefaultValue(row).length < 10"
            >
              <div class="flex">
                <mtd-input
                  v-model="row.value"
                  :placeholder="getDefaultValue(row)"
                  style="flex: 1"
                  :class="{
                    'job-background': isJobHighlighted(row),
                    'bulk-background': row.isBulk && bulkEdit,
                    'not-refer-background': row.referValue && row.value !== row.referValue && !bulkEdit,
                  }"
                  genre="line"
                  @change="changeSource(row.source, $index)"
                ></mtd-input>
              </div>
            </mtd-tooltip>
            <mtd-button
              v-if="disabled && (row.value || getDefaultValue(row)).length > 100"
              type="text-primary"
              @click="copyValue(row.value || getDefaultValue(row))"
              >参数较长，点击复制</mtd-button
            >
          </div>
          <mtd-input-number
            v-else-if="row.type === 'long' || row.type === 'number'"
            v-model="row.value"
            class="context-param-input-number"
            :precision="row.type === 'long' ? 0 : undefined"
            :placeholder="String(getDefaultValue(row) || '-')"
            :controls="false"
            :allow-empty="true"
            :class="{
              'job-background': isJobHighlighted(row),
              'bulk-background': row.isBulk && bulkEdit,
              'not-refer-background': row.referValue && row.value !== row.referValue && !bulkEdit,
            }"
            @change="changeSource(row.source, $index)"
          >
          </mtd-input-number>
          <mtd-select
            v-else
            v-model="row.value"
            :placeholder="handleSelectValue(row)"
            :class="{
              'job-background': isJobHighlighted(row),
              'bulk-background': row.isBulk && bulkEdit,
              'not-refer-background': row.referValue && row.value !== row.referValue && !bulkEdit,
            }"
            genre="line"
            @change="changeSource(row.source, $index)"
          >
            <mtd-option
              v-for="option of getCustomTypeOptions(row.type)"
              :disabled="option.disabled"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </mtd-select>
          <mtd-tooltip content="该字段下有多个值，修改后将全部覆盖" placement="top" v-if="row.flag === 'mix'">
            <mtd-tag theme="green" style="margin-left: 5px" v-if="row.flag === 'mix'">mix</mtd-tag>
          </mtd-tooltip>
        </template>
      </mtd-table-column>
      <mtd-table-column prop="source" label="来源" v-if="needSource" width="250">
        <template #default="{ row }">
          <source-viewer :source="getEnvGlobalParamSource(row) || row.source" :isCurrentSource="row.source === curSource"></source-viewer>
        </template>
      </mtd-table-column>
      <mtd-table-column label="操作" v-if="!disabled">
        <template #default="{ row, $index }">
          <div class="action-buttons">
            <mtd-button
              v-if="row.source !== row.defaultSource && row.value !== null && row.value !== undefined"
              type="text-primary"
              @click="setDefault($index)"
              style="text-overflow: ellipsis; max-width: 300px; overflow: hidden"
            >
              恢复默认({{ ['string', 'long', 'number'].includes(row.type) ? row.defaultValue : handleSelectValue(row) }})
            </mtd-button>
            <mtd-button v-if="row.referValue && row.value !== row.referValue" type="text-primary" @click="setRefer($index)">
              置为推荐值({{ ['string', 'long', 'number'].includes(row.type) ? row.defaultValue : handleSelectValue(row) }})
            </mtd-button>
            <mtd-button v-if="row.value !== '<empty>' && row.type === 'string'" type="text-primary" @click="setEmpty(row, $index)">
              置为空值(&lt;empty&gt;)
            </mtd-button>
            <mtd-button type="text-primary" v-if="row.isBulk && bulkEdit" @click="setBulkDefault($index)">取消批量配置</mtd-button>
          </div>
        </template>
      </mtd-table-column>
    </mtd-table>
  </div>
</template>
<script lang="ts">
import { postGpuSpecEnumList } from '@/api/modules/gpuSpecEnum';
import { Component, InjectReactive, Prop, PropSync, Spectra, Weaver } from '@/decorators';
import { ContextParam } from '@/model/customEval';
import modalEval from '@/store/modules/modelEval';
import { EvalSource } from '@/utils';
import SourceViewer from './SourceViewer.vue';

@Component({
  components: {
    SourceViewer,
  },
})
@Spectra
export default class ContextParamDefinition extends Weaver(modalEval) {
  @Prop({ default: '' })
  curSource!: string;

  @PropSync('value', { default: () => [] })
  contextParamsDefine!: ContextParam[];

  @Prop({ default: false })
  disabled?: boolean;

  @Prop({ default: false })
  canEditGlobalAlias?: boolean;

  @Prop({ default: true })
  needSource?: boolean;

  @Prop({ default: false })
  hasOverrideFlag?: boolean;

  @InjectReactive('paramsMap')
  globalParamsMap?: any = undefined;

  @InjectReactive('modelMetaSize')
  modelMetaSize?: string;

  @Prop({ default: false })
  bulkEdit?: boolean;

  @Prop()
  jobHighlighted?: boolean;

  gpuSpecEnumList: any = [];

  EvalSource = EvalSource;

  isJobHighlighted(row) {
    return this.jobHighlighted && row.value !== null && row.value !== undefined && !this.bulkEdit;
  }

  getEnvGlobalParamValue(row) {
    const value = this.globalParamsMap?.get(row.globalAliasName)?.value || row.value || row.defaultValue || '<环境变量>';
    row.defaultValue = value;
    return this.handleSelectValue(row);
  }

  getEnvGlobalParamSource(row) {
    if (!row.globalAliasName || !this.globalParamsMap || !this.globalParamsMap?.get(row.globalAliasName)) return;
    return this.globalParamsMap?.get(row.globalAliasName).source;
  }

  changeSource(source: string, idx: number) {
    if (source !== this.curSource) {
      this.$set(this.contextParamsDefine[idx], `source`, this.curSource);
    }
    if (this.bulkEdit) {
      this.$set(this.contextParamsDefine[idx], 'isBulk', true);
    }
  }

  setDefault(idx: number) {
    const param = this.contextParamsDefine[idx];
    this.$set(param, `value`, null);
    this.$set(param, `source`, param.defaultSource);
    if (this.bulkEdit) this.$set(param, `isBulk`, true);
  }

  setRefer(idx: number) {
    const param = this.contextParamsDefine[idx];
    this.$set(param, 'value', param.referValue);
  }

  setBulkDefault(idx: number) {
    const param = this.contextParamsDefine[idx];
    this.$set(param, 'value', param.bulkDefaultValue === '<多值>' ? null : param.bulkDefaultValue);
    this.$set(param, 'isBulk', false);
  }

  setEmpty(row, idx) {
    row.value = '<empty>';
    this.changeSource(row.source, idx);
  }

  async created() {
    if (!this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList?.length) this.action$modelEvalListEvalDataSizeMeta({});
    this.gpuSpecEnumList = (await postGpuSpecEnumList({})).data.gpuSpecEnumList;
    if (this.globalParamsMap) {
      this.contextParamsDefine.forEach((param) => {
        const globalSettedValue = this.globalParamsMap?.get(param.globalAliasName)?.value;
        if (globalSettedValue) this.$set(param, 'defaultValue', globalSettedValue);
      });
    }
  }

  getCustomTypeOptions(type) {
    switch (type) {
      case 'evalDataSize':
        const options: any = this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList.map((dataSize) => ({
          label: dataSize.label,
          value: dataSize.name,
        }));
        if (this.curSource === EvalSource.GLOBAL_ALIAS_JOB)
          options.unshift({
            label: `<auto>(${options.find((x) => x.value === this.modelMetaSize)?.label || '模型未配置规模'})`,
            value: '-1',
            disabled: !Boolean(this.modelMetaSize),
          });
        return options;
      case 'gpuSpec':
        return this.gpuSpecEnumList?.map((gpuSpec) => ({ label: gpuSpec.label, value: gpuSpec.label }));
      case 'resourceType':
        return [
          { label: 'GPU任务', value: 'gpuJob' },
          { label: 'CPU任务', value: 'cpuJob' },
        ];
      case 'bool':
        return [
          { label: '是', value: 'True' },
          { label: '否', value: 'False' },
        ];
      default:
        return [];
    }
  }

  handleSelectValue(row, isDefault = true) {
    const defaultTypeValue = isDefault
      ? row.bulkDefaultValue === '<多值>'
        ? row.bulkDefaultValue
        : row.referValue || row.defaultValue
      : row.value || row.defaultValue;
    if (['resourceType', 'evalDataSize', 'gpuSpec', 'bool'].includes(row.type)) {
      const options = this.getCustomTypeOptions(row.type);
      const foundOption = options.find((x) => x.value === defaultTypeValue);
      if (foundOption) return foundOption.label;
    }

    return defaultTypeValue;
  }

  copyValue(meg) {
    navigator.clipboard
      .writeText(meg)
      .then(() => {
        this.$mtd.message.success('复制成功');
      })
      .catch((err) => {
        this.$mtd.message.warning('复制失败');
        console.error(err);
      });
  }

  shouldShowGlobalAlias(row) {
    return row.globalAliasType && !this.canEditGlobalAlias;
  }
  getDisplayValue(row) {
    const getTruthValue = (row) => {
      if (row.value === '') return '#空字符串("")';
      if (row.value === 0) return 0;
      return row.value || row.defaultValue;
    };
    return ['string', 'long', 'number'].includes(row.type) ? getTruthValue(row) : this.handleSelectValue(row, false);
  }
  getDefaultValue(row) {
    if (this.bulkEdit) {
      return row.bulkDefaultValue === '<多值>' ? row.bulkDefaultValue : `默认(${row.defaultValue})`;
    }
    if (row.value === '') {
      return '';
    }
    return row.defaultValue;
  }
  truncatedValue(value) {
    return value.length > 100 ? value.slice(0, 100) + '...' : value;
  }
  isLongValue(value) {
    return value.length > 100;
  }
}
</script>
<style lang="scss">
.context-param {
  .context-param-input-number {
    border-top: 0;
    border-left: 0;
    border-right: 0;
    border-radius: 0;
    text-align: left;
    .mtd-input-number {
      background-color: transparent;
    }
  }

  .mtd-input-number-disabled {
    .mtd-input-number {
      background-color: #f5f5f5;
    }
  }
}

.action-buttons {
  display: flex;
  align-items: center;

  .mtd-btn {
    position: relative;
    & + .mtd-btn {
      margin-left: 8px;
      padding-left: 8px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 12px;
        background-color: #e0e0e0;
      }
    }
  }
}
</style>
<style scoped lang="scss">
::v-deep {
  .mtd-input-invalid .mtd-input {
    border-color: rgba(0, 0, 0, 0.12);
    &:hover {
      border-color: #2a8efe;
    }
  }
  .mtd-input-number-invalid.mtd-input-number-wrapper {
    border-color: rgba(0, 0, 0, 0.12);
    &:hover {
      border-color: #2a8efe;
    }
  }
}
::v-deep .mtd-table-cell {
  display: flex;
  align-items: center;
}
.bulk-background {
  background-color: rgb(203, 225, 254);
}
.job-background {
  background-color: rgba(255, 0, 0, 0.3);
}
.not-refer-background {
  background-color: rgba(255, 0, 0, 0.3);
}
</style>
