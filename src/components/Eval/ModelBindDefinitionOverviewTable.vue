<template>
  <mtd-table :data="tableData">
    <mtd-table-column prop="anchorPath" label="锚点路径"> </mtd-table-column>
    <mtd-table-column prop="bindType" width="150" label="绑定类型"> </mtd-table-column>
    <mtd-table-column prop="pluginBindInfo" width="300" label="插件/参数绑定">
      <template #default="{ row }">
        【<mtd-button type="text-primary" @click="$emit('vertex-jump', row)"> {{ row.bindInfo.label }}</mtd-button
        >】
      </template>
    </mtd-table-column>
    <mtd-table-column width="50" v-if="!readonly">
      <template #default="{ row }">
        <mtd-button :disabled="readonly" type="text-primary" @click="$emit('del-item', row)">x</mtd-button>
      </template>
    </mtd-table-column>
  </mtd-table>
</template>

<script lang="ts">
import { Component, Prop, Vue } from '@/decorators';

@Component
export default class ModelBindDefinitionOverviewTable extends Vue {
  @Prop({ required: true })
  tableData!: any[];

  @Prop({ default: false })
  readonly?: boolean;
}
</script>
