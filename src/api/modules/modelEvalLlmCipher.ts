import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1334166, catid=227021, projectId=35802, created=2023-11-29 15:19:39, last-modified=2023-11-30 11:21:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmCipher/decryptFiles)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1334166)
 */
export function postModelEvalLlmCipherDecryptFiles(
  params: model.modelEvalLlmCipher.IPostModelEvalLlmCipherDecryptFilesParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmCipher.IPostModelEvalLlmCipherDecryptFilesResponse>('/webApi/modelEvalLlmCipher/decryptFiles', {
    data: pick(params, ['fileList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1334171, catid=227021, projectId=35802, created=2023-11-29 15:23:35, last-modified=2023-11-30 11:21:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmCipher/decryptText)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1334171)
 */
export function postModelEvalLlmCipherDecryptText(
  params: model.modelEvalLlmCipher.IPostModelEvalLlmCipherDecryptTextParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmCipher.IPostModelEvalLlmCipherDecryptTextResponse>('/webApi/modelEvalLlmCipher/decryptText', {
    data: pick(params, ['encryptedText']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1334195, catid=227021, projectId=35802, created=2023-11-29 15:57:44, last-modified=2023-11-30 11:22:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmCipher/uploadFile)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1334195)
 */
export function postModelEvalLlmCipherUploadFile(
  params: model.modelEvalLlmCipher.IPostModelEvalLlmCipherUploadFileParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmCipher.IPostModelEvalLlmCipherUploadFileResponse>('/webApi/modelEvalLlmCipher/uploadFile', {
    data: pick(params, ['file']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
