<template>
  <div class="statNameUnVersioned">
    <mtd-cascader
      v-model="innerValue"
      :data="options"
      placeholder="请选择指标维度"
      :multiple="false"
      checked-strategy="all"
      :check-strictly="true"
      change-on-select
      style="width: 100%"
      popper-class="custom-cascader-dropdown"
      @change="handleCascader"
      expand-trigger="hover"
      :expandable-on-disabled="true"
      clearable
      :size="size"
      filterable
    />
  </div>
</template>
<script lang="ts">
import { modelEvalData } from '@/api';
import { Component, Prop, PropSync, Spectra, Watch, Weaver } from '@/decorators';
import ModelEval from '@/store/modules/modelEval';
@Component({ components: {} })
@Spectra
export default class StatNameUnVersioned extends Weaver(ModelEval) {
  @PropSync('value', { default: () => [] })
  innerValue!: any;
  @Prop({ default: '' })
  runSpecSetIdFilter!: string;
  @PropSync('dataSubSetIdListFilter', { default: () => [] })
  dataSubSetIdList!: any;
  @Prop({ default: () => {} })
  parList?: any;
  @Prop({ default: false })
  isRunSpecSetIdFilter!: boolean;
  @Prop({ default: false })
  isDataSubSetIdList!: boolean;
  @Prop({ default: false })
  isdisabled!: boolean;
  options: any = [];
  @Prop({ default: '' })
  size!: string;
  mounted() {
    this.getStatNameData(true);
  }

  @Watch('runSpecSetIdFilter', { deep: true })
  async handleStatNameFilter(value) {
    if (this.isRunSpecSetIdFilter) {
      this.getStatNameData(false);
    }
  }

  @Watch('dataSubSetIdList', { deep: true, immediate: true })
  async handleDataSubSetIdList(value) {
    if (this.isDataSubSetIdList) {
      this.getStatNameData(false);
    }
  }

  @Watch('dataSetOptions', { immediate: true })
  handleOptions() {
    this.options = JSON.parse(JSON.stringify(this.dataSetOptions));
  }

  async getModelEvalUnVersionedListStatMeta(flag) {
    await this.action$modelEvalUnVersionedListStatMeta({});

    if (this.innerValue?.length === 0 && flag) {
      // this.innerValue = this.statNameList?.length ? [this.statNameList[0]] : [];
      // this.isDataSubSetIdList && this.$emit('handleDataSet');
    } else {
      const paths = this.findStatNames(this.modelEvalUnVersionedListStatMeta?.statMetaList, this.$props.value);
      if (paths.length) {
        this.innerValue = paths;
      } else {
        this.innerValue = [];
      }
    }
  }

  findStatNames(list, statNames) {
    const paths: any = [];
    list.forEach((item) => {
      if (statNames?.length === 1) {
        if (item.name === statNames[0]) {
          paths.push(statNames[0]);
        }
        item.children?.length &&
          item.children.forEach((i) => {
            if (i.name === statNames[0]) {
              paths.push(item.name);
              paths.push(i.name);
            }
          });
      }

      if (statNames?.length === 2) {
        if (item.name === statNames[0]) {
          paths.push(statNames[0]);
        }
        item.children?.length &&
          item.children.forEach((i) => {
            if (i.name === statNames[1]) {
              paths.push(i.name);
            }
          });
      }
    });
    return paths;
  }

  get dataSetOptions() {
    try {
      if (!this.modelEvalUnVersionedListStatMeta.statMetaList) return [];
      const list =
        this.modelEvalUnVersionedListStatMeta?.statMetaList?.map((item: any) => ({
          ...item,
          children:
            item.children?.map((i) => ({
              ...i,
              value: i.name,
              label: i.label || i.name,
              isLeaf: true,
              disabled: this.getDisabledOptions(i),
            })) || [],
          value: item.name,
          label: item.label || item.name,
          isLeaf: false,
          disabled: this.getDisabledOptions(item),
        })) ?? [];

      if (this.parList) this.parList(list);
      return list;
    } catch (err) {
      console.log(err);
      return [];
    }
  }

  getDisabledOptions(i) {
    const name = i.name;
    const winRateFlag = i?.winRateFlag;
    if (this.isdisabled && winRateFlag) {
      return true;
    }

    const index = this.statNameList.findIndex((item) => item === name);
    if (index === -1) {
      return true;
    }
    return false;
  }
  handleCascader(value, selectedOptions) {
    this.$emit('handleChange', value, selectedOptions);
    this.isDataSubSetIdList && this.$emit('handleDataSet');
  }

  statNameList: any = [];
  async getStatNameData(flag) {
    try {
      if (this.isRunSpecSetIdFilter || this.isDataSubSetIdList) {
        await this.action$benchmarkNoVersioned({ keywordFilter: '', offset: 0, limit: 1000000, statNameFilter: '' });
        const runSpecSetList = this.benchmarkNoVersioned?.runSpecSetList || [];
        const runSpecIndex = runSpecSetList.findIndex((item) => item.id === Number(this.runSpecSetIdFilter));
        const res = await modelEvalData.postModelEvalDataListStatName({
          runSpecSetIdFilter: runSpecIndex > -1 ? this.runSpecSetIdFilter : '',
          dataSubSetIdListFilter: this.dataSubSetIdList.map((item) => item?.id || item),
        });
        this.statNameList = res.data.statNameList;
        this.getModelEvalUnVersionedListStatMeta(flag);
      } else {
        const res = await modelEvalData.postModelEvalDataListStatName({
          runSpecSetIdFilter: '',
          dataSubSetIdListFilter: [],
        });
        this.statNameList = res.data.statNameList;
        this.getModelEvalUnVersionedListStatMeta(flag);
      }
    } catch (e) {
      console.log(e);
    }
  }
}
</script>
<style lang="scss">
.statNameUnVersioned {
  line-height: 20px;
  width: 100%;
}
// .custom-cascader-dropdown {
//   z-index: 2005 !important; /* 设置较高的 z-index 值 */
// }
</style>
