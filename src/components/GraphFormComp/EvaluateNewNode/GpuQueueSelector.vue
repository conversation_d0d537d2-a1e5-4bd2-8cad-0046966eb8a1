<template>
  <div class="common-width">
    <mtd-select
      style="width: calc(100% - 100px)"
      v-if="isEdit"
      :value="model"
      :filterable="true"
      popper-class="benchmark-select"
      @change="change"
      clearable
    >
      <mtd-option v-for="v in optionList" :key="v.queue" :label="v.queue" :value="v.queue">
        <div class="benchmark-select-option">
          <div class="benchmark-select-option-name">
            <span>
              {{ v.queue }}
            </span>
          </div>
        </div>
      </mtd-option>
    </mtd-select>
    <span v-else>{{ model }}</span>
    <mtd-button type="text-primary" v-if="model" :href="genMlpUrl()" target="_blank"
      >查看资源
      <mtd-icon name="square-stack-up" />
    </mtd-button>
    <div v-if="configCovered" style="color: red; font-size: 12px">注意：执行计划【{{ configCovered }}】集群的队列覆盖了此默认配置</div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Watch, Weaver } from '@/decorators';
import modelManage from '@/store/modules/modelManage';

@Component
@Spectra
export default class GpuQueueSelector extends Weaver(modelManage) {
  @Prop()
  model!: any;
  @Prop()
  globalContext: any;
  @Prop()
  isEdit!: boolean;
  @Prop()
  context!: any;

  benchMark: any;
  change(v) {
    this.$emit('change', v);
  }

  created() {
    this.action$gpuQueueList({ projectGroup: this.experimentBaseInfo?.experimentConfig?.projectGroup });
  }

  get optionList() {
    return this.gpuQueueList || [];
  }

  configCovered = '';

  @Watch('context.nodeParam.submitInfoList', { immediate: true })
  configChange(v) {
    this.configCovered = v
      ?.filter((config) => config.resourceConfigList?.some((resource) => this.isOverride(resource)))
      .map((x) => x.flowName)
      .join('、');
  }

  isOverride(resource) {
    return resource.resourceType === 'gpuJob' && resource.consumerDefineList?.[0].vertexDefineId === 2 && resource.queue;
  }

  genMlpUrl() {
    return `https://mlp.sankuai.com/ml/#/resourceManage/childQueueDetail?queueName=${this.model}&cluster=${this.model.split('.')[1]}`;
  }
}
</script>
<style lang="scss"></style>
