import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1425760, catid=240661, projectId=35802, created=2024-06-21 16:02:53, last-modified=2024-07-01 16:32:16
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLogUnVersioned/getDataSubSetLog)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425760)
 */
export function postModelEvalLogUnVersionedGetDataSubSetLog(
  params: model.modelEvalLogUnVersioned.IPostModelEvalLogUnVersionedGetDataSubSetLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLogUnVersioned.IPostModelEvalLogUnVersionedGetDataSubSetLogResponse>(
    '/webApi/modelEvalLogUnVersioned/getDataSubSetLog',
    {
      data: pick(params, ['dataSubSetIdList', 'actionType', 'userMis', 'startTime', 'endTime', 'sortConfig', 'offset', 'limit']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1425759, catid=240661, projectId=35802, created=2024-06-21 15:59:23, last-modified=2024-06-21 16:02:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLogUnVersioned/getCategoryLog)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425759)
 */
export function postModelEvalLogUnVersionedGetCategoryLog(
  params: model.modelEvalLogUnVersioned.IPostModelEvalLogUnVersionedGetCategoryLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLogUnVersioned.IPostModelEvalLogUnVersionedGetCategoryLogResponse>(
    '/webApi/modelEvalLogUnVersioned/getCategoryLog',
    {
      data: pick(params, [
        'type',
        'categoryIdsFilter',
        'subSetTags',
        'statsFilter',
        'actionType',
        'userMis',
        'startTime',
        'endTime',
        'sortConfig',
        'offset',
        'limit',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
