<template>
  <div>
    <!-- <mtd-button @click="log">log</mtd-button> -->
    <mtd-loading v-if="loading" />
    <mtd-tree v-else :data="data" :load-data="loadChildren" node-key="anchorPath" :default-expand-all="true">
      <template #default="{ data }">
        <span v-if="data.type === TreeNodeType.ANCHOR">
          {{ anchorMap.get(data.anchorPath)?.label }}
          <span @click.stop>
            <anchor-bind-definition
              :ref="`${data.anchorPath}-bind`"
              :defaultAnchorDefine="getAnchor(data.anchorPath)"
              :source="source"
              :forceCover="Boolean(data.anchorInfo?.forceCover)"
              :disabled="getDisableCase(data)"
              :snapshot="Boolean(snapshot)"
              @update="updateNode"
            />
          </span>
        </span>
        <span v-else> &lt;{{ data.implInfo.order }}&gt;<source-viewer :label="data.implInfo.label" :source="data.implInfo.source" /> </span>
      </template>
    </mtd-tree>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Watch, Weaver } from '@/decorators';
import { AnchorDefine, BindType, PluginBind, TreeNode, TreeNodeType, VertexDefine } from '@/model/customEval';
import evalCustomPlugin from '@/store/modules/evalCustomPlugin';
import evalFlowVertexDefine from '@/store/modules/evalFlowVertexDefine';
import { EvalSource } from '@/utils';
import { handleSourceExtends } from '@/views/modelEval/utils';
import { cloneDeep } from 'lodash';
import AnchorBindDefinition from './AnchorBindDefinition.vue';
import SourceViewer from './SourceViewer.vue';

@Component({
  components: {
    AnchorBindDefinition,
    SourceViewer,
  },
})
@Spectra
export default class NodeBindDefinition extends Weaver(evalCustomPlugin, evalFlowVertexDefine) {
  data: TreeNode[] = [];
  TreeNodeType = TreeNodeType;
  rootPluginBindMap: Map<string, PluginBind[]> = new Map();
  public anchorMap: Map<string, AnchorDefine> = new Map();
  @Prop()
  id?: number;
  @Prop()
  source!: string;
  @Prop()
  initData?: VertexDefine;
  @Prop()
  defaultExpandPath?: string;
  @Prop()
  globalDisabledPath?: string[];
  @Prop({ default: false })
  disabled?: boolean;
  @Prop()
  snapshot?: any;

  loading = true;
  hasInit = false;
  mounted() {
    this.initConsumerAnchors();
  }
  // 获取最终绑定的节点列表
  getPluginBindList() {
    const res: PluginBind[] = [];
    for (const [k, bindList] of this.rootPluginBindMap.entries()) {
      // 如果传进来了，但是没在这个节点展开，原样传出去
      if (!this.anchorMap.get(k)) {
        res.push(...bindList);
      }
    }

    for (const anchor of this.anchorMap.values()) {
      const cloneAnchor = cloneDeep(anchor);
      res.push(
        ...cloneAnchor.pluginBindList
          .filter((anchorBind) => anchorBind.bindType)
          .map((anchorBind) => {
            // 如果是参数绑定且在当前层级有一个参数，那么就需要把它设置成当前层级
            if (anchorBind.bindType === BindType.PARAM && anchorBind.contextParams.find((param) => param.source === this.source)) {
              anchorBind.source = this.source;
            }

            return {
              ...anchorBind,
              anchorPath: anchor.anchorPath!,
            };
          })
          .filter((anchorBind) => anchorBind.source === this.source)
      );
    }

    return res;
  }

  @Watch('initData.anchor')
  @Watch('initData.consumerDefineList', { deep: true })
  initDataChanged() {
    this.anchorMap.clear();
    this.initConsumerAnchors();
  }

  log() {
    console.log(this.anchorMap, this.rootPluginBindMap, this.data);
  }

  async initConsumerAnchors() {
    this.loading = true;
    let { initData } = this;

    if (this.id) {
      const res = await this.action$evalFlowVertexDefine({ id: this.id });
      initData = res.evalFlowVertexDefine as any;
    }

    if (!initData) {
      this.$mtd.message.error('没数据啊');
      return;
    }

    const { anchor, consumerDefineList, pluginBindList } = cloneDeep(initData);

    const rootAnchorList: AnchorDefine[] = [];
    consumerDefineList.forEach((consumer) => {
      const anchorPath = `${anchor}/${consumer.rootAnchor}`;

      // 在给评测执行单元绑定插件时，不允许在 API 类型的消费者上进行绑定。
      if (this.source === EvalSource.RUN_SPEC && consumer.servingType === 'API') {
        this.disabledRootPath.push(anchorPath);
      }

      const consumerAnchor = {
        anchor: consumer.rootAnchor,
        anchorPath,
        label: consumer.label,
        requirePluginInterface: consumer.rootPluginInterface,
        requirePluginInterfaceLabel: consumer.rootPluginInterfaceLabel,
        allowMultipleBind: 'NO',
        pluginBindList: [],
      };
      rootAnchorList.push(consumerAnchor);
    });
    this.rootPluginBindMap = new Map();
    pluginBindList.forEach((pluginBind) => {
      if (!this.rootPluginBindMap.get(pluginBind.anchorPath!)) this.rootPluginBindMap.set(pluginBind.anchorPath!, []);
      this.rootPluginBindMap.get(pluginBind.anchorPath!)?.push(pluginBind);
    });

    await this.generateTreeRoot(rootAnchorList);
    this.loading = false;
    if (this.defaultExpandPath && !this.hasInit) {
      setTimeout(() => (this.$refs[`${this.defaultExpandPath}-bind`] as AnchorBindDefinition)?.openModal(), 100);
    }

    this.hasInit = true;
  }

  getAnchor(anchorPath: string) {
    return cloneDeep(this.anchorMap.get(anchorPath));
  }

  getSnapShotImpl(interfaceName: string, implementName: string) {
    return this.snapshot?.find((impl) => impl.interfaceName === interfaceName && impl.implementName === implementName);
  }

  async generateTreeRoot(rootAnchorList: AnchorDefine[]) {
    const res = rootAnchorList.map(async (item) => {
      const implList = [...(this.rootPluginBindMap.get(item.anchorPath!) || [])];
      const { pluginBindList, prevPluginBindList } = handleSourceExtends(implList, this.source);
      item.pluginBindList = pluginBindList;
      item.prevPluginBindList = prevPluginBindList;
      const children = await this.generateAnchorNodeChildren(item);
      const node = {
        type: TreeNodeType.ANCHOR,
        anchorPath: item.anchorPath!,
        children,
        isLeaf: true,
        anchorInfo: {
          forceCover: this.source === EvalSource.NODE_DEFINE,
        },
      };
      item.bindNode = node;
      this.anchorMap.set(item.anchorPath!, item);

      return node;
    });

    this.data = await Promise.all(res);
  }

  async generateAnchorNodeChildren(anchor: AnchorDefine): Promise<TreeNode[] | undefined> {
    const { pluginBindList } = anchor;
    if (!pluginBindList.length) return undefined;
    if (pluginBindList.length === 1) {
      // 单层 下一级直接是子锚点
      return this.generateImplNodeChildren(pluginBindList[0], anchor);
    }

    // 双层，实现+子锚点
    const children: TreeNode[] = [];
    await Promise.all(
      pluginBindList.map(async (impl, idx) => {
        if (impl.extra?.isSymmetricDifference) return;

        const implDefine =
          this.getSnapShotImpl(anchor.requirePluginInterface, impl.pluginImplement!) ||
          cloneDeep(
            await this.action$evalCustomPluginImplementDefine({
              interfaceName: anchor.requirePluginInterface,
              implementName: impl.pluginImplement!,
            })
          ).evalCustomPluginImplement;
        impl.pluginImplementLabel = implDefine.implementLabel;
        children.push({
          type: TreeNodeType.IMPL,
          anchorPath: `${anchor.anchorPath}@${impl.pluginImplement}`,
          implInfo: {
            source: impl.source,
            label: String(impl.pluginImplementLabel || impl.pluginImplement),
            order: idx + 1,
          },
          children: await this.generateImplNodeChildren(impl, anchor),
          isLeaf: true,
        });
      })
    );
    children.sort((a, b) => a.implInfo!.order - b.implInfo!.order);
    return children;
  }
  async generateImplNodeChildren(impl: PluginBind, parentAnchor: AnchorDefine) {
    const children: TreeNode[] = [];

    if (impl.extra?.isSymmetricDifference) return children;

    const implInfo =
      cloneDeep(this.getSnapShotImpl(parentAnchor.requirePluginInterface, impl.pluginImplement!)) ||
      cloneDeep(
        await this.action$evalCustomPluginImplementDefine({
          implementName: impl.pluginImplement!,
          interfaceName: parentAnchor.requirePluginInterface,
        })
      ).evalCustomPluginImplement;

    impl.pluginImplementLabel = implInfo.implementLabel;

    // @ts-ignore
    for (const subAnchor of implInfo.requireAnchorList as AnchorDefine[]) {
      const anchorPath = `${parentAnchor.anchorPath}@${implInfo.implementName}/${subAnchor.anchor}`;
      const implList = [...(this.rootPluginBindMap.get(anchorPath) || []), ...(subAnchor.pluginBindList || [])];
      const { pluginBindList, prevPluginBindList } = handleSourceExtends(implList, this.source);
      const checkHasSubAnchor = async (subImpl: PluginBind) => {
        const subImplDefine =
          this.getSnapShotImpl(subAnchor.requirePluginInterface, subImpl.pluginImplement!) ||
          (
            await this.action$evalCustomPluginImplementDefine({
              interfaceName: subAnchor.requirePluginInterface,
              implementName: subImpl.pluginImplement!,
            })
          ).evalCustomPluginImplement;
        return !subImplDefine.requireAnchorList?.length;
      };

      const isLeaf = pluginBindList.length === 1 && !implList[0].extra?.isSymmetricDifference && (await checkHasSubAnchor(implList[0]));
      const node: TreeNode = {
        type: TreeNodeType.ANCHOR,
        anchorPath,
        isLeaf,
        children: [],
      };
      subAnchor.anchorPath = anchorPath;
      subAnchor.bindNode = node;
      subAnchor.pluginBindList = pluginBindList;
      subAnchor.prevPluginBindList = prevPluginBindList;
      if (this.defaultExpandPath?.startsWith(anchorPath)) {
        node.children = (await this.generateAnchorNodeChildren(subAnchor)) || [];
      }

      this.anchorMap.set(anchorPath, subAnchor);
      children.push(node);
    }

    return children;
  }

  async loadChildren(node, callback) {
    const {
      data: { anchorPath },
    } = node;
    const anchor = this.anchorMap.get(anchorPath);
    anchor && callback(await this.generateAnchorNodeChildren(anchor));
  }

  async updateNode({ anchor, changedBindType, currBindType }: { anchor: AnchorDefine; changedBindType: boolean; currBindType: string }) {
    if (this.disabled) return;
    this.loading = true;
    let oriNode;
    const remainedPluginAnchorPath = anchor.pluginBindList.map((x) => anchor.anchorPath! + '@' + x.pluginImplement);

    for (const [k, v] of this.anchorMap.entries()) {
      if (k === anchor.anchorPath) oriNode = v.bindNode;
      // 如果是子节点，不在新的绑定当中或者改变了绑定方式的，也删除
      if (k.startsWith(`${anchor.anchorPath}@`) && (changedBindType || !remainedPluginAnchorPath.find((path) => k.startsWith(path!)))) {
        this.anchorMap.delete(k);
        // 子rootPluginBindMap删除当前层级的绑定
        const oriBindList = this.rootPluginBindMap.get(k);
        if (oriBindList)
          this.rootPluginBindMap.set(
            k,
            oriBindList.filter((x) => x.source !== this.source)
          );
      }
    }

    anchor.bindNode = oriNode;
    this.anchorMap.set(anchor.anchorPath!, anchor);
    this.rootPluginBindMap.set(anchor.anchorPath!, anchor.pluginBindList);
    const newChildren = await this.generateAnchorNodeChildren(anchor);
    this.$set(oriNode, 'children', newChildren);
    this.loading = false;
    this.$emit('nodeUpdated', { anchor, changedBindType, currBindType });
  }

  disabledRootPath: string[] = [];
  getDisableCase(data: TreeNode) {
    if (this.disabledRootPath.find((path) => data.anchorPath.startsWith(path))) {
      return true;
    }

    if (this.globalDisabledPath?.find((path) => data.anchorPath.startsWith(path))) {
      return '当前节点在全局已进行过绑定，无法单独在执行单元中修改';
    }

    return this.disabled;
  }
}
</script>
<style lang="scss"></style>
