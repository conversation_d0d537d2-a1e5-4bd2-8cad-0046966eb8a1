import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1338415, catid=233466, projectId=35802, created=2023-12-06 17:22:11, last-modified=2024-01-31 14:28:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/chat/killWorker)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1338415)
 */
export function postChatKillWorker(params: model.chat.IPostChatKillWorkerParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.chat.IPostChatKillWorkerResponse>('/webApi/chat/killWorker', {
    data: pick(params, ['workerId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1338407, catid=233466, projectId=35802, created=2023-12-06 17:22:11, last-modified=2024-01-31 14:28:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/chat/generate)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1338407)
 */
export function postChatGenerate(params: model.chat.IPostChatGenerateParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.chat.IPostChatGenerateResponse>('/webApi/chat/generate', {
    data: pick(params, ['parameter', 'promptPrefix', 'promptSuffix', 'prompts', 'systemPromptPrefix', 'systemPromptSuffix', 'workerId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1338417, catid=233466, projectId=35802, created=2023-12-06 17:22:11, last-modified=2024-01-31 14:28:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/chat/listWorker)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1338417)
 */
export function getChatListWorker(params: model.chat.IGetChatListWorkerParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.chat.IGetChatListWorkerResponse>('/webApi/chat/listWorker', {
    params: pick(params, ['modelId', 'namePiece']),
    ...options,
  });
}

/**
 * @desc id=1338399, catid=233466, projectId=35802, created=2023-12-06 17:22:11, last-modified=2024-01-31 14:28:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/chat/extendExpireTime)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1338399)
 */
export function postChatExtendExpireTime(params: model.chat.IPostChatExtendExpireTimeParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.chat.IPostChatExtendExpireTimeResponse>('/webApi/chat/extendExpireTime', {
    data: pick(params, ['extendTime', 'workerId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
