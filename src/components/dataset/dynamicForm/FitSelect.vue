<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';

@Component({})
export default class FitSelect extends Vue {
  @Prop() formItem!: any;
}
</script>

<template>
  <div class="sum-threshold-label">
    <div class="fitting-type-container">
      <div class="sum-threshold-require">{{ ' 拟合算子：' }}</div>
      <mtd-select v-model="formItem.fittingType" placeholder="请选择拟合算子">
        <mtd-option value="equal" label="相等" />
        <mtd-option v-if="['el-checkbox-group'].includes(formItem.formItemType)" value="contain" label="包含" />
        <mtd-option value="none" label="无" />
        <mtd-option v-if="['el-checkbox-group'].includes(formItem.formItemType)" value="intersection" label="交集" />
      </mtd-select>
    </div>
    <div class="fitting-value-container">
      <div v-if="formItem.fittingType === 'intersection'" class="sum-threshold-require sum-threshold">
        拟合阈值>= <mtd-input-number v-model="formItem.fittingValue" :min="1" :step="1" :controls="false" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.sum-threshold-label {
  flex-basis: 100%;
}
.sum-threshold-require::before {
  content: '*';
  color: #f5483b;
  display: inline-block;
  margin-right: 4px;
}
.sum-threshold .mtd-input-number-wrapper {
  margin-left: 8px;
}
.fitting-type-container {
  display: flex;
}
.fitting-value-container {
  margin-top: 8px;
}
</style>
