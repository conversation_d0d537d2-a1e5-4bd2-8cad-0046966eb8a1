import { TableData } from './ExpLR';

export const defaultTableConfig = {
  resizable: true,
  useSticky: true,
  bordered: 'cell' as const,
};

export const mergeTableConfig = (config: Record<string, any>) => ({
  ...defaultTableConfig,
  ...config,
});

// export const autoMergeRow = (data: TableData): TableData['data'] => {
//     const { rowHeaderKeys = [], data: tableData = [] } = data || {};
//     const newTableData = tableData.map((row) => Object.fromEntries(Object.entries(row).map(([key, cell]) => [key, { ...cell, rowSpan: 1, colSpan: 1 }])));
//     // rowHeaderKeys前端去重
//     const newRowHeaderKeys = rowHeaderKeys.reduce(
//         (prev, header) => (prev?.includes(header) ? prev : [...prev, header]),
//         [] as string[]
//     );
//   newRowHeaderKeys.forEach((headerKey, index) => {
//     const parentHeaderKey = index === 0 ? '' : rowHeaderKeys[index - 1];
//     const { length } = newTableData;
//     // 倒序遍历tableData
//     for (let i = length - 1; i > 0; i--) {
//       const shouldMerge = newTableData[i]
//                 && newTableData[i][headerKey]
//                 && newTableData[i - 1]
//                 && newTableData[i - 1][headerKey]
//                 && newTableData[i][headerKey]?.value === newTableData[i - 1][headerKey]?.value &&
//                 (parentHeaderKey
//                   ? newTableData[i][parentHeaderKey] && newTableData[i][parentHeaderKey]?.rowSpan === 0
//                   : true);
//       if (shouldMerge) {
//         newTableData[i - 1][headerKey].rowSpan += newTableData[i][headerKey].rowSpan;
//         newTableData[i][headerKey].rowSpan = 0;
//       }
//     }
//   });

//   return newTableData;
// };

export const autoMergeRow = (data: TableData): TableData['data'] => {
  const { data: tableData = [] } = data || {};
  const newTableData = tableData.map((row) =>
    Object.fromEntries(Object.entries(row).map(([key, cell]) => [key, { ...cell, rowSpan: 1, colSpan: 1 }]))
  );
  // rowHeaderKeys前端去重
  const rowHeaderKeys = Object.keys(newTableData[0]);
  const newRowHeaderKeys = rowHeaderKeys.reduce((prev, header) => (prev?.includes(header) ? prev : [...prev, header]), [] as string[]);
  newRowHeaderKeys.forEach((headerKey, index) => {
    const parentHeaderKey = index === 0 ? '' : newRowHeaderKeys[index - 1];
    const { length } = newTableData;
    // 倒序遍历tableData
    for (let i = length - 1; i > 0; i--) {
      const shouldMerge =
        newTableData[i] &&
        newTableData[i][headerKey] &&
        newTableData[i - 1] &&
        newTableData[i - 1][headerKey] &&
        newTableData[i][headerKey]?.value === newTableData[i - 1][headerKey]?.value &&
        (parentHeaderKey ? newTableData[i][headerKey] && newTableData[i][headerKey]?.rowSpan >= 1 : true);
      if (shouldMerge) {
        newTableData[i - 1][headerKey].rowSpan += newTableData[i][headerKey].rowSpan;
        newTableData[i][headerKey].rowSpan = 0;
      }
    }
  });

  const result = [newTableData[newTableData.length - 1]];
  const allRowSpansZero = areAllRowSpansZero(result);

  if (allRowSpansZero) {
    newRowHeaderKeys.forEach((item) => {
      newTableData[newTableData.length - 1][item].rowSpan = 1;
    });
  }

  return newTableData;
};

function areAllRowSpansZero(data) {
  for (const column in data[0]) {
    const allZeroRowSpans = data.every((row) => row[column].rowSpan === 0);
    if (!allZeroRowSpans) {
      return false;
    }
  }

  return true;
}
