import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1296848, catid=227025, projectId=35802, created=2023-10-13 19:27:58, last-modified=2024-08-05 09:14:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCustomReport/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296848)
 */
export function postModelEvalCustomReportList(
  params: model.modelEvalCustomReport.IPostModelEvalCustomReportListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCustomReport.IPostModelEvalCustomReportListResponse>('/webApi/modelEvalCustomReport/list', {
    data: pick(params, ['typeFilter', 'keyword', 'sortByName', 'category', 'offset', 'limit', 'statusFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1414050, catid=227025, projectId=35802, created=2024-05-27 11:34:49, last-modified=2024-08-05 09:18:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCustomReport/copy)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1414050)
 */
export function postModelEvalCustomReportCopy(
  params: model.modelEvalCustomReport.IPostModelEvalCustomReportCopyParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCustomReport.IPostModelEvalCustomReportCopyResponse>('/webApi/modelEvalCustomReport/copy', {
    data: pick(params, ['id', 'name', 'category']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1344227, catid=227025, projectId=35802, created=2023-12-15 14:29:45, last-modified=2024-08-05 11:08:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCustomReport/modelEvalSubSetStatDataDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344227)
 */
export function postModelEvalCustomReportModelEvalSubSetStatDataDetail(
  params: model.modelEvalCustomReport.IPostModelEvalCustomReportModelEvalSubSetStatDataDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCustomReport.IPostModelEvalCustomReportModelEvalSubSetStatDataDetailResponse>(
    '/webApi/modelEvalCustomReport/modelEvalSubSetStatDataDetail',
    {
      data: pick(params, ['model', 'dataSubSetId', 'evalDataSize']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1296852, catid=227025, projectId=35802, created=2023-10-13 19:27:58, last-modified=2023-11-21 14:38:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCustomReport/listImportModule)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296852)
 */
export function postModelEvalCustomReportListImportModule(
  params: model.modelEvalCustomReport.IPostModelEvalCustomReportListImportModuleParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCustomReport.IPostModelEvalCustomReportListImportModuleResponse>(
    '/webApi/modelEvalCustomReport/listImportModule',
    {
      data: pick(params, ['moduleNameList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1296862, catid=227025, projectId=35802, created=2023-10-13 19:27:58, last-modified=2024-08-05 09:19:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCustomReport/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296862)
 */
export function postModelEvalCustomReportUpsert(
  params: model.modelEvalCustomReport.IPostModelEvalCustomReportUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCustomReport.IPostModelEvalCustomReportUpsertResponse>('/webApi/modelEvalCustomReport/upsert', {
    data: pick(params, [
      'id',
      'name',
      'category',
      'type',
      'ownerList',
      'queryConditionList',
      'importModuleList',
      'script',
      'dataOutputList',
      'upstreamList',
      'cacheSwitch',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296866, catid=227025, projectId=35802, created=2023-10-13 19:27:58, last-modified=2023-11-21 14:38:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCustomReport/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296866)
 */
export function postModelEvalCustomReportDelete(
  params: model.modelEvalCustomReport.IPostModelEvalCustomReportDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCustomReport.IPostModelEvalCustomReportDeleteResponse>('/webApi/modelEvalCustomReport/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296899, catid=227025, projectId=35802, created=2023-10-13 19:27:59, last-modified=2024-08-15 15:18:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCustomReport/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296899)
 */
export function postModelEvalCustomReportGet(
  params: model.modelEvalCustomReport.IPostModelEvalCustomReportGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCustomReport.IPostModelEvalCustomReportGetResponse>('/webApi/modelEvalCustomReport/get', {
    data: pick(params, ['id', 'category']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296906, catid=227025, projectId=35802, created=2023-10-13 19:27:59, last-modified=2023-11-21 14:38:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCustomReport/setStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296906)
 */
export function postModelEvalCustomReportSetStatus(
  params: model.modelEvalCustomReport.IPostModelEvalCustomReportSetStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCustomReport.IPostModelEvalCustomReportSetStatusResponse>('/webApi/modelEvalCustomReport/setStatus', {
    data: pick(params, ['id', 'status']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1349316, catid=227034, projectId=35802, created=2024-01-02 15:46:39, last-modified=2024-01-02 15:48:34
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalCustomReport/listDataSetStatusMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1349316)
 */
export function postModelEvalCustomReportListDataSetStatusMeta(
  params: model.modelEvalCustomReport.IPostModelEvalCustomReportListDataSetStatusMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalCustomReport.IPostModelEvalCustomReportListDataSetStatusMetaResponse>(
    '/webApi/modelEvalCustomReport/listDataSetStatusMeta',
    {
      data: params,
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
