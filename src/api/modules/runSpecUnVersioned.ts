import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1418859, catid=227021, projectId=35802, created=2024-06-10 10:35:07, last-modified=2024-06-26 11:20:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecUnVersioned/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418859)
 */
export function postRunSpecUnVersionedList(
  params: model.runSpecUnVersioned.IPostRunSpecUnVersionedListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecUnVersioned.IPostRunSpecUnVersionedListResponse>('/webApi/runSpecUnVersioned/list', {
    data: pick(params, ['dataSubSetIdList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
