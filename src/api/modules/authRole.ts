import * as model from '@/model';
import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import http from '../http';

/**
 * @desc id=1276323, catid=224565, projectId=35802, created=2023-09-18 17:22:21, last-modified=2023-12-04 16:56:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/authRole/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1276323)
 */
export function postAuthRoleList(params: model.authRole.IPostAuthRoleListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.authRole.IPostAuthRoleListResponse>('/webApi/authRole/list', {
    data: pick(params, ['keyword', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1276332, catid=224565, projectId=35802, created=2023-09-18 17:22:21, last-modified=2023-12-04 16:58:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/authRole/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1276332)
 */
export function postAuthRoleUpsert(params: model.authRole.IPostAuthRoleUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.authRole.IPostAuthRoleUpsertResponse>('/webApi/authRole/upsert', {
    data: pick(params, ['type', 'authRoleName', 'authNickName', 'memberList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1276340, catid=224565, projectId=35802, created=2023-09-18 17:22:22, last-modified=2023-11-21 14:37:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/authRole/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1276340)
 */
export function postAuthRoleDelete(params: model.authRole.IPostAuthRoleDeleteParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.authRole.IPostAuthRoleDeleteResponse>('/webApi/authRole/delete', {
    data: pick(params, ['authRoleName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1276337, catid=224565, projectId=35802, created=2023-09-18 17:22:22, last-modified=2023-12-04 16:57:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/authRole/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1276337)
 */
export function postAuthRoleGet(params: model.authRole.IPostAuthRoleGetParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.authRole.IPostAuthRoleGetResponse>('/webApi/authRole/get', {
    data: pick(params, ['authRoleName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
