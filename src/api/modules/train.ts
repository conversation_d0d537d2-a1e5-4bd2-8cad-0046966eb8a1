import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1433211, catid=238704, projectId=35802, created=2024-07-08 11:20:58, last-modified=2024-08-08 11:06:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/addDocker)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433211)
 */
export function getTrainExperimentAddDocker(params: model.train.IGetTrainExperimentAddDockerParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.train.IGetTrainExperimentAddDockerResponse>('/webApi/train/experiment/addDocker', {
    params: pick(params, ['dockerForTensorboard', 'dockerIpList', 'multiTensorboard']),
    ...options,
  });
}

/**
 * @desc id=1375893, catid=238704, projectId=35802, created=2024-03-05 11:25:47, last-modified=2024-08-08 11:06:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/saveReport)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1375893)
 */
export function postTrainExperimentSaveReport(
  params: model.train.IPostTrainExperimentSaveReportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.train.IPostTrainExperimentSaveReportResponse>('/webApi/train/experiment/saveReport', {
    data: pick(params, ['experimentIds', 'reportContent', 'reportId', 'reportName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1433237, catid=238704, projectId=35802, created=2024-07-08 11:21:57, last-modified=2024-08-08 11:06:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/removeDocker)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433237)
 */
export function getTrainExperimentRemoveDocker(
  params: model.train.IGetTrainExperimentRemoveDockerParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.train.IGetTrainExperimentRemoveDockerResponse>('/webApi/train/experiment/removeDocker', {
    params: pick(params, ['dockerIpList']),
    ...options,
  });
}

/**
 * @desc id=1433249, catid=238704, projectId=35802, created=2024-07-08 11:22:09, last-modified=2024-08-08 11:06:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/unlinkTensorboard)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433249)
 */
export function getTrainExperimentUnlinkTensorboard(
  params: model.train.IGetTrainExperimentUnlinkTensorboardParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.train.IGetTrainExperimentUnlinkTensorboardResponse>('/webApi/train/experiment/unlinkTensorboard', {
    params: pick(params, ['modelMetaId']),
    ...options,
  });
}

/**
 * @desc id=1433228, catid=238704, projectId=35802, created=2024-07-08 11:21:37, last-modified=2024-08-08 11:06:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/launchTensorboard)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433228)
 */
export function getTrainExperimentLaunchTensorboard(
  params: model.train.IGetTrainExperimentLaunchTensorboardParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.train.IGetTrainExperimentLaunchTensorboardResponse>('/webApi/train/experiment/launchTensorboard', {
    params: pick(params, ['dockerIp', 'port']),
    ...options,
  });
}

/**
 * @desc id=1452667, catid=238704, projectId=35802, created=2024-08-06 17:35:36, last-modified=2024-08-08 11:06:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/enableOrNotDocker)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1452667)
 */
export function getTrainExperimentEnableOrNotDocker(
  params: model.train.IGetTrainExperimentEnableOrNotDockerParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.train.IGetTrainExperimentEnableOrNotDockerResponse>('/webApi/train/experiment/enableOrNotDocker', {
    params: pick(params, ['dockerIpList', 'enable']),
    ...options,
  });
}

/**
 * @desc id=1433220, catid=238704, projectId=35802, created=2024-07-08 11:21:35, last-modified=2024-08-08 11:06:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/killTensorboard)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433220)
 */
export function getTrainExperimentKillTensorboard(
  params: model.train.IGetTrainExperimentKillTensorboardParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.train.IGetTrainExperimentKillTensorboardResponse>('/webApi/train/experiment/killTensorboard', {
    params: pick(params, ['dockerIp', 'port']),
    ...options,
  });
}

/**
 * @desc id=1375877, catid=238704, projectId=35802, created=2024-03-05 11:25:47, last-modified=2024-08-08 11:06:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/listReport)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1375877)
 */
export function postTrainExperimentListReport(
  params: model.train.IPostTrainExperimentListReportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.train.IPostTrainExperimentListReportResponse>('/webApi/train/experiment/listReport', {
    data: pick(params, ['curPage', 'owner', 'reportName', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1375884, catid=238704, projectId=35802, created=2024-03-05 11:25:47, last-modified=2024-08-08 11:06:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/reportDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1375884)
 */
export function postTrainExperimentReportDetail(
  params: model.train.IPostTrainExperimentReportDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.train.IPostTrainExperimentReportDetailResponse>('/webApi/train/experiment/reportDetail', {
    data: pick(params, ['reportId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1433234, catid=238704, projectId=35802, created=2024-07-08 11:21:53, last-modified=2024-08-08 11:06:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/moveTensorboardToMultiDocker)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433234)
 */
export function getTrainExperimentMoveTensorboardToMultiDocker(
  params: model.train.IGetTrainExperimentMoveTensorboardToMultiDockerParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.train.IGetTrainExperimentMoveTensorboardToMultiDockerResponse>(
    '/webApi/train/experiment/moveTensorboardToMultiDocker',
    {
      params: pick(params, ['modelMetaId']),
      ...options,
    }
  );
}

/**
 * @desc id=1373140, catid=238704, projectId=35802, created=2024-02-27 10:28:03, last-modified=2024-08-08 11:06:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/diffMetricsAndParams)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373140)
 */
export function postTrainExperimentDiffMetricsAndParams(
  params: model.train.IPostTrainExperimentDiffMetricsAndParamsParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.train.IPostTrainExperimentDiffMetricsAndParamsResponse>('/webApi/train/experiment/diffMetricsAndParams', {
    data: pick(params, ['experimentIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1373142, catid=238704, projectId=35802, created=2024-02-27 10:28:03, last-modified=2024-08-08 11:06:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/listTrainExperiment)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373142)
 */
export function postTrainExperimentListTrainExperiment(
  params: model.train.IPostTrainExperimentListTrainExperimentParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.train.IPostTrainExperimentListTrainExperimentResponse>('/webApi/train/experiment/listTrainExperiment', {
    data: pick(params, ['curPage', 'experimentName', 'owner', 'showCount', 'status']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1453548, catid=238704, projectId=35802, created=2024-08-08 11:06:11, last-modified=2024-08-08 11:06:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/train/experiment/registerTensorboard)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1453548)
 */
export function getTrainExperimentRegisterTensorboard(
  params: model.train.IGetTrainExperimentRegisterTensorboardParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.train.IGetTrainExperimentRegisterTensorboardResponse>('/webApi/train/experiment/registerTensorboard', {
    params: pick(params, ['taskDetailIds']),
    ...options,
  });
}
