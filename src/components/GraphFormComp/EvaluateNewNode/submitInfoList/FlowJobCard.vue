<template>
  <mtd-card :class="borderStatusClass" body-class="flow-config-card" style="max-width: 600px">
    <div class="flow-config-info" @click="openModal('params')">
      <div class="flow-config-label-container">
        <div class="flow-config-label">
          <status-tag
            v-if="foundBenchmark(flowConfig.flowId) && flowJob?.status"
            :status="flowJob?.status"
            style="margin-top: -4px"
          />&nbsp;
          <span style="color: grey; margin-right: 2px">#{{ index + 1 }}</span>
          <span class="flow-name-text">{{ flowConfig.flowName }}</span>
        </div>
        <mtd-button
          type="text-primary"
          v-for="item in [foundBenchmark(flowConfig.flowId)].filter((x) => x)"
          :key="item.flowId"
          @click.stop="handleViewJobHistory(item)"
          size="small"
          style="font-size: 15px; flex-shrink: 0"
          >[执行历史]
        </mtd-button>
      </div>
      <div style="flex-shrink: 0" v-for="item in [foundBenchmark(flowConfig.flowId)].filter((x) => x)">
        <mtd-button type="text-primary" class="card-button" @click.stop="handleViewJobDetail(item.jobId)">
          执行详情#{{ item.jobId }}
        </mtd-button>
      </div>
    </div>
    <div class="tag-container" @click="openModal('params')">
      <div class="flex justify-between">
        <div>
          <span>数据</span>&nbsp;
          <mtd-popover trigger="hover">
            <div slot="content">
              <mtd-tag v-for="item in flowConfig.benchmarkList" :key="item.runSpecSetName">{{ item.runSpecSetName }}</mtd-tag>
            </div>
            <span style="color: #1c6cdc; font-weight: bold">数据集&nbsp;{{ flowConfig.benchmarkList.length }}</span> </mtd-popover
          >&nbsp;
          <mtd-popover trigger="hover" placement="top" :disabled="!repeatRunSpec.length">
            <span class="font-bold"
              >执行单元&nbsp;{{ flowConfig.runSpecFlowConfigList.length
              }}<span v-if="repeatRunSpec.length"
                >(<span style="text-decoration: underline">{{ flowConfig.runSpecFlowConfigList.filter((x) => !x.repeatFlag).length }}</span
                >)</span
              ></span
            >

            <div slot="content" class="repeat-runspec-content">
              {{ flowConfig.flowName }} - 配置存在重复，不执行的执行单元：
              <ul style="margin-left: 20px">
                <li v-for="runSpec of repeatRunSpec.slice(0, 10)" :key="runSpec.id">
                  执行单元:#{{ runSpec.runSpecId }}:&nbsp;{{ runSpec.jobBenchmark.runSpecSetName }}@{{
                    getDataSizeLabel(runSpec.evalDataSize)
                  }}&nbsp;=>&nbsp;{{ runSpec.dataSubSetName }}
                </li>
                <li v-if="repeatRunSpec.length > 10">...共{{ repeatRunSpec.length }}个执行单元</li>
              </ul>
            </div>
          </mtd-popover>
        </div>
        <mtd-button type="text-primary" @click.stop="openModal('params')">
          <mtd-tooltip
            :disabled="!paramOverrideCount"
            :content="`和默认值存在${paramOverrideCount}个不同的配置项。覆盖配置会在 Fork 画布时继承。`"
          >
            <div class="inline-block align-middle" style="font-size: 15px">
              <span v-if="paramOverrideCount" class="badge-dot">{{ paramOverrideCount }}</span
              >参数配置
              <mtd-icon name="setting" />
            </div>
          </mtd-tooltip>
        </mtd-button>
      </div>
      <div class="flex justify-between">
        <div>
          <span>资源&nbsp;&nbsp;</span>
          <span style="color: #9152f5; font-weight: bold" v-for="(item, idx) in getGpuList()" :key="item.total">
            <span v-if="idx !== 0">,&nbsp;</span>
            <mtd-popover trigger="hover" placement="top" :disabled="item.reused === ''">
              <div slot="content" class="repeat-runspec-content">
                存在可复用本节点内其他执行计划的资源：
                <ul style="margin-left: 20px">
                  <li v-for="item of getUniqueKeyMapValues()" :key="item.name">
                    {{ `集群[${item.name.substring(0, 4)}]: ${item.gpuSpec} * ${item.cost}` }}
                  </li>
                </ul>
              </div>

              <span
                >{{ item.total }}
                <span v-if="item.reused !== ''"
                  >(<span class="underline">{{ item.reused }}</span
                  >)</span
                >
              </span>
            </mtd-popover>
          </span>
        </div>
        <mtd-button type="text-primary" @click.stop="openModal('resource')">
          <mtd-tooltip
            :disabled="!resourceCoveredCount"
            :content="`和默认值存在${resourceCoveredCount}个不同的配置项。覆盖配置会在 Fork 画布时继承。`"
          >
            <div class="inline-block align-middle" style="font-size: 15px">
              <span v-if="resourceCoveredCount" class="badge-dot">{{ resourceCoveredCount }}</span
              >资源配置
              <mtd-icon name="slider-settings" />
            </div>
          </mtd-tooltip>
        </mtd-button>
      </div>
      <div v-if="foundBenchmark(flowConfig.flowId)?.jobId">
        <template v-if="flowJob">
          <div class="flex justify-between" style="width: 100%">
            <div>
              执行单元&nbsp;
              <status-count-tag :statusCount="flowJob?.globalFlowStatus?.runSpecStatusCount" /> &nbsp;
              <span class="time-text">用时：{{ flowJob?.globalFlowStatus?.eclipsedTime || '-' }}</span>
            </div>
            <failed-cluster-modal :flowConfig="flowConfig" @jump="handleViewJobDetail" @click.stop />
          </div>
          <div :class="['status-container', getTheme(flowJob?.status)]">
            <mtd-tooltip :content="flowJob?.message" placement="top" v-if="flowJob?.message">
              <span class="status-message">{{ flowJob?.message }}</span>
            </mtd-tooltip>
          </div>
        </template>
      </div>
    </div>
    <params-config-modal
      :isEdit="isEdit"
      :initConfig="initConfig"
      :context="context"
      :saveRunspecSetConfig="saveRunspecSetConfig"
      :openModal.sync="paramModalOpen"
      :sourceMetaId="sourceMetaId"
      @init="genResourceBase"
      ref="paramsConfigModal"
    />
  </mtd-card>
</template>
<script lang="ts">
import { Component, Prop, Ref, Spectra, Weaver } from '@/decorators';
import { BindType, PluginBind } from '@/model/customEval';
import evalFlow from '@/store/modules/evalFlow';
import evalFlowJob from '@/store/modules/evalFlowJob';
import evalFlowVersion from '@/store/modules/evalFlowVersion';
import modelManage from '@/store/modules/modelManage';
import { EvalSource } from '@/utils';
import StatusCountTag from '@/views/modelEval/FlowChartJobExecute/components/StatusCountTag.vue';
import StatusTag from '@/views/modelEval/FlowChartJobExecute/components/StatusTag.vue';
import { StatusConfig } from '@/views/modelEval/utils';
import { cloneDeep, flatten } from 'lodash';
import FailedClusterModal from './FailedClusterModal.vue';
import GlobalAliasParams from './GlobalAliasParams.vue';
import ParamsConfigModal from './ParamsConfigModal.vue';
import PluginBindPreview from './PluginBindPreview.vue';
import ResourceConfigList from './ResourceConfigList.vue';

@Component({
  components: {
    GlobalAliasParams,
    ResourceConfigList,
    PluginBindPreview,
    StatusTag,
    StatusCountTag,
    ParamsConfigModal,
    FailedClusterModal,
  },
})
@Spectra
export default class FlowJobCard extends Weaver(evalFlowJob, evalFlow, evalFlowVersion, modelManage) {
  @Prop()
  isEdit!: boolean;
  @Prop()
  foundBenchmark;
  @Prop()
  flowConfig;
  @Prop()
  context;
  @Prop({ default: () => 0 })
  sourceMetaId;
  @Prop()
  saveRunspecSetConfig;
  @Prop()
  clusterUniqueKeyMap;
  @Prop()
  index;
  @Ref()
  paramsConfigModal!: ParamsConfigModal;

  paramModalOpen = false;
  resourceModalOpen = false;
  failedClusterModalOpen = false;
  resourceBase: any = [];
  oriParamCount = -1;
  oriResourceCount = -1;

  get paramOverrideCount() {
    let count = 0;
    this.flowConfig.envGlobalAliasParams.forEach((param) => {
      if (param.value !== 'undefined' && param.value !== null) count++;
    });
    try {
      const globalBindList: PluginBind[] = flatten(JSON.parse(this.flowConfig.globalAnchorBindMapJson || '[]').map((x) => x[1]));
      const runSpecBindList: PluginBind[] = flatten(JSON.parse(this.flowConfig.runSpecAnchorBindMapJson || '[]').map((x) => x[1]));
      [...globalBindList, ...runSpecBindList].forEach((bind) => {
        if (bind.bindType === BindType.COVER) count++;
        bind.contextParams.forEach((param) => {
          // 特殊逻辑：EvalModelInfer/model 的“计算资源类型”参数在此项目上不计数。
          if (bind.anchorPath === 'EvalModelInfer/model' && param.name === '_RESOURCE_TYPE') return;
          if (param.source === EvalSource.JOB) count++;
        });
      });
    } catch (e) {
      console.warn(e);
    }
    if (this.oriParamCount === -1) this.oriParamCount = count;
    else if (this.oriParamCount !== count) this.$emit('configChanged');
    return count;
  }

  get initConfig() {
    return cloneDeep(this.flowConfig);
  }

  get resourceCoveredCount() {
    const checkParamList = ['queue', 'workerCpuCore', 'workerCpuMem', 'workerGpuCore', 'workerInstanceCount'];
    const count = this.flowConfig.resourceConfigList.reduce((prevCount, curResource) => {
      let curCount = prevCount;
      checkParamList.forEach((key) => curResource[key] && curCount++);
      return curCount;
    }, 0);
    if (this.oriResourceCount === -1) this.oriResourceCount = count;
    else if (this.oriResourceCount !== count) this.$emit('configChanged');
    return count;
  }
  get flowJob() {
    return this.evalFlowJobList?.evalFlowJobList.find((x) => x.flowId === this.flowConfig.flowId);
  }

  get borderStatusClass() {
    switch (this.flowJob?.status) {
      case 'RUNNING':
        return 'shadow-running';
      case 'DONE':
        return 'shadow-done';
      case 'ERROR':
      case 'KILLED':
        return 'shadow-error';
      default:
        return '';
    }
  }

  get repeatRunSpec() {
    return this.flowConfig.runSpecFlowConfigList.filter((x) => x.repeatFlag);
  }

  getUniqueKeyMapValues() {
    return [...this.clusterUniqueKeyMap.values()].filter(
      (x) => x.gpuSpec && x.flowId !== this.flowConfig.flowId && x.source.has(`${x.name}||${this.flowConfig.flowId}`)
    );
  }

  getRunSpecJobId(job) {
    return '' + job.runSpecSetId + job.runSpecId + job.evalDataSize;
  }
  handleViewJobHistory(flowJob) {
    // “画布ID”+“画布节点ID”+“流程图”+“流程图版本”
    window.open(
      this.$router.resolve({
        name: 'flow-chart-job-execute-list',
        query: {
          expId: this.$route.params.experimentId,
          expNodeId: String(this.context.id),
          flowId: flowJob.flowId,
          flowVersion: flowJob.flowVersion,
        },
      }).href,
      '_blank',
      'noopener'
    );
  }

  handleViewJobDetail(jobId) {
    window.open(
      this.$router.resolve({
        name: 'flow-chart-job-execute-detail',
        params: {
          id: String(jobId),
        },
      }).href,
      '_blank',
      'noopener'
    );
  }

  getGpuList() {
    const reusedGpuSpecCount = {};
    const gpuSpecCount = this.flowConfig.resourceConfigList.reduce((acc, config) => {
      if (!config.gpuSpec) return acc;

      acc[config.gpuSpec] = (acc[config.gpuSpec] || 0) + this.getGpuCount(config);
      reusedGpuSpecCount[config.gpuSpec] =
        (reusedGpuSpecCount[config.gpuSpec] || 0) +
        ([...this.clusterUniqueKeyMap.values()].some((v) => v.flowId === this.flowConfig.flowId && v.name === config.clusterName)
          ? this.getGpuCount(config)
          : 0);
      return acc;
    }, {});
    return Object.entries(gpuSpecCount)
      .map(([k, v]) => ({
        total: `${k} * ${v}`,
        reused: `${reusedGpuSpecCount[k] !== v ? `${reusedGpuSpecCount[k]}` : ''}`,
      }))
      .filter((x) => x.total !== '0');
  }

  getTheme(status) {
    return StatusConfig[status].theme;
  }

  getGpuCount(config) {
    const defaultConfig = this.flowConfig.defaultResourceConfigList.find((item) => item.clusterName === config.clusterName);
    const workerGpuCore = config.workerGpuCore ?? defaultConfig?.workerGpuCore ?? 0;
    const workerInstanceCount = config.workerInstanceCount ?? defaultConfig?.workerInstanceCount ?? 0;
    const workers = config.workers ?? defaultConfig?.workers ?? 0;
    return workerGpuCore * workerInstanceCount * workers;
  }

  genResourceBase(tableData, defaultTableData) {
    // 删除被修改掉的聚合集群
    [...this.clusterUniqueKeyMap.entries()].forEach(([k, v]) => {
      if (v.flowId === this.flowConfig.flowId && !tableData.some((x) => x.uniqueKey === k)) {
        this.clusterUniqueKeyMap.delete(k);
      }
    });
    // 加上新的
    tableData.forEach((resource) => {
      if (!resource.uniqueKey) return;
      const gpuCost = this.getGpuCount(resource);
      const maxClusterCost = this.clusterUniqueKeyMap.get(resource.uniqueKey);
      if ((maxClusterCost && gpuCost > maxClusterCost.cost) || !maxClusterCost) {
        this.clusterUniqueKeyMap.set(resource.uniqueKey, {
          cost: gpuCost,
          flowId: this.flowConfig.flowId,
          flowName: this.flowConfig.flowName,
          gpuSpec: resource.gpuSpec,
          name: resource.clusterName,
          source:
            maxClusterCost?.source?.add(`${resource.clusterName}||${this.flowConfig.flowId}`) ||
            new Set([`${resource.clusterName}||${this.flowConfig.flowId}`]),
        });
      } else {
        maxClusterCost.source.add(`${resource.clusterName}||${this.flowConfig.flowId}`);
      }
    });
    this.$forceUpdate();
    this.$emit('uniqueMapUpdate');

    if (JSON.stringify(this.flowConfig.defaultResourceConfigList) !== JSON.stringify(defaultTableData))
      this.flowConfig.defaultResourceConfigList = defaultTableData;
  }

  async openModal(type) {
    if (this.evalFlow.evalFlow?.id !== this.flowConfig.flowId) {
      await this.action$evalFlow({
        id: this.flowConfig.flowId,
      });
      await this.action$evalFlowVersion({
        flowId: this.flowConfig.flowId,
        version: this.flowConfig.flowVersion,
      });
    }
    this.paramModalOpen = true;
    this.paramsConfigModal.activeTab = type;
  }

  getDataSizeLabel(dataSize) {
    return this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList.find((x) => x.name === String(dataSize))?.label || '-';
  }
}
</script>
<style lang="less" scoped>
.status-text {
  max-width: 230px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .flow-config-card {
  position: relative;
  cursor: pointer;
  padding: 10px;
}

::v-deep .mtd-badge-text {
  background-color: rgba(255, 0, 0, 0.4);
}

.flow-config-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
}
.card-button {
  font-weight: bold;
  font-size: 19px;
  flex-shrink: 0;
}
.flow-config-label-container {
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 0;
  overflow: hidden;
  align-items: center;
  ::v-deep .mtd-tooltip-rel {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.flow-config-label {
  font-size: 19px;
  display: flex;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flow-name-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tag-container {
  font-size: 15px;
  .badge-dot {
    position: relative;
    top: -1px;
    flex-shrink: 0;
    display: inline-block;
    color: white;
    background-color: #ff000099;
    border-radius: 6px;
    height: 12px;
    padding: 0 3px;
    font-size: 12px;
    line-height: 12px;
    margin-right: 2px;
  }
}

.repeat-runspec-content {
  padding: 10px;
}

// 基础阴影样式
.shadow-error {
  box-shadow: 0 0 8px rgba(255, 59, 48, 0.6);
}

.shadow-done {
  box-shadow: 0 0 8px rgba(52, 199, 89, 0.6);
}

.shadow-running {
  box-shadow: 0 0 8px rgba(0, 122, 255, 0.6);
  animation: breathing 2s ease-in-out infinite;
}

// 呼吸灯动画
@keyframes breathing {
  0% {
    box-shadow: 0 0 8px rgba(0, 122, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 12px rgba(0, 122, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 8px rgba(0, 122, 255, 0.3);
  }
}
.status-container {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  &.blue {
    color: #1c6cdc;
  }
  &.green {
    color: #34c759;
  }
  &.red {
    color: #ff3b30;
  }
  &.yellow {
    color: #ff9500;
  }
  &.gray {
    color: #999;
  }
}
.time-text {
  color: #1c6cdc;
  margin-right: 8px;
  font-weight: bold;
}

.status-message {
  max-width: 230px;
  border-radius: 4px;
  font-weight: bold;
}
</style>
