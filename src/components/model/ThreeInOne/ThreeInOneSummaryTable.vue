<template>
  <div>
    <div style="margin-bottom: 10px; padding-right: 100px; position: relative">
      <div v-if="PARAMS.fullDimensionGroupBtn && PARAMS.params.filterGroupConfigList && PARAMS.tableDataFullDimension">
        <mtd-button
          v-for="(B, I) in PARAMS.params.filterGroupConfigList"
          :key="I"
          ghost
          :type="I === selectGroup ? 'primary' : ''"
          @click="selectGroupBtn(B, I)"
          style="margin-right: 10px"
          :disabled="loading"
          >{{ B.name }}</mtd-button
        >
      </div>
      <mtd-button
        v-if="PARAMS.tableDataFullDimension && PARAMS.refreshBtnFlag"
        class="refreshBtn"
        type="primary"
        @click="
          () => {
            getModelEvalIUnifiedModelDetailDiffTable && getModelEvalIUnifiedModelDetailDiffTable(false, selectGroupName);
          }
        "
        >刷新报表</mtd-button
      >
    </div>
    <div :key="keyChange" class="summary-container" :style="{ paddingTop: PARAMS.params.filterGroupConfigList?.length ? '' : '40px' }">
      <mtd-table
        v-export-table="`模型效果报表-综合`"
        :data="getSummaryTable"
        bordered
        :cellStyle="getCellStyle"
        :loading="
          LS('modelEvalSummaryModelDiffTableMultiple', 'modelEvalListStatMeta', 'modelEvalIUnifiedModelOverviewDiffTable') || loading
        "
        loading-message=""
      >
        <mtd-table-column prop="dimension" label="评测维度" align="center" min-width="300">
          <div slot-scope="{ row }" style="white-space: pre-wrap">
            <mtd-tooltip style="font-size: 12px" placement="top" :content="row.dimension.hoverName || '-'">
              <span>{{ row.typeLabelFirst }}{{ row.dimension.modelName }}{{ row.typeLabelEnd }}</span>
            </mtd-tooltip>
          </div>
        </mtd-table-column>
        <mtd-table-column
          v-for="(item, index) in getSummaryLabel"
          :key="item.data.name"
          :column-key="item.data.name"
          :context="item.data"
          align="center"
          min-width="170"
        >
          <template #header="{ column }">
            {{ column.context.label
            }}<span
              v-if="column.context.unitList[0]?.hasStatSubSetCount !== column.context.unitList[0]?.totalSubSetCount"
              style="color: red"
              >*</span
            >
          </template>

          <template slot-scope="{ row, column }">
            <div class="col-item">
              <span v-if="!row[index]?.data?.length">-</span>
              <span v-for="(i, id) in row[index]?.data" :key="id" style="text-align: center">
                <mtd-tooltip :content="tableTooltipHTML(i.detail)" size="small" placement="top">
                  <span style="cursor: pointer"
                    >{{ i.value || '' }} <span style="white-space: nowrap">{{ i.detail.dataSetOpennessStatus }}</span></span
                  >
                </mtd-tooltip>
                <EvalDataSizePopover
                  v-if="column.context.label !== '综合性' && PARAMS.evalDataSizeConfiguration === true"
                  :modelListItem="getModelListItem(row, column.context.label, id)"
                  style="margin-left: 5px"
                />
              </span>
            </div>
          </template>
        </mtd-table-column>
      </mtd-table>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Spectra, Weaver, Watch } from '@/decorators';
import ModelEval from '@/store/modules/modelEval';
import ModelEvalSummary from '@/store/modules/modelEvalSummary';
import EvalDataSizePopover from './EvalDataSizePopover.vue';
import type { SummaryTableParams } from '@/types/modelCommonComponent';
import { cloneDeep } from 'lodash';

@Component({ components: { EvalDataSizePopover } })
@Spectra
export default class ThreeInOneSummaryTable extends Weaver(ModelEvalSummary, ModelEval) {
  @Prop({ type: Object, default: () => ({ tableData: null, evalDataSizeConfiguration: true }) }) PARAMS!: SummaryTableParams;
  @Prop({ default: () => () => void 0 })
  getModelEvalIUnifiedModelDetailDiffTable?: any;
  @Prop()
  keyChange?: boolean;

  loading = false;

  selectGroup = 0;
  selectGroupName = '';
  async selectGroupBtn(mas, index) {
    if (this.selectGroup === index) return;
    this.selectGroup = index;
    this.loading = true;
    this.selectGroupName = mas.name;
    await this.getModelEvalIUnifiedModelDetailDiffTable(false, mas.name);
    this.loading = false;
  }

  get evalDataSize() {
    if (!this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList?.length) return {};
    const obj: any = {};
    obj.AUTO = 'Auto';
    for (const value of this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList) {
      obj[value.name] = value.label;
    }

    return obj;
  }

  get getSummaryTable() {
    const summaryTable = this.PARAMS?.tableDataFullDimension?.summaryTable?.cellList || [];

    if (summaryTable.length === 0) {
      return [];
    }

    const { originRequest } = this.PARAMS.tableDataFullDimension;
    const evalModelList = originRequest.evalModelList?.map((item) => {
      null;
      return {
        dimension: this.getModelName({ name: item.name, family: item.family, modelEvalTextFlag: true }),
        typeLabelFirst: '评测模型-绝对值 / 能力达成度\n(模型名称：',
        typeLabelEnd: this.getModelName({ name: item.name, family: item.family, modelEvalTextFlag: true })?.hoverName
          ? ')'
          : '【评测模型未选取】',
        name: item.name,
        family: item.family,
        modelName: 'evalModel',
      };
    });

    const tableData = [
      ...evalModelList,
      {
        dimension: this.getModelName(originRequest.baseModel),
        typeLabelFirst: '对照模型-绝对值\n(模型名称：',
        typeLabelEnd: this.getModelName(originRequest.baseModel)?.hoverName ? ')' : '【对照模型未选取】',
        name: originRequest.baseModel?.name,
        family: originRequest.baseModel?.family,
        modelName: 'baseModel',
      },
    ];
    if (originRequest.baseLineModel) {
      // 基线模型
      tableData.splice(evalModelList.length + 1, 0, {
        dimension: this.getModelName(originRequest.baseLineModel),
        typeLabelFirst: '基线模型-绝对值\n(模型名称：',
        typeLabelEnd: this.getModelName(originRequest.baseLineModel)?.hoverName ? ')' : '【基线模型未选取】',
        name: originRequest.baseLineModel?.name,
        family: originRequest.baseLineModel?.family,
        modelName: 'baseLineModel',
      });
    }

    for (const col of summaryTable) {
      // 绝对值都用【-】，百分比都用【#DIV/0!】
      for (let j = 0; j < evalModelList.length; j++) {
        tableData[j][col.columnIndex] = {
          data: col.data.unitList.map((item) => {
            return {
              value: `${this.parseValue(item.evalModelList[j]?.value, '-')} / ${this.parseValue(
                item.evalModelList[j]?.basePercentage,
                '#DIV/0!'
              )}`,
              detail: item,
            };
          }),
        };
      }

      tableData[evalModelList.length][col.columnIndex] = {
        data: col.data.unitList.map((item) => {
          return {
            value: this.parseValue(item.baseModel?.value, '-'),
            detail: item,
          };
        }),
      };

      if (originRequest.baseLineModel) {
        tableData[evalModelList.length + 1][col.columnIndex] = {
          data: col.data.unitList.map((item) => {
            return {
              value: this.parseValue(item.baseLineModel?.basePercentage, '#DIV/0!'),
              detail: item,
            };
          }),
        };
      }

      if (tableData[evalModelList.length + 1]) {
        tableData[evalModelList.length + 1][col.columnIndex] = {
          data: col.data.unitList.map((item) => {
            return {
              value: this.parseValue(item.baseLineModel?.value, '-'),
              detail: item,
            };
          }),
        };
      }
    }

    return tableData;
  }

  get getSummaryLabel() {
    return this.PARAMS?.tableDataFullDimension?.summaryTable?.cellList;
  }

  getModelListItem(row, columnTitle, id) {
    const { cellList } = this.PARAMS.tableDataFullDimension?.summaryTable;
    const column = cellList.find((item) => columnTitle === item.data.label).data.unitList[id];
    switch (row.modelName) {
      case 'evalModel': {
        const { modelEvalStatCalculateResultList } = column;
        const calculateUnitList =
          modelEvalStatCalculateResultList?.find((init) => init.name === row.name && init.family === row.family)?.calculateUnitList || [];
        return cloneDeep(calculateUnitList)?.sort((a, b) => a.evalDataSize - b.evalDataSize);
      }

      case 'baseModel':
        return cloneDeep(column.baseResult?.calculateUnitList)?.sort((a, b) => a.evalDataSize - b.evalDataSize);

      case 'baseLineModel':
        return cloneDeep(column.baseLineResult?.calculateUnitList)?.sort((a, b) => a.evalDataSize - b.evalDataSize);

      default:
        return [];
    }
  }

  parseValue(value, defaultValue) {
    if (!value || value === 'NaN') {
      return defaultValue;
    }

    return value;
  }

  tableTooltipHTML(rowData) {
    try {
      return `评测子集数量：有效${rowData.hasStatSubSetCount}/总计${rowData.totalSubSetCount}`;
    } catch (e) {
      console.error(e);
    }
  }

  getModelName(model) {
    if (model.modelEvalTextFlag) {
      const { originRequest } = this.PARAMS.tableDataFullDimension;
      const res = originRequest.evalModelList.find((item) => item.family === model.family && item.name === model.name);
      if (res) model = res;
    }

    const benchmarking = model.llmModel.modelInfo?.modelMeta.benchmarking || false;
    const modelName = benchmarking ? model.llmModel.modelInfo?.modelMeta.modelShortName : model.llmModel.modelInfo?.modelMeta.modelName;
    return {
      modelName: `${modelName} / ${model.llmModel.modelInfo?.modelCheckpoint} / ${model.llmModel.label}`,
      hoverName: model.llmModel.modelInfo?.modelMeta.modelName,
    };
  }

  getCellStyle({ column }) {
    return { backgroundColor: column.context?.style };
  }
}
</script>

<style scoped lang="scss">
.refreshBtn {
  position: absolute;
  right: 0;
  top: 0;
}
.summary-container {
  .title {
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.87);
    margin-bottom: 15px;
  }
  .mtd-table {
    td {
      padding: 0;
      .mtd-table-cell {
        padding: 0;
        div {
          padding: 12px 12px;
        }
      }
    }
  }
  .col-item {
    display: flex;
    flex-direction: column;
  }
  ::v-deep .export-button {
    float: right;
  }
}
</style>
