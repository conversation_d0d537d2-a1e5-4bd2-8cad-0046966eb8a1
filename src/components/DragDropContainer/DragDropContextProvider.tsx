import { createContext } from '@/utils/context';

export interface DragDropState {}

export const DragDropContext: any = createContext<DragDropState>({
  mouseDownEvent: null,
  dragData: null,
  dragRef: null,
  dropRef: null,
  dragStarted: false,
  dragging: false,
  clientXY: null,
  shiftKey: false,
  dropRefOnShift: null,
  dropRefOnShiftRelease: null,
  mouseEventOnShift: null,
  mouseEventOnShiftRelease: null,
  elementOnShiftRelease: null,
});

DragDropContext.displayName = 'DragDropContext';
