const SET_TABLE_DATA = 'SET_TABLE_DATA';
const TRANSFORM_TABLE_DATA = 'TRANSFORM_TABLE_DATA';
const FILTER_SORT_TABLE_DATA = 'FILTER_SORT_TABLE_DATA';
const SLICE_TABLE_DATA = 'SLICE_TABLE_DATA';
const GET_CONTRAST_ROW_DATA = 'GET_CONTRAST_ROW_DATA';
const EXPORT_TABLE_DATA = 'EXPORT_TABLE_DATA';

const GET_SCATTER_CHART_DATA = 'GET_SCATTER_CHART_DATA';
const GET_BAR_CHART_DATA = 'GET_BAR_CHART_DATA';

// 表格数据
let tableData;
// 表头数据
let tableColumn;
// 固定表头
let fixColumns;
// 固定表头长度
let fixColumnsLength = 0;

// 过滤的表格数据
let filterTableData;
// 过滤的表头数据
let filterTableColumn;
// 裁剪的树结构

// 评测集数据规模中出现的规模，取法如下：遍历所有calculateUnitList取distinct。
let groupValueUniqueArr = [];

const get = (object, path, defaultValue) => {
  const keys = Array.isArray(path) ? path : path.split('.');
  let result = object;
  for (const key of keys) {
    result = result ? result[key] : undefined;
    if (result === undefined) {
      return defaultValue;
    }
  }

  return result;
};

const setTableData = (data) => {
  const { PARAMS, modelMap, evalDataSizeMetaList, showDataSubSetDescription } = data;
  const {
    isMean = true,
    isMacroMean,
    isEvalDataSize = true,
    highlightModelList,
    order,
    evalDataSizeConfiguration,
    modelTags = [],
  } = PARAMS;
  const { modelList = [], dataSubSetList = [], dataSetList = [] } = PARAMS.tableData;

  // 数据集遍历, 空间换时间
  const dataSetMap = dataSubSetList.reduce((obj, item) => {
    obj[item.id] = item;
    return obj;
  }, {});
  dataSetList.reduce((obj, item) => {
    obj[item.id] = item;
    return obj;
  }, dataSetMap);

  // 工具函数
  const getModelName = (model) => `${model.family}:${model.name}`;
  const getModelLabel = (model) => model.llmModel?.label || modelMap[getModelName(model)];
  const getEvalDataSize = (evalDataSize) => evalDataSizeMetaList.find((item) => item.name === evalDataSize)?.label || '';
  const getEvalDataSizeArr = (model) => {
    const { groupValue } = model;
    const calculateUnitList = groupValue?.result?.result?.calculateUnitList ?? [];
    const arr = calculateUnitList.map((item) => getEvalDataSize(item.evalDataSize)); // 筛选出与 evalDataSize 不同的项
    const uniqueArr = [...new Set(arr)];
    groupValueUniqueArr = uniqueArr;
    return uniqueArr;
  };

  const countUnequalCategories = (calculateUnitList, evalDataSize) => {
    if (!calculateUnitList || calculateUnitList?.length === 0) {
      return {};
    }

    const categoryCounts = {};
    // 遍历 calculateUnitList，统计每个种类不相等的数量
    calculateUnitList.forEach((unit) => {
      // 假设 unit.category 是种类的属性，unit.size 是需要与 evalDataSize 比较的值
      // if (unit.evalDataSize !== evalDataSize) {
      // 如果 categoryCounts 中还没有这个种类，初始化为 0
      if (!categoryCounts[unit.evalDataSize]) {
        categoryCounts[unit.evalDataSize] = 0;
      }

      // 对应种类的计数加 1
      categoryCounts[unit.evalDataSize]++;
      // }
    });

    // 创建一个新对象来存储转换后的键
    const transformedCategoryCounts = {
      groupValueUniqueArr, // 初始化 count 为一个空数组
      count: [],
    };
    for (const key in categoryCounts) {
      // 假设 getEvalDataSize 是一个函数，用于处理 key
      const transformedKey = getEvalDataSize(key);
      transformedCategoryCounts.count.push(transformedKey); // 将转换后的键添加到数组中
      transformedCategoryCounts[transformedKey] = categoryCounts[key]; // 将计数添加到对象中
    }

    return transformedCategoryCounts;
  };

  const getToolTipHTML = (fieldData, { isAverage, rowData } = {}) => {
    const { calculateUnitList, hasStatSubSetCount, totalSubSetCount } = fieldData;
    if (hasStatSubSetCount === 1 && totalSubSetCount === 1 && calculateUnitList?.[0].totalInstanceCount > 0) {
      return `聚合数据条数: ${calculateUnitList[0].instanceCount}`;
    }

    let result = `评测子集数量：有效${hasStatSubSetCount} / 总计${totalSubSetCount}`;
    if (hasStatSubSetCount < totalSubSetCount) {
      let children = `<li>${(fieldData.result?.notCalculateDataSubSetList || [])
        .map((_) => `<li>${_.dataSet.name || ''}:${_.name}</li>`)
        .join('')}`;
      if (isAverage) {
        children = (rowData.groupValue?.result?.result?.notCalculateDataSubSetList || [])
          .map((_) => `<li>${_.dataSet.name || ''}:${_.name}</li>`)
          .join('');
      }

      result += `
      <details>
      <summary style="cursor: pointer">查看详情</summary>
      <div>无效数据子集：<i onclick="navigator.clipboard.writeText(event.target.parentElement.nextElementSibling.innerText)" style="transform: scale(1.02);cursor: pointer" class="icon mtdicon-copy-o"></i></div>
      <ul style="max-width: 800px; max-height: 100px; overflow: auto;">
      ${children}
      </ul>
     </details>
      `;
    }

    return result;
  };

  const getFieldValue = (fieldData, rowData, { isMean: isAverage } = {}) => ({
    value: fieldData.value,
    secondValue: fieldData.secondValue,
    hasStatSubSetCount: fieldData.hasStatSubSetCount,
    totalSubSetCount: fieldData.totalSubSetCount,
    confidenceInterval: fieldData?.confidenceInterval,
    // 跳转链接需要
    categoryId: fieldData.categoryId,
    dataSubSetId: fieldData.dataSubSetId,
    // tooltip
    toolTipHTML: getToolTipHTML(fieldData, { isAverage, rowData }),
    // EvalDataSize提示
    ...(evalDataSizeConfiguration && {
      diffCountUnequalCategories: countUnequalCategories(fieldData.result.calculateUnitList, rowData.evalDataSize),
      diffEvalDataSize: fieldData.result.calculateUnitList?.map((item) => ({
        evalDataSize: getEvalDataSize(item.evalDataSize),
        dataSetLabel: item.dataSetLabel,
        dataSubSetLabel: item.dataSubSetLabel,
        subSetId: item.subSetId,
      })),
    }),
    // todo 补齐数据
  });

  // 表头处理
  const firstData = modelList[0];
  fixColumns = [];
  // 根据参数设置固定表头
  if (order) fixColumns.push({ title: '序号', type: 'seq', fixed: 'left', width: 80, colId: 'seq', field: 'seq' });
  const modelLabelObj = { title: '模型名称', field: 'modelLabel', fixed: 'left', width: 300 };
  const evalDataSizeObj = { title: '评测集数据规模', field: 'evalDataSize' };

  // 模型名称
  if (PARAMS.filterColumn && PARAMS.filterColumn.modelFilter) {
    modelLabelObj.filters = modelList.reduce(
      (acc, item) => {
        const { label } = item.llmModel;
        if (!acc.tempSet.has(label)) {
          acc.tempSet.add(label);
          acc.result.push({ label, value: label });
        }

        return acc;
      },
      { tempSet: new Set(), result: [] }
    ).result;
  }

  // 评测集数据规模
  if (PARAMS.filterColumn && PARAMS.filterColumn.evalDataSizeFilter) {
    const uniqueLabels = new Set();
    evalDataSizeObj.filters = modelList
      .map((item) => {
        let label = getEvalDataSizeArr(item)
          .map((items) => items.toString())
          .join('/');
        if (!label) label = getEvalDataSize(item.evalDataSize);

        if (uniqueLabels.has(label)) {
          return null;
        }

        uniqueLabels.add(label);
        return { label, value: label };
      })
      .filter((item) => item !== null);
  }

  fixColumns.push(modelLabelObj);
  // if (isEvalDataSize) fixColumns.push(evalDataSizeObj);
  fixColumns.push(evalDataSizeObj);
  if (Array.isArray(modelTags)) {
    modelTags.forEach((item) => {
      const filterObj = { title: item.title, field: item.tagName };
      if (item.filterFlag === true) {
        const uniqueCombinationSet = new Set();
        filterObj.filters = modelList
          .map((model) => {
            const value = get(model, 'llmModel.tags', [])
              .find((tag) => tag.name === item.tagName)
              ?.values.join();
            return { value, label: value };
          })
          .filter((obj) => {
            const combination = `${obj.value}_${obj.label}`;
            if (uniqueCombinationSet.has(combination)) return false;
            uniqueCombinationSet.add(combination);
            return true;
          });
      }

      fixColumns.push(filterObj);
    });
  }

  const labelHead = {
    comprehensiveValue: '综合评测胜率',
    subjectiveValue: '主观评测胜率',
    objectiveValue: '客观评测准确率',
  };
  const unShowList = ['comprehensiveValue', 'objectiveValue', 'subjectiveValue'];
  if (isMean && !firstData.winRateValue) fixColumns.push({ title: '平均值', field: 'mean' });
  if (firstData.winRateValue) {
    for (const k in firstData.winRateValue) {
      if (unShowList.includes(k)) fixColumns.push({ title: labelHead[k], field: k });
    }
  }

  if (isMacroMean) fixColumns.push({ title: 'macro平均值', field: 'macroMean' });

  // 更新固定列长度
  fixColumnsLength = fixColumns.length;
  fixColumns.forEach((item) => {
    item.slots = { default: 'col', header: 'header' };
    item.isFixedColumn = true;
    item.colId = item.field || item.type;
  });

  let columns = [];

  // 获取动态表头
  if (!firstData.winRateValue) {
    columns = firstData.valueList.map((item, index) => {
      const { dataSetId, dataSubSetId = '', label = '' } = item;

      let targetDataSet;
      if (showDataSubSetDescription) {
        targetDataSet = dataSetMap[dataSubSetId] || dataSetMap[dataSetId];
      }

      const colId = `col_${index}`;

      return {
        colId,
        field: colId,
        title: label,
        hasEmpty: false,
        fixed: null,
        width: 200,
        slots: { default: 'col', header: 'header' },
        showBlackTag: targetDataSet?.publicStatus === 'BLACK',
        description: showDataSubSetDescription && targetDataSet?.description,
      };
    });
  }

  tableColumn = [...fixColumns, ...columns];

  // 行数据处理
  tableData = modelList.map((model, dataIndex) => {
    const valueList = model.valueList || [];
    const { winRateValue } = model;
    // 获取固定列的行数据
    const fixedColumnRowData = [];
    if (order) fixedColumnRowData.push({ type: 'seq', data: { value: dataIndex } });
    fixedColumnRowData.push({ type: 'modelLabel', data: { value: getModelLabel(model) } });
    fixedColumnRowData.push({
      type: 'evalDataSize',
      data: { value: getEvalDataSize(model.evalDataSize), diffCalculateUnitList: getEvalDataSizeArr(model) },
    });
    if (Array.isArray(modelTags)) {
      modelTags.forEach((item) => {
        fixedColumnRowData.push({
          type: item.tagName,
          data: { value: get(model, 'llmModel.tags', []).find((tag) => tag.name === item.tagName)?.values, customTags: true },
        });
      });
    }

    if (isMean && !winRateValue)
      fixedColumnRowData.push({
        type: 'mean',
        data: {
          ...getFieldValue(model.groupValue.result, model, { isMean: true }),
          confidenceInterval: model.groupValue?.confidenceInterval,
        },
      });
    if (isMacroMean) fixedColumnRowData.push({ type: 'macroMean', data: { value: model.groupValue.macroMean } });

    if (winRateValue)
      for (const k in winRateValue)
        if (unShowList.includes(k))
          fixedColumnRowData.push({
            type: k,
            data: { value: winRateValue[k] === '-' ? null : winRateValue[k], tipValue: winRateValue, flag: winRateValue?.flag },
          });

    const valueMap = fixedColumnRowData.reduce((obj, item) => {
      item.data.isFixedColumn = true;
      obj[item.type] = item.data;
      return obj;
    }, {});

    // 行数据是否有空
    let rowDataHasEmpty = false;
    if (!winRateValue) {
      valueList.reduce((obj, valueItem, index) => {
        const colId = `col_${index}`;
        obj[colId] = getFieldValue(valueItem, model);
        // 列index
        const columnIndex = fixColumnsLength + index;
        // 列数据是否有空
        const columnDataHasEmpty = tableColumn[columnIndex].hasEmpty;

        if (!rowDataHasEmpty || !columnDataHasEmpty) {
          let { hasStatSubSetCount, totalSubSetCount } = valueItem;
          hasStatSubSetCount = Number(hasStatSubSetCount);
          totalSubSetCount = Number(totalSubSetCount);
          // 不能同时为0 且 存在空的数据
          if (!(hasStatSubSetCount === 0 && totalSubSetCount === 0) && hasStatSubSetCount < totalSubSetCount) {
            // eslint-disable-next-line no-multi-assign
            rowDataHasEmpty = tableColumn[columnIndex].hasEmpty = true;
          }
        }

        return obj;
      }, valueMap);
    } else {
      rowDataHasEmpty =
        (winRateValue.subjectiveValueEfficient < winRateValue.subjectiveValueTotal &&
          +winRateValue.subjectiveValueEfficient + +winRateValue.subjectiveValueTotal !== 0) ||
        (winRateValue.objectiveValueEfficient < winRateValue.objectiveValueTotal &&
          +winRateValue.objectiveValueEfficient + +winRateValue.objectiveValueTotal !== 0);
    }

    return {
      rowKey: `row_${dataIndex}`, // 行key
      dataIndex, // 数据索引
      hasEmpty: rowDataHasEmpty, // 行是否有空数据
      modelLabel: getModelLabel(model),
      highlight: !!highlightModelList?.find((item) => item.name === model.name && item.family === model.family), // 是否高亮
      valueMap, // 动态列数据
      model: { family: model.family, name: model.name }, // 模型信息, 后续数据结构可能会变
    };
  });
};

const filterSortTableData = (data, { exportTableData } = {}) => {
  // exportTableData = true 导出时将 filterTableData 全部数据一同导出，避免筛选时导出无关数据
  const { PARAMS, rowSortOrderData, columnSortOrderData, confidenceIntervalName, rowFilter, columnFilter, filterModelProperties } = data;
  // 对比值判断
  const isSecondFirst = PARAMS.secondTrue && confidenceIntervalName?.itemDisplayType === 'SECOND_FIRST';
  const valueKey = isSecondFirst ? 'secondValue' : 'value';
  const { modelTags = [] } = PARAMS;

  // 行筛选
  if (rowFilter === 'noEmpty') {
    filterTableData = tableData.filter((item) => !item.hasEmpty);
  } else {
    filterTableData = [...tableData];
  }

  // 行排序
  if (rowSortOrderData.colId !== null) {
    const { order, colId } = rowSortOrderData;
    const columnData = tableColumn.find((item) => item.colId === colId);
    if (columnData) {
      const { field } = columnData;

      filterTableData.sort((a, b) => {
        const aData = a.valueMap[field];
        const bData = b.valueMap[field];
        if (!aData || !bData) {
          return a.dataIndex - b.dataIndex;
        }

        const winningList = Object.keys({
          comprehensiveValue: '综合评测胜率',
          objectiveValue: '客观评测准确率',
          subjectiveValue: '主观评测胜率',
        });
        let compareResult;
        // 字符串列做处理
        if (colId === 'modelLabel' || colId === 'evalDataSize') {
          compareResult = aData.value > bData.value ? 1 : -1;
        } else if (colId === 'mean' || colId === 'macroMean' || winningList.includes(colId)) {
          compareResult = (aData.value || 0) - (bData.value || 0);
        } else {
          compareResult = (aData[valueKey] || 0) - (bData[valueKey] || 0);
        }

        return order === 'descending' ? -compareResult : compareResult;
      });
    } else {
      filterTableData.sort((a, b) => a.dataIndex - b.dataIndex);
    }
  } else {
    filterTableData.sort((a, b) => a.dataIndex - b.dataIndex);
  }

  // && filterModelProperties.values.length !== 0
  if (filterModelProperties) {
    const filterFunction = (item) => {
      const valueModelLabel = item.llmModel ? item.llmModel.label : item.modelLabel;
      const { evalDataSize } = item.valueMap;
      let valueEvalDataSize = evalDataSize.value;
      if (evalDataSize.diffCalculateUnitList.length > 0) {
        valueEvalDataSize = evalDataSize.diffCalculateUnitList.join('/');
      }

      return filterModelProperties.filters.every((filterItem) => {
        if (filterItem.field === 'modelLabel') {
          return filterItem.values.some((ii) => valueModelLabel === ii);
        }

        if (filterItem.field === 'evalDataSize') {
          return filterItem.values.some((ii) => valueEvalDataSize === ii);
        }

        // 外部传入 有风险
        try {
          if (modelTags.length !== 0) {
            return modelTags.some((customizeTag) => {
              if (filterItem.field === customizeTag.tagName) {
                return filterItem.values.some((ii) => (item.valueMap[customizeTag.tagName].value || []).join() === (ii || ''));
              }

              return false;
            });
          }
        } catch (err) {
          console.log(err);
          return false;
        }

        return true;
      });
    };

    filterTableData = filterTableData.filter(filterFunction);
  }

  // 列筛选
  if (columnFilter === 'hasEmpty') {
    filterTableColumn = tableColumn.filter((item) => item.isFixedColumn || item.hasEmpty);
  } else if (columnFilter === 'noHasEmpty') {
    filterTableColumn = tableColumn.filter((item) => item.isFixedColumn || !item.hasEmpty);
  } else {
    filterTableColumn = [...tableColumn];
  }

  // 列排序
  if (columnSortOrderData.rowKey !== null) {
    const { rowKey, order } = columnSortOrderData;
    const rowData = filterTableData.find((item) => item.rowKey === rowKey);
    if (rowData) {
      filterTableColumn.sort((a, b) => {
        if (a.isFixedColumn || b.isFixedColumn) {
          return 0;
        }

        const aData = rowData.valueMap[a.field];
        const bData = rowData.valueMap[b.field];

        if (!aData || !bData) {
          return a.field - b.field;
        }

        const compareResult = (aData[valueKey] || 0) - (bData[valueKey] || 0);

        return order === 'descending' ? -compareResult : compareResult;
      });
    } else {
      filterTableColumn.sort((a, b) => (a.isFixedColumn || b.isFixedColumn ? 0 : a.field - b.field));
    }
  } else {
    filterTableColumn.sort((a, b) => (a.isFixedColumn || b.isFixedColumn ? 0 : a.field - b.field));
  }

  const returnObj = {
    tableColumn: filterTableColumn,
    tableData: filterTableData.map((item) => ({ rowKey: item.rowKey, ...(item.highlight && { highlight: item.highlight }) })),
  };

  if (exportTableData) returnObj.filterTableData = filterTableData;
  return returnObj;
};

const sliceTableData = (data) => {
  let { rowKeys, columnIds } = data;
  columnIds = fixColumns.map((item) => item.colId).concat(columnIds.split(','));
  rowKeys = rowKeys.split(',');
  return filterTableData.reduce((obj, item) => {
    if (rowKeys.includes(item.rowKey)) {
      obj[item.rowKey] = {
        ...item,
        valueMap: columnIds.reduce((obj1, item1) => {
          if (item.valueMap[item1]) {
            obj1[item1] = item.valueMap[item1];
          }

          return obj1;
        }, {}),
      };
    }

    return obj;
  }, {});
};

const getContraRowData = (data) => {
  return tableData.find((item) => item.rowKey === data.rowKey);
};

const getExportTableData = ({ showSecondValue = false, data }) => {
  const res = filterSortTableData(data, { exportTableData: true });
  const { withConfidenceInterval } = data;
  const tableHeader = res.tableColumn.map((column) => `"${column.title}"`).join(',');
  const tableBody = res.filterTableData.map((row) =>
    res.tableColumn
      .map((column) => {
        const { value, secondValue, hasStatSubSetCount, totalSubSetCount, confidenceInterval, diffCalculateUnitList } =
          row.valueMap[column.field];

        const secondValueStr = showSecondValue && secondValue ? `${secondValue}±` : '';
        let valueStr;
        if (column.field === 'seq') {
          valueStr = value + 1;
        } else if (column.field === 'evalDataSize') {
          const flag = Array.isArray(diffCalculateUnitList) && diffCalculateUnitList.length !== 0;
          valueStr = flag ? diffCalculateUnitList.join('/') : String(value) || '-';
        } else {
          valueStr = value || '-';
        }

        const confidenceIntervalStr = withConfidenceInterval && confidenceInterval ? `±${confidenceInterval}` : '';
        const hasEmptyStr = hasStatSubSetCount !== totalSubSetCount ? ' *' : '';

        return `"${secondValueStr}${valueStr}${confidenceIntervalStr}${hasEmptyStr}"`;
      })
      .join(',')
  );

  return [tableHeader, ...tableBody].join('\r\n');
};

const getScatterChartData = () => {
  if (tableColumn.length - fixColumnsLength === 1) {
    const column = tableColumn.find((item) => !item.isFixedColumn);
    const { colId } = column;
    const filterData = tableData
      .filter((item) => item.valueMap[colId].value)
      .sort((a, b) => b.valueMap[colId].value - a.valueMap[colId].value);
    return {
      column,
      chartData: filterData.map((item, index) => {
        const toolTipData = {
          index: index + 1,
          modelName: item.modelLabel,
          value: item.valueMap[colId].value,
          percent: `${Math.round(((index + 1) / filterData.length) * 100)}%`,
          tip: `${tableData.length - filterData.length}个模型未参与排名`,
        };
        return [index + 1, item.valueMap[colId].value, toolTipData];
      }),
    };
  }
};

const getBarChartData = () => {
  if (tableColumn.length - fixColumnsLength === 1) {
    const fixDecimal = (i, length = 2) => parseFloat(i.toFixed(length));
    const getRowValue = (range) => ({ range, value: 0, modelList: [] });

    let currentKey = '0';
    const column = tableColumn.find((item) => !item.isFixedColumn);
    const { colId } = column;
    const filterData = tableData.filter((item) => item.valueMap[colId].value);
    const rows = Object.values(
      [...filterData, ...Array.from({ length: 20 }).map((_, i) => fixDecimal(i * 0.05))]
        .sort((a, b) => {
          if (typeof a === 'number' && typeof b === 'number') {
            return a - b;
          }

          if (typeof a === 'number') {
            return a - parseFloat(b.valueMap[colId].value) || 1;
          }

          if (typeof b === 'number') {
            return parseFloat(a.valueMap[colId].value) - b || -1;
          }

          return parseFloat(a.valueMap[colId].value) - parseFloat(b.valueMap[colId].value);
        })
        .reduce(
          (obj, item) => {
            if (typeof item === 'number') {
              const key = `${fixDecimal(item)}-${fixDecimal(item + 0.05)}`;
              obj[key] = getRowValue(key);
              currentKey = key;
            } else {
              obj[currentKey].modelList.push(item);
              obj[currentKey].value++;
            }

            return obj;
          },
          { ['0']: getRowValue('0') }
        )
    );

    return { title: `${column.title} 上各模型的得分分布`, rows };
  }
};

self.addEventListener('message', function (event) {
  let data;
  if (typeof event.data === 'string') {
    data = JSON.parse(event.data);
  } else {
    data = event.data;
  }

  switch (data.type) {
    case SET_TABLE_DATA:
      self.postMessage({ type: SET_TABLE_DATA, messageId: data.messageId });
      break;
    case TRANSFORM_TABLE_DATA:
      setTableData(data);
      self.postMessage({ type: TRANSFORM_TABLE_DATA, messageId: data.messageId });
      break;
    case FILTER_SORT_TABLE_DATA:
      self.postMessage({ type: FILTER_SORT_TABLE_DATA, ...filterSortTableData(data), messageId: data.messageId });
      break;
    case SLICE_TABLE_DATA:
      self.postMessage({ type: SLICE_TABLE_DATA, messageId: data.messageId, tableData: sliceTableData(data) });
      break;
    case GET_CONTRAST_ROW_DATA:
      self.postMessage({ type: GET_CONTRAST_ROW_DATA, messageId: data.messageId, rowData: getContraRowData(data) });
      break;
    case EXPORT_TABLE_DATA:
      self.postMessage({ type: EXPORT_TABLE_DATA, messageId: data.messageId, tableData: getExportTableData(data) });
      break;
    case GET_SCATTER_CHART_DATA:
      self.postMessage({ type: GET_SCATTER_CHART_DATA, messageId: data.messageId, data: getScatterChartData() });
      break;
    case GET_BAR_CHART_DATA:
      self.postMessage({ type: GET_SCATTER_CHART_DATA, messageId: data.messageId, data: getBarChartData() });
      break;
    default:
      if (data.type || data.messageId) {
        self.postMessage({ type: data.type, messageId: data.messageId });
      }

      break;
  }
});
