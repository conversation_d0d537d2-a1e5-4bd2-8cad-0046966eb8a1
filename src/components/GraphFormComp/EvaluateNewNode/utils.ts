import { ContextParam } from '@/model/customEval';
import { cloneDeep } from 'lodash';

export function mergeParamDefinition(sourceParam: ContextParam[], mergeParam: ContextParam[]): ContextParam[] {
  const paramMap = new Map(sourceParam.map((param) => [param.name, param]));
  mergeParam.forEach((param) => {
    const resParam = paramMap.get(param.name);
    if (resParam)
      paramMap.set(param.name, {
        ...resParam,
        ...param,
      });
  });
  return cloneDeep([...paramMap.values()]);
}
