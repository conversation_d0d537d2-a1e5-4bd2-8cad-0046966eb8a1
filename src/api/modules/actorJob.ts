import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1296913, catid=227033, projectId=35802, created=2023-10-13 19:28:00, last-modified=2023-11-21 14:38:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/actorJob/kill)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296913)
 */
export function postActorJobKill(params: model.actorJob.IPostActorJobKillParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.actorJob.IPostActorJobKillResponse>('/webApi/actorJob/kill', {
    data: pick(params, ['batchId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296915, catid=227033, projectId=35802, created=2023-10-13 19:28:00, last-modified=2023-11-21 14:38:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/actorJob/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296915)
 */
export function postActorJobList(params: model.actorJob.IPostActorJobListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.actorJob.IPostActorJobListResponse>('/webApi/actorJob/list', {
    data: pick(params, ['keyword', 'batchId', 'template', 'showChildren', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296916, catid=227033, projectId=35802, created=2023-10-13 19:28:00, last-modified=2023-11-21 14:38:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/actorJob/listLog)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296916)
 */
export function postActorJobListLog(params: model.actorJob.IPostActorJobListLogParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.actorJob.IPostActorJobListLogResponse>('/webApi/actorJob/listLog', {
    data: pick(params, ['id', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296922, catid=227033, projectId=35802, created=2023-10-13 19:28:00, last-modified=2023-11-21 14:38:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/actorJob/launch)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296922)
 */
export function postActorJobLaunch(params: model.actorJob.IPostActorJobLaunchParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.actorJob.IPostActorJobLaunchResponse>('/webApi/actorJob/launch', {
    data: pick(params, ['actor', 'jobSet', 'priority', 'inputClass', 'input', 'configObject']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296926, catid=227033, projectId=35802, created=2023-10-13 19:28:00, last-modified=2023-11-21 14:38:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/actorJob/listTemplateMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296926)
 */
export function postActorJobListTemplateMeta(
  params: model.actorJob.IPostActorJobListTemplateMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.actorJob.IPostActorJobListTemplateMetaResponse>('/webApi/actorJob/listTemplateMeta', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296932, catid=227033, projectId=35802, created=2023-10-13 19:28:01, last-modified=2023-11-21 14:38:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/actorJob/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296932)
 */
export function postActorJobGet(params: model.actorJob.IPostActorJobGetParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.actorJob.IPostActorJobGetResponse>('/webApi/actorJob/get', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
