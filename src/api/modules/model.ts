import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1507890, catid=257170, projectId=35802, created=2025-03-03 18:54:50, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/registerModelMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507890)
 */
export function postModelManagerV2RegisterModelMeta(
  params: model.model.IPostModelManagerV2RegisterModelMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2RegisterModelMetaResponse>('/webApi/model/manager/v2/registerModelMeta', {
    data: pick(params, ['baseVo', 'extraInfoFields', 'tagValues']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1520978, catid=257170, projectId=35802, created=2025-05-21 11:04:56, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/registerModelMetaByFork)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1520978)
 */
export function postModelManagerV2RegisterModelMetaByFork(
  params: model.model.IPostModelManagerV2RegisterModelMetaByForkParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2RegisterModelMetaByForkResponse>('/webApi/model/manager/v2/registerModelMetaByFork', {
    data: pick(params, [
      'benchmarking',
      'bestCheckpoint',
      'byUpdate',
      'canRead',
      'canWrite',
      'cts',
      'modelFamily',
      'modelMetaId',
      'modelName',
      'modelSeries',
      'modelShortName',
      'modelSize',
      'modelStage',
      'modelType',
      'modelUserSettingVars',
      'modelUserSettings',
      'ownerList',
      'roleList',
      'skipCheck',
      'userList',
      'uts',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507894, catid=257170, projectId=35802, created=2025-03-03 18:54:50, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelMetaBaseInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507894)
 */
export function postModelManagerV2UpdateModelMetaBaseInfo(
  params: model.model.IPostModelManagerV2UpdateModelMetaBaseInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelMetaBaseInfoResponse>('/webApi/model/manager/v2/updateModelMetaBaseInfo', {
    data: pick(params, [
      'benchmarking',
      'bestCheckpoint',
      'byUpdate',
      'canRead',
      'canWrite',
      'cts',
      'modelFamily',
      'modelMetaId',
      'modelName',
      'modelSeries',
      'modelShortName',
      'modelSize',
      'modelStage',
      'modelType',
      'modelUserSettingVars',
      'modelUserSettings',
      'ownerList',
      'roleList',
      'skipCheck',
      'userList',
      'uts',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507903, catid=257170, projectId=35802, created=2025-03-03 18:54:51, last-modified=2025-05-26 11:26:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelMetaExtraFieldGroup)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507903)
 */
export function postModelManagerV2UpdateModelMetaExtraFieldGroup(
  params: model.model.IPostModelManagerV2UpdateModelMetaExtraFieldGroupParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelMetaExtraFieldGroupResponse>(
    '/webApi/model/manager/v2/updateModelMetaExtraFieldGroup',
    {
      data: pick(params, ['extraFieldGroup', 'extraInfoFields', 'modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507904, catid=257170, projectId=35802, created=2025-03-03 18:54:51, last-modified=2025-05-26 11:26:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelMetaTag)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507904)
 */
export function postModelManagerV2UpdateModelMetaTag(
  params: model.model.IPostModelManagerV2UpdateModelMetaTagParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelMetaTagResponse>('/webApi/model/manager/v2/updateModelMetaTag', {
    data: pick(params, ['modelMetaId', 'tagValues']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1516847, catid=257170, projectId=35802, created=2025-03-31 14:03:39, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelComparisonConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516847)
 */
export function postModelManagerV2UpdateModelComparisonConfig(
  params: model.model.IPostModelManagerV2UpdateModelComparisonConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelComparisonConfigResponse>(
    '/webApi/model/manager/v2/updateModelComparisonConfig',
    {
      data: pick(params, ['benchmarkConfigs', 'modelMetaId', 'modelMetaIds']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1516366, catid=257170, projectId=35802, created=2025-03-24 10:53:21, last-modified=2025-05-26 11:26:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelMetaTrainTaskDescription)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516366)
 */
export function postModelManagerV2UpdateModelMetaTrainTaskDescription(
  params: model.model.IPostModelManagerV2UpdateModelMetaTrainTaskDescriptionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelMetaTrainTaskDescriptionResponse>(
    '/webApi/model/manager/v2/updateModelMetaTrainTaskDescription',
    {
      data: pick(params, ['description', 'mlpJobId', 'modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507900, catid=257170, projectId=35802, created=2025-03-03 18:54:51, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelMetaEvalPlugin)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507900)
 */
export function postModelManagerV2UpdateModelMetaEvalPlugin(
  params: model.model.IPostModelManagerV2UpdateModelMetaEvalPluginParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelMetaEvalPluginResponse>('/webApi/model/manager/v2/updateModelMetaEvalPlugin', {
    data: pick(params, ['modelMetaId', 'pluginBindList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1517490, catid=257170, projectId=35802, created=2025-04-10 15:39:12, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelMetaEvalPluginList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1517490)
 */
export function postModelManagerV2UpdateModelMetaEvalPluginList(
  params: model.model.IPostModelManagerV2UpdateModelMetaEvalPluginListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelMetaEvalPluginListResponse>(
    '/webApi/model/manager/v2/updateModelMetaEvalPluginList',
    {
      data: pick(params, ['groupList', 'modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507896, catid=257170, projectId=35802, created=2025-03-03 18:54:50, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelMetaConfigFile)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507896)
 */
export function postModelManagerV2UpdateModelMetaConfigFile(
  params: model.model.IPostModelManagerV2UpdateModelMetaConfigFileParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelMetaConfigFileResponse>('/webApi/model/manager/v2/updateModelMetaConfigFile', {
    data: pick(params, ['configFilePath', 'fileNames', 'modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1515083, catid=257170, projectId=35802, created=2025-03-18 14:45:35, last-modified=2025-05-26 11:26:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/listEvalRecordByModelId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1515083)
 */
export function postModelManagerV2ListEvalRecordByModelId(
  params: model.model.IPostModelManagerV2ListEvalRecordByModelIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2ListEvalRecordByModelIdResponse>('/webApi/model/manager/v2/listEvalRecordByModelId', {
    data: pick(params, ['curPage', 'evalDatasetName', 'id', 'showCount', 'statName', 'type']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1517754, catid=257170, projectId=35802, created=2025-04-13 17:52:03, last-modified=2025-05-26 11:26:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getTrainDataSetPathList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1517754)
 */
export function postModelManagerV2GetTrainDataSetPathList(
  params: model.model.IPostModelManagerV2GetTrainDataSetPathListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetTrainDataSetPathListResponse>('/webApi/model/manager/v2/getTrainDataSetPathList', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507822, catid=257170, projectId=35802, created=2025-03-03 18:54:48, last-modified=2025-05-26 11:26:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelTokenizerPathByMlpJobId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507822)
 */
export function getModelManagerV2GetModelTokenizerPathByMlpJobId(
  params: model.model.IGetModelManagerV2GetModelTokenizerPathByMlpJobIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerV2GetModelTokenizerPathByMlpJobIdResponse>(
    '/webApi/model/manager/v2/getModelTokenizerPathByMlpJobId',
    {
      params: pick(params, ['mlpJobId']),
      ...options,
    }
  );
}

/**
 * @desc id=1507867, catid=257170, projectId=35802, created=2025-03-03 18:54:49, last-modified=2025-05-26 11:26:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/overviewModelTrainTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507867)
 */
export function postModelManagerV2OverviewModelTrainTask(
  params: model.model.IPostModelManagerV2OverviewModelTrainTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2OverviewModelTrainTaskResponse>('/webApi/model/manager/v2/overviewModelTrainTask', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507873, catid=257170, projectId=35802, created=2025-03-03 18:54:50, last-modified=2025-05-26 11:26:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/overviewModelTrainTaskMetricsByTensorboard)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507873)
 */
export function postModelManagerV2OverviewModelTrainTaskMetricsByTensorboard(
  params: model.model.IPostModelManagerV2OverviewModelTrainTaskMetricsByTensorboardParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2OverviewModelTrainTaskMetricsByTensorboardResponse>(
    '/webApi/model/manager/v2/overviewModelTrainTaskMetricsByTensorboard',
    {
      data: pick(params, ['modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507882, catid=257170, projectId=35802, created=2025-03-03 18:54:50, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/overviewModelTrainTaskResourceStatistic)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507882)
 */
export function postModelManagerV2OverviewModelTrainTaskResourceStatistic(
  params: model.model.IPostModelManagerV2OverviewModelTrainTaskResourceStatisticParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2OverviewModelTrainTaskResourceStatisticResponse>(
    '/webApi/model/manager/v2/overviewModelTrainTaskResourceStatistic',
    {
      data: pick(params, ['modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507844, catid=257170, projectId=35802, created=2025-03-03 18:54:48, last-modified=2025-05-26 11:26:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/overviewModelEvalMetricsRepresentative)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507844)
 */
export function postModelManagerV2OverviewModelEvalMetricsRepresentative(
  params: model.model.IPostModelManagerV2OverviewModelEvalMetricsRepresentativeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2OverviewModelEvalMetricsRepresentativeResponse>(
    '/webApi/model/manager/v2/overviewModelEvalMetricsRepresentative',
    {
      data: pick(params, ['modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1516845, catid=257170, projectId=35802, created=2025-03-31 14:03:38, last-modified=2025-05-26 11:26:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/overviewModelPerformance)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516845)
 */
export function postModelManagerV2OverviewModelPerformance(
  params: model.model.IPostModelManagerV2OverviewModelPerformanceParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2OverviewModelPerformanceResponse>('/webApi/model/manager/v2/overviewModelPerformance', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507851, catid=257170, projectId=35802, created=2025-03-03 18:54:49, last-modified=2025-05-26 11:26:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/overviewModelEvalStatistic)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507851)
 */
export function postModelManagerV2OverviewModelEvalStatistic(
  params: model.model.IPostModelManagerV2OverviewModelEvalStatisticParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2OverviewModelEvalStatisticResponse>(
    '/webApi/model/manager/v2/overviewModelEvalStatistic',
    {
      data: pick(params, ['modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507860, catid=257170, projectId=35802, created=2025-03-03 18:54:49, last-modified=2025-05-26 11:26:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/overviewModelMetaConfigFile)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507860)
 */
export function postModelManagerV2OverviewModelMetaConfigFile(
  params: model.model.IPostModelManagerV2OverviewModelMetaConfigFileParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2OverviewModelMetaConfigFileResponse>(
    '/webApi/model/manager/v2/overviewModelMetaConfigFile',
    {
      data: pick(params, ['modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507863, catid=257170, projectId=35802, created=2025-03-03 18:54:49, last-modified=2025-05-26 11:26:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/overviewModelTrainDataReport)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507863)
 */
export function postModelManagerV2OverviewModelTrainDataReport(
  params: model.model.IPostModelManagerV2OverviewModelTrainDataReportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2OverviewModelTrainDataReportResponse>(
    '/webApi/model/manager/v2/overviewModelTrainDataReport',
    {
      data: pick(params, ['modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1517755, catid=257170, projectId=35802, created=2025-04-13 17:52:04, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/registerTrainDataSetPathList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1517755)
 */
export function postModelManagerV2RegisterTrainDataSetPathList(
  params: model.model.IPostModelManagerV2RegisterTrainDataSetPathListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2RegisterTrainDataSetPathListResponse>(
    '/webApi/model/manager/v2/registerTrainDataSetPathList',
    {
      data: pick(params, ['modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507905, catid=257170, projectId=35802, created=2025-03-03 18:54:51, last-modified=2025-05-26 11:26:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelMetaTrainTaskByAdd)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507905)
 */
export function postModelManagerV2UpdateModelMetaTrainTaskByAdd(
  params: model.model.IPostModelManagerV2UpdateModelMetaTrainTaskByAddParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelMetaTrainTaskByAddResponse>(
    '/webApi/model/manager/v2/updateModelMetaTrainTaskByAdd',
    {
      data: pick(params, ['description', 'mlpJobId', 'modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1521255, catid=257170, projectId=35802, created=2025-05-26 11:26:02, last-modified=2025-05-26 11:26:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/applyAddModelFamily)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1521255)
 */
export function postModelManagerV2ApplyAddModelFamily(
  params: model.model.IPostModelManagerV2ApplyAddModelFamilyParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2ApplyAddModelFamilyResponse>('/webApi/model/manager/v2/applyAddModelFamily', {
    data: pick(params, ['description', 'modelFamily']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507790, catid=257170, projectId=35802, created=2025-03-03 18:54:47, last-modified=2025-05-26 11:26:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/applyPermissionForModelMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507790)
 */
export function postModelManagerV2ApplyPermissionForModelMeta(
  params: model.model.IPostModelManagerV2ApplyPermissionForModelMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2ApplyPermissionForModelMetaResponse>(
    '/webApi/model/manager/v2/applyPermissionForModelMeta',
    {
      data: pick(params, ['applyForOwnerRole', 'description', 'modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507912, catid=257170, projectId=35802, created=2025-03-03 18:54:52, last-modified=2025-05-26 11:26:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/updateModelMetaTrainTaskByRemove)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507912)
 */
export function postModelManagerV2UpdateModelMetaTrainTaskByRemove(
  params: model.model.IPostModelManagerV2UpdateModelMetaTrainTaskByRemoveParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2UpdateModelMetaTrainTaskByRemoveResponse>(
    '/webApi/model/manager/v2/updateModelMetaTrainTaskByRemove',
    {
      data: pick(params, ['description', 'mlpJobId', 'modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1517285, catid=257170, projectId=35802, created=2025-04-08 18:15:07, last-modified=2025-05-26 11:26:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/listBenchmarkingModelNames)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1517285)
 */
export function getModelManagerV2ListBenchmarkingModelNames(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerV2ListBenchmarkingModelNamesResponse>('/webApi/model/manager/v2/listBenchmarkingModelNames', {
    ...options,
  });
}

/**
 * @desc id=1507793, catid=257170, projectId=35802, created=2025-03-03 18:54:47, last-modified=2025-05-26 11:26:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelMetaBaseInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507793)
 */
export function postModelManagerV2GetModelMetaBaseInfo(
  params: model.model.IPostModelManagerV2GetModelMetaBaseInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetModelMetaBaseInfoResponse>('/webApi/model/manager/v2/getModelMetaBaseInfo', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507838, catid=257170, projectId=35802, created=2025-03-03 18:54:48, last-modified=2025-05-26 11:26:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/listModelSize)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507838)
 */
export function getModelManagerV2ListModelSize(
  params: model.model.IGetModelManagerV2ListModelSizeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerV2ListModelSizeResponse>('/webApi/model/manager/v2/listModelSize', {
    params: pick(params, ['modelFamily']),
    ...options,
  });
}

/**
 * @desc id=1514140, catid=257170, projectId=35802, created=2025-03-12 10:47:20, last-modified=2025-05-26 11:26:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/listModelMetaExtraFieldEnums)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1514140)
 */
export function getModelManagerV2ListModelMetaExtraFieldEnums(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerV2ListModelMetaExtraFieldEnumsResponse>(
    '/webApi/model/manager/v2/listModelMetaExtraFieldEnums',
    {
      ...options,
    }
  );
}

/**
 * @desc id=1507811, catid=257170, projectId=35802, created=2025-03-03 18:54:47, last-modified=2025-05-26 11:26:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelMetaExtraField)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507811)
 */
export function postModelManagerV2GetModelMetaExtraField(
  params: model.model.IPostModelManagerV2GetModelMetaExtraFieldParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetModelMetaExtraFieldResponse>('/webApi/model/manager/v2/getModelMetaExtraField', {
    data: pick(params, ['extraFieldGroup', 'extraInfoFields', 'modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1516360, catid=257170, projectId=35802, created=2025-03-24 10:53:19, last-modified=2025-05-26 11:26:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelMetaCollectionState)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516360)
 */
export function postModelManagerV2GetModelMetaCollectionState(
  params: model.model.IPostModelManagerV2GetModelMetaCollectionStateParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetModelMetaCollectionStateResponse>(
    '/webApi/model/manager/v2/getModelMetaCollectionState',
    {
      data: pick(params, ['modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507831, catid=257170, projectId=35802, created=2025-03-03 18:54:48, last-modified=2025-05-26 11:26:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/listModelFamily)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507831)
 */
export function getModelManagerV2ListModelFamily(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerV2ListModelFamilyResponse>('/webApi/model/manager/v2/listModelFamily', {
    ...options,
  });
}

/**
 * @desc id=1507815, catid=257170, projectId=35802, created=2025-03-03 18:54:48, last-modified=2025-05-26 11:26:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelMetaTag)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507815)
 */
export function postModelManagerV2GetModelMetaTag(
  params: model.model.IPostModelManagerV2GetModelMetaTagParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetModelMetaTagResponse>('/webApi/model/manager/v2/getModelMetaTag', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1516840, catid=257170, projectId=35802, created=2025-03-31 14:03:36, last-modified=2025-05-26 11:26:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelComparisonConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516840)
 */
export function postModelManagerV2GetModelComparisonConfig(
  params: model.model.IPostModelManagerV2GetModelComparisonConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetModelComparisonConfigResponse>('/webApi/model/manager/v2/getModelComparisonConfig', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507821, catid=257170, projectId=35802, created=2025-03-03 18:54:48, last-modified=2025-05-26 11:26:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelMetaTrainTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507821)
 */
export function postModelManagerV2GetModelMetaTrainTask(
  params: model.model.IPostModelManagerV2GetModelMetaTrainTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetModelMetaTrainTaskResponse>('/webApi/model/manager/v2/getModelMetaTrainTask', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1514974, catid=257170, projectId=35802, created=2025-03-17 14:19:10, last-modified=2025-05-26 11:26:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelEvalPlugin)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1514974)
 */
export function postModelManagerV2GetModelEvalPlugin(
  params: model.model.IPostModelManagerV2GetModelEvalPluginParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetModelEvalPluginResponse>('/webApi/model/manager/v2/getModelEvalPlugin', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1517483, catid=257170, projectId=35802, created=2025-04-10 15:39:09, last-modified=2025-05-26 11:26:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelMetaEvalPluginList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1517483)
 */
export function postModelManagerV2GetModelMetaEvalPluginList(
  params: model.model.IPostModelManagerV2GetModelMetaEvalPluginListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetModelMetaEvalPluginListResponse>(
    '/webApi/model/manager/v2/getModelMetaEvalPluginList',
    {
      data: pick(params, ['modelMetaId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507802, catid=257170, projectId=35802, created=2025-03-03 18:54:47, last-modified=2025-05-26 11:26:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelMetaConfigFile)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507802)
 */
export function postModelManagerV2GetModelMetaConfigFile(
  params: model.model.IPostModelManagerV2GetModelMetaConfigFileParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2GetModelMetaConfigFileResponse>('/webApi/model/manager/v2/getModelMetaConfigFile', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1516573, catid=257170, projectId=35802, created=2025-03-26 20:31:32, last-modified=2025-05-26 11:26:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/getModelConfigFilesByTokenizerPath)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516573)
 */
export function getModelManagerV2GetModelConfigFilesByTokenizerPath(
  params: model.model.IGetModelManagerV2GetModelConfigFilesByTokenizerPathParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerV2GetModelConfigFilesByTokenizerPathResponse>(
    '/webApi/model/manager/v2/getModelConfigFilesByTokenizerPath',
    {
      params: pick(params, ['modelConfigPath']),
      ...options,
    }
  );
}

/**
 * @desc id=1516578, catid=257170, projectId=35802, created=2025-03-26 20:31:35, last-modified=2025-05-26 11:26:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/v2/reloadModelMetaConfigFile)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516578)
 */
export function postModelManagerV2ReloadModelMetaConfigFile(
  params: model.model.IPostModelManagerV2ReloadModelMetaConfigFileParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerV2ReloadModelMetaConfigFileResponse>('/webApi/model/manager/v2/reloadModelMetaConfigFile', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1457607, catid=250695, projectId=35802, created=2024-08-22 17:48:06, last-modified=2024-08-22 17:48:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/search/listSearchModelDynamicFieldValues)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1457607)
 */
export function postModelSearchListSearchModelDynamicFieldValues(
  params: model.model.IPostModelSearchListSearchModelDynamicFieldValuesParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelSearchListSearchModelDynamicFieldValuesResponse>(
    '/webApi/model/search/listSearchModelDynamicFieldValues',
    {
      data: pick(params, ['fieldValues', 'identification']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1457615, catid=250695, projectId=35802, created=2024-08-22 17:48:06, last-modified=2024-08-22 17:48:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/search/searchModelByAllField)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1457615)
 */
export function postModelSearchSearchModelByAllField(
  params: model.model.IPostModelSearchSearchModelByAllFieldParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelSearchSearchModelByAllFieldResponse>('/webApi/model/search/searchModelByAllField', {
    data: pick(params, [
      'curPage',
      'evalBenchmarks',
      'evalModelName',
      'evalModelNameRegularMode',
      'fieldInputValues',
      'modelName',
      'modelNameRegularMode',
      'modelPath',
      'modelPathRegularMode',
      'showCount',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1457620, catid=250695, projectId=35802, created=2024-08-22 17:48:06, last-modified=2024-08-22 17:48:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/search/searchModelByEvalModelName)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1457620)
 */
export function postModelSearchSearchModelByEvalModelName(
  params: model.model.IPostModelSearchSearchModelByEvalModelNameParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelSearchSearchModelByEvalModelNameResponse>('/webApi/model/search/searchModelByEvalModelName', {
    data: pick(params, ['evalModelNames']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1457609, catid=250695, projectId=35802, created=2024-08-22 17:48:06, last-modified=2024-08-22 17:48:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/search/listSearchModelDynamicFields)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1457609)
 */
export function getModelSearchListSearchModelDynamicFields(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelSearchListSearchModelDynamicFieldsResponse>('/webApi/model/search/listSearchModelDynamicFields', {
    ...options,
  });
}

/**
 * @desc id=1358705, catid=236686, projectId=35802, created=2024-01-19 14:38:44, last-modified=2024-01-19 15:33:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/load/diff/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1358705)
 */
export function postModelLoadDiffList(params: model.model.IPostModelLoadDiffListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelLoadDiffListResponse>('/webApi/model/load/diff/list', {
    data: pick(params, ['pageNumber', 'pageSize', 'taskName', 'operator']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1358814, catid=236686, projectId=35802, created=2024-01-19 15:11:43, last-modified=2024-01-19 15:30:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/load/diff/task/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1358814)
 */
export function postModelLoadDiffTaskDelete(params: model.model.IPostModelLoadDiffTaskDeleteParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelLoadDiffTaskDeleteResponse>('/webApi/model/load/diff/task/delete', {
    params: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1358806, catid=236686, projectId=35802, created=2024-01-19 15:10:06, last-modified=2024-01-19 15:11:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/load/diff/task/copy)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1358806)
 */
export function postModelLoadDiffTaskCopy(params: model.model.IPostModelLoadDiffTaskCopyParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelLoadDiffTaskCopyResponse>('/webApi/model/load/diff/task/copy', {
    params: pick(params, ['taskId']),
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1358791, catid=236686, projectId=35802, created=2024-01-19 15:08:56, last-modified=2024-01-19 15:11:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/load/diff/task/run)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1358791)
 */
export function postModelLoadDiffTaskRun(params: model.model.IPostModelLoadDiffTaskRunParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelLoadDiffTaskRunResponse>('/webApi/model/load/diff/task/run', {
    params: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1358747, catid=236686, projectId=35802, created=2024-01-19 14:48:58, last-modified=2024-01-19 15:41:25
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/load/diff/add)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1358747)
 */
export function postModelLoadDiffAdd(params: model.model.IPostModelLoadDiffAddParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelLoadDiffAddResponse>('/webApi/model/load/diff/add', {
    data: pick(params, [
      'id',
      'taskName',
      'fileId',
      'ipAInfo',
      'ipBInfo',
      'modelName',
      'modelVersion',
      'config',
      'quakeType',
      'requestCount',
      'qpsStage',
      'creator',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1358789, catid=236686, projectId=35802, created=2024-01-19 15:07:52, last-modified=2024-01-19 15:08:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/load/diff/report/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1358789)
 */
export function getModelLoadDiffReportDetail(params: model.model.IGetModelLoadDiffReportDetailParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelLoadDiffReportDetailResponse>('/webApi/model/load/diff/report/detail', {
    params: pick(params, ['reportId']),
    ...options,
  });
}

/**
 * @desc id=1358776, catid=236686, projectId=35802, created=2024-01-19 15:00:35, last-modified=2024-01-19 15:33:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/load/diff/run/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1358776)
 */
export function postModelLoadDiffRunList(params: model.model.IPostModelLoadDiffRunListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelLoadDiffRunListResponse>('/webApi/model/load/diff/run/list', {
    data: pick(params, ['pageNumber', 'pageSize', 'runId', 'taskId', 'operator', 'resultStatus']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1358754, catid=236686, projectId=35802, created=2024-01-19 14:52:54, last-modified=2024-01-19 15:42:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/load/diff/modify)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1358754)
 */
export function postModelLoadDiffModify(params: model.model.IPostModelLoadDiffModifyParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelLoadDiffModifyResponse>('/webApi/model/load/diff/modify', {
    data: pick(params, [
      'id',
      'taskName',
      'fileId',
      'ipAInfo',
      'ipBInfo',
      'modelName',
      'modelVersion',
      'config',
      'quakeType',
      'requestCount',
      'qpsStage',
      'creator',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1344413, catid=234344, projectId=35802, created=2023-12-15 17:15:31, last-modified=2025-05-26 11:26:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/trainTrace/listModelSimpleRelation)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344413)
 */
export function postModelTrainTraceListModelSimpleRelation(
  params: model.model.IPostModelTrainTraceListModelSimpleRelationParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelTrainTraceListModelSimpleRelationResponse>('/webApi/model/trainTrace/listModelSimpleRelation', {
    data: pick(params, ['curPage', 'mlpJobCreator', 'mlpJobId', 'modelName', 'modelPath', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1344410, catid=234344, projectId=35802, created=2023-12-15 17:15:31, last-modified=2025-05-26 11:26:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/trainTrace/listModelCheckpointByMlpJob)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344410)
 */
export function postModelTrainTraceListModelCheckpointByMlpJob(
  params: model.model.IPostModelTrainTraceListModelCheckpointByMlpJobParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelTrainTraceListModelCheckpointByMlpJobResponse>(
    '/webApi/model/trainTrace/listModelCheckpointByMlpJob',
    {
      data: pick(params, ['curPage', 'mlpJobId', 'modelCheckpoint', 'showCount']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1344408, catid=234344, projectId=35802, created=2023-12-15 17:15:30, last-modified=2025-05-26 11:26:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/trainTrace/listConfigByMlpJob)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344408)
 */
export function getModelTrainTraceListConfigByMlpJob(
  params: model.model.IGetModelTrainTraceListConfigByMlpJobParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelTrainTraceListConfigByMlpJobResponse>('/webApi/model/trainTrace/listConfigByMlpJob', {
    params: pick(params, ['mlpJobId']),
    ...options,
  });
}

/**
 * @desc id=1455452, catid=226762, projectId=35802, created=2024-08-13 19:25:32, last-modified=2025-05-26 11:26:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/searchModelByAllField)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1455452)
 */
export function postModelManagerSearchModelByAllField(
  params: model.model.IPostModelManagerSearchModelByAllFieldParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerSearchModelByAllFieldResponse>('/webApi/model/manager/searchModelByAllField', {
    data: pick(params, [
      'baseSftField',
      'bastCheckPoint',
      'benchmarkingField',
      'curPage',
      'evalBenchmarks',
      'evalModelName',
      'evalModelNameRegularMode',
      'fieldInputValues',
      'modelName',
      'modelNameRegularMode',
      'modelPath',
      'modelPathRegularMode',
      'onlyMasterEvalName',
      'scene',
      'showCount',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1484382, catid=226762, projectId=35802, created=2024-10-29 16:07:48, last-modified=2024-10-30 16:04:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/createEvalScoreRecordAll)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1484382)
 */
export function postModelManagerCreateEvalScoreRecordAll(options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelManagerCreateEvalScoreRecordAllResponse>('/webApi/model/manager/createEvalScoreRecordAll', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1482887, catid=226762, projectId=35802, created=2024-10-23 10:27:17, last-modified=2024-10-30 16:04:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/modifyColdBackupPath)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1482887)
 */
export function postModelManagerModifyColdBackupPath(options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelManagerModifyColdBackupPathResponse>('/webApi/model/manager/modifyColdBackupPath', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1484394, catid=226762, projectId=35802, created=2024-10-29 16:07:54, last-modified=2025-05-26 11:26:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/updateEvalRecordScore)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1484394)
 */
export function postModelManagerUpdateEvalRecordScore(
  params: model.model.IPostModelManagerUpdateEvalRecordScoreParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerUpdateEvalRecordScoreResponse>('/webApi/model/manager/updateEvalRecordScore', {
    params: pick(params, ['evalRecordIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1483714, catid=226762, projectId=35802, created=2024-10-28 15:11:20, last-modified=2024-10-28 15:11:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/createEvalScoreRecord)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1483714)
 */
export function postModelManagerCreateEvalScoreRecord(options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelManagerCreateEvalScoreRecordResponse>('/webApi/model/manager/createEvalScoreRecord', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1380018, catid=226762, projectId=35802, created=2024-03-13 14:08:21, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/deleteModelCheckpoint)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1380018)
 */
export function postModelManagerDeleteModelCheckpoint(
  params: model.model.IPostModelManagerDeleteModelCheckpointParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerDeleteModelCheckpointResponse>('/webApi/model/manager/deleteModelCheckpoint', {
    data: pick(params, ['modelIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1362722, catid=226762, projectId=35802, created=2024-01-29 11:08:24, last-modified=2025-05-26 11:25:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/refreshModelCheckpoint)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1362722)
 */
export function postModelManagerRefreshModelCheckpoint(
  params: model.model.IPostModelManagerRefreshModelCheckpointParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerRefreshModelCheckpointResponse>('/webApi/model/manager/refreshModelCheckpoint', {
    data: pick(params, ['modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1470929, catid=226762, projectId=35802, created=2024-09-12 15:46:36, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/cancelCollection)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1470929)
 */
export function postModelManagerCancelCollection(
  params: model.model.IPostModelManagerCancelCollectionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerCancelCollectionResponse>('/webApi/model/manager/cancelCollection', {
    params: pick(params, ['metaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1394916, catid=226762, projectId=35802, created=2024-04-15 14:19:01, last-modified=2025-05-26 11:26:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/scanModelAndValidateModel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1394916)
 */
export function getModelManagerScanModelAndValidateModel(
  params: model.model.IGetModelManagerScanModelAndValidateModelParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerScanModelAndValidateModelResponse>('/webApi/model/manager/scanModelAndValidateModel', {
    params: pick(params, ['modelMetaId']),
    ...options,
  });
}

/**
 * @desc id=1503442, catid=226762, projectId=35802, created=2024-12-27 16:22:10, last-modified=2025-05-26 11:26:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/updateModelRelationByMlpJobId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503442)
 */
export function getModelManagerUpdateModelRelationByMlpJobId(
  params: model.model.IGetModelManagerUpdateModelRelationByMlpJobIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerUpdateModelRelationByMlpJobIdResponse>(
    '/webApi/model/manager/updateModelRelationByMlpJobId',
    {
      params: pick(params, ['mlpJobId', 'modelMetaId']),
      ...options,
    }
  );
}

/**
 * @desc id=1434080, catid=226762, projectId=35802, created=2024-07-09 20:42:42, last-modified=2024-08-19 15:08:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/getTagsValue)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1434080)
 */
export function getModelManagerGetTagsValue(params: model.model.IGetModelManagerGetTagsValueParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerGetTagsValueResponse>('/webApi/model/manager/getTagsValue', {
    params: pick(params, ['modelTagList', 'resourceType']),
    ...options,
  });
}

/**
 * @desc id=1469964, catid=226762, projectId=35802, created=2024-09-10 19:23:32, last-modified=2025-05-26 11:26:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/searchMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1469964)
 */
export function postModelManagerSearchMeta(params: model.model.IPostModelManagerSearchMetaParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelManagerSearchMetaResponse>('/webApi/model/manager/searchMeta', {
    data: pick(params, [
      'aboutMe',
      'curPage',
      'fieldInputValues',
      'modelName',
      'modelPath',
      'modelSizeParam',
      'myCollection',
      'orderType',
      'showCount',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1470133, catid=226762, projectId=35802, created=2024-09-11 17:16:41, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/addCollection)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1470133)
 */
export function postModelManagerAddCollection(
  params: model.model.IPostModelManagerAddCollectionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerAddCollectionResponse>('/webApi/model/manager/addCollection', {
    params: pick(params, ['metaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1470264, catid=226762, projectId=35802, created=2024-09-12 14:48:30, last-modified=2025-05-26 11:26:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/updateTab)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1470264)
 */
export function postModelManagerUpdateTab(params: model.model.IPostModelManagerUpdateTabParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelManagerUpdateTabResponse>('/webApi/model/manager/updateTab', {
    data: pick(params, ['content', 'creator', 'cts', 'lastUpdater', 'resourceId', 'resourceType', 'tabId', 'tabType', 'uts']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1294878, catid=226762, projectId=35802, created=2023-10-11 17:14:48, last-modified=2025-05-26 11:26:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/updateModelMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1294878)
 */
export function postModelManagerUpdateModelMeta(
  params: model.model.IPostModelManagerUpdateModelMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerUpdateModelMetaResponse>('/webApi/model/manager/updateModelMeta', {
    data: pick(params, [
      'autoEval',
      'autoEvalBaseExperimentId',
      'autoEvalCkptRegex',
      'autoEvalDataSize',
      'autoEvalEvaluateModelName',
      'autoEvalExperimentName',
      'autoEvalModelFormat',
      'benchmarking',
      'bestCheckpoint',
      'bestCheckpointModelInfo',
      'collected',
      'contextLength',
      'cts',
      'description',
      'extTokenizerPath',
      'extendEnvParams',
      'extraInfo',
      'extraInfoFields',
      'metaId',
      'modelCheckpoint',
      'modelDomain',
      'modelFamily',
      'modelId',
      'modelName',
      'modelNum',
      'modelSeries',
      'modelShortName',
      'modelSize',
      'modelStage',
      'modelTargetLanguage',
      'modelType',
      'modelUserSettings',
      'orgName',
      'overwriteExtraInfoFields',
      'ownerList',
      'pluginBindList',
      'promptPrefix',
      'promptSuffix',
      'roleList',
      'stopSequences',
      'tagValues',
      'trainDataVersion',
      'uniqueIdentification',
      'userList',
      'uts',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1482374, catid=226762, projectId=35802, created=2024-10-21 15:54:33, last-modified=2025-05-26 11:26:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/updateModelTokenAndFlops)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1482374)
 */
export function postModelManagerUpdateModelTokenAndFlops(
  params: model.model.IPostModelManagerUpdateModelTokenAndFlopsParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerUpdateModelTokenAndFlopsResponse>('/webApi/model/manager/updateModelTokenAndFlops', {
    data: pick(params, [
      'autoEval',
      'autoEvalBaseExperimentId',
      'autoEvalCkptRegex',
      'autoEvalDataSize',
      'autoEvalEvaluateModelName',
      'autoEvalExperimentName',
      'autoEvalModelFormat',
      'benchmarking',
      'bestCheckpoint',
      'bestCheckpointModelInfo',
      'collected',
      'contextLength',
      'cts',
      'description',
      'extTokenizerPath',
      'extendEnvParams',
      'extraInfo',
      'extraInfoFields',
      'metaId',
      'modelCheckpoint',
      'modelDomain',
      'modelFamily',
      'modelId',
      'modelName',
      'modelNum',
      'modelSeries',
      'modelShortName',
      'modelSize',
      'modelStage',
      'modelTargetLanguage',
      'modelType',
      'modelUserSettings',
      'orgName',
      'overwriteExtraInfoFields',
      'ownerList',
      'pluginBindList',
      'promptPrefix',
      'promptSuffix',
      'roleList',
      'stopSequences',
      'tagValues',
      'trainDataVersion',
      'uniqueIdentification',
      'userList',
      'uts',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1344406, catid=226762, projectId=35802, created=2023-12-15 17:15:30, last-modified=2025-05-26 11:26:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/updateModelTag)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344406)
 */
export function postModelManagerUpdateModelTag(
  params: model.model.IPostModelManagerUpdateModelTagParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerUpdateModelTagResponse>('/webApi/model/manager/updateModelTag', {
    data: pick(params, ['modelId', 'tagValues']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1471747, catid=226762, projectId=35802, created=2024-09-14 17:06:06, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/getEvalHistoryBatch)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1471747)
 */
export function postModelManagerGetEvalHistoryBatch(
  params: model.model.IPostModelManagerGetEvalHistoryBatchParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerGetEvalHistoryBatchResponse>('/webApi/model/manager/getEvalHistoryBatch', {
    data: pick(params, ['curPage', 'evalBenchmark', 'experimentTaskDetailIds', 'modelId', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1503123, catid=226762, projectId=35802, created=2024-12-24 19:17:54, last-modified=2025-05-26 11:25:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelRelationUpstream)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503123)
 */
export function postModelManagerListModelRelationUpstream(
  params: model.model.IPostModelManagerListModelRelationUpstreamParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerListModelRelationUpstreamResponse>('/webApi/model/manager/listModelRelationUpstream', {
    data: pick(params, ['modelIds', 'modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1503114, catid=226762, projectId=35802, created=2024-12-24 19:17:53, last-modified=2025-05-26 11:25:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelRelationDownstream)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503114)
 */
export function postModelManagerListModelRelationDownstream(
  params: model.model.IPostModelManagerListModelRelationDownstreamParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerListModelRelationDownstreamResponse>('/webApi/model/manager/listModelRelationDownstream', {
    data: pick(params, ['modelIds', 'modelMetaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1332160, catid=226762, projectId=35802, created=2023-11-24 10:25:01, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/getModelMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332160)
 */
export function getModelManagerGetModelMeta(params: model.model.IGetModelManagerGetModelMetaParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerGetModelMetaResponse>('/webApi/model/manager/getModelMeta', {
    params: pick(params, ['modelMetaId']),
    ...options,
  });
}

/**
 * @desc id=1346990, catid=226762, projectId=35802, created=2023-12-21 16:14:06, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/getModelCheckpointByModelMetaId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1346990)
 */
export function getModelManagerGetModelCheckpointByModelMetaId(
  params: model.model.IGetModelManagerGetModelCheckpointByModelMetaIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerGetModelCheckpointByModelMetaIdResponse>(
    '/webApi/model/manager/getModelCheckpointByModelMetaId',
    {
      params: pick(params, ['modelCheckpoint', 'modelMetaId']),
      ...options,
    }
  );
}

/**
 * @desc id=1344386, catid=226762, projectId=35802, created=2023-12-15 17:15:29, last-modified=2025-05-26 11:25:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344386)
 */
export function postModelManagerListModelInfo(
  params: model.model.IPostModelManagerListModelInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerListModelInfoResponse>('/webApi/model/manager/listModelInfo', {
    data: pick(params, ['curPage', 'ignoreStatus', 'modelCheckpoint', 'modelId', 'modelMetaId', 'modelTagId', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1356213, catid=226762, projectId=35802, created=2024-01-16 18:44:40, last-modified=2025-05-26 11:25:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelInfoForFork)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1356213)
 */
export function postModelManagerListModelInfoForFork(
  params: model.model.IPostModelManagerListModelInfoForForkParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerListModelInfoForForkResponse>('/webApi/model/manager/listModelInfoForFork', {
    data: pick(params, ['curPage', 'experimentId', 'modelFormat', 'modelName', 'modelPath', 'regularMode', 'showCount', 'tagValue']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1455688, catid=226762, projectId=35802, created=2024-08-14 15:46:02, last-modified=2025-05-26 11:25:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listSearchModelDynamicFieldValues)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1455688)
 */
export function postModelManagerListSearchModelDynamicFieldValues(
  params: model.model.IPostModelManagerListSearchModelDynamicFieldValuesParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerListSearchModelDynamicFieldValuesResponse>(
    '/webApi/model/manager/listSearchModelDynamicFieldValues',
    {
      data: pick(params, ['fieldValues', 'identification']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1490916, catid=226762, projectId=35802, created=2024-11-15 16:00:32, last-modified=2025-05-26 11:25:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/queryDisableQueue)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1490916)
 */
export function postModelManagerQueryDisableQueue(
  params: model.model.IPostModelManagerQueryDisableQueueParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerQueryDisableQueueResponse>('/webApi/model/manager/queryDisableQueue', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1469957, catid=226762, projectId=35802, created=2024-09-10 19:23:29, last-modified=2024-09-10 19:23:29
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/getExperimentByModelId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1469957)
 */
export function postModelManagerGetExperimentByModelId(
  params: model.model.IPostModelManagerGetExperimentByModelIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerGetExperimentByModelIdResponse>('/webApi/model/manager/getExperimentByModelId', {
    data: pick(params, ['curPage', 'id', 'showCount', 'type']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1434081, catid=226762, projectId=35802, created=2024-07-09 20:42:43, last-modified=2024-08-19 15:08:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listEvalByModelId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1434081)
 */
export function getModelManagerListEvalByModelId(
  params: model.model.IGetModelManagerListEvalByModelIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerListEvalByModelIdResponse>('/webApi/model/manager/listEvalByModelId', {
    params: pick(params, ['modelId']),
    ...options,
  });
}

/**
 * @desc id=1346986, catid=226762, projectId=35802, created=2023-12-21 16:14:06, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/authOwnForModelMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1346986)
 */
export function getModelManagerAuthOwnForModelMeta(
  params: model.model.IGetModelManagerAuthOwnForModelMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerAuthOwnForModelMetaResponse>('/webApi/model/manager/authOwnForModelMeta', {
    params: pick(params, ['modelMetaId']),
    ...options,
  });
}

/**
 * @desc id=1485305, catid=226762, projectId=35802, created=2024-11-04 15:52:28, last-modified=2025-05-26 11:25:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/modelPluginCheck)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1485305)
 */
export function postModelManagerModelPluginCheck(
  params: model.model.IPostModelManagerModelPluginCheckParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerModelPluginCheckResponse>('/webApi/model/manager/modelPluginCheck', {
    data: pick(params, ['modelMetaIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1484390, catid=226762, projectId=35802, created=2024-10-29 16:07:49, last-modified=2024-10-30 16:04:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/createEvalScoreRecordBatch)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1484390)
 */
export function postModelManagerCreateEvalScoreRecordBatch(
  params: model.model.IPostModelManagerCreateEvalScoreRecordBatchParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerCreateEvalScoreRecordBatchResponse>('/webApi/model/manager/createEvalScoreRecordBatch', {
    params: pick(params, ['modelIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1455453, catid=226762, projectId=35802, created=2024-08-13 19:25:33, last-modified=2025-05-26 11:26:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/searchModelByEvalModelName)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1455453)
 */
export function postModelManagerSearchModelByEvalModelName(
  params: model.model.IPostModelManagerSearchModelByEvalModelNameParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerSearchModelByEvalModelNameResponse>('/webApi/model/manager/searchModelByEvalModelName', {
    data: pick(params, ['evalModelNames']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1455449, catid=226762, projectId=35802, created=2024-08-13 19:25:31, last-modified=2025-05-26 11:25:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listSearchModelDynamicFields)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1455449)
 */
export function getModelManagerListSearchModelDynamicFields(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListSearchModelDynamicFieldsResponse>('/webApi/model/manager/listSearchModelDynamicFields', {
    ...options,
  });
}

/**
 * @desc id=1448840, catid=226762, projectId=35802, created=2024-07-26 14:01:32, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listCheckPointId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1448840)
 */
export function getModelManagerListCheckPointId(
  params: model.model.IGetModelManagerListCheckPointIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerListCheckPointIdResponse>('/webApi/model/manager/listCheckPointId', {
    params: pick(params, ['userInput']),
    ...options,
  });
}

/**
 * @desc id=1294882, catid=226762, projectId=35802, created=2023-10-11 17:19:15, last-modified=2025-05-26 11:25:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelNames)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1294882)
 */
export function getModelManagerListModelNames(
  params: model.model.IGetModelManagerListModelNamesParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerListModelNamesResponse>('/webApi/model/manager/listModelNames', {
    params: pick(params, ['modelName']),
    ...options,
  });
}

/**
 * @desc id=1499786, catid=226762, projectId=35802, created=2024-12-10 17:51:35, last-modified=2025-05-26 11:25:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/registerDownLoadModelInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499786)
 */
export function postModelManagerRegisterDownLoadModelInfo(
  params: model.model.IPostModelManagerRegisterDownLoadModelInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerRegisterDownLoadModelInfoResponse>('/webApi/model/manager/registerDownLoadModelInfo', {
    data: pick(params, ['downloadRecordId', 'modelFamily', 'modelSeries', 'modelSize']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1481501, catid=226762, projectId=35802, created=2024-10-15 19:41:26, last-modified=2025-05-26 11:25:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/registerColdBackupModel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1481501)
 */
export function postModelManagerRegisterColdBackupModel(
  params: model.model.IPostModelManagerRegisterColdBackupModelParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerRegisterColdBackupModelResponse>('/webApi/model/manager/registerColdBackupModel', {
    params: pick(params, ['metaId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1344404, catid=226762, projectId=35802, created=2023-12-15 17:15:30, last-modified=2025-05-26 11:26:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/registerModelInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344404)
 */
export function postModelManagerRegisterModelInfo(
  params: model.model.IPostModelManagerRegisterModelInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerRegisterModelInfoResponse>('/webApi/model/manager/registerModelInfo', {
    data: pick(params, ['requests']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1294874, catid=226762, projectId=35802, created=2023-10-11 17:14:48, last-modified=2025-05-26 11:25:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/register)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1294874)
 */
export function postModelManagerRegister(params: model.model.IPostModelManagerRegisterParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelManagerRegisterResponse>('/webApi/model/manager/register', {
    data: pick(params, ['modelInfo', 'modelMeta']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1499792, catid=226762, projectId=35802, created=2024-12-10 17:51:35, last-modified=2025-05-26 11:26:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/registerModelPath)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499792)
 */
export function postModelManagerRegisterModelPath(
  params: model.model.IPostModelManagerRegisterModelPathParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerRegisterModelPathResponse>('/webApi/model/manager/registerModelPath', {
    data: pick(params, [
      'creator',
      'customTagValues',
      'extraInfoFields',
      'mlpJobId',
      'modelBasePath',
      'modelFormat',
      'modelMetaId',
      'modelPathType',
      'pp',
      'registerChannel',
      'registerScene',
      'taskDetailId',
      'tp',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1455889, catid=226762, projectId=35802, created=2024-08-15 14:45:00, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/bestCheckpoint)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1455889)
 */
export function postModelManagerBestCheckpoint(
  params: model.model.IPostModelManagerBestCheckpointParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerBestCheckpointResponse>('/webApi/model/manager/bestCheckpoint', {
    data: pick(params, ['modelIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1470261, catid=226762, projectId=35802, created=2024-09-12 14:48:27, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/getTabById)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1470261)
 */
export function getModelManagerGetTabById(params: model.model.IGetModelManagerGetTabByIdParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerGetTabByIdResponse>('/webApi/model/manager/getTabById', {
    params: pick(params, ['id', 'tabType', 'type']),
    ...options,
  });
}

/**
 * @desc id=1469947, catid=226762, projectId=35802, created=2024-09-10 19:23:28, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/getCkptByMetaId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1469947)
 */
export function postModelManagerGetCkptByMetaId(
  params: model.model.IPostModelManagerGetCkptByMetaIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerGetCkptByMetaIdResponse>('/webApi/model/manager/getCkptByMetaId', {
    data: pick(params, ['curPage', 'evalDatasetName', 'id', 'showCount', 'statName', 'type']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1455896, catid=226762, projectId=35802, created=2024-08-15 14:45:02, last-modified=2025-05-26 11:25:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelMetaTagDynamicFields)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1455896)
 */
export function getModelManagerListModelMetaTagDynamicFields(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListModelMetaTagDynamicFieldsResponse>(
    '/webApi/model/manager/listModelMetaTagDynamicFields',
    {
      ...options,
    }
  );
}

/**
 * @desc id=1348322, catid=226762, projectId=35802, created=2023-12-27 14:04:13, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/getModelInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1348322)
 */
export function getModelManagerGetModelInfo(params: model.model.IGetModelManagerGetModelInfoParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerGetModelInfoResponse>('/webApi/model/manager/getModelInfo', {
    params: pick(params, ['modelId']),
    ...options,
  });
}

/**
 * @desc id=1294857, catid=226762, projectId=35802, created=2023-10-11 17:14:47, last-modified=2025-05-26 11:25:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1294857)
 */
export function postModelManagerListModelMeta(
  params: model.model.IPostModelManagerListModelMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerListModelMetaResponse>('/webApi/model/manager/listModelMeta', {
    data: pick(params, ['curPage', 'modelFamily', 'modelName', 'modelPath', 'modelStage', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1307434, catid=226762, projectId=35802, created=2023-10-19 11:01:39, last-modified=2025-05-26 11:25:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelSize)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1307434)
 */
export function getModelManagerListModelSize(params: model.model.IGetModelManagerListModelSizeParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListModelSizeResponse>('/webApi/model/manager/listModelSize', {
    params: pick(params, ['modelFamily']),
    ...options,
  });
}

/**
 * @desc id=1388277, catid=226762, projectId=35802, created=2024-04-02 10:12:56, last-modified=2024-09-11 16:13:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelSizeV2)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1388277)
 */
export function getModelManagerListModelSizeV2(
  params: model.model.IGetModelManagerListModelSizeV2Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerListModelSizeV2Response>('/webApi/model/manager/listModelSizeV2', {
    params: pick(params, ['modelFamily', 'modelType']),
    ...options,
  });
}

/**
 * @desc id=1294846, catid=226762, projectId=35802, created=2023-10-11 17:14:46, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelFamily)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1294846)
 */
export function getModelManagerListModelFamily(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListModelFamilyResponse>('/webApi/model/manager/listModelFamily', {
    ...options,
  });
}

/**
 * @desc id=1387687, catid=226762, projectId=35802, created=2024-03-29 17:48:11, last-modified=2025-05-26 11:25:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelFamilyV2)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1387687)
 */
export function getModelManagerListModelFamilyV2(
  params: model.model.IGetModelManagerListModelFamilyV2Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerListModelFamilyV2Response>('/webApi/model/manager/listModelFamilyV2', {
    params: pick(params, ['modelType']),
    ...options,
  });
}

/**
 * @desc id=1344395, catid=226762, projectId=35802, created=2023-12-15 17:15:30, last-modified=2024-12-10 10:52:16
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelTags)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344395)
 */
export function getModelManagerListModelTags(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListModelTagsResponse>('/webApi/model/manager/listModelTags', {
    ...options,
  });
}

/**
 * @desc id=1346997, catid=226762, projectId=35802, created=2023-12-21 16:14:06, last-modified=2024-12-10 10:52:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listCustomModelTags)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1346997)
 */
export function getModelManagerListCustomModelTags(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListCustomModelTagsResponse>('/webApi/model/manager/listCustomModelTags', {
    ...options,
  });
}

/**
 * @desc id=1457599, catid=226762, projectId=35802, created=2024-08-22 17:48:04, last-modified=2025-05-26 11:25:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelTagsV2)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1457599)
 */
export function getModelManagerListModelTagsV2(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListModelTagsV2Response>('/webApi/model/manager/listModelTagsV2', {
    ...options,
  });
}

/**
 * @desc id=1457594, catid=226762, projectId=35802, created=2024-08-22 17:48:02, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listCustomModelTagsV2)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1457594)
 */
export function getModelManagerListCustomModelTagsV2(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListCustomModelTagsV2Response>('/webApi/model/manager/listCustomModelTagsV2', {
    ...options,
  });
}

/**
 * @desc id=1294848, catid=226762, projectId=35802, created=2023-10-11 17:14:47, last-modified=2025-05-26 11:25:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelFormat)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1294848)
 */
export function getModelManagerListModelFormat(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListModelFormatResponse>('/webApi/model/manager/listModelFormat', {
    ...options,
  });
}

/**
 * @desc id=1294864, catid=226762, projectId=35802, created=2023-10-11 17:14:47, last-modified=2025-05-26 11:25:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelStage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1294864)
 */
export function getModelManagerListModelStage(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelManagerListModelStageResponse>('/webApi/model/manager/listModelStage', {
    ...options,
  });
}

/**
 * @desc id=1387692, catid=226762, projectId=35802, created=2024-03-29 17:48:12, last-modified=2025-05-26 11:25:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/listModelStageV2)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1387692)
 */
export function getModelManagerListModelStageV2(
  params: model.model.IGetModelManagerListModelStageV2Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerListModelStageV2Response>('/webApi/model/manager/listModelStageV2', {
    params: pick(params, ['modelType']),
    ...options,
  });
}

/**
 * @desc id=1351478, catid=226762, projectId=35802, created=2024-01-09 15:36:31, last-modified=2024-12-10 10:52:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/completeModelAndMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1351478)
 */
export function postModelManagerCompleteModelAndMeta(
  params: model.model.IPostModelManagerCompleteModelAndMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerCompleteModelAndMetaResponse>('/webApi/model/manager/completeModelAndMeta', {
    data: pick(params, ['experimentTaskDetailId', 'modelCheckpoint', 'modelFormat', 'modelMeta', 'modelPath', 'pp', 'tp']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1469950, catid=226762, projectId=35802, created=2024-09-10 19:23:28, last-modified=2025-05-26 11:25:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/getEvalByModelId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1469950)
 */
export function postModelManagerGetEvalByModelId(
  params: model.model.IPostModelManagerGetEvalByModelIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelManagerGetEvalByModelIdResponse>('/webApi/model/manager/getEvalByModelId', {
    data: pick(params, ['curPage', 'evalDatasetName', 'id', 'showCount', 'statName', 'type']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1481878, catid=226762, projectId=35802, created=2024-10-17 18:48:33, last-modified=2025-05-26 11:26:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/manager/searchModelByMetaAndPath)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1481878)
 */
export function getModelManagerSearchModelByMetaAndPath(
  params: model.model.IGetModelManagerSearchModelByMetaAndPathParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelManagerSearchModelByMetaAndPathResponse>('/webApi/model/manager/searchModelByMetaAndPath', {
    params: pick(params, ['metaId', 'pathQuery']),
    ...options,
  });
}

/**
 * @desc id=1295723, catid=226914, projectId=35802, created=2023-10-12 16:24:47, last-modified=2025-05-26 11:25:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295723)
 */
export function postModelExperimentTaskGetDetail(
  params: model.model.IPostModelExperimentTaskGetDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskGetDetailResponse>('/webApi/model/experiment/task/getDetail', {
    data: pick(params, ['retryNum', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1499689, catid=226914, projectId=35802, created=2024-12-10 10:52:09, last-modified=2024-12-11 16:15:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/experimentSourceTransfer)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499689)
 */
export function postModelExperimentExperimentSourceTransfer(
  params: model.model.IPostModelExperimentExperimentSourceTransferParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentExperimentSourceTransferResponse>('/webApi/model/experiment/experimentSourceTransfer', {
    params: pick(params, ['metaIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1295716, catid=226914, projectId=35802, created=2023-10-12 16:24:47, last-modified=2025-05-26 11:25:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/saveExperimentConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295716)
 */
export function postModelExperimentSaveExperimentConfig(
  params: model.model.IPostModelExperimentSaveExperimentConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentSaveExperimentConfigResponse>('/webApi/model/experiment/saveExperimentConfig', {
    data: pick(params, ['edges', 'experimentId', 'feDisplayConfig', 'nodes', 'slaTask']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1302914, catid=226914, projectId=35802, created=2023-10-18 16:53:36, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/saveTaskDetailNode)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1302914)
 */
export function postModelExperimentTaskSaveTaskDetailNode(
  params: model.model.IPostModelExperimentTaskSaveTaskDetailNodeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskSaveTaskDetailNodeResponse>('/webApi/model/experiment/task/saveTaskDetailNode', {
    data: pick(params, ['node', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1295684, catid=226914, projectId=35802, created=2023-10-12 16:24:45, last-modified=2025-05-26 11:25:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/create)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295684)
 */
export function postModelExperimentCreate(params: model.model.IPostModelExperimentCreateParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelExperimentCreateResponse>('/webApi/model/experiment/create', {
    data: pick(params, [
      'createSource',
      'datasetId',
      'datasetVersionId',
      'experimentDescription',
      'experimentName',
      'experimentType',
      'modelId',
      'modelMetaId',
      'projectGroup',
      'template',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1503426, catid=226914, projectId=35802, created=2024-12-27 16:07:05, last-modified=2025-05-26 11:25:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/createAndSaveTrainLeakageExperiment)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503426)
 */
export function postModelExperimentCreateAndSaveTrainLeakageExperiment(
  params: model.model.IPostModelExperimentCreateAndSaveTrainLeakageExperimentParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentCreateAndSaveTrainLeakageExperimentResponse>(
    '/webApi/model/experiment/createAndSaveTrainLeakageExperiment',
    {
      data: pick(params, ['createRequest', 'operator', 'saveRequest']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1295689, catid=226914, projectId=35802, created=2023-10-12 16:24:46, last-modified=2025-05-26 11:25:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295689)
 */
export function postModelExperimentDelete(params: model.model.IPostModelExperimentDeleteParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelExperimentDeleteResponse>('/webApi/model/experiment/delete', {
    data: pick(params, ['experimentIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1485148, catid=226914, projectId=35802, created=2024-11-01 17:04:08, last-modified=2025-05-26 11:25:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/quickExperiment)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1485148)
 */
export function postModelExperimentQuickExperiment(
  params: model.model.IPostModelExperimentQuickExperimentParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentQuickExperimentResponse>('/webApi/model/experiment/quickExperiment', {
    data: pick(params, ['experimentParams']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1494090, catid=226914, projectId=35802, created=2024-11-19 17:36:07, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/types)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1494090)
 */
export function getModelExperimentTypes(params: model.model.IGetModelExperimentTypesParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelExperimentTypesResponse>('/webApi/model/experiment/types', {
    params: pick(params, ['businessType']),
    ...options,
  });
}

/**
 * @desc id=1494088, catid=226914, projectId=35802, created=2024-11-19 17:36:05, last-modified=2025-05-26 11:25:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/scene)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1494088)
 */
export function getModelExperimentScene(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelExperimentSceneResponse>('/webApi/model/experiment/scene', {
    ...options,
  });
}

/**
 * @desc id=1356203, catid=226914, projectId=35802, created=2024-01-16 18:44:37, last-modified=2025-05-26 11:25:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/batchFork)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1356203)
 */
export function postModelExperimentBatchFork(params: model.model.IPostModelExperimentBatchForkParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelExperimentBatchForkResponse>('/webApi/model/experiment/batchFork', {
    data: pick(params, [
      'baseExperimentId',
      'batchFork',
      'evaluateModelName',
      'experimentDescription',
      'experimentName',
      'feDisplayConfig',
      'modelIds',
      'projectGroup',
      'resourceType',
      'retryNum',
      'taskId',
      'version',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1356210, catid=226914, projectId=35802, created=2024-01-16 18:44:37, last-modified=2025-05-26 11:25:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/batchForkAndRun)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1356210)
 */
export function postModelExperimentBatchForkAndRun(
  params: model.model.IPostModelExperimentBatchForkAndRunParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentBatchForkAndRunResponse>('/webApi/model/experiment/batchForkAndRun', {
    data: pick(params, [
      'baseExperimentId',
      'batchFork',
      'evaluateModelName',
      'experimentDescription',
      'experimentName',
      'feDisplayConfig',
      'modelIds',
      'projectGroup',
      'resourceType',
      'retryNum',
      'taskId',
      'version',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1517445, catid=226914, projectId=35802, created=2025-04-10 11:18:19, last-modified=2025-05-26 11:25:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/dataBatchFork)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1517445)
 */
export function postModelExperimentDataBatchFork(
  params: model.model.IPostModelExperimentDataBatchForkParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentDataBatchForkResponse>('/webApi/model/experiment/dataBatchFork', {
    data: pick(params, [
      'baseExperimentId',
      'datasetVersionIds',
      'experimentDescription',
      'experimentName',
      'feDisplayConfig',
      'operator',
      'registerDatasetId',
      'registerSuffix',
      'run',
      'taskId',
      'version',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1295744, catid=226914, projectId=35802, created=2023-10-12 16:24:48, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/stop)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295744)
 */
export function postModelExperimentTaskStop(params: model.model.IPostModelExperimentTaskStopParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelExperimentTaskStopResponse>('/webApi/model/experiment/task/stop', {
    data: pick(params, ['retryNum', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1302913, catid=226914, projectId=35802, created=2023-10-18 16:53:35, last-modified=2025-05-26 11:25:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/recoverTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1302913)
 */
export function postModelExperimentTaskRecoverTask(
  params: model.model.IPostModelExperimentTaskRecoverTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskRecoverTaskResponse>('/webApi/model/experiment/task/recoverTask', {
    data: pick(params, ['retryNum', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1414064, catid=226914, projectId=35802, created=2024-05-27 14:01:05, last-modified=2025-05-26 11:25:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/listFridayResource)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1414064)
 */
export function getModelExperimentListFridayResource(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelExperimentListFridayResourceResponse>('/webApi/model/experiment/listFridayResource', {
    ...options,
  });
}

/**
 * @desc id=1502994, catid=226914, projectId=35802, created=2024-12-24 10:39:28, last-modified=2025-05-26 11:25:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/getDataNewId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502994)
 */
export function getModelExperimentGetDataNewId(
  params: model.model.IGetModelExperimentGetDataNewIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelExperimentGetDataNewIdResponse>('/webApi/model/experiment/getDataNewId', {
    params: pick(params, ['oldDataId']),
    ...options,
  });
}

/**
 * @desc id=1461102, catid=226914, projectId=35802, created=2024-08-26 17:25:58, last-modified=2025-05-26 11:25:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/getSpecialAbility)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1461102)
 */
export function getModelExperimentGetSpecialAbility(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelExperimentGetSpecialAbilityResponse>('/webApi/model/experiment/getSpecialAbility', {
    ...options,
  });
}

/**
 * @desc id=1502170, catid=226914, projectId=35802, created=2024-12-23 10:28:31, last-modified=2024-12-23 10:30:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/listExperimentBatchNumber)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502170)
 */
export function postModelExperimentListExperimentBatchNumber(
  params: model.model.IPostModelExperimentListExperimentBatchNumberParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentListExperimentBatchNumberResponse>(
    '/webApi/model/experiment/listExperimentBatchNumber',
    {
      data: pick(params, ['batchNumber']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1295733, catid=226914, projectId=35802, created=2023-10-12 16:24:48, last-modified=2025-05-26 11:25:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295733)
 */
export function postModelExperimentTaskList(params: model.model.IPostModelExperimentTaskListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelExperimentTaskListResponse>('/webApi/model/experiment/task/list', {
    data: pick(params, ['businessType', 'curPage', 'experimentId', 'experimentTypes', 'keyword', 'modelMetaId', 'showCount']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1295742, catid=226914, projectId=35802, created=2023-10-12 16:24:48, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/status)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295742)
 */
export function postModelExperimentTaskStatus(
  params: model.model.IPostModelExperimentTaskStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskStatusResponse>('/webApi/model/experiment/task/status', {
    data: pick(params, ['retryNum', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1295729, catid=226914, projectId=35802, created=2023-10-12 16:24:47, last-modified=2025-05-26 11:25:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getNodeResult)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295729)
 */
export function postModelExperimentTaskGetNodeResult(
  params: model.model.IPostModelExperimentTaskGetNodeResultParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskGetNodeResultResponse>('/webApi/model/experiment/task/getNodeResult', {
    data: pick(params, ['nodeId', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1499691, catid=226914, projectId=35802, created=2024-12-10 10:52:09, last-modified=2024-12-11 16:15:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/experimentTransfer)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499691)
 */
export function postModelExperimentExperimentTransfer(
  params: model.model.IPostModelExperimentExperimentTransferParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentExperimentTransferResponse>('/webApi/model/experiment/experimentTransfer', {
    params: pick(params, ['experimentIds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1295705, catid=226914, projectId=35802, created=2023-10-12 16:24:47, last-modified=2025-05-26 11:25:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/listExperimentConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295705)
 */
export function postModelExperimentListExperimentConfig(
  params: model.model.IPostModelExperimentListExperimentConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentListExperimentConfigResponse>('/webApi/model/experiment/listExperimentConfig', {
    data: pick(params, [
      'curPage',
      'experimentName',
      'experimentStatus',
      'experimentType',
      'modelName',
      'owner',
      'showCount',
      'slaTask',
      'sourceModelId',
      'type',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1308126, catid=226914, projectId=35802, created=2023-10-19 16:44:05, last-modified=2025-05-26 11:25:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/getExperimentBaseInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1308126)
 */
export function postModelExperimentGetExperimentBaseInfo(
  params: model.model.IPostModelExperimentGetExperimentBaseInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentGetExperimentBaseInfoResponse>('/webApi/model/experiment/getExperimentBaseInfo', {
    data: pick(params, ['businessType', 'experimentId', 'experimentName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1295694, catid=226914, projectId=35802, created=2023-10-12 16:24:46, last-modified=2025-05-26 11:25:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/getExperimentConfigDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295694)
 */
export function getModelExperimentGetExperimentConfigDetail(
  params: model.model.IGetModelExperimentGetExperimentConfigDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelExperimentGetExperimentConfigDetailResponse>('/webApi/model/experiment/getExperimentConfigDetail', {
    params: pick(params, ['experimentId']),
    ...options,
  });
}

/**
 * @desc id=1295696, catid=226914, projectId=35802, created=2023-10-12 16:24:46, last-modified=2025-05-26 11:25:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/getInferGpuNum)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295696)
 */
export function getModelExperimentGetInferGpuNum(
  params: model.model.IGetModelExperimentGetInferGpuNumParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelExperimentGetInferGpuNumResponse>('/webApi/model/experiment/getInferGpuNum', {
    params: pick(params, ['modelId']),
    ...options,
  });
}

/**
 * @desc id=1295712, catid=226914, projectId=35802, created=2023-10-12 16:24:47, last-modified=2023-12-15 17:27:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/listModelStep)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295712)
 */
export function getModelExperimentListModelStep(
  params: model.model.IGetModelExperimentListModelStepParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelExperimentListModelStepResponse>('/webApi/model/experiment/listModelStep', {
    params: pick(params, ['modelId']),
    ...options,
  });
}

/**
 * @desc id=1333069, catid=226914, projectId=35802, created=2023-11-27 16:12:57, last-modified=2025-05-26 11:25:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getTensorBoradUrl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1333069)
 */
export function postModelExperimentTaskGetTensorBoradUrl(
  params: model.model.IPostModelExperimentTaskGetTensorBoradUrlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskGetTensorBoradUrlResponse>('/webApi/model/experiment/task/getTensorBoradUrl', {
    data: pick(params, ['nodeId', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1309823, catid=226914, projectId=35802, created=2023-10-24 11:25:04, last-modified=2025-05-26 11:25:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getHopeLogUrl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1309823)
 */
export function postModelExperimentTaskGetHopeLogUrl(
  params: model.model.IPostModelExperimentTaskGetHopeLogUrlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskGetHopeLogUrlResponse>('/webApi/model/experiment/task/getHopeLogUrl', {
    data: pick(params, ['nodeId', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1309828, catid=226914, projectId=35802, created=2023-10-24 11:25:04, last-modified=2025-05-26 11:25:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getMlpLogUrl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1309828)
 */
export function postModelExperimentTaskGetMlpLogUrl(
  params: model.model.IPostModelExperimentTaskGetMlpLogUrlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskGetMlpLogUrlResponse>('/webApi/model/experiment/task/getMlpLogUrl', {
    data: pick(params, ['nodeId', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1309814, catid=226914, projectId=35802, created=2023-10-24 11:25:03, last-modified=2025-05-26 11:25:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getDockerUrl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1309814)
 */
export function postModelExperimentTaskGetDockerUrl(
  params: model.model.IPostModelExperimentTaskGetDockerUrlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskGetDockerUrlResponse>('/webApi/model/experiment/task/getDockerUrl', {
    data: pick(params, ['nodeId', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1311803, catid=226914, projectId=35802, created=2023-10-26 18:42:32, last-modified=2025-05-26 11:25:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/getEvalBenchmark)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1311803)
 */
export function getModelExperimentGetEvalBenchmark(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelExperimentGetEvalBenchmarkResponse>('/webApi/model/experiment/getEvalBenchmark', {
    ...options,
  });
}

/**
 * @desc id=1416026, catid=226914, projectId=35802, created=2024-06-03 09:43:45, last-modified=2025-05-26 11:25:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/getDataSetNameForEvalScore)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1416026)
 */
export function getModelExperimentGetDataSetNameForEvalScore(options?: FlowHttpRequestOptions) {
  return http.get<model.model.IGetModelExperimentGetDataSetNameForEvalScoreResponse>(
    '/webApi/model/experiment/getDataSetNameForEvalScore',
    {
      ...options,
    }
  );
}

/**
 * @desc id=1416035, catid=226914, projectId=35802, created=2024-06-03 09:43:50, last-modified=2025-05-26 11:25:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getMlpLogUrlForEvalScore)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1416035)
 */
export function postModelExperimentTaskGetMlpLogUrlForEvalScore(
  params: model.model.IPostModelExperimentTaskGetMlpLogUrlForEvalScoreParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskGetMlpLogUrlForEvalScoreResponse>(
    '/webApi/model/experiment/task/getMlpLogUrlForEvalScore',
    {
      data: pick(params, ['nodeId', 'taskId', 'version']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1416027, catid=226914, projectId=35802, created=2024-06-03 09:43:49, last-modified=2025-05-26 11:25:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getEvalScoreTaskUrl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1416027)
 */
export function postModelExperimentTaskGetEvalScoreTaskUrl(
  params: model.model.IPostModelExperimentTaskGetEvalScoreTaskUrlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskGetEvalScoreTaskUrlResponse>('/webApi/model/experiment/task/getEvalScoreTaskUrl', {
    data: pick(params, ['nodeId', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1478879, catid=226914, projectId=35802, created=2024-09-25 14:46:26, last-modified=2025-05-26 11:25:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getModelIdForEvalNode)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1478879)
 */
export function postModelExperimentTaskGetModelIdForEvalNode(
  params: model.model.IPostModelExperimentTaskGetModelIdForEvalNodeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskGetModelIdForEvalNodeResponse>(
    '/webApi/model/experiment/task/getModelIdForEvalNode',
    {
      data: pick(params, ['nodeId', 'taskId', 'version']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1471132, catid=226914, projectId=35802, created=2024-09-13 15:20:09, last-modified=2025-05-26 11:25:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getEvalJobId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1471132)
 */
export function getModelExperimentTaskGetEvalJobId(
  params: model.model.IGetModelExperimentTaskGetEvalJobIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelExperimentTaskGetEvalJobIdResponse>('/webApi/model/experiment/task/getEvalJobId', {
    params: pick(params, ['taskDetailId']),
    ...options,
  });
}

/**
 * @desc id=1484939, catid=226914, projectId=35802, created=2024-10-31 18:02:56, last-modified=2025-05-26 11:25:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/getOutputModelMetaId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1484939)
 */
export function getModelExperimentTaskGetOutputModelMetaId(
  params: model.model.IGetModelExperimentTaskGetOutputModelMetaIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelExperimentTaskGetOutputModelMetaIdResponse>('/webApi/model/experiment/task/getOutputModelMetaId', {
    params: pick(params, ['taskDetailId']),
    ...options,
  });
}

/**
 * @desc id=1322372, catid=226914, projectId=35802, created=2023-11-06 19:55:12, last-modified=2025-05-26 11:25:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/getDeployTritonBackend)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1322372)
 */
export function getModelExperimentGetDeployTritonBackend(
  params: model.model.IGetModelExperimentGetDeployTritonBackendParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.model.IGetModelExperimentGetDeployTritonBackendResponse>('/webApi/model/experiment/getDeployTritonBackend', {
    params: pick(params, ['modelId']),
    ...options,
  });
}

/**
 * @desc id=1295735, catid=226914, projectId=35802, created=2023-10-12 16:24:48, last-modified=2025-05-26 11:25:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/run)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1295735)
 */
export function postModelExperimentTaskRun(params: model.model.IPostModelExperimentTaskRunParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.model.IPostModelExperimentTaskRunResponse>('/webApi/model/experiment/task/run', {
    data: pick(params, ['businessType', 'commonContext', 'description', 'experimentId', 'experimentName', 'retryNum']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1334142, catid=226914, projectId=35802, created=2023-11-29 14:56:57, last-modified=2025-05-26 11:25:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/runFrom)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1334142)
 */
export function postModelExperimentTaskRunFrom(
  params: model.model.IPostModelExperimentTaskRunFromParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskRunFromResponse>('/webApi/model/experiment/task/runFrom', {
    data: pick(params, ['description', 'nodeId', 'nodeIds', 'retryNum', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1503653, catid=226914, projectId=35802, created=2024-12-31 14:48:48, last-modified=2025-05-26 11:25:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/model/experiment/task/runByNodes)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503653)
 */
export function postModelExperimentTaskRunByNodes(
  params: model.model.IPostModelExperimentTaskRunByNodesParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.model.IPostModelExperimentTaskRunByNodesResponse>('/webApi/model/experiment/task/runByNodes', {
    data: pick(params, ['description', 'nodeId', 'nodeIds', 'retryNum', 'taskId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
