<template>
  <div class="op-mark-config-container">
    <mtd-button class="all-none-btn" type="text-primary" @click="allSelectNone">无拟合算子</mtd-button>
    <div v-for="config in TaskConfigFiting" :key="config.id" class="op-config-form">
      <DynamicForm
        v-if="config.type === 'annotationForm'"
        ref="dynamicForm"
        :isPreview="true"
        :formConfig="config.areaConfig"
        :formData="formData"
      >
        <div slot="formItem" slot-scope="{ formItem }" style="margin-top: 10px; display: flex; flex-wrap: wrap">
          <FitSelect ref="fitSelect" :form-item="formItem" />
        </div>
      </DynamicForm>
      <template v-if="config.type === 'loopCols'">
        <div v-for="loopItem in config.content" :key="loopItem.id">
          <DynamicForm
            v-if="loopItem.type === 'annotationForm' && loopItem.areaConfig"
            ref="loopDynamicForm"
            :isPreview="true"
            :form-config="loopItem.areaConfig"
            :formData="formData"
          >
            <div slot="formItem" slot-scope="{ formItem }" style="margin-top: 10px; display: flex; flex-wrap: wrap">
              <FitSelect ref="fitSelect" :form-item="formItem" />
            </div>
          </DynamicForm>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Model, Prop, Ref, Spectra, Watch, Weaver } from '@/decorators';
import dataManage from '@/store/modules/dataManage';
import { Form } from '@ss/mtd-vue/es/components/form';
import DynamicForm from './dynamicForm/DynamicForm.vue';
import FitSelect from './dynamicForm/FitSelect.vue';

@Component({ components: { DynamicForm, FitSelect } })
@Spectra
export default class OpMarkConfig extends Weaver(dataManage) {
  @Model('change')
  value;
  @Prop({ default: () => ({ id: undefined }) })
  config;
  @Ref('dynamicForm')
  dynamicFormRef: Form;
  @Ref('fitSelect')
  fitSelectRef;
  @Ref('loopDynamicForm')
  loopDynamicFormRef: Form;

  formData: any = {};

  @Watch('config.id')
  changeId() {
    this.getOpConfig();
  }

  async getOpConfig() {
    const opId = this.config.id;
    opId && (await this.action$dataHubTaskGetTaskConfigInfo({ taskConfigId: `${opId}` }));
    this.clearValidate();
    this.$nextTick(() => {
      this.fitData();
    });
  }

  get TaskConfigFiting() {
    if (this.dataHubTaskGetTaskConfigInfo?.config) {
      try {
        return JSON.parse(this.dataHubTaskGetTaskConfigInfo.config).contentArea;
      } catch (e) {
        console.log(e);
      }
    }

    return [];
  }

  get fitConfig() {
    return this.config.showData;
  }

  fitData() {
    if (!(this.fitConfig?.resColsFit || this.fitConfig?.loopResColsFit)) return;
    this.TaskConfigFiting.forEach((content) => {
      if (content.type === 'annotationForm') {
        Object.keys(this.fitConfig?.resColsFit || {}).forEach((keyStr) => {
          const keyLevels = keyStr.split('.');
          if (content.id === keyLevels[1]) {
            content.areaConfig?.formItemList.forEach((item) => {
              if (item.vModel === keyLevels[2]) {
                this.$set(item, 'fittingType', this.fitConfig.resColsFit[keyStr].type);
                this.$set(item, 'fittingValue', this.fitConfig.resColsFit[keyStr].value);
              }
            });
          }
        });
      } else if (content.type === 'loopCols') {
        Object.keys(this.fitConfig?.loopResColsFit || {}).forEach((keyStr) => {
          const keyLevels = keyStr.split('.');
          content.content.forEach((item) => {
            if (item.id === keyLevels[2]) {
              item.areaConfig?.formItemList.forEach((loopItem) => {
                if (loopItem.vModel === keyLevels[3]) {
                  this.$set(loopItem, 'fittingType', this.fitConfig.loopResColsFit[keyStr].type);
                  this.$set(loopItem, 'fittingValue', this.fitConfig.loopResColsFit[keyStr].value);
                }
              });
            }
          });
        });
      }
    });
    this.clearValidate();
  }

  getData() {
    let isValid = false;
    const fitInfo = this.TaskConfigFiting.reduce(
      (obj, content) => {
        if (content.type === 'annotationForm') {
          const id = `contentArea.${content.id}`;
          content.areaConfig?.formItemList.forEach((item) => {
            if (!item.fittingType || (item.fittingType === 'intersection' && !item.fittingValue)) {
              isValid = true;
            }

            obj.resColsFit[`${id}.${item.vModel}`] = {
              type: item.fittingType,
              value: item.fittingValue || null,
            };
          });
        } else if (content.type === 'loopCols') {
          content.content?.forEach((item) => {
            if (item.type === 'annotationForm') {
              const id = `loop.contentArea.${item.id}`;
              item.areaConfig?.formItemList.forEach((loopItem) => {
                if (!loopItem.fittingType || (loopItem.fittingType === 'intersection' && !loopItem.fittingValue)) {
                  isValid = true;
                }

                obj.loopResColsFit[`${id}.${loopItem.vModel}`] = {
                  type: loopItem.fittingType,
                  value: loopItem.fittingValue || null,
                };
              });
            }
          });
        }

        return obj;
      },
      {
        resColsFit: {},
        loopResColsFit: {},
      }
    );
    if (isValid) {
      this.$mtd.message.warning('请选择全部拟合算子');
      return false;
    }

    return fitInfo;
  }

  clearValidate() {
    this.$nextTick(() => {
      this.dynamicFormRef && this.dynamicFormRef[0]?.clearValidate();
      this.loopDynamicFormRef && this.loopDynamicFormRef[0]?.clearValidate();
    });
  }

  allSelectNone() {
    this.fitSelectRef.forEach((item) => {
      item.formItem && this.$set(item.formItem, 'fittingType', 'none');
    });
  }

  mounted() {
    this.getOpConfig();
  }
}
</script>

<style lang="scss" scoped>
.op-mark-config-container {
  > .op-config-form {
    > .dynamic-form {
      &::v-deep > form {
        .mtd-row {
          border-bottom: 1px solid #eee;
        }
      }
    }
  }
  .all-none-btn {
    position: absolute;
    right: 0;
    top: 6px;
  }
}
</style>
