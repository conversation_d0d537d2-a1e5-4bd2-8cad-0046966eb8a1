<template>
  <div class="experiment-task-list-drawer-container">
    <mtd-drawer v-model="config.visible" title="运行历史" width="1000px" destroy-on-close>
      <mtd-loading :loading="config.loading || false" type="line-scale">
        <ExperimentTask></ExperimentTask>
      </mtd-loading>
    </mtd-drawer>
  </div>
</template>

<script lang="ts">
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Prop } from '@/decorators';
import ExperimentTask from '@/views/DataManage/Experiment/Task.vue';
interface configType {
  visible?: boolean;
  [k: string]: unknown;
}

@Component({ components: { ExperimentTask } })
@Spectra
export default class ExperimentTaskListDrawer extends Weaver() {
  @Prop({ default: () => ({ visible: false }) })
  config!: configType;
}
</script>

<style lang="scss">
.experiment-task-list-drawer-container {
}
</style>
