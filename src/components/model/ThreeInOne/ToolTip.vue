<template>
  <mtd-tooltip placement="top" @input="handleInput">
    <div style="cursor: pointer; width: fit-content">
      -
      <span style="color: #f40">*</span>
    </div>
    <div slot="content">
      <div>{{ countDesc }}</div>
      <div class="list">
        <div class="view" @click="select = !select">
          <i class="mtdicon mtdicon-triangle-right" :class="select ? 'mtdicon-triangle-down' : 'mtdicon-triangle-right'"></i>
          查看详情
        </div>
        <div v-if="select" class="select">
          <div class="select-item" v-for="d in detailList" :key="d">{{ d }}</div>
        </div>
      </div>
    </div>
  </mtd-tooltip>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component
export default class ToolTip extends Vue {
  @Prop({ default: '' }) countDesc!: string;
  @Prop({ default: () => [] }) detailList!: string[];

  select = false;
  handleInput(flag) {
    if (!flag) {
      this.select = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.view {
  display: flex;
  align-items: center;
  width: fit-content;
  cursor: pointer;
  user-select: none;
}
.mtdicon {
  font-size: 18px;
}

.select {
  max-height: 120px;
  overflow-y: auto;
}
</style>
