import * as model from '@/model';
import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import http from '../http';

/**
 * @desc 用户控制台：【接入新能力】搜索组织架构
 * @desc id=1276282, catid=224560, projectId=35802, created=2023-09-18 17:22:19, last-modified=2023-11-21 14:37:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/meta/org/search)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1276282)
 */
export function postMetaOrgSearch(params: model.meta.IPostMetaOrgSearchParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.meta.IPostMetaOrgSearchResponse>('/webApi/meta/org/search', {
    data: pick(params, ['keyword', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc 用户控制台/管理后台：校验当前用户是否具有任一权限
 * @desc id=1276268, catid=224560, projectId=35802, created=2023-09-18 17:22:18, last-modified=2023-11-21 14:37:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/meta/permission/authAny)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1276268)
 */
export function postMetaPermissionAuthAny(params: model.meta.IPostMetaPermissionAuthAnyParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.meta.IPostMetaPermissionAuthAnyResponse>('/webApi/meta/permission/authAny', {
    data: pick(params, ['authUnitList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc 用户控制台/管理后台：获取当前用户基本信息
 * @desc id=1276273, catid=224560, projectId=35802, created=2023-09-18 17:22:18, last-modified=2023-11-21 14:37:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/meta/user/getUserInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1276273)
 */
export function postMetaUserGetUserInfo(params: model.meta.IPostMetaUserGetUserInfoParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.meta.IPostMetaUserGetUserInfoResponse>('/webApi/meta/user/getUserInfo', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1285996, catid=224560, projectId=35802, created=2023-09-25 10:25:40, last-modified=2023-11-21 14:37:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/meta/user/listProjectGroup)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1285996)
 */
export function getMetaUserListProjectGroup(options?: FlowHttpRequestOptions) {
  return http.get<model.meta.IGetMetaUserListProjectGroupResponse>('/webApi/meta/user/listProjectGroup', {
    ...options,
  });
}
