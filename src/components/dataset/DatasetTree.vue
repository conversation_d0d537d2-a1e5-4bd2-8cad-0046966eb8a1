<template>
  <div class="dataset-tree-container">
    <div class="title">
      <mtd-button type="primary" @click="() => createRef?.openModal()">新建数据集</mtd-button>
      <mtd-icon-button class="expand-btn" type="secondary" icon="mtdicon mtdicon-expand" @click="$emit('expand')" />
    </div>
    <div class="stage-container">
      <div class="font-bold">数据阶段</div>
      <mtd-radio-group v-model="searchForm.stage" @change="filterRef?.changeStage()">
        <mtd-radio v-for="stage in stages" :value="stage.id" :key="stage.id">{{ stage.name }}</mtd-radio>
      </mtd-radio-group>
    </div>
    <mtd-collapse v-model="collapse" type="sample">
      <mtd-collapse-item :title="`查询条件（${conditionNum}）`" value="1">
        <DatasetTreeFilter
          :showKeys="['type', 'creator', 'datasetVersionType', 'relevantSubsetList']"
          class="tree-filter-content-1719892094500"
          :form="searchForm"
          @change="searchFun()"
          ref="filterRef"
        >
          <template slot="formItem">
            <mtd-form-item label="Identifier" prop="identifier">
              <mtd-input
                v-model="searchForm.identifier"
                placeholder="输入identifier"
                :disabled="!!searchForm.datasetName || !!searchForm.datasetVersionName"
                @enter="searchFun()"
              />
            </mtd-form-item>
            <mtd-form-item label="数据集名称" prop="datasetName">
              <mtd-input
                v-model="searchForm.datasetName"
                placeholder="搜索数据集名称"
                :disabled="!!searchForm.identifier"
                @enter="searchFun()"
              >
                <template #suffix>
                  <mtd-button
                    class="btn-regular-item"
                    :type="searchForm.datasetNameRegular ? 'text-primary' : 'text'"
                    @click="searchForm.datasetNameRegular = !searchForm.datasetNameRegular"
                    >.*</mtd-button
                  >
                </template>
              </mtd-input>
            </mtd-form-item>
            <mtd-form-item label="版本名称" prop="datasetVersionName">
              <mtd-input
                v-model="searchForm.datasetVersionName"
                placeholder="搜索数据集版本名称"
                :disabled="!!searchForm.identifier"
                @enter="searchFun()"
              >
                <template #suffix>
                  <mtd-button
                    class="btn-regular-item"
                    :type="searchForm.datasetVersionNameRegular ? 'text-primary' : 'text'"
                    @click="searchForm.datasetVersionNameRegular = !searchForm.datasetVersionNameRegular"
                    >.*</mtd-button
                  >
                </template>
              </mtd-input>
            </mtd-form-item>
          </template>
        </DatasetTreeFilter>
      </mtd-collapse-item>
    </mtd-collapse>
    <div class="tree-container">
      <mtd-tree
        ref="treeRef"
        :data="treeList"
        node-key="uid"
        :props="treeProps"
        :expanded-keys.sync="expandedKeys"
        :selected-keys.sync="selectedKeys"
        :node-class="(node) => `node-class-${node.data.uid} ${node.$parent ? '' : 'dataset-node-class'}`"
        @node-click="nodeClick"
      >
        <template #default="{ node, data }">
          <div class="node-container">
            <div
              v-dom-inserted="
                (ele) => {
                  data.tipDisabled = ele.scrollWidth <= ele.clientWidth;
                }
              "
              class="node-name"
            >
              <mtd-tooltip :content="data.name" placement="top" :disabled="data.tipDisabled">
                <span>{{ data.name }}</span>
              </mtd-tooltip>
            </div>
            <mtd-icon-button class="copy-btn" icon="mtdicon mtdicon-copy-o" @click.stop="copyNodeName(node)" />
            <mtd-tag v-if="!node.$parent" class="node-satge-tag">{{ data.stageName }}</mtd-tag>
          </div>
        </template>
      </mtd-tree>
      <div ref="loadMore" class="load-more">
        {{
          pageInfo.pageNumber !== 0 && pageInfo.pageNumber * pageInfo.pageSize >= total && !LS('dataHubDatasetTrainDatasetAndVersionPage')
            ? '已加载全部数据'
            : '数据加载中...'
        }}
      </div>
    </div>
    <CreateDataset ref="create" @success="searchFun()" />
  </div>
</template>

<script lang="ts">
import DatasetTreeFilter, { ICategory } from '@/components/dataset/DatasetTreeFilter.vue';
import { Component, Prop, Ref, Spectra, Watch, Weaver } from '@/decorators';
import datasetManage from '@/store/modules/datasetManage';
import CreateDataset from '@/views/DataManage/PretrainDataset/CreateDataset.vue';
import { cloneDeep } from 'lodash';

export interface IVitem {
  id: number;
  name: string;
  [k: string]: unknown;
}

export interface ITreeCurDataset {
  id: number;
  name: string;
  vid: number;
  vList: IVitem[];
  stage: string;
}

@Component({ components: { DatasetTreeFilter, CreateDataset } })
@Spectra
export default class DatasetTree extends Weaver(datasetManage) {
  @Prop({ default: () => ({} as ITreeCurDataset) })
  dataset!: ITreeCurDataset;

  @Ref('treeRef') treeRef;
  @Ref('filterRef') filterRef!: DatasetTreeFilter;
  @Ref('create') createRef;
  @Ref('loadMore') loadMoreRef!: HTMLDivElement;

  collapse = '';
  searchForm = {
    datasetName: '',
    datasetVersionName: '',
    datasetNameRegular: false,
    datasetVersionNameRegular: false,
    stage: 0,
    type: 0,
    datasetVersionType: 0,
    creator: '',
    relevantSubsetList: [] as any,
    identifier: '',
  };
  pageInfo = {
    pageSize: 1000,
    pageNumber: 1,
  };
  total = 0;
  expandedKeys: string[] = [];
  selectedKeys: string[] = [];
  treeProps = {
    title: 'name',
    children: 'datasetVersionList',
  };

  filterKeys = ['datasetName', 'datasetVersionName', 'type', 'datasetVersionType', 'creator', 'relevantSubsetList'];
  get conditionNum() {
    return this.filterKeys.filter((key) => {
      return (
        (typeof this.searchForm[key] === 'object' && this.searchForm[key]?.length) ||
        (typeof this.searchForm[key] !== 'object' && this.searchForm[key]) ||
        this.searchForm[key] === 0
      );
    }).length;
  }

  @Watch('dataset.vid')
  onDatasetVidChange(vid) {
    if (!vid) return;
    const versionUid = `version-${vid}`;
    if (!this.selectedKeys.includes(versionUid)) {
      this.findNodeByTree();
    }
  }

  initSearchForm() {
    Object.keys(this.searchForm).forEach((key) => {
      this.searchForm[key] = key.includes('Regular') ? false : undefined;
    });
  }

  scrollToChildNode(versionUid, block = 'center', behavior = 'instant') {
    const nodeEle = document.getElementsByClassName(`node-class-${versionUid}`);
    this.$nextTick(() => {
      nodeEle[0] && nodeEle[0].scrollIntoView({ behavior, block } as ScrollIntoViewOptions);
    });
  }

  nodeClick({ $parent: parent }, data) {
    const dataset = parent ? parent.data : data;
    this.selectedKeys = [data.uid];
    parent && this.selectedKeys.push(dataset.uid);
    this.expandedKeys = [parent ? dataset.uid : data.uid];

    Object.assign(this.dataset, {
      id: dataset.id,
      name: dataset.name,
      vList: dataset.datasetVersionList,
      vid: parent ? data.id : undefined,
      stage: this.searchForm.stage,
    });
  }

  treeList = [] as any;
  async getDatasetAndVersionList(params?) {
    const data = {
      param: Object.assign(
        this.searchForm,
        { relevantSubsetList: this.searchForm.relevantSubsetList?.map((item) => item.id) },
        params || {}
      ),
      ...this.pageInfo,
    };
    const dataCopy = cloneDeep(data);
    if (dataCopy.param.identifier) {
      const [datasetName, datasetVersionName] = dataCopy.param.identifier.split('/');
      dataCopy.param.datasetName = datasetName;
      dataCopy.param.datasetVersionName = datasetVersionName;
    }
    const res = await this.action$dataHubDatasetTrainDatasetAndVersionPage(dataCopy);
    if (!res) return;
    res.list?.forEach((dataset) => {
      dataset.uid = `dataset-${dataset.id}`;
      dataset.tipDisabled = false;
      dataset.datasetVersionList?.forEach((version) => {
        version.uid = `version-${version.id}`;
        version.tipDisabled = false;
      });
    });
    this.pageInfo.pageNumber === 1 && (this.treeList.length = 0);
    this.treeList.push(...(res.list || []));
    this.total = res.total || 0;
  }

  searchFun(data?, isFirstLoad = false) {
    this.pageInfo.pageNumber = 1;
    this.treeList = [];
    this.selectedKeys = [];
    this.expandedKeys = [];
    this.getDatasetAndVersionList(data).then(() => {
      if (this.treeList[0]) {
        if (this.isFirstLoad || isFirstLoad) {
          this.isFirstLoad = false;
          this.findNodeByTree();
        } else {
          this.nodeClick({} as any, this.treeList[0]);
        }
      }
    });
  }

  stages: ICategory[] = [];
  async getPretrainStages() {
    +this.dataset.id && this.initStage();
    const res = await this.action$dataHubCategoryCategories({
      dimId: '2',
      parentId: '',
    });
    res && this.stages.splice(0, this.stages.length, ...res);
    +this.dataset.id || this.initStage();
  }

  initStage() {
    if (+this.dataset?.stage) {
      this.searchForm.stage = +this.dataset.stage;
      this.isFirstLoad = !!+this.dataset.id;
      this.filterRef?.changeStage();
      return;
    }
    if (+this.dataset.id) return;
    this.action$getDataHubDatasetTrainDatasetAndVersionGetCurUserDataStage()
      .then((res) => {
        const stage = this.stages.find((item) => item.name === res?.stage);
        this.searchForm.stage = stage ? stage.id : this.stages[0].id;
      })
      .catch(() => {
        this.searchForm.stage = this.stages[0]?.id;
      })
      .finally(() => {
        this.isFirstLoad = !!+this.dataset.id;
        this.filterRef?.changeStage();
      });
  }
  isFirstLoad = true;

  findNodeByTree() {
    const curNodeData = this.treeList.find((item) => item.id === this.dataset.id);
    if (curNodeData) {
      const { id, vid } = this.dataset;
      if (vid) {
        const curVerData = curNodeData.datasetVersionList.find((item) => item.id === vid);
        curVerData && this.nodeClick({ $parent: { data: curNodeData } }, curVerData);
        curVerData || this.nodeClick({} as any, curNodeData);
        setTimeout(() => {
          this.scrollToChildNode(`version-${vid}`, 'nearest', 'smooth');
        }, 0);
      } else {
        this.nodeClick({} as any, curNodeData);
      }
      this.scrollToChildNode(`dataset-${id}`);
    }
  }

  changeLoadMore() {
    const ob = new IntersectionObserver(
      (entries: IntersectionObserverEntry[], observer: IntersectionObserver) => {
        if (
          this.LS(
            'dataHubDatasetTrainDatasetAndVersionPage',
            'dataHubCategoryCategories',
            'getDataHubDatasetTrainDatasetAndVersionGetCurUserDataStage'
          ) ||
          (this.pageInfo.pageNumber !== 0 && this.pageInfo.pageNumber * this.pageInfo.pageSize >= this.total)
        ) {
          return false;
        }

        if (entries[0].isIntersecting) {
          this.pageInfo.pageNumber++;
          this.getDatasetAndVersionList();
        }
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 0.1,
      }
    );
    ob.observe(this.loadMoreRef);
  }

  created() {
    this.$root.$on('dataset-tree-refresh', () => {
      this.searchFun(null, true);
    });
    this.$root.$on('dataset-name-search', (name) => {
      if (!this.selectedKeys.length) {
        this.collapse = '1';
        this.searchForm.datasetName = name;
        const timer = setInterval(() => {
          if (!this.LS('dataHubDatasetTrainDatasetAndVersionPage')) {
            this.searchFun(null, true);
            clearInterval(timer);
          }
        }, 100);
      }
    });
  }

  beforeDestroy() {
    this.$root.$off('dataset-tree-refresh');
    this.$root.$off('dataset-name-search');
  }

  mounted() {
    this.initSearchForm();
    this.changeLoadMore();
    this.getPretrainStages();
  }

  copyNodeName(node) {
    let text = node.title;
    if (node.$parent?.title) text = `${node.$parent.title}/${text}`;
    navigator.clipboard
      .writeText(text)
      .then(() => {
        this.$mtd.message.success('复制成功');
      })
      .catch(() => {
        this.$mtd.message.error('复制失败');
      });
  }
}
</script>

<style lang="scss" scoped>
.dataset-tree-container {
  width: 280px;
  margin-right: 20px;
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  overflow: hidden;
  border-radius: 6px;
  .title {
    flex-shrink: 0;
    margin-bottom: 14px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .expand-btn {
      font-size: 20px;
    }
  }
  .mtd-input-wrapper {
    width: 100%;
    margin-bottom: 10px;
    .mtd-input-suffix-inner {
      border-left: 1px solid rgba(0, 0, 0, 0.12);
    }
  }
  .btn-regular-item {
    width: 31px;
    min-width: unset;
    margin-top: 1px;
    height: calc(100% - 2px);
    border-radius: 0 3px 3px 0;
    font-size: 18px;
    &.mtd-btn-text-primary {
      background: #edf5fd;
    }
    &.mtd-btn-text:focus {
      color: rgba(0, 0, 0, 0.72);
    }
  }
  .stage-container {
    flex-shrink: 0;
  }

  .tree-container {
    flex-grow: 1;
    height: calc(100% - 174px);
    width: calc(100% + 10px);
    overflow-y: scroll;
    padding-right: 16px;
    .load-more {
      text-align: center;
      border-top: 1px solid #eee;
      color: #aaa;
      font-size: 12px;
    }
  }

  .mtd-tree {
    .mtd-tree-node-children {
      max-height: 200px;
      overflow-y: scroll;
    }
    .dataset-node-class {
      > .mtd-tree-node-content {
        > .mtd-visible-hidden {
          visibility: visible;
          color: #ddd;
          transform: rotate(-90deg);
        }
      }
    }
  }

  .node-container {
    display: flex;
    justify-content: space-between;

    .node-name {
      overflow: hidden;
      text-overflow: ellipsis;
      text-wrap: nowrap;
      flex: 1;
    }

    .copy-btn {
      margin: 0 8px;
      font-size: 14px;
      opacity: 0; // 默认隐藏
      transition: opacity 0.2s; // 添加过渡效果
    }

    &:hover {
      .copy-btn {
        opacity: 1; // 悬停时显示
      }
    }
  }
  .mtd-collapse.mtd-collapse-sample {
    flex-shrink: 0;
    margin-top: 6px;
    border: 1px solid #eee;
    border-radius: 6px;
    margin-bottom: 10px;
    ::v-deep .mtd-collapse-item {
      .mtd-collapse-item-header {
        padding: 6px;
        .mtdicon {
          right: 0;
          left: unset;
          line-height: 32px;
          color: #1b67ff;
        }
      }
      .mtd-collapse-item-wrapper {
        padding: 0 6px;
        .mtd-collapse-item-content {
          padding: 6px 0;
          border-top: 1px solid #eee;
          .dataset-tree-filter-container {
            padding: 0;
            .tree-form-container {
              .mtd-form-item {
                margin: 0;
                margin-bottom: 8px;
                .mtd-form-item-content {
                  > .mtd-select {
                    width: 100% !important;
                  }
                  > .mtd-input-wrapper {
                    margin-bottom: 0;
                    width: 100% !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
