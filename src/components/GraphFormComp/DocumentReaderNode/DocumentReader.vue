<template>
  <div class="documentReaderContainer">
    <mtd-radio-group v-if="isEdit" v-model="context.nodeParam.isCustomReader" @input="changeRadio">
      <mtd-radio :value="false">已有</mtd-radio>
      <mtd-radio :value="true">自定义</mtd-radio>
    </mtd-radio-group>
    <div v-else>{{ context.nodeParam.isCustomReader ? '自定义' : '已有' }}</div>
    <div v-if="isEdit" class="flex gap-2 items-center">
      <mtd-icon-button
        @click="onTip"
        v-if="!context.nodeParam.isCustomReader"
        class="demo-icon-btn"
        icon="mtdicon mtdicon-question-circle-o"
      />
      <mtd-select v-if="!context.nodeParam.isCustomReader" :value="context.nodeParam.documentReader" style="width: 100%" @change="change">
        <mtd-option v-for="item in documentReaderHashListOptions" :key="item.value" :label="item.label" :value="item.value" />
      </mtd-select>
      <SqlEdit
        v-else
        title="详情"
        :value="context.nodeParam.documentReader"
        @input="change"
        mode="ace/mode/python"
        :showFormat="false"
        :showFooter="!!isEdit"
      />
    </div>
    <div v-else>{{ context.nodeParam.documentReader }}</div>
  </div>
</template>
<script lang="ts">
import SqlEdit from '@/components/SqlEdit.vue';
import { Component, Prop, Spectra, Vue, Watch } from '@/decorators';
import { documentReaderDefault, documentReaderHashList, documentReaderHashListTokenize } from '@/utils/dataManage/nodeDic';

@Component({ components: { SqlEdit } })
@Spectra
export default class DocumentReader extends Vue {
  @Prop()
  model!: any;
  @Prop()
  context: any;
  @Prop()
  globalContext: any;
  @Prop()
  isEdit!: boolean;
  @Prop({ default: false })
  isDataType?: boolean;

  get documentReaderHashListOptions() {
    const map = {
      118: documentReaderHashListTokenize,
      126: documentReaderHashList,
      157: documentReaderHashList,
    };
    return map[this.context.nodeType?.toString()] ?? documentReaderHashList;
  }
  @Watch('context.nodeParam', { immediate: true, deep: true })
  nodeParamChange(v) {
    if (v.documentReader && typeof v.isCustomReader !== 'boolean') {
      this.$set(v, 'isCustomReader', false);
    } else if (!v.documentReader && typeof v.isCustomReader !== 'boolean') {
      this.$set(v, 'isCustomReader', false);
      this.change('data_withid');
    }

    if (this.isDataType && typeof v.dataType === 'number') {
      this.$set(v, 'isCustomReader', false);
      v.dataType === 0 && this.change('data_withid');
      v.dataType === 1 && this.change('data_json');
      this.$set(v, 'dataType', undefined);
    }
  }

  change(v) {
    this.$set(this.context.nodeParam, 'documentReader', v);
    this.$emit('change', v);
  }

  onTip() {
    window.open('https://km.sankuai.com/collabpage/2673220088', '_blank');
  }

  lastDocumentReader = '';
  changeRadio(v) {
    let { lastDocumentReader } = this;
    if (v && !lastDocumentReader) {
      lastDocumentReader = documentReaderDefault;
    }
    if (!v && !lastDocumentReader) {
      lastDocumentReader = 'data_withid';
    }
    this.lastDocumentReader = this.context.nodeParam.documentReader;
    this.change(lastDocumentReader ?? '');
    this.$set(this.context.nodeParam, 'isCustomReader', v);
  }
}
</script>
<style lang="scss" scoped>
.documentReaderContainer {
}
</style>
