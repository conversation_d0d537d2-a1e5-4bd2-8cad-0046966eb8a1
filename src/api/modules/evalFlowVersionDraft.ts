import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1426862, catid=246786, projectId=35802, created=2024-06-25 16:42:17, last-modified=2024-07-01 18:47:29
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVersionDraft/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426862)
 */
export function postEvalFlowVersionDraftUpsert(
  params: model.evalFlowVersionDraft.IPostEvalFlowVersionDraftUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVersionDraft.IPostEvalFlowVersionDraftUpsertResponse>('/webApi/evalFlowVersionDraft/upsert', {
    data: pick(params, ['flowId', 'flowVersion', 'userMis', 'content']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426895, catid=246786, projectId=35802, created=2024-06-25 16:46:55, last-modified=2024-07-01 18:53:30
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVersionDraft/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426895)
 */
export function postEvalFlowVersionDraftDelete(
  params: model.evalFlowVersionDraft.IPostEvalFlowVersionDraftDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVersionDraft.IPostEvalFlowVersionDraftDeleteResponse>('/webApi/evalFlowVersionDraft/delete', {
    data: pick(params, ['flowId', 'flowVersion', 'userMis']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426886, catid=246786, projectId=35802, created=2024-06-25 16:45:29, last-modified=2024-07-01 18:50:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVersionDraft/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426886)
 */
export function postEvalFlowVersionDraftGet(
  params: model.evalFlowVersionDraft.IPostEvalFlowVersionDraftGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVersionDraft.IPostEvalFlowVersionDraftGetResponse>('/webApi/evalFlowVersionDraft/get', {
    data: pick(params, ['flowId', 'flowVersion', 'userMis']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
