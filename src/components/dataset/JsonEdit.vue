<template>
  <div class="sqlEditContainer">
    <mtd-modal v-model="modalVisible" class="modal1703128944715" :title="disabled ? '详情' : '编辑JSON'" width="60vw" @closed="initmodal">
      <div class="editor-container">
        <Editor
          ref="resEditorRef"
          v-model="editValue"
          :read-only="!editAble"
          :tools="{ fullscreen: false }"
          style="width: 100%; height: 100%"
        />
      </div>
      <template #footer>
        <mtd-button v-if="templateJson" @click="setValue(templateJson)">填入模版</mtd-button>
        <mtd-button @click="modalVisible = false">取消</mtd-button>
        <mtd-button v-if="!disabled" type="primary" @click="confirm">确认</mtd-button>
      </template>
    </mtd-modal>
  </div>
</template>

<script lang="ts">
import store from '@/store';
import Editor from '@snfe/backend-ui/lib/editor';
import '@snfe/backend-ui/lib/index.css';
import { Component, Prop, PropSync, Vue, Ref } from 'vue-property-decorator';

@Component({ components: { Editor } })
export default class JsonEdit extends Vue {
  @PropSync('value', { default: '' }) valueInner!: string;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: '' }) templateJson?: string;
  @Ref('resEditorRef') resEditorRef!: Editor;
  editAble = true;

  editValue = '';

  modalVisible = false;

  mounted() {}

  editContent() {
    this.initmodal(true);
  }

  initmodal(v?: boolean) {
    this.editValue = v ? this.valueInner : '';
    this.modalVisible = v || false;
    v && this.formatJson();
    v || this.$emit('cancel');
  }

  checkJson() {
    try {
      JSON.parse(this.editValue);
    } catch (error) {
      this.$mtd.message.error(`json解析错误: ${error}`);
      return false;
    }

    return true;
  }

  formatJson() {
    this.$nextTick(() => {
      this.resEditorRef.format().finally(() => {
        setTimeout(() => {
          this.disabled && this.editAble && (this.editAble = false);
        }, 200);
      });
    });
  }

  setValue(v) {
    this.editValue = v || '';
    if (!this.checkJson()) return;
    this.formatJson();
  }

  confirm() {
    if (!this.checkJson()) return;
    this.$emit('change', this.editValue);
    this.initmodal();
  }
}

export function editJson(str: string, disabled = false, templateJson?: string) {
  const instance: JsonEdit = new JsonEdit({ store, propsData: { value: str, disabled, templateJson } });
  instance.$mount();
  instance.editContent();

  return new Promise((resolve, reject) => {
    instance.$on('change', (v) => {
      instance.$destroy();
      return resolve(v);
    });
    instance.$on('cancel', () => {
      instance.$destroy();
      return reject();
    });
  });
}
</script>

<style lang="scss" scoped>
.sqlEditContainer {
  width: inherit;
  > textarea {
    width: inherit;
  }
}

.modal1703128944715 {
  .mtd-modal-content {
    > .editor-container {
      height: 60vh;
    }
  }
}
</style>
