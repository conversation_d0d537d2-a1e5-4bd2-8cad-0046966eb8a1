import { FlowHttpRequestOptions, pick, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1465373, catid=251811, projectId=35802, created=2024-09-02 19:41:31, last-modified=2024-10-11 16:04:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/common/user)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465373)
 */
export function getJobServiceStorageCommonUser(options?: FlowHttpRequestOptions) {
  return http.get<model.jobService.IGetJobServiceStorageCommonUserResponse>('/jobService/storage/common/user', {
    ...options,
  });
}

/**
 * @desc id=1479306, catid=251811, projectId=35802, created=2024-09-26 17:13:52, last-modified=2024-10-11 16:04:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/common/defaultBackupPath)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1479306)
 */
export function getJobServiceStorageCommonDefaultBackupPath(
  params: model.jobService.IGetJobServiceStorageCommonDefaultBackupPathParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.jobService.IGetJobServiceStorageCommonDefaultBackupPathResponse>('/jobService/storage/common/defaultBackupPath', {
    params: pick(params, ['path']),
    ...options,
  });
}

/**
 * @desc id=1465269, catid=251819, projectId=35802, created=2024-09-02 16:29:26, last-modified=2024-10-11 16:04:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/task)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465269)
 */
export function postJobServiceStorageTask(params: model.jobService.IPostJobServiceStorageTaskParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.jobService.IPostJobServiceStorageTaskResponse>('/jobService/storage/task', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465297, catid=251819, projectId=35802, created=2024-09-02 16:29:27, last-modified=2024-10-11 16:04:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/task/startRemove)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465297)
 */
export function postJobServiceStorageTaskStartRemove(
  params: model.jobService.IPostJobServiceStorageTaskStartRemoveParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceStorageTaskStartRemoveResponse>('/jobService/storage/task/startRemove', {
    data: pick(params, ['idList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465293, catid=251819, projectId=35802, created=2024-09-02 16:29:27, last-modified=2024-10-11 16:04:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/task/startBackup)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465293)
 */
export function postJobServiceStorageTaskStartBackup(
  params: model.jobService.IPostJobServiceStorageTaskStartBackupParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceStorageTaskStartBackupResponse>('/jobService/storage/task/startBackup', {
    data: pick(params, ['idList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465304, catid=251819, projectId=35802, created=2024-09-02 16:29:27, last-modified=2024-10-11 16:04:45
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/task/startScan)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465304)
 */
export function postJobServiceStorageTaskStartScan(
  params: model.jobService.IPostJobServiceStorageTaskStartScanParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceStorageTaskStartScanResponse>('/jobService/storage/task/startScan', {
    data: pick(params, ['idList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465286, catid=251819, projectId=35802, created=2024-09-02 16:29:27, last-modified=2024-10-11 16:04:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/task/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465286)
 */
export function getJobServiceStorageTaskList(
  params: model.jobService.IGetJobServiceStorageTaskListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.jobService.IGetJobServiceStorageTaskListResponse>('/jobService/storage/task/list', {
    params: pick(params, ['page', 'pageSize', 'taskType']),
    ...options,
  });
}

/**
 * @desc id=1465278, catid=251819, projectId=35802, created=2024-09-02 16:29:26, last-modified=2024-10-11 16:04:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/task/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465278)
 */
export function getJobServiceStorageTaskDetail(
  params: model.jobService.IGetJobServiceStorageTaskDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.jobService.IGetJobServiceStorageTaskDetailResponse>('/jobService/storage/task/detail', {
    params: pick(params, ['taskId']),
    ...options,
  });
}

/**
 * @desc id=1478874, catid=251819, projectId=35802, created=2024-09-25 14:41:20, last-modified=2024-10-11 16:04:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/task/pathTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1478874)
 */
export function getJobServiceStorageTaskPathTask(
  params: model.jobService.IGetJobServiceStorageTaskPathTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.jobService.IGetJobServiceStorageTaskPathTaskResponse>('/jobService/storage/task/pathTask', {
    params: pick(params, ['page', 'pageSize', 'taskId']),
    ...options,
  });
}

/**
 * @desc id=1480561, catid=251822, projectId=35802, created=2024-10-11 16:04:42, last-modified=2024-10-11 16:04:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/path/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1480561)
 */
export function postJobServiceStoragePathDelete(
  params: model.jobService.IPostJobServiceStoragePathDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceStoragePathDeleteResponse>('/jobService/storage/path/delete', {
    data: pick(params, [
      'id',
      'taskType',
      'path',
      'lastAccessTime',
      'pathSize',
      'totalFiles',
      'owner',
      'updateTime',
      'message',
      'backupPath',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465239, catid=251822, projectId=35802, created=2024-09-02 16:29:24, last-modified=2024-10-11 16:04:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/path/add)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465239)
 */
export function postJobServiceStoragePathAdd(
  params: model.jobService.IPostJobServiceStoragePathAddParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceStoragePathAddResponse>('/jobService/storage/path/add', {
    data: pick(params, [
      'id',
      'taskType',
      'path',
      'lastAccessTime',
      'pathSize',
      'totalFiles',
      'owner',
      'updateTime',
      'message',
      'backupPath',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465242, catid=251822, projectId=35802, created=2024-09-02 16:29:25, last-modified=2024-10-11 16:04:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/path/edit)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465242)
 */
export function postJobServiceStoragePathEdit(
  params: model.jobService.IPostJobServiceStoragePathEditParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceStoragePathEditResponse>('/jobService/storage/path/edit', {
    data: pick(params, [
      'id',
      'taskType',
      'path',
      'lastAccessTime',
      'pathSize',
      'totalFiles',
      'owner',
      'updateTime',
      'message',
      'backupPath',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1478871, catid=251822, projectId=35802, created=2024-09-25 14:41:19, last-modified=2024-10-11 16:04:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/path/pathTask)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1478871)
 */
export function getJobServiceStoragePathPathTask(
  params: model.jobService.IGetJobServiceStoragePathPathTaskParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.jobService.IGetJobServiceStoragePathPathTaskResponse>('/jobService/storage/path/pathTask', {
    params: pick(params, ['page', 'pageSize', 'pathId']),
    ...options,
  });
}

/**
 * @desc id=1465249, catid=251822, projectId=35802, created=2024-09-02 16:29:25, last-modified=2024-10-11 16:04:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/path/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465249)
 */
export function getJobServiceStoragePathList(
  params: model.jobService.IGetJobServiceStoragePathListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.jobService.IGetJobServiceStoragePathListResponse>('/jobService/storage/path/list', {
    params: pick(params, ['page', 'pageSize', 'pathType']),
    ...options,
  });
}

/**
 * @desc id=1465240, catid=251822, projectId=35802, created=2024-09-02 16:29:25, last-modified=2024-10-11 16:04:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/path/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465240)
 */
export function getJobServiceStoragePathDetail(
  params: model.jobService.IGetJobServiceStoragePathDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.jobService.IGetJobServiceStoragePathDetailResponse>('/jobService/storage/path/detail', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1465266, catid=251827, projectId=35802, created=2024-09-02 16:29:26, last-modified=2024-10-11 16:04:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/scanConf/remove)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465266)
 */
export function postJobServiceStorageScanConfRemove(
  params: model.jobService.IPostJobServiceStorageScanConfRemoveParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceStorageScanConfRemoveResponse>('/jobService/storage/scanConf/remove', {
    data: pick(params, ['idList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465252, catid=251827, projectId=35802, created=2024-09-02 16:29:25, last-modified=2024-10-11 16:04:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/scanConf/add)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465252)
 */
export function postJobServiceStorageScanConfAdd(
  params: model.jobService.IPostJobServiceStorageScanConfAddParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceStorageScanConfAddResponse>('/jobService/storage/scanConf/add', {
    data: pick(params, ['id', 'path', 'type', 'scanDepth', 'condition', 'owner']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465255, catid=251827, projectId=35802, created=2024-09-02 16:29:26, last-modified=2024-10-11 16:04:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/scanConf/edit)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465255)
 */
export function postJobServiceStorageScanConfEdit(
  params: model.jobService.IPostJobServiceStorageScanConfEditParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceStorageScanConfEditResponse>('/jobService/storage/scanConf/edit', {
    data: pick(params, ['id', 'path', 'type', 'scanDepth', 'condition', 'owner']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465263, catid=251827, projectId=35802, created=2024-09-02 16:29:26, last-modified=2024-10-11 16:04:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/storage/scanConf/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465263)
 */
export function getJobServiceStorageScanConfList(
  params: model.jobService.IGetJobServiceStorageScanConfListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.jobService.IGetJobServiceStorageScanConfListResponse>('/jobService/storage/scanConf/list', {
    params: pick(params, ['page', 'pageSize']),
    ...options,
  });
}

/**
 * @desc id=1468701, catid=252083, projectId=35802, created=2024-09-05 14:39:59, last-modified=2024-09-05 17:37:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/internal/storage/hello)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1468701)
 */
export function getJobServiceInternalStorageHello(options?: FlowHttpRequestOptions) {
  return http.get<model.jobService.IGetJobServiceInternalStorageHelloResponse>('/jobService/internal/storage/hello', {
    ...options,
  });
}

/**
 * @desc id=1468707, catid=252083, projectId=35802, created=2024-09-05 14:40:00, last-modified=2024-09-05 17:37:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/internal/storage/scanResult)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1468707)
 */
export function postJobServiceInternalStorageScanResult(
  params: model.jobService.IPostJobServiceInternalStorageScanResultParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceInternalStorageScanResultResponse>('/jobService/internal/storage/scanResult', {
    data: pick(params, ['pathInfos']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1478859, catid=252083, projectId=35802, created=2024-09-25 14:41:17, last-modified=2024-10-11 16:04:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/internal/storage/dolphinScanCallback)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1478859)
 */
export function postJobServiceInternalStorageDolphinScanCallback(
  params: model.jobService.IPostJobServiceInternalStorageDolphinScanCallbackParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceInternalStorageDolphinScanCallbackResponse>(
    '/jobService/internal/storage/dolphinScanCallback',
    {
      data: pick(params, ['pathInfos', 'taskId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1478861, catid=252083, projectId=35802, created=2024-09-25 14:41:17, last-modified=2024-10-11 16:04:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/internal/storage/softRemoveCallback)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1478861)
 */
export function postJobServiceInternalStorageSoftRemoveCallback(
  params: model.jobService.IPostJobServiceInternalStorageSoftRemoveCallbackParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.jobService.IPostJobServiceInternalStorageSoftRemoveCallbackResponse>(
    '/jobService/internal/storage/softRemoveCallback',
    {
      data: pick(params, ['createTime', 'id', 'message', 'pathId', 'sourcePath', 'status', 'targetPath', 'taskId', 'type', 'updateTime']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1478868, catid=252083, projectId=35802, created=2024-09-25 14:41:17, last-modified=2024-10-11 16:04:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/jobService/internal/storage/test)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1478868)
 */
export function getJobServiceInternalStorageTest(options?: FlowHttpRequestOptions) {
  return http.get<model.jobService.IGetJobServiceInternalStorageTestResponse>('/jobService/internal/storage/test', {
    ...options,
  });
}
