import { getModelExperimentTaskGetOutputModelMetaId } from '@/api/modules/model';
import { Component, Prop, Spectra, <PERSON>, Weaver } from '@/decorators';
import { BindType, PluginBind } from '@/model/customEval';
import evalFlow from '@/store/modules/evalFlow';
import evalFlowJob from '@/store/modules/evalFlowJob';
import evalFlowVersion from '@/store/modules/evalFlowVersion';
import modelEvalDataSubSetUnVersioned from '@/store/modules/modelEvalDataSubSetUnVersioned';
import modelManage from '@/store/modules/modelManage';
import { useGraphStore } from '@/views/modelManage/components/use-graph-store';
import { cloneDeep } from 'lodash';

@Component({
  components: {},
})
@Spectra
export default class RunSpecSetConfigListBase extends Weaver(
  evalFlowJob,
  evalFlow,
  evalFlowVersion,
  modelEvalDataSubSetUnVersioned,
  modelManage
) {
  @Prop()
  context?: any;
  @Prop()
  globalContext?: any;
  @Prop()
  model!: typeof this.evalFlowJobPreview.evalFlowJobPreviewList;

  errMsg = '';
  loading = false;
  sourceConfigList: typeof this.evalFlowJobPreview.evalFlowJobPreviewList = [];
  exportConfigList: typeof this.sourceConfigList = [];

  tmpIdx = -1;

  async mounted() {
    this.sourceMetaId = await this.getSourceMetaId();
    if (this.context?.nodeParam?.benchmarkList) this.initData(this.model || [], true);
  }

  get nodeInfo() {
    return (this.taskStatLists || this.experimentConfigDetail?.status || this.taskDetail?.status)?.nodeStatus?.find(
      (x) => x.nodeId === Number(this.context.id)
    );
  }

  benchmarkCacheJson = this.context?.benchmarkCacheJson || '';
  // 切换画布之后的初始化
  @Watch('context.nodeParam.benchmarkList', { immediate: true })
  clearSourceConfigListByBenchmark(v) {
    if (!v) return;
    if (this.benchmarkCacheJson === JSON.stringify(v)) return;
    if (!this.benchmarkCacheJson) {
      this.benchmarkCacheJson = JSON.stringify(v);
    } else {
      this.benchmarkCacheJson = JSON.stringify(v);
      this.initData(this.model || [], false);
    }
  }

  @Watch('context.nodeParam.modelMetaBindGroupId')
  modelMetaBindGroupIdChanged() {
    this.initData(this.model || [], false);
  }

  // 更换默认队列后，显卡得更新
  @Watch('context.nodeParam.gpuJobQueue')
  @Watch('context.nodeParam.otherGpuJobQueue')
  clearSourceConfigListByJobQueue() {
    this.sourceConfigList.forEach((config) => {
      ((config.resourceConfigList as any) || []).forEach(
        (resourceConfig) => (resourceConfig.gpuSpec = this.validateDefaultGpuType(resourceConfig))
      );
    });

    this.exportConfigList.forEach((config) => {
      ((config.resourceConfigList as any) || []).forEach(
        (resourceConfig) => (resourceConfig.gpuSpec = this.validateDefaultGpuType(resourceConfig))
      );
    });

    this.initData(this.model || [], false);
  }

  sourceMetaId = 0;
  async getSourceMetaId() {
    const { edges, nodes } = this.globalContext.getFields('graphData');
    const prevNodeMap = new Map();
    edges.forEach((edge) => {
      prevNodeMap.set(edge.target, edge.source);
    });
    let ptr = this.context.id;
    while (ptr) {
      const node = nodes.find((n) => n.id === ptr);
      // 向前递归找精调模型节点。要适配其他会改meta的节点改这里
      if (node.nodeType === 2) {
        // case1: 精调模型节点上配置了输出模型
        if (node.nodeParam?.outputModelMetaId) return node.nodeParam?.outputModelMetaId;
        // case2: 精调模型节点没配，但是完成执行了，产生了一个新meta
        const nodeInfo = (this.taskStatLists || this.experimentConfigDetail?.status || this.taskDetail?.status)?.nodeStatus?.find(
          (x) => x.nodeId === Number(ptr)
        );
        if (nodeInfo) {
          const res = await getModelExperimentTaskGetOutputModelMetaId({
            taskDetailId: String(nodeInfo.taskDetailId),
          });
          if (res.data.logUrl) return Number(res.data.logUrl);
        }
      }
      ptr = prevNodeMap.get(ptr);
    }
    // case3: 没有改变meta的节点，用实验信息的
    return this.experimentBaseInfo.modelMeta?.metaId || this.useGraphStore.modelInfo?.modelMetaId;
  }
  useGraphStore = useGraphStore();

  // 数据初始化逻辑 --start

  initTimeStamp = -1;
  async initData(mergeConfigList: typeof this.model, useCache) {
    this.setPreviewingState(mergeConfigList);
    const { modelId } = this.useGraphStore.modelInfo;
    this.cancelRequest('evalFlowJobPreview');
    const timeStamp = Date.now();
    this.initTimeStamp = timeStamp;
    await this.fetchPreviewData(useCache, modelId);

    if (this.errMsg) return;

    this.resetPreviewingState(mergeConfigList);

    this.processEvalFlowJobPreview(mergeConfigList, timeStamp);
  }

  setPreviewingState(mergeConfigList) {
    if (mergeConfigList.length) {
      mergeConfigList[0].previewing = true;
      this.$emit('change', mergeConfigList);
    }
  }

  async fetchPreviewData(useCache, modelId) {
    if (useCache && this.context.previewCache?.[this.sourceMetaId]) {
      this.mutation$evalFlowJobPreview(this.context.previewCache[this.sourceMetaId]);
    } else {
      this.loading = true;
      try {
        await this.action$evalFlowJobPreview({
          benchmarkList: this.context.nodeParam.benchmarkList,
          modelId,
          modelMetaId: Number(this.sourceMetaId),
          modelMetaBindGroupId: this.context.nodeParam.modelMetaBindGroupId ? this.context.nodeParam.modelMetaBindGroupId : undefined,
          expTaskDetailId: this.$route.params.taskId ? this.nodeInfo?.taskDetailId : undefined,
        } as any);
        this.context.previewCache = { [this.sourceMetaId]: this.evalFlowJobPreview };
        this.errMsg = '';
        this.loading = false;
      } catch (e) {
        this.errMsg = String(e);
        if (String(e) === 'Cancel') return;
        this.loading = false;
      }
    }
  }

  resetPreviewingState(mergeConfigList) {
    if (mergeConfigList.length) {
      mergeConfigList[0].previewing = false;
    }
  }

  processEvalFlowJobPreview(mergeConfigList, timeStamp) {
    const sourceConfigList: any = [];
    const exportConfigList: any = [];
    for (const oriConfig of this.evalFlowJobPreview.evalFlowJobPreviewList) {
      if (timeStamp !== this.initTimeStamp) return;
      const mergeConfig = mergeConfigList.find((config) => config.flowId === oriConfig.flowId) || mergeConfigList[0];
      this.processPreview(oriConfig);

      if (!mergeConfig) {
        sourceConfigList.push(this.createSourceConfig(oriConfig));
        exportConfigList.push(this.createExportConfig(oriConfig));
      } else {
        sourceConfigList.push(this.createMergedSourceConfig(oriConfig, mergeConfig));
        exportConfigList.push(this.createMergedExportConfig(oriConfig, mergeConfig));
      }
    }
    this.sourceConfigList = sourceConfigList;
    this.exportConfigList = exportConfigList;
    this.$emit('change', this.exportConfigList);
  }

  processPreview(oriConfig) {
    oriConfig.sourceEnvGlobalAliasParams = Object.freeze(cloneDeep(oriConfig.envGlobalAliasParams));
    oriConfig.previewEnvGlobalAliasParams = Object.freeze(cloneDeep(oriConfig.defaultUsedEnvGlobalAliasParams));
    oriConfig.previewGlobalAliasParams = Object.freeze(cloneDeep(oriConfig.globalAliasParams));
    oriConfig.previewResourceConfigList = Object.freeze(cloneDeep(oriConfig.resourceConfigList));
  }

  createSourceConfig(oriConfig) {
    return {
      ...oriConfig,
      defaultResourceConfigList: cloneDeep(oriConfig.resourceConfigList),
      globalAliasParams: [],
      envGlobalAliasParams: [],
      resourceConfigList: oriConfig.resourceConfigList.map((resource) => ({
        clusterName: resource.clusterName,
        gpuSpec: this.validateDefaultGpuType(resource),
        workers: resource.workers,
        dockerImage: resource.dockerImage,
      })),
    };
  }

  createExportConfig(oriConfig) {
    return {
      ...oriConfig,
      sourceMetaId: this.sourceMetaId,
      globalAliasParams: [],
      envGlobalAliasParams: [],
      resourceConfigList: oriConfig.resourceConfigList.map((resource) => ({
        clusterName: resource.clusterName,
        gpuSpec: this.validateDefaultGpuType(resource),
        workers: resource.workers,
        dockerImage: resource.dockerImage,
      })),
      globalAliasParamsJson: [],
      envGlobalAliasParamsJson: [],
    };
  }

  createMergedSourceConfig(oriConfig, mergeConfig) {
    return {
      ...oriConfig,
      globalAnchorBindMapJson: this.mergeGlobalAnchorBindMap(oriConfig, mergeConfig),
      runSpecAnchorBindMapJson: this.mergeRunSpecAnchorBindMap(oriConfig, mergeConfig),
      defaultResourceConfigList: cloneDeep(oriConfig.resourceConfigList),
      resourceConfigList: this.mergeResourceConfigList(oriConfig, mergeConfig),
      globalAliasParams: mergeConfig.globalAliasParams,
      envGlobalAliasParams: mergeConfig.envGlobalAliasParams,
      flowId: oriConfig.flowId,
      flowName: oriConfig.flowName,
      flowVersion: oriConfig.flowVersion,
    };
  }

  createMergedExportConfig(oriConfig, mergeConfig) {
    return {
      ...mergeConfig,
      globalAnchorBindMapJson: this.mergeGlobalAnchorBindMap(oriConfig, mergeConfig),
      runSpecAnchorBindMapJson: this.mergeRunSpecAnchorBindMap(oriConfig, mergeConfig),
      flowId: oriConfig.flowId,
      flowName: oriConfig.flowName,
      flowVersion: oriConfig.flowVersion,
      sourceMetaId: this.sourceMetaId,
      resourceConfigListJson: mergeConfig.resourceConfigList,
      globalAliasParamsJson: mergeConfig.globalAliasParams,
      envGlobalAliasParamsJson: mergeConfig.envGlobalAliasParams,
    };
  }

  mergeResourceConfigList(oriConfig, mergeConfig) {
    // flowId不同，说明是复制过来的，需要以origin为基底，merge为覆盖
    const mergeConfigList = mergeConfig.resourceConfigList.map((x) => {
      return Object.fromEntries(Object.entries(x).map(([k, v]) => [k, v || null]));
    });

    return oriConfig.resourceConfigList.map((resource) => {
      const foundConfig = mergeConfigList.find((x) => x.clusterName === resource.clusterName) || {};
      return {
        clusterName: resource.clusterName,
        workers: resource.workers,
        dockerImage: resource.dockerImage,
        ...foundConfig,
        gpuSpec: this.validateDefaultGpuType({ ...resource, ...foundConfig }),
      };
    });
  }
  // 整合全局绑定
  mergeGlobalAnchorBindMap(oriConfig, mergeConfig) {
    const oriGlobalBindList = oriConfig.globalPluginBindList;
    const globalAnchorBindMap: Map<string, PluginBind> = new Map(JSON.parse(mergeConfig.globalAnchorBindMapJson || '[]'));
    const defaultGlobalAnchorBindMap: Map<string, PluginBind> = new Map(JSON.parse(oriConfig.globalAnchorBindMapJson || '[]'));
    [...globalAnchorBindMap.values()].forEach((bindList) => {
      const curBind = bindList[0];
      if (
        curBind.bindType === BindType.PARAM &&
        !oriGlobalBindList.some((oriBind) => oriBind.pluginImplement === curBind.pluginImplement)
      ) {
        globalAnchorBindMap.delete(curBind.anchorPath);
        if (defaultGlobalAnchorBindMap.get(curBind.anchorPath)) {
          globalAnchorBindMap.set(curBind.anchorPath, defaultGlobalAnchorBindMap.get(curBind.anchorPath)!);
        }
      }
    });
    return JSON.stringify([...globalAnchorBindMap.entries()]);
  }
  // 整合runSpec层的绑定。有可能传过来的对不上
  mergeRunSpecAnchorBindMap(oriConfig, mergeConfig) {
    const runSpecKeyList = oriConfig.runSpecFlowConfigList.map((x) => `${x.runSpecSetId}-${x.runSpecId}-${x.evalDataSize}`);
    const runSpecAnchorBindMap: Map<string, PluginBind> = new Map(JSON.parse(mergeConfig.runSpecAnchorBindMapJson || '[]'));
    [...runSpecAnchorBindMap.keys()].forEach((bindKey) => {
      if (!runSpecKeyList.some((key) => bindKey.startsWith(key))) {
        runSpecAnchorBindMap.delete(bindKey);
      }
    });
    return JSON.stringify([...runSpecAnchorBindMap.entries()]);
  }

  validateDefaultGpuType(resource) {
    if (resource.resourceType === 'cpuJob') return null;
    const defaultQueue =
      resource.consumerDefineList?.[0].vertexDefineId === 2
        ? this.context.nodeParam.gpuJobQueue
        : this.context.nodeParam.otherGpuJobQueue || this.context.nodeParam.gpuJobQueue;
    const targetQueue = this.gpuQueueList.find((x) => x.queue === (resource.queue || defaultQueue));
    if (resource.gpuSpec in (targetQueue?.gpuType || [])) return resource.gpuSpec;
    else return targetQueue?.gpuType?.[0] || 'NONE';
  }

  // 数据初始化逻辑 --end
}
