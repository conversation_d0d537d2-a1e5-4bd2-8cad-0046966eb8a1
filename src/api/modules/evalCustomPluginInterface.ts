import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1426376, catid=246786, projectId=35802, created=2024-06-24 16:41:12, last-modified=2024-07-25 16:19:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalCustomPluginInterface/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426376)
 */
export function postEvalCustomPluginInterfaceList(
  params: model.evalCustomPluginInterface.IPostEvalCustomPluginInterfaceListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalCustomPluginInterface.IPostEvalCustomPluginInterfaceListResponse>('/webApi/evalCustomPluginInterface/list', {
    data: pick(params, ['searchFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426381, catid=246786, projectId=35802, created=2024-06-24 16:49:48, last-modified=2024-08-06 19:06:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalCustomPluginInterface/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426381)
 */
export function postEvalCustomPluginInterfaceGet(
  params: model.evalCustomPluginInterface.IPostEvalCustomPluginInterfaceGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalCustomPluginInterface.IPostEvalCustomPluginInterfaceGetResponse>('/webApi/evalCustomPluginInterface/get', {
    data: pick(params, ['id', 'interfaceName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426379, catid=246786, projectId=35802, created=2024-06-24 16:48:13, last-modified=2024-08-06 19:06:35
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalCustomPluginInterface/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426379)
 */
export function postEvalCustomPluginInterfaceUpsert(
  params: model.evalCustomPluginInterface.IPostEvalCustomPluginInterfaceUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalCustomPluginInterface.IPostEvalCustomPluginInterfaceUpsertResponse>(
    '/webApi/evalCustomPluginInterface/upsert',
    {
      data: pick(params, ['id', 'interfaceName', 'interfaceLabel', 'contextParamsDefine']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
