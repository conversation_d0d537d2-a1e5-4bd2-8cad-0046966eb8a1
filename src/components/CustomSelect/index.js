import { withFormItem } from '@ss/mtd-vue/es/components/form-item';
import createHoc from '@ss/mtd-vue/es/utils/hoc';
import Picker from './CustomSelect.vue';

const Control = createHoc(
  {
    name: 'CustomPicker',
    model: {
      prop: 'value',
    },
    props: {
      defaultVisible: {
        type: Boolean,
        default: false,
      },
      visible: Boolean,
    },
    data() {
      return {
        vs: this.defaultVisible,
      };
    },
    computed: {
      isControlled() {
        return 'visible' in this.$options.propsData;
      },
      _visible() {
        return this.isControlled ? this.visible : this.vs;
      },
    },
    methods: {
      handleVisibleChange(v) {
        this.$emit('update:visible', v);
        if (!this.isControlled) {
          this.vs = v;
        }
      },
      updatePopper() {
        const { wrappedInstance } = this.$refs;
        wrappedInstance && wrappedInstance.updatePopper();
      },
      getPopper() {
        const { wrappedInstance } = this.$refs;
        return wrappedInstance.$refs.popper;
      },
      focus() {
        const { wrappedInstance } = this.$refs;
        wrappedInstance.focus();
      },
      blur() {
        const { wrappedInstance } = this.$refs;
        wrappedInstance.blur();
      },
    },
  },
  {
    withRef: true,
    mapStateToProps(context) {
      return {
        visible: context._visible,
      };
    },
    mapMethodToListener(context) {
      return {
        'update:visible': context.handleVisibleChange,
      };
    },
  }
)(Picker);

const CustomSelect = withFormItem('CustomSelect', Control, {
  withRef: true,
  methods: {
    focus() {
      this.$refs.wrappedInstance.$refs.wrappedInstance.focus();
    },
    blur() {
      this.$refs.wrappedInstance.$refs.wrappedInstance.blur();
    },
  },
});

/* istanbul ignore next */
CustomSelect.install = function (Vue) {
  Vue.component(CustomSelect.name, CustomSelect);
};

export default CustomSelect;
