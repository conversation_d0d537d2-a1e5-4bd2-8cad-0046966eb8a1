<template>
  <div style="padding: 10px 30px 10px 10px">
    <div v-if="!showChart" class="tips">{{ InstructionFollowChartText }}</div>
    <v-chart v-if="showChart" ref="chart" :option="option" class="chart" autoresize style="width: 100%; height: 900px" />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Ref, <PERSON><PERSON>ra, Weaver } from '@/decorators';
import ModelEvalIUnified from '@/store/modules/modelEvalIUnified';
import type { ColumnLineChartParams } from '@/types/modelCommonComponent';

import { LineChart } from 'echarts/charts';
import {
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import VChart from 'vue-echarts';

echarts.use([
  LineChart,
  GridComponent,
  UniversalTransition,
  CanvasRenderer,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
  DataZoomComponent,
]);
@Component({
  components: {
    VChart,
  },
})
@Spectra
export default class InstructionFollowChart extends Weaver(ModelEvalIUnified) {
  @Ref('chart')
  $chart: any;
  @Prop({ default: '请选择数据子集和评测项' })
  InstructionFollowChartText!: string;
  @Prop()
  PARAMS!: ColumnLineChartParams;
  @Prop({ default: () => ({ rowKey: null, order: '' }) })
  columnSortOrderData!: {
    rowKey: null | string;
    order: 'ascending' | 'descending' | '';
  };
  showChart = false;
  option: any = {};
  columnsFilter: any = [];
  series: any = [];
  get lineCount() {
    return this.PARAMS.lineCount || Infinity;
  }

  handleTable(data) {
    if (data?.length > 0) {
      this.showChart = true;
      this.columnsFilter = [];
      const valueList: any = [];
      this.series = [];
      data.map((model, dataIndex) => {
        this.columnsFilter.push({
          label: model.modelName,
          key: `row_${dataIndex}`,
        });
        valueList.push({ name: model.modelName });
      });

      const series: any[] = [];

      data.forEach((model) => {
        model.capability.forEach((cap) => {
          const treeSignature = cap.tree.join('|');

          // 生成显示名称
          const displayName = cap.tree.length > 1 ? `${cap.name} {path|(${cap.tree.join('→')})}` : cap.name;

          let seriesEntry = series.find((entry) => entry.treeSignature === treeSignature);

          if (!seriesEntry) {
            seriesEntry = {
              type: 'line',
              id: treeSignature, // 唯一标识
              name: displayName, // 动态生成的显示名称
              originalName: cap.name,
              treeSignature: treeSignature,
              data: [],
            };
            series.push(seriesEntry);
          }

          seriesEntry.data.push({
            value: cap.percentage,
            commonQuery: [
              {
                ...cap,
                displayPath: cap.tree.join(' → '),
              },
            ],
          });
        });
      });

      this.series = series;

      this.setOption(this.series, valueList);
    } else {
      this.showChart = false;
    }
  }
  setOption(series, valueList) {
    const maxPercent = (50 / valueList.length) * 100;
    this.option = {
      title: '',
      grid: {
        left: 30, // 左侧边距
        right: 20, // 右侧边距
        bottom: 480,
      },
      toolbox: { show: true, feature: { saveAsImage: {} } },
      legend: {
        selector: ['all', 'inverse'],
        padding: [5, 5, 5, 50],
        left: 0,
        width: '95%',
        bottom: 60,
        selected: this.columnsFilter.reduce((obj, item, index) => {
          obj[item.label] = index < (this.lineCount as number);
          return obj;
        }, {}),
        // 保持图例文本简洁
        formatter: (name: string) => name,
        textStyle: {
          rich: {
            path: {
              color: '#999', // 浅灰色
              fontSize: 12, // 小字号
              verticalAlign: 'baseline', // 对齐方式
              padding: [0, 0, 2, 0], // 微调位置（可选）
            },
          },
        },
      },
      dataZoom: [
        { type: 'inside', start: 0, end: maxPercent, left: 'center', right: 'auto' },
        { start: 0, end: maxPercent, left: 'center', right: 'auto' },
      ],
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: valueList.map((item) => item.name),
        triggerEvent: true,
        axisLabel: {
          // interval: 0,
          minInterval: 1, // 设置最小间隔
          fontSize: 12, // 设置字体大小
          rotate: 90,
          fontWeight: 'bold',
          color: '#555',
          overflow: 'truncate',
          width: 350,
          rich: {
            clickable: {
              width: 50,
              align: 'center',
              verticalAlign: 'middle',
              cursor: 'pointer',
            },
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}',
        },
      },
      series: series,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params: any) {
          let result = '<div style="text-align: left;">';

          const grouped: Record<
            string,
            Array<{
              name: string;
              percentage: string;
              correct: string;
              wrong: string;
              color: string; // 新增颜色字段
              tree: any;
            }>
          > = {};

          params.forEach((param) => {
            const seriesName = param.seriesName;
            const commonQuery = (param.data as any).commonQuery[0];

            if (!grouped[seriesName]) {
              grouped[seriesName] = [];
            }

            grouped[seriesName].push({
              name: commonQuery.name,
              percentage: commonQuery.percentage,
              correct: commonQuery.correct,
              wrong: commonQuery.wrong,
              color: param.color, // 获取系列颜色
              tree: commonQuery.tree,
            });
          });

          Object.entries(grouped).forEach(([seriesName, items]) => {
            result += `<div style="margin-bottom: 8px;">`;

            items.forEach((item) => {
              const displayName = item.tree.length > 1 ? `(${item.tree.join('→')})` : '';
              result += `
          <div style="margin-top: 4px;">
            <div style="display: flex; align-items: center; gap: 8px;">
              <!-- 颜色小圆点 -->
              <span style="
                display: inline-block;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: ${item.color};
              "></span>
              <div style="
                display: flex;
                justify-content: space-between;
                flex-grow: 1;
                font-size: 16px;
                color: #000;
              ">
                <span>${item.name} <span style="font-size: 12px; color: #999; margin: 0 5px;">
                  ${displayName}
                </span></span>
                <span>${item.percentage}</span>
              </div>
            </div>
            <div style="
              color: #666;
              font-size: 12px;
              display: flex;
              gap: 20px;
              margin-left: 16px; /* 对齐文字 */
            ">
              <span>correct: ${item.correct}</span>
              <span>wrong: ${item.wrong}</span>
            </div>
          </div>
        `;
            });

            result += `</div>`;
          });

          result += '</div>';
          return result;
        },
      },
    };
  }
}
</script>
<style scoped lang="less">
.showFilter {
  text-align: left;
  display: flex;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
}
.filterOptionContent-name {
  margin-left: 20px;
  margin-right: 10px;
}
.tips {
  margin: 20px 0 20px 0;
}
</style>
