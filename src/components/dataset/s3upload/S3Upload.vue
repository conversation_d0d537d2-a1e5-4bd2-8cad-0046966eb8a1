<template>
  <div class="upload-container">
    <mtd-upload
      ref="upload"
      class="upload-base"
      action="#"
      multiple
      :file-list="files ?? []"
      :http-request="customUploadFile"
      show-file-down
      :on-download="onDownloadFile"
      :accept="accept"
      :limit="limit"
      :before-remove="beforeRemove"
      :on-success="onSuccess"
      :on-error="onError"
      :disabled="disabled"
    >
      <mtd-button :disabled="disabled" icon="mtdicon-export-o">点击上传</mtd-button>
    </mtd-upload>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Re<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from '@/decorators';
import datasetManage from '@/store/modules/datasetManage';
import { uploadFile, uploadPartFile } from './upload';

@Component({ components: {} })
@Spectra
export default class S3Upload extends Weaver(datasetManage) {
  @Prop({ default: () => [] }) files;
  @Prop({ default: '.xlsx,.json,.txt,.jsonl' }) accept?: string;
  @Prop({ default: 20 }) limit?: number;
  @Prop({ default: false }) disabled?: boolean;
  @Ref('upload') uploadRef;

  beforeRemove(file) {
    return this.$mtd.confirm({
      title: '删除文件',
      message: `确定移除${file.name}？`,
    });
  }

  getFiles() {
    const files = this.uploadRef?.uploadFiles;
    if (!files.length) {
      this.$mtd.message.error('请上传文件');
      return [];
    }
    const errFiles = [] as any;
    const uploadingFiles = [] as any;
    const successFiles = [] as any;
    files.forEach((file) => {
      file.status === 'uploading' && uploadingFiles.push(file);
      file.status === 'fail' && errFiles.push(file);
      file.status === 'success' && successFiles.push(file);
    });
    if (uploadingFiles.length) {
      this.$mtd.message.error('文件正在上传，请稍后再试');
      return [];
    }
    if (errFiles.length) {
      this.$mtd.message.error('存在上传失败的文件');
      return [];
    }
    return successFiles;
  }

  onSuccess(res, file) {
    this.$mtd.message.success(`${file.name}上传成功`);
  }

  onError(err, file) {
    this.$mtd.message.error(`${file.name}上传失败`);
  }

  async customUploadFile(options) {
    const { file } = options;
    if (!file) {
      this.$mtd.message.error('文件为空');
      return Promise.reject('options.file is null');
    }

    if (file.size < 1024 * 1024 * 100) {
      return uploadFile(options);
    } else {
      const key = await this.action$getS3key({ fileName: file.name });
      if (!key) {
        this.$mtd.message.error('获取S3key失败');
        return Promise.reject('获取S3key失败');
      }
      return uploadPartFile(options, key);
    }
  }

  onDownloadFile(e, file) {
    e.preventDefault();
    if (file?.response?.key) {
      this.action$getS3keyUrl({ key: file.response.key }).then((res) => {
        res && window.open(res, '_blank');
        res || this.$mtd.message.error('获取s3下载链接失败');
      });
    } else this.$mtd.message.error('文件s3key不存在');
  }
}
</script>

<style lang="scss" scoped>
.upload-container {
  .upload-base {
    margin-top: 10px;
  }
}
</style>
