<template>
  <div class="three-in-one-table">
    <slot name="top" :worker="worker"></slot>
    <div class="editBtns">
      <div v-if="PARAMS.unUseTemplateBtnFlag" style="margin-right: 10px; display: flex; justify-content: end">
        不使用模版
        <mtd-switch style="margin-left: 10px" :value="PARAMS.templateFlag" @change="templateFlagClick" />
      </div>
      <mtd-button
        class="refreshBtn"
        type="primary"
        @click="
          () => {
            getModelDashboard && getModelDashboard(false, false);
          }
        "
        >刷新报表</mtd-button
      >
    </div>
    <div class="vxe-table-container">
      <vxe-toolbar :loading="loading">
        <template #buttons>
          <div class="flex-center" style="width: 100%">
            <mtd-form v-if="PARAMS?.isFilter !== false" inline class="filtrate" style="flex: 1">
              <mtd-form-item label="行筛选" :label-width="60" class="margin-right-threeInOne">
                <mtd-select :disabled="unTabelData" v-model="form.rowFilter" @change="selectInit(false)">
                  <mtd-option label="全部显示" value="all" />
                  <mtd-option label="只看数据完整的" value="noEmpty" />
                </mtd-select>
              </mtd-form-item>
              <mtd-form-item label="列筛选" :label-width="60" class="margin-right-threeInOne">
                <mtd-select :disabled="unTabelData" v-model="form.columnFilter" @change="selectInit">
                  <mtd-option label="全部显示" value="all" />
                  <mtd-option label="只看数据完整的" value="noHasEmpty" />
                  <mtd-option label="只看数据不完整的" value="hasEmpty" />
                </mtd-select>
              </mtd-form-item>
              <!-- <mtd-form-item label="显示置信区间" :label-width="100" class="margin-right-threeInOne">
                <mtd-checkbox :disabled="unTabelData" v-model="form.withConfidenceInterval"></mtd-checkbox>
              </mtd-form-item> -->
            </mtd-form>

            <div class="flex-center">
              <mtd-button
                :disabled="unTabelData"
                style="margin-left: 10px"
                class="csv-btn"
                size="small"
                ghost
                type="primary"
                @click="handleExport"
              >
                <i class="mtdicon mtdicon-download-o" />
                导出表格
              </mtd-button>
            </div>
          </div>
        </template>
      </vxe-toolbar>
      <div class="vxe-grid-header" v-if="PARAMS.unTableDataModelEffects">
        <div class="vxe-grid-header-text">
          <mtd-tooltip
            style="font-size: 12px"
            placement="top"
            :content="getModelName(PARAMS.unTableDataModelEffects.baseModel)?.hoverName || '-'"
          >
            <span>对照模型：{{ getModelName(PARAMS.unTableDataModelEffects.baseModel)?.modelName || '-' }}</span>
          </mtd-tooltip>
        </div>
        <div class="vxe-grid-header-text" v-if="PARAMS.unTableDataModelEffects?.baseLineModel">
          <mtd-tooltip
            style="font-size: 12px"
            placement="top"
            :content="getModelName(PARAMS.unTableDataModelEffects.baseLineModel)?.hoverName || '-'"
          >
            <span>基线模型：{{ getModelName(PARAMS.unTableDataModelEffects.baseLineModel)?.modelName || '-' }}</span>
          </mtd-tooltip>
        </div>
      </div>
      <vxe-grid
        ref="xGrid"
        border
        align="center"
        max-height="650"
        :row-style="rowStyle"
        :scrollToLeftOnChange="true"
        header-row-class-name="vxe-thead"
        :column-config="{ width: 200, useKey: true }"
        :row-config="{ keyField: 'rowKey', isHover: true, useKey: true, height: 60 }"
        :loading="loading || LS('modelEvalIUnifiedModelDataSubSetDetailDiffTable', 'modelEvalIUnifiedModelCategoryDetailDiffTable')"
        :scroll-x="{ enabled: true, gt: 0, oSize: 5 }"
        :scroll-y="{ enabled: true, gt: 0, oSize: 5 }"
        @scroll="handleScroll"
        @filter-change="handleFilterChange"
      >
        <!--   表头   -->
        <template #header="{ columnIndex, column }">
          <TableHeaderCell
            :columnData="getColumnData(columnIndex)"
            :column="column"
            :rowSortOrderData="rowSortOrderData"
            :isRowSortable="isRowSortable"
            @sortClick="changeColSortOrder"
            @groupChange="groupChange"
          />
        </template>
        <!--  单元格    -->
        <template #col="{ rowid, column }">
          <EffectsUnuseTemplateBody
            :discussion="getDiscussion(rowid, column)"
            :tableKey="PARAMS.key"
            :rowKey="rowid"
            :rowData="getRowData(rowid)"
            :contrastRowData="contrastRowData"
            :column="column"
            :columnSortOrderData="columnSortOrderData"
            :isColumnSortable="isColumnSortable"
            :showSecondValue="showSecondValue"
            :evalDataSizeConfiguration="PARAMS.evalDataSizeConfiguration"
            :getToolTipHTML="PARAMS.isTableTooltip && PARAMS.tableTooltipHTML"
            :withConfidenceInterval="form.withConfidenceInterval"
            :filterGroup="filterGroup"
            :checkMark="checkMark"
            @modelClick="handelModelClick"
            @sortClick="changeColumnSortOrder"
          />
        </template>
      </vxe-grid>
    </div>
    <slot name="bottom" :columnSortOrderData="columnSortOrderData"></slot>
  </div>
</template>

<script lang="ts">
import EffectsUnuseTemplateBody from '@/components/model/ThreeInOne/EffectsUnuseTemplateBody.vue';
import ModelColumnTableWorker from '@/components/model/ThreeInOne/ModelColumnTableWorker';
// 表头变化较小
import TableHeaderCell from '@/components/model/ThreeInOne/TableHeaderCell.vue';
import { Component, Prop, Ref, Spectra, Watch, Weaver } from '@/decorators';
import ModelEval from '@/store/modules/modelEval';
import modelEvalIUnifiedStore from '@/store/modules/modelEvalIUnified';
import type { ColumnTableParams } from '@/types/modelCommonComponent';
import dayjs from 'dayjs';
import { cloneDeep, isEqual } from 'lodash';
import Vue from 'vue';
import { Inject } from 'vue-property-decorator';
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
Vue.use(VXETable);

@Component({
  components: { EffectsUnuseTemplateBody, TableHeaderCell },
})
@Spectra
export default class ModelEffectsUnuseTemplate extends Weaver(ModelEval, modelEvalIUnifiedStore) {
  @Inject({ default: () => null }) readonly kmIframeProps!: any;
  @Ref('xGrid')
  refXGrid!: any;
  @Prop()
  PARAMS!: ColumnTableParams;
  @Prop()
  confidenceIntervalName!: any;
  @Prop({ default: false })
  showDataSetDescription!: boolean;
  @Prop({ default: () => [] })
  selectedDataSet!: any[];
  @Prop({ default: true })
  screen?: boolean;
  @Prop({ default: false })
  geCategory!: any;
  @Prop()
  queryCriteria!: any;
  @Prop()
  getModelDashboard?: any;
  @Prop({ default: () => () => void 0 })
  templateFlagClick?: any;
  @Prop({ default: 'dataSubSet' })
  modelType?: string;
  @Prop({ default: false })
  independentUtility?: boolean;
  @Prop()
  filterGroupInit?: any;

  // 筛选器
  form: {
    rowFilter: 'all' | 'noEmpty';
    columnFilter: 'all' | 'noHasEmpty' | 'hasEmpty';
    withConfidenceInterval: false | true;
  } = { rowFilter: 'all', columnFilter: 'all', withConfidenceInterval: false };

  checkMark = false;
  // 排序
  columnSortOrderData: { rowKey: null | string; order: 'ascending' | 'descending' | '' } = { rowKey: null, order: '' };
  rowSortOrderData: {
    colId: null | string;
    order: 'ascending' | 'descending' | '';
    index: null | number;
  } = { colId: null, order: '', index: null };

  // 表格数据
  $tableData: any[] = [];
  $tableColumn: {
    type?: string;
    title?: string;
    field?: string;
    fixed?: string | null;
    width?: number | null;
    minWidth?: number | null;
    slots?: any;
    hasEmpty?: boolean;
    colId: string;
  }[] = [];

  showTableTips = false;
  contrastRowData: any = null;
  filterModelProperties: any = null;
  filterModelPropertiesMetadata: any = [];

  loading = false;
  worker: ModelColumnTableWorker = null as any;

  SET_TABLE_DATA = 'SET_TABLE_DATA';
  TRANSFORM_TABLE_DATA = 'TRANSFORM_TABLE_DATA';
  FILTER_SORT_TABLE_DATA = 'FILTER_SORT_TABLE_DATA';
  SLICE_TABLE_DATA = 'SLICE_TABLE_DATA';
  GET_CONTRAST_ROW_DATA = 'GET_CONTRAST_ROW_DATA';
  EXPORT_TABLE_DATA = 'EXPORT_TABLE_DATA';
  filterGroup = {
    selectGroup: {
      firstIndex: -1,
      secondIndex: 0,
      title: '',
    },
    type: 'OFF',
  };

  getEvalDataSize(evalDataSize) {
    return this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList.find((item) => item.name === evalDataSize)?.label || '';
  }

  getModelName(model) {
    const benchmarking = model.llmModel.modelInfo?.modelMeta.benchmarking || false;
    const modelName = benchmarking ? model.llmModel.modelInfo?.modelMeta.modelShortName : model.llmModel.modelInfo?.modelMeta.modelName;
    return {
      modelName: `${modelName} / ${model.llmModel.modelInfo?.modelCheckpoint} / ${model.llmModel.label}`,
      hoverName: model.llmModel.modelInfo?.modelMeta.modelName,
    };
  }

  groupChange({ selectGroup, type }) {
    if (selectGroup.firstIndex === this.filterGroup.selectGroup.firstIndex && this.filterGroup.type === 'ON') {
      this.filterGroup = {
        selectGroup: {
          firstIndex: -1,
          secondIndex: 0,
          title: '',
        },
        type: 'OFF',
      };
    }

    this.filterGroup = { selectGroup, type };
    this.setTableData();
  }
  get modelMap() {
    return this.modelSimpleList.llmModelList.reduce((obj, item) => {
      obj[`${item.family}:${item.name}`] = item.label || `${item.family}:${item.name}`;
      return obj;
    }, {});
  }

  get evalDataSizeMetaList() {
    return this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList || [];
  }

  get evalDataSize() {
    if (!this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList?.length) return {};
    const obj: any = {};
    obj.AUTO = 'Auto';
    for (const value of this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList) {
      obj[value.label] = value.name;
    }

    return obj;
  }

  // 行排序
  get isColumnSortable() {
    return this.PARAMS.columnSort || this.PARAMS.columnSort === undefined;
  }

  // 列排序
  get isRowSortable() {
    return this.PARAMS.rowSort || this.PARAMS.rowSort === undefined;
  }

  get showSecondValue() {
    return this.PARAMS.secondTrue && this.confidenceIntervalName?.itemDisplayType === 'SECOND_FIRST';
  }

  get metaVersionId() {
    if (this.PARAMS?.metaVersionId) {
      return this.PARAMS?.metaVersionId;
    }

    return this.$store.state.metaVersion?.id || parseInt(this.$route.params.metaVersionId, 10);
  }

  is$tableDataDirty = false;

  getColumnData(columnIndex) {
    return this.$tableColumn[columnIndex];
  }

  get getRowData() {
    // 脏标记更新
    if (this.is$tableDataDirty) {
      this.is$tableDataDirty = false;
    }

    return (rowid) => this.$tableData[rowid];
  }

  getDiscussion(rowKey, { field, title }) {
    if (!this.kmIframeProps) return;
    const rowData = this.getRowData(rowKey);
    if (!rowData) return null;

    const discussionAnchor = `tableKey:${this.PARAMS.key},modelLabel:${rowData.modelLabel},column:${title}`;
    const refKey = `${rowKey}_${field}`;
    return this.kmIframeProps.discussionList.find((item) => {
      if (item.anchor.discussionAnchor === discussionAnchor) {
        if (!item.dom && this.$refs[refKey]) {
          item.dom = this.$refs[refKey];
        }

        if (item.dom && !this.$refs[refKey]) {
          delete item.dom;
        }

        return true;
      }

      return false;
    });
  }

  @Watch('loading')
  handleLoadingChange() {
    this.$emit('onLoading', this.loading);
  }

  unTabelData = true;

  @Watch('PARAMS.unTableDataModelEffects')
  handleTableDataChange(newVal?, oldVal?) {
    if (isEqual(newVal, oldVal)) return;

    if (!this.PARAMS.unTableDataModelEffects) {
      this.unTabelData = true;
      return;
    }

    this.unTabelData = false;
    this.checkMark = false;
    // 新数据列筛选条件清空
    this.filterModelProperties = null;
    this.filterModelPropertiesMetadata = [];

    // 默认排序
    if (this.PARAMS.isMean || typeof this.PARAMS.isMean === 'undefined') {
      if (this.PARAMS.isMeanSort) {
        this.rowSortOrderData.colId = 'mean';
        this.rowSortOrderData.order = 'descending';
      }

      if (!this.PARAMS.isSortDefault) {
        this.columnSortOrderData = { rowKey: 'row_0', order: 'descending' };
      }
    }

    const modelList = this.PARAMS.unTableDataModelEffects?.evalModelList || [];
    if (modelList.length === 0) return;

    this.filterGroup = {
      selectGroup: {
        firstIndex: -1,
        secondIndex: 0,
        title: '',
      },
      type: 'OFF',
    };
    this.setTableData();
  }

  changeModelType(type) {
    this.modelType = type;
  }

  async mounted() {
    this.initWorker();
    if (!this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList?.length) await this.action$modelEvalListEvalDataSizeMeta({});
    if (!this.modelEvalUnVersionedListStatMeta?.statMetaList?.length) await this.action$modelEvalUnVersionedListStatMeta({});
    this.handleTableDataChange();
  }

  beforeDestroy() {
    this.worker.terminate();
  }

  selectInit(bol = true) {
    if (!bol) this.columnSortOrderData = { rowKey: null, order: '' };
    this.contrastRowData = null;
    this.getFilterTableData();
  }

  async handelModelClick(value) {
    try {
      if (!this.PARAMS.isContrastModel) return;
      if (this.contrastRowData?.rowKey === value.rowKey) {
        this.contrastRowData = null;
      } else {
        const { rowData } = await this.worker.sendMessageSync(this.GET_CONTRAST_ROW_DATA, { rowKey: value.rowKey });
        this.contrastRowData = Object.seal(rowData);
      }
    } catch (err) {
      console.error(err);
    }
  }

  changeColumnSortOrder(rowKey) {
    if (this.columnSortOrderData.rowKey === rowKey) {
      if (this.columnSortOrderData.order === 'ascending') {
        this.columnSortOrderData.order = 'descending';
      } else if (this.columnSortOrderData.order === 'descending') {
        this.columnSortOrderData.rowKey = null;
        this.columnSortOrderData.order = '';
      }
    } else {
      this.columnSortOrderData.rowKey = rowKey;
      this.columnSortOrderData.order = 'ascending';
    }

    this.getFilterTableData();
  }

  changeColSortOrder(colId, index) {
    if (this.rowSortOrderData.colId === colId) {
      if (this.rowSortOrderData.order === 'ascending') {
        this.rowSortOrderData.order = 'descending';
        this.rowSortOrderData.index = index;
      } else if (this.rowSortOrderData.order === 'descending') {
        this.rowSortOrderData.colId = null;
        this.rowSortOrderData.order = '';
        this.rowSortOrderData.index = null;
      }
    } else {
      this.rowSortOrderData.colId = colId;
      this.rowSortOrderData.order = 'ascending';
      this.rowSortOrderData.index = index;
    }

    this.contrastRowData = null;
    this.getFilterTableData();
  }

  keepFilterCss() {
    if (this.filterModelPropertiesMetadata.length === 0) return;
    // 保存列筛选的视觉状态
    this.filterModelPropertiesMetadata.forEach((T: any) => {
      const filtersArr: any = this.$tableColumn.filter((R: any) => R.filters);
      let target: any;
      filtersArr.forEach((R: any) => {
        if (R.filters && T.field === R.field) target = R;
      });

      if (target) {
        const indexTarget = T.filters.findIndex((IT: any) => IT.field === T.field);
        const updatedFilters = target.filters.map((filter) => {
          return {
            ...filter,
            checked: T.filters[indexTarget].values.includes(filter.value),
          };
        });

        const column = this.refXGrid.getColumnByField(T.field);
        this.refXGrid.setFilter(column, updatedFilters);
      }
    });
  }

  async handleFilterChange({ field, filters, values }) {
    // 减少不必要的刷新
    if (values.length === 0 && this.filterModelPropertiesMetadata.length === 0) return false;
    // 取需要的数据 没用数据较多
    this.filterModelProperties = {
      values: [...values],
      filters: filters.map((i: any) => {
        if ('column' in i) {
          delete i.column;
        }

        return i;
      }),
      field,
    };

    // 因未能使用 xTable.updateData() 更新数据，需要手动记录所有筛选状态
    if (this.filterModelPropertiesMetadata.length === 0) {
      this.filterModelPropertiesMetadata.push(cloneDeep(this.filterModelProperties));
    } else {
      let found = false;
      for (let i = 0; i < this.filterModelPropertiesMetadata.length; i++) {
        if (field === this.filterModelPropertiesMetadata[i].field) {
          this.filterModelPropertiesMetadata[i] = cloneDeep(this.filterModelProperties);
          found = true;
          break;
        }
      }

      if (!found && values.length !== 0) {
        this.filterModelPropertiesMetadata.push(cloneDeep(this.filterModelProperties));
      }

      // 针对重置筛选的时候
      let deleteIndex = -1;
      this.filterModelPropertiesMetadata.forEach((R, I) => {
        if (values.length === 0 && R.field === field) deleteIndex = I;
        const pushTarget = R.filters.find((RR) => RR.field === R.field);
        if (R.field !== field && R.filters.length > 0) this.filterModelProperties.filters.push(cloneDeep(pushTarget));
      });
      if (deleteIndex !== -1) this.filterModelPropertiesMetadata.splice(deleteIndex, 1);
    }

    await this.getFilterTableData();
  }

  $throttleTimer: any = null;
  handleScroll() {
    if (!this.$throttleTimer) {
      this.$throttleTimer = setTimeout(() => {
        this.getSliceTableData();
        if (this.kmIframeProps) {
          this.kmIframeProps.updateDiscussion = !this.kmIframeProps.updateDiscussion;
        }

        this.$throttleTimer = null;
      }, 200);
    }
  }

  currentRowKeys = '';
  currentColumnIds = '';

  getSliceTableData() {
    const { tableColumn } = this.refXGrid.getTableColumn();
    const { tableData } = this.refXGrid.getTableData();
    const rowKeys = tableData.map((item) => item.rowKey).join(',');
    const columnIds = tableColumn.map((item) => item.id).join(',');
    if (columnIds !== this.currentColumnIds || rowKeys !== this.currentRowKeys) {
      this.currentColumnIds = columnIds;
      this.currentRowKeys = rowKeys;
      this.worker.sendMessage(this.SLICE_TABLE_DATA, { rowKeys, columnIds });
    }
  }

  async getFilterTableData() {
    try {
      this.loading = true;
      this.currentRowKeys = '';
      this.currentColumnIds = '';
      const { tableColumn, tableData } = await this.worker.sendMessageSync(this.FILTER_SORT_TABLE_DATA, {
        PARAMS: Object.fromEntries(
          Object.entries(this.PARAMS).filter((entry) => typeof entry[1] !== 'function' && entry[0] !== 'unTableDataModelEffects')
        ),
        rowSortOrderData: this.rowSortOrderData,
        columnSortOrderData: this.columnSortOrderData,
        confidenceIntervalName: this.confidenceIntervalName,
        filterModelProperties: this.filterModelProperties,
        ...this.form,
      });
      this.$tableColumn = Object.seal(tableColumn);

      await this.refXGrid.reloadColumn(this.$tableColumn);
      await this.refXGrid.reloadData(Object.seal(tableData));

      this.getSliceTableData();
      this.loading = false;
      this.keepFilterCss();
    } catch (e) {
      console.error(e);
    } finally {
      this.loading = false;
      this.keepFilterCss();
    }
  }

  async setTableData() {
    try {
      this.loading = true;
      this.currentRowKeys = '';
      this.currentColumnIds = '';
      this.$tableData = [];
      this.$tableColumn = [];

      await this.worker.sendMessageSync(this.TRANSFORM_TABLE_DATA, {
        PARAMS: Object.fromEntries(Object.entries(this.PARAMS).filter((entry) => typeof entry[1] !== 'function')),
        modelMap: this.modelMap,
        evalDataSizeMetaList: this.evalDataSizeMetaList,
        showDataSubSetDescription: this.showDataSetDescription,
        selectedDataSet: this.selectedDataSet,
        // fixedColumnList: [{ a: 1, b: 2 }],
        filterGroup: this.filterGroupInit ? this.filterGroupInit : this.filterGroup,
        modelType: this.modelType,
        checkMark: this.checkMark,
        comparison: true,
        statMetaList: this.modelEvalUnVersionedListStatMeta?.statMetaList || [],
      });
      await this.getFilterTableData();
      this.loading = false;
    } catch (e) {
      console.error(e);
    }
  }

  initWorker() {
    // 初始化worker
    this.worker = Object.seal(new ModelColumnTableWorker());
    let timer: null | number = null;
    this.worker.addListener(this.SLICE_TABLE_DATA, (data) => {
      timer && cancelIdleCallback(timer);
      timer = requestIdleCallback(() => {
        this.$tableData = data.tableData;
        this.is$tableDataDirty = true;
        timer = null;
      });
    });
  }

  updateCategoryIdListAndReturn(tree, res) {
    const recursiveUpdate = (tree) => {
      for (const item of tree) {
        if (item.type === 'EXPRESSION' && item.leftParam.data === 'categoryIdList') {
          item.rightParam.data = [
            {
              version: {
                id: res.metaVersionId,
              },
              llmEval: {
                value: [],
                node: [],
              },
              llmEvalCustom: {
                value: [],
                node: [],
              },
            },
          ];
          if (res.type === 'LLM-EVAL') {
            item.rightParam.data[0].llmEval.value = [res.id];
            item.rightParam.data[0].llmEval.node = [res];
          } else {
            item.rightParam.data[0].llmEvalCustom.value = [res.id];
            item.rightParam.data[0].llmEvalCustom.node = [res];
          }
          return true; // 找到第一个后停止查找
        } else if (item.type === 'LOGIC' && item.leaves) {
          const found = recursiveUpdate(item.leaves);
          if (found) return true; // 如果在子节点中找到，停止查找
        }
      }
      return false; // 没有找到，继续查找
    };

    recursiveUpdate(tree);
    return tree; // 返回修改后的树结构
  }

  rowStyle(data) {
    if (this.contrastRowData?.rowKey === data.row.rowKey) {
      return { backgroundColor: '#0d46d92b' };
    }

    if (data.row.highlight) {
      return { backgroundColor: '#ffd200' };
    }
  }

  async handleExport() {
    try {
      this.$mtd.message.warning('正在导出csv');
      const data = {
        PARAMS: Object.fromEntries(Object.entries(this.PARAMS).filter((entry) => typeof entry[1] !== 'function')),
        rowSortOrderData: this.rowSortOrderData,
        columnSortOrderData: this.columnSortOrderData,
        confidenceIntervalName: this.confidenceIntervalName,
        filterModelProperties: this.filterModelProperties,
        ...this.form,
        withConfidenceInterval: this.form.withConfidenceInterval,
      };
      const { tableData } = await this.worker.sendMessageSync(this.EXPORT_TABLE_DATA, {
        showSecondValue: this.showSecondValue,
        data,
        comparison: true,
      });
      const csvContent = `data:text/csv;charset=utf-8,\uFEFF${tableData}`;
      const link = document.createElement('a');
      link.href = encodeURI(csvContent);
      link.download = `表格${dayjs().format('MMDDHHmmss')}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (e) {
      console.error(e);
    }
  }
}
</script>

<style lang="scss">
.backColor-240-247-255 {
  background-color: rgb(240, 247, 255);
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.display-none {
  display: none;
}
.display-none-important {
  display: none !important;
}
.margin-right-threeInOne {
  margin-right: 40px !important;
  margin-bottom: 0 !important;
}
.merge-cell-identification {
  width: 10px;
  display: block;
  transform: translateX(-10px);
  height: 120px !important;
}
.eval-data-size-tooltip {
  max-width: unset;
  .content {
    font-size: 12px;
  }
  .title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
  }
}
</style>
<style scoped lang="scss">
.editBtns {
  display: flex;
  align-items: center;
  justify-content: end;
}
.refreshBtn {
  margin-left: 10px;
}
.arrows {
  position: relative;
  left: -40px;
  transform: rotate(45deg);
  animation: moveX 1.5s linear infinite;
  user-select: none;
}

@keyframes moveX {
  0% {
    transform: translateX(0) rotate(45deg); /* 初始位置 */
  }
  50% {
    transform: translateX(30px) rotate(45deg); /* 向右移动60px */
  }
  100% {
    transform: translateX(0) rotate(45deg); /* 返回初始位置 */
  }
}

.vxe-grid-header {
  background: #f8f8f9;
  width: 100%;
  // height: 60px;
  padding: 20px 0;
  font-size: 14px;
  border: 1px solid #e9eaec;
  border-bottom: 0px solid #e9eaec;
}
.vxe-grid-header-text {
  width: 100%;
  text-align: center;
  line-height: 30px;
}
.patternComparison {
  line-height: 30px;
  &:hover {
    cursor: pointer;
    color: rgb(75, 156, 238);
    background: rgb(239, 246, 255);
  }
}
.dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #0a70f5;
  border-radius: 50%;
  margin-right: 4px;
}
::v-deep .vxe-cell {
  padding: 0 !important;
}
.three-in-one-table {
  user-select: none;
}
.cleanDirtyBox {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid rgb(216 216 216);
  .cleanDirty {
    line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.vxe-table-container {
  min-height: 40px;
  width: 100%;
  //overflow: hidden;
  font-size: 12px;
  margin-bottom: 10px;

  .spanTD {
    font-size: 12px;
    position: relative;
  }

  .show {
    display: inline-block;
  }

  .showBlock {
    display: block;
  }

  .hid {
    display: none;
  }

  .discussion {
    border-bottom: 2px solid rgba(255, 209, 0, 0.6);
    transition: background-color 0.2s;
    &.discussion-active {
      background-color: rgba(255, 209, 0, 0.25) !important;
    }
  }
  .clickable-value {
    cursor: pointer;
    transition: color 0.2s;

    &:hover {
      color: #1c6cdc;
    }
  }

  .blackTag {
    font-size: 10px;
    flex-shrink: 0;
    padding: 0 2px;
    height: 16px;
    line-height: 12px;
    border-radius: 3px;
    border: 1px solid #000000;
    position: absolute;
    top: 5px;
    left: 5px;
  }

  .mtd-table-sortable {
    cursor: pointer;
    flex-shrink: 0;
    margin-left: 2px;
    margin-right: -5px;
    transform: translateY(-2px);

    &.descending .mtd-table-sortable-descending {
      border-top-color: #1c6cdc;
    }

    &.ascending .mtd-table-sortable-ascending {
      border-bottom-color: #1c6cdc;
    }
  }

  .model-name-cell-col {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .mtdicon-question-circle-o {
      vertical-align: bottom;
      margin-left: 3px;
      color: #1c6cdc;
      font-size: 15px;
    }
  }

  .table-tips {
    margin-left: auto;
    color: #0a70f5;
    line-height: 24px;
    font-size: 12px;
    user-select: none;
  }

  .modelName {
    min-width: 278px;
    text-align: left;
    font-weight: bold;
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 60px;
    font-size: 12px;
    padding: 0 10px;

    .modelNameTxt {
      user-select: none;
      flex: 1;
      line-height: 16px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 5;
    }

    .model-btn {
      height: 12px;
      line-height: 12px;
      padding: 0 2px;
      margin-left: 4px;
      vertical-align: text-bottom;

      ::v-deep .mtd-tag-content {
        font-size: 10px;
      }
    }
  }

  ::v-deep {
    .vxe-header--column {
      line-height: 16px;
      position: relative;
    }

    .vxe-thead {
      height: 120px;
      font-size: 12px !important;
    }

    .vxe-body--row {
      transition: background 0.2s;

      .vxe-cell--label {
        font-size: 12px !important;
      }
    }

    .vxe-body--column {
      // 会引起滚动高度异常
      //height: 30px !important;
    }

    .c--ellipsis {
      white-space: pre-wrap !important;
    }

    .vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--title {
      overflow: visible;
      text-overflow: clip;
      white-space: normal;
    }

    .vxe-table--render-default .vxe-header--column.col--ellipsis > .vxe-cell {
    }
    .vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell {
    }
  }
}
</style>
