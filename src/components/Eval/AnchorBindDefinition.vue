<template>
  <div class="anchor-bind-container">
    <div v-if="bulkEdit" @click="openModal">编辑</div>
    <div class="anchor-bind-preview" v-else>
      【<mtd-button type="text-primary" @click="openModal">
        <span v-for="(tag, idx) in previewTags" :key="idx">
          <span v-if="idx !== 0">+</span>
          <source-viewer :label="tag.label" :source="tag.source"></source-viewer>
        </span> </mtd-button
      >】
      <span v-if="showBindType">&lt;{{ bindType }}&gt;</span>
    </div>

    <mtd-modal v-model="visible" title="锚点绑定详情" :width="1300" :mount-on-create="false" :closable="Boolean(disabled)">
      <div slot="title" class="flex justify-between">
        <span>锚点绑定详情</span>
        <div v-if="!disabled">
          <mtd-button type="primary" @click="closeModal(true)">保存</mtd-button>
          <mtd-button class="ml-4" @click="closeModal(false)">取消</mtd-button>
        </div>
      </div>
      <mtd-form :label-width="150">
        <mtd-form-item label="当前绑定层级" prop="source">
          <source-viewer :source="source"></source-viewer>
        </mtd-form-item>
        <mtd-form-item v-if="source !== 'PluginAnchor'" label="锚点路径" prop="anchorPath">
          {{ anchorDefine.anchorPath }}
        </mtd-form-item>
        <mtd-form-item label="锚点名" prop="anchor">
          {{ anchorDefine.anchor }}
        </mtd-form-item>
        <mtd-form-item label="锚点展示名" prop="label">
          {{ anchorDefine.label || anchorDefine.anchor }}
        </mtd-form-item>
        <mtd-form-item label="接口基类" prop="interfaceName">
          {{ anchorDefine.requirePluginInterface }}
        </mtd-form-item>
        <mtd-form-item label="接口基类展示名" prop="interfaceLabel">
          {{ anchorDefine.requirePluginInterfaceLabel }}
        </mtd-form-item>
        <mtd-form-item label="允许绑定多个插件" prop="allowMultiplePlugins">
          <mtd-switch v-model="anchorDefine.allowMultipleBind" true-value="YES" false-value="NO" disabled> </mtd-switch>
        </mtd-form-item>
        <mtd-form-item label="绑定模式" prop="bindType">
          <mtd-radio-group :value="bindType" :disabled="forceCover || Boolean(disabled)" @change="confirmBindTypeChange">
            <mtd-radio-button :value="BindType.COVER" :disabled="bulkEdit">覆盖绑定</mtd-radio-button>
            <mtd-radio-button :value="BindType.PARAM" :disabled="bulkEdit">参数绑定</mtd-radio-button>
            <mtd-radio-button
              :value="BindType.APPEND"
              :disabled="!(anchorDefine.allowMultipleBind === 'YES' && (source === 'RunSpec' || source === 'Model'))"
              >追加绑定</mtd-radio-button
            >
            <mtd-radio-button :value="BindType.EXTEND">继承上级</mtd-radio-button>
          </mtd-radio-group>
        </mtd-form-item>
        <mtd-announcement v-if="typeof disabled === 'string'" :title="disabled" type="warning" show-icon />
        <VueDraggable
          v-model="anchorDefine.pluginBindList"
          target=".sort-target"
          animation="150"
          :options="{ fallbackOnBody: true, direction: 'vertical', forceFallback: true }"
          :onMove="checkMoved"
          handle=".handle"
          @end="updateOrderIndex"
        >
          <table class="plugin-bind-table">
            <thead>
              <tr>
                <th v-if="anchorDefine.allowMultipleBind === 'YES'">排序</th>
                <th>实现类</th>
                <th>上下文入参</th>
                <th v-if="anchorDefine.allowMultipleBind === 'YES'">
                  <mtd-icon-button icon="mtdicon mtdicon-add" :disabled="getDisabled('addPlugin')" @click="addPlugin" />
                </th>
              </tr>
            </thead>
            <tbody class="sort-target">
              <tr v-for="(item, index) in anchorDefine.pluginBindList" :key="index" :fixed="getDisabled('sort', item)">
                <td
                  v-if="anchorDefine.allowMultipleBind === 'YES'"
                  :class="getDisabled('sort', item) ? 'cursor-not-allowed' : 'handle cursor-move'"
                >
                  &lt;{{ index + 1 }}&gt;
                </td>
                <td>
                  <mtd-select
                    :value="item.pluginImplement"
                    class="plugin-implement-select"
                    :style="`background-color: ${evalSourceView[item.source]?.color || 'transparent'};`"
                    :disabled="getDisabled('implement', item) || item.extra?.forceCover || bulkEdit"
                    :formatter="(val) => formatPluginImplement(val, item)"
                    :loading="selectLoading"
                    :filterable="true"
                    :remote="true"
                    :remote-method="searchImplList"
                    genre="line"
                    @change="(val) => pluginImplementChange(val, index)"
                  >
                    <mtd-option
                      v-for="impl in pluginImplList"
                      :key="impl.implementName"
                      :value="impl.implementName"
                      :label="impl.implementLabel"
                    >
                      {{ `<${impl.status === 'ONLINE' ? '上线' : '下线'}>${impl.implementLabel}` }}
                    </mtd-option>
                  </mtd-select>
                </td>
                <td>
                  <mtd-loading v-if="item.extra?.isLoading"></mtd-loading>
                  <context-param-definition
                    v-else-if="!item.extra?.isSymmetricDifference"
                    ref="contextParamDefinition"
                    :curSource="source"
                    :disabled="getDisabled('param', item)"
                    :value="item.contextParams"
                    :bulkEdit="bulkEdit"
                  />
                </td>
                <td v-if="anchorDefine.allowMultipleBind === 'YES'">
                  <mtd-icon-button icon="mtdicon mtdicon-delete-o" :disabled="getDisabled('delPlugin', item)" @click="delPlugin(index)" />
                </td>
              </tr>
            </tbody>
          </table>
        </VueDraggable>
      </mtd-form>
    </mtd-modal>
  </div>
</template>
<script lang="ts">
import { CatchError, Component, Prop, ProvideReactive, Spectra, Weaver } from '@/decorators';
import { AnchorDefine, BindType, ContextParam, PluginBind } from '@/model/customEval';
import evalCustomPlugin from '@/store/modules/evalCustomPlugin';
import { evalSourceView } from '@/utils';
import { cloneDeep, debounce } from 'lodash';
import { VueDraggable } from 'vue-draggable-plus';
import ContextParamDefinition from './ContextParamDefinition.vue';
import SourceViewer from './SourceViewer.vue';
@Component({
  components: { VueDraggable, ContextParamDefinition, SourceViewer },
})
@Spectra
export default class AnchorBindDefinition extends Weaver(evalCustomPlugin) {
  @Prop()
  defaultAnchorDefine!: AnchorDefine;
  @Prop()
  source!: string;
  @Prop()
  showBindType?: boolean;
  @Prop({ default: false })
  forceCover?: boolean;
  @Prop({ default: false })
  disabled?: boolean | string;
  @Prop({ default: false })
  snapshot?: boolean;
  @Prop({ default: false })
  bulkEdit?: boolean;
  @ProvideReactive('modelMetaSize')
  modelMetaSize = '';
  BindType = BindType;
  bindType = BindType.COVER;
  oriBindType: BindType = BindType.EXTEND;
  evalSourceView = evalSourceView;

  anchorDefine: AnchorDefine = {
    anchor: '',
    anchorPath: '',
    label: '',
    requirePluginInterface: '',
    requirePluginInterfaceLabel: '',
    allowMultipleBind: 'NO',
    pluginBindList: [],
  };
  visible = false;
  pluginImplListParam = {
    searchFilter: '-',
    interfaceNameFilter: '',
    ownerFilter: '',
    statusFilter: '',
    offset: 0,
    limit: 1000,
  };
  pluginImplList: {
    implementName: string;
    implementLabel: string;
    status: string;
  }[] = [];

  selectLoading = false;
  rules = {};

  get previewTags() {
    const { pluginBindList } = this.anchorDefine;
    if (!pluginBindList?.length) return [{ label: '未绑定' }];
    if (pluginBindList.length === 1)
      return [{ source: pluginBindList[0].source, label: pluginBindList[0].pluginImplementLabel || pluginBindList[0].pluginImplement }];

    const sourceMap = new Map();
    pluginBindList.forEach((item) => {
      if (sourceMap.has(item.source)) {
        const sourceInfo = sourceMap.get(item.source);
        sourceInfo.count++;
        sourceInfo.label = `${sourceInfo.count}个绑定`;
      } else {
        sourceMap.set(item.source, {
          count: 1,
          source: item.source,
          label: '1个绑定',
        });
      }
    });
    return Array.from(sourceMap.values());
  }

  mounted() {
    if (this.defaultAnchorDefine) this.anchorDefine = cloneDeep(this.defaultAnchorDefine);
  }

  getDisabled(pos: string, item?: PluginBind) {
    const canEdit = this.bindType === BindType.COVER || (this.bindType === BindType.APPEND && item?.source === this.source);
    const resCanEditConditionMap = {
      addPlugin: canEdit || this.bindType === BindType.APPEND,
      implement: canEdit && !item?.extra?.isLoading,
      param: canEdit || this.bindType === BindType.PARAM,
      delPlugin: canEdit,
      sort: canEdit || item?.extra?.isSymmetricDifference,
    };
    return !resCanEditConditionMap[pos] || Boolean(this.disabled);
  }

  async openModal() {
    this.anchorDefine = cloneDeep(this.defaultAnchorDefine);
    this.visible = true;
    this.pluginImplListParam.interfaceNameFilter = this.anchorDefine.requirePluginInterface;
    if (this.forceCover) {
      this.bindType = BindType.COVER;
    } else {
      const curSourcePlugin = this.anchorDefine.pluginBindList.find((bind) => bind.source === this.source);
      if (curSourcePlugin) this.bindType = curSourcePlugin.bindType as BindType;
      // 因为参数绑定拿到的实际anchor是上一层的 所以需要手动生成bindType
      else if (
        this.anchorDefine.pluginBindList.find(
          (bind) => bind.source !== this.source && bind.contextParams.find((param) => param.source === this.source)
        )
      ) {
        this.bindType = BindType.PARAM;
      } else {
        this.bindType = BindType.EXTEND;
      }
      this.oriBindType = this.bindType;
    }

    // 初始化自带一个
    if (this.anchorDefine.pluginBindList?.length === 0) {
      await this.addPlugin();
    }

    this.searchImplList();
    // 补充merge参数定义（包括默认值），外面传进来的没有
    if (this.anchorDefine.pluginBindList.length && !this.snapshot) {
      await Promise.all(this.anchorDefine.pluginBindList.map(this.addPluginBindParamDefine));
    }
  }
  async addPluginBindParamDefine(plugin) {
    if (!plugin.pluginImplement || plugin.extra?.isSymmetricDifference) return;

    const requestRes = await this.action$evalCustomPluginImplementDefine({
      interfaceName: this.anchorDefine.requirePluginInterface,
      implementName: plugin.pluginImplement!,
    });
    const { contextParamsDefine, interfaceLabel } = cloneDeep(requestRes?.evalCustomPluginImplement);

    const curParamsMap = new Map<string, ContextParam>(plugin.contextParams.map((define) => [define.name, define]));

    // 取前一层的实现
    const prefBindList = this.anchorDefine.prevPluginBindList?.find((x) => x.pluginImplement === plugin.pluginImplement);
    const prevParamsMap = new Map<string, ContextParam>((prefBindList?.contextParams || []).map((define) => [define.name, define]));

    // merge参数定义，添加defaultValue和defaultSource，并清空value
    (contextParamsDefine as any).forEach((param: ContextParam, idx) => {
      const curParam = curParamsMap.get(param.name);
      param.defaultSource = param.source;
      param.defaultValue = param.value!;
      param.value = null;
      if (curParam) {
        param.source = curParam.source;
        param.flag = curParam?.flag || '';
        if (curParam.source !== this.source) {
          param.defaultSource = curParam.source;
          param.defaultValue = curParam.value!;
        } else {
          param.value = curParam.value;
          const prevParam = prevParamsMap.get(param.name);
          if (prevParam) {
            param.defaultSource = prevParam.source;
            param.defaultValue = prevParam.value!;
          }
        }
      }
    });
    if (this.bulkEdit) {
      this.anchorDefine.requirePluginInterfaceLabel = interfaceLabel;
    }
    this.$set(plugin, 'contextParams', contextParamsDefine);
  }

  async closeModal(saved) {
    if (!saved) {
      this.anchorDefine = cloneDeep(this.defaultAnchorDefine);
      this.visible = false;
      return;
    }
    if (!this.anchorDefine.pluginBindList?.length || this.anchorDefine.pluginBindList.some((x) => !x.pluginImplement)) {
      await this.$mtd.confirm({
        title: '确认信息',
        message: '该节点无实现类或有实现类未设置，关闭窗口不会保存设置内容',
        showCancelButton: true,
        onOk: () => {
          this.anchorDefine = cloneDeep(this.defaultAnchorDefine);
          this.visible = false;
        },
        onCancel: () => {},
      });
    } else {
      let appendIndex = 1;
      let hasOverrideParam = false;

      this.anchorDefine.pluginBindList?.forEach((pluginBind) => {
        pluginBind.contextParams = pluginBind.contextParams.filter((param) => param.value !== undefined && param.value !== null);
        if (pluginBind.contextParams.length) hasOverrideParam = true;
        if (pluginBind.source === this.source || pluginBind.contextParams.find((params) => params.source === this.source))
          pluginBind.bindType = this.bindType;

        if (this.bindType === BindType.APPEND && pluginBind.source === this.source) {
          pluginBind.orderIndex = appendIndex;
          appendIndex += 1;
        }
      });

      if (this.bindType === BindType.PARAM && !hasOverrideParam) this.bindType = BindType.EXTEND;
      this.visible = false;
      this.$emit('update', { anchor: this.anchorDefine, changedBindType: this.bindType !== this.oriBindType, currBindType: this.bindType });
    }
  }

  searchImplList(query = '') {
    if (query === this.pluginImplListParam.searchFilter) return;
    this.selectLoading = true;
    this.pluginImplListParam.searchFilter = query;
    debounce(async () => {
      try {
        const res = await this.action$evalCustomPluginImplementList(this.pluginImplListParam);
        if (query !== this.pluginImplListParam.searchFilter) return;
        this.pluginImplList = res.evalCustomPluginImplementList.sort((a, b) => {
          if (a.status === 'ONLINE' && b.status !== 'ONLINE') return -1;
          if (a.status !== 'ONLINE' && b.status === 'ONLINE') return 1;
          return 0;
        });

        this.selectLoading = false;
      } catch (e) {
        console.error(e);
      }
    }, 200)();
  }

  updateOrderIndex() {
    this.anchorDefine.pluginBindList.forEach((item, index) => {
      item.orderIndex = index + 1;
    });
  }

  async addPlugin() {
    if (this.anchorDefine.pluginBindList.some((plugin) => plugin.extra?.isSymmetricDifference)) {
      try {
        await this.$mtd.confirm({
          message: '新增绑定会清除各执行单元该锚点的绑定差异',
          okButtonText: '确定',
          cancelButtonText: '取消',
        });
      } catch (e) {
        console.log(e);
      }

      this.$set(
        this.anchorDefine,
        'pluginBindList',
        this.anchorDefine.pluginBindList.filter((item) => !item.extra?.isSymmetricDifference)
      );
    }

    this.anchorDefine.pluginBindList.push({
      anchorPath: this.anchorDefine.anchorPath!,
      orderIndex: this.anchorDefine.pluginBindList.length + 1,
      bindType: this.bindType,
      pluginInterface: this.anchorDefine.requirePluginInterface,
      pluginImplement: '',
      source: this.source,
      contextParams: [],
      extra: {
        isAddedItem: true, // 前端记录新增的插件用
      },
    });
  }

  delPlugin(idx: number) {
    this.anchorDefine.pluginBindList.splice(idx, 1);
    this.updateOrderIndex();
  }

  checkMoved(e) {
    return !e.related.getAttribute('fixed');
  }

  hasDuplicatePluginImplement(array, val) {
    const count = array.filter((item) => item.pluginImplement === val).length;
    return count >= 1;
  }

  async pluginImplementChange(val: string, index) {
    if (!val) return;
    if (this.anchorDefine.allowMultipleBind === 'YES') {
      const result = this.hasDuplicatePluginImplement(this.anchorDefine.pluginBindList, val);
      if (result) {
        this.$mtd.message({
          message: '实现类选择不能重复',
          type: 'warning',
        });
        this.anchorDefine.pluginBindList[index].pluginImplement = '';
        return;
      }
    }

    if (this.anchorDefine.pluginBindList[index]?.extra?.isSymmetricDifference) {
      try {
        await this.$mtd.confirm({
          message: '修改该绑定会清除各执行单元该锚点的绑定差异',
          okButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: true,
        });
      } catch (e) {
        console.log(e);
      }
    }

    this.$set(this.anchorDefine.pluginBindList, index, {
      orderIndex: index + 1,
      pluginImplement: val,
      source: this.source,
      contextParams: [],
      extra: {
        isAddedItem: true,
        isLoading: true,
      },
    });
    const res = await this.action$evalCustomPluginImplementDefine({
      interfaceName: this.anchorDefine.requirePluginInterface,
      implementName: val,
    });
    const { contextParamsDefine: contextParams } = res.evalCustomPluginImplement;
    this.$set(this.anchorDefine.pluginBindList, index, {
      anchorPath: this.anchorDefine.anchorPath,
      orderIndex: index + 1,
      bindType: this.bindType,
      pluginInterface: this.anchorDefine.requirePluginInterface,
      pluginImplement: val,
      pluginImplementLabel: res.evalCustomPluginImplement.implementLabel,
      source: this.source,
      contextParams: contextParams.map((x) => ({ ...x, defaultValue: x.value, defaultSource: x.source, value: null })),
      extra: {
        isAddedItem: true,
        isLoading: false,
      },
    });
  }

  @CatchError
  async confirmBindTypeChange(val) {
    if (val === BindType.COVER) {
      const pluginBindList = cloneDeep(this.defaultAnchorDefine.pluginBindList);
      pluginBindList.forEach((item) => {
        item.source = this.source;
        item.bindType = this.bindType;
      });
      pluginBindList.forEach(await this.addPluginBindParamDefine);

      this.$set(this.anchorDefine, 'pluginBindList', pluginBindList);
    } else {
      const prevSourcePluginBindList = cloneDeep(this.defaultAnchorDefine.prevPluginBindList || this.defaultAnchorDefine.pluginBindList);
      prevSourcePluginBindList.forEach(await this.addPluginBindParamDefine);
      this.$set(this.anchorDefine, 'pluginBindList', prevSourcePluginBindList);
    }

    this.bindType = val;
  }

  formatPluginImplement(val, item) {
    if (!val) return '';
    return `${val.label}${item.source === this.source ? '<当前>' : ''}`;
  }
}
</script>
<style lang="scss">
.anchor-bind-container {
  display: inline-block;
}
.plugin-bind-table {
  width: 100%;
  border-collapse: collapse;
  th,
  td {
    border: 1px solid #ddd;
    padding: 8px;
  }
  th {
    background-color: #f2f2f2;
  }
}
.plugin-implement-select {
  .mtd-input {
    padding-left: 5px !important;
  }
}
</style>
