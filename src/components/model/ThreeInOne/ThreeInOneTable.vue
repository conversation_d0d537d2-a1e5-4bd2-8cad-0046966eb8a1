<template>
  <div class="three-in-one-table">
    <slot name="top" :worker="worker"></slot>
    <div :style="{ marginLeft: screen ? '0px' : '100px' }">
      <mtd-radio-group
        v-model="modelType"
        type="fill"
        @change="handleRadio"
        :disabled="LS('modelEvalIUnifiedModelDataSubSetView', 'modelEvalIUnifiedModelCategoryView')"
      >
        <mtd-radio-button value="dataSubSet">数据子集视图</mtd-radio-button>
        <mtd-radio-button value="category">类目视图</mtd-radio-button>
      </mtd-radio-group>
    </div>
    <div class="vxe-table-container">
      <vxe-toolbar :loading="loading">
        <template #buttons>
          <div class="flex-center" style="width: 100%">
            <mtd-form v-if="PARAMS?.isFilter !== false" inline class="filtrate" style="flex: 1">
              <mtd-form-item label="行筛选" :label-width="60" class="margin-right-threeInOne">
                <mtd-select :disabled="unTabelData" v-model="form.rowFilter" @change="selectInit(false)">
                  <mtd-option label="全部显示" value="all" />
                  <mtd-option label="只看数据完整的" value="noEmpty" />
                </mtd-select>
              </mtd-form-item>
              <mtd-form-item label="列筛选" :label-width="60" class="margin-right-threeInOne">
                <mtd-select :disabled="unTabelData" v-model="form.columnFilter" @change="selectInit">
                  <mtd-option label="全部显示" value="all" />
                  <mtd-option label="只看数据完整的" value="noHasEmpty" />
                  <mtd-option label="只看数据不完整的" value="hasEmpty" />
                </mtd-select>
              </mtd-form-item>
              <mtd-form-item label="显示置信区间" :label-width="100" class="margin-right-threeInOne">
                <mtd-checkbox :disabled="unTabelData" v-model="form.withConfidenceInterval"></mtd-checkbox>
              </mtd-form-item>
              <mtd-form-item label="输出token平均数" :label-width="140" class="margin-right-threeInOne">
                <mtd-checkbox :disabled="unTabelData" v-model="form.tokenOutputAvg"></mtd-checkbox>
              </mtd-form-item>
              <mtd-form-item label="平均tiktoken数" :label-width="140" class="margin-right-threeInOne">
                <mtd-checkbox :disabled="unTabelData" v-model="form.tikTokenOutputAvg"></mtd-checkbox>
              </mtd-form-item>
              <mtd-form-item label="平均字符数" :label-width="140" class="margin-right-threeInOne">
                <mtd-checkbox :disabled="unTabelData" v-model="form.textLenOutputAvg"></mtd-checkbox>
              </mtd-form-item>
              <mtd-form-item label="bugfix" :label-width="130" class="margin-right-threeInOne">
                <div class="badcase">
                  <mtd-switch v-model="form.containsBadCase" size="small" @change="handleContainsBadCase" />
                </div>
              </mtd-form-item>
            </mtd-form>

            <div class="flex-center">
              <div v-if="PARAMS.patternComparisonFlag" class="flex-center">
                <svg
                  v-if="PARAMS.keyframesFlag && !checkMark"
                  t="1724811457531"
                  class="arrows"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="5911"
                  width="30"
                  height="30"
                >
                  <path
                    d="M695.629739 224.593198c-6.04774 0-11.587919-4.027733-13.220093-10.155291-1.966795-7.305383 2.367931-14.82566 9.68764-16.778128l31.403221-8.416693c7.291057-1.980097 14.811334 2.367931 16.778128 9.686616 1.952468 7.305383-2.382257 14.82566-9.68764 16.779152l-31.403221 8.41567C697.998693 224.445842 696.806541 224.593198 695.629739 224.593198z"
                    fill="#1c6cdc"
                    p-id="5912"
                  ></path>
                  <path
                    d="M409.267732 301.314557c-6.04774 0-11.586896-4.027733-13.219069-10.155291-1.966795-7.305383 2.367931-14.82566 9.68764-16.778128l227.006154-60.813041c7.346315-1.9934 14.812357 2.367931 16.778128 9.68764 1.953492 7.305383-2.381234 14.824637-9.68764 16.778128l-227.006154 60.812017C411.636686 301.167201 410.445558 301.314557 409.267732 301.314557z"
                    fill="#1c6cdc"
                    p-id="5913"
                  ></path>
                  <path
                    d="M232.288972 949.922864c-17.72878 0-34.440393-8.081049-51.098795-24.753777L76.210217 820.181965c-52.008514-51.995211-12.938684-91.051739 5.833869-109.823268C208.739627 589.323063 268.856819 531.961634 297.504276 504.665964l-116.192326-74.862012c-40.047088-21.702278-60.987003-60.531632-56.062853-104.111173 5.981225-52.918233 49.426713-99.039667 108.084671-114.76072L779.737322 64.553225c59.635215-16.015765 112.326275-2.823302 141.013641 34.574447 23.387663 30.480198 29.100782 74.005504 16.095583 122.535801l-146.404418 546.364669c-18.839067 70.365604-81.177857 102.417601-131.125433 102.417601l0 0c-40.890292 0-74.822103-20.097734-88.912006-52.563146l-59.527768-114.186645c-29.676903 29.208229-91.185792 89.566922-219.368196 215.159338C277.152762 933.197948 257.243317 949.922864 232.288972 949.922864zM104.937492 768.683057c0.106424 0 1.297552 4.027733 10.021238 12.751419L219.939713 886.421598c8.616238 8.616238 12.30935 8.696056 12.349259 8.696056 0.561795 0 5.873778-0.427742 20.658506-15.225773 65.74947-64.404846 236.721423-231.991702 245.109464-240.514819 5.861498-6.235005 14.277168-9.392928 22.89443-8.482185 9.111518 0.976234 17.139356 6.448876 21.381983 14.569834l77.162404 148.024312c0.361227 0.708128 0.696872 1.431605 1.00284 2.167363 7.707542 18.545378 31.658024 19.990287 38.817074 19.990287 29.690206 0 66.819848-19.335371 78.178547-61.803601l146.418744-546.363645c8.496511-31.711236 6.141884-58.337663-6.635117-74.996065-12.698207-16.537651-33.410947-20.00359-48.556902-20.00359-10.704806 0-22.411429 1.685385-34.802644 5.003967L247.529071 263.863596c-42.843784 11.480472-64.933895 42.428322-67.823712 67.997674-2.408863 21.341051 7.653307 39.137369 28.339442 50.094932 0.695848 0.361227 1.364067 0.762363 2.020006 1.177825l146.418744 94.329389c7.305383 4.710278 11.948123 12.604062 12.510942 21.287839 0.548492 8.670473-3.037173 17.099447-9.686616 22.706141-5.178953 4.803399-174.302815 166.314887-238.968603 228.090858-12.938684 12.951987-15.093766 17.688871-15.400758 19.147082L104.938516 768.683057z"
                    fill="#1c6cdc"
                    p-id="5914"
                  ></path>
                </svg>
                对比勾选
                <mtd-switch style="margin-left: 10px" v-model="checkMark" :disabled="unTabelData" />
              </div>
              <mtd-button
                :disabled="unTabelData"
                style="margin-left: 10px"
                class="csv-btn"
                size="small"
                ghost
                type="primary"
                @click="handleExport"
              >
                <i class="mtdicon mtdicon-download-o" />
                导出表格
              </mtd-button>
            </div>
          </div>
        </template>
      </vxe-toolbar>
      <vxe-grid
        ref="xGrid"
        border
        align="center"
        max-height="650"
        :row-style="rowStyle"
        :scrollToLeftOnChange="true"
        header-row-class-name="vxe-thead"
        :column-config="{ width: 200, useKey: true }"
        :row-config="{ keyField: 'rowKey', isHover: true, useKey: true, height: 85 }"
        :loading="loading || LS('modelEvalIUnifiedModelDataSubSetView', 'modelEvalIUnifiedModelCategoryView')"
        :scroll-x="{ enabled: true, gt: 0, oSize: 5 }"
        :scroll-y="{ enabled: true, gt: 0, oSize: 5 }"
        @scroll="handleScroll"
        @filter-change="handleFilterChange"
      >
        <!--   表头   -->
        <template #header="{ columnIndex, column }">
          <TableColumnHeader
            :columnData="getColumnData(columnIndex)"
            :column="column"
            :rowSortOrderData="rowSortOrderData"
            :isRowSortable="isRowSortable"
            @sortClick="changeColSortOrder"
            @groupChange="groupChange"
          />
        </template>
        <!--  单元格    -->
        <template #col="{ rowid, column }">
          <TableBodyCell
            :discussion="getDiscussion(rowid, column)"
            :tableKey="PARAMS.key"
            :rowKey="rowid"
            :rowData="getRowData(rowid)"
            :contrastRowData="contrastRowData"
            :column="column"
            :columnSortOrderData="columnSortOrderData"
            :isColumnSortable="isColumnSortable"
            :showSecondValue="showSecondValue"
            :evalDataSizeConfiguration="PARAMS.evalDataSizeConfiguration"
            :getToolTipHTML="PARAMS.isTableTooltip && PARAMS.tableTooltipHTML"
            :withConfidenceInterval="form.withConfidenceInterval"
            :tokenOutputAvg="form.tokenOutputAvg"
            :tikTokenOutputAvg="form.tikTokenOutputAvg"
            :textLenOutputAvg="form.textLenOutputAvg"
            :filterGroup="filterGroup"
            :patternComparison="patternComparison"
            :checkMark="checkMark"
            @modelClick="handelModelClick"
            @sortClick="changeColumnSortOrder"
            @valueClick="handleValueClick"
            @checkboxChange="checkboxChange"
          />
        </template>
      </vxe-grid>
    </div>
    <slot name="bottom" :columnSortOrderData="columnSortOrderData"></slot>
  </div>
</template>

<script lang="ts">
import ModelColumnTableWorker from '@/components/model/ThreeInOne/ModelColumnTableWorker';
import TableBodyCell from '@/components/model/ThreeInOne/TableBodyCell.vue';
import TableColumnHeader from '@/components/model/ThreeInOne/TableHeaderCell.vue';
import { Component, Prop, Ref, Spectra, Watch, Weaver } from '@/decorators';
import ModelEval from '@/store/modules/modelEval';
import modelEvalIUnifiedStore from '@/store/modules/modelEvalIUnified';
import type { ColumnTableParams } from '@/types/modelCommonComponent';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import Vue from 'vue';
import { Inject } from 'vue-property-decorator';
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
Vue.use(VXETable);
interface Model {
  family: string;
  name: string;
  modelId: string;
}

interface ComparisonModel {
  evalModelList: Model[];
  baseModel: Model | {};
  baseLineModel?: Model | {};
}

@Component({
  components: { TableBodyCell, TableColumnHeader },
})
@Spectra
export default class ThreeInOneTable extends Weaver(ModelEval, modelEvalIUnifiedStore) {
  @Inject({ default: () => null }) readonly kmIframeProps!: any;
  @Ref('xGrid')
  refXGrid!: any;
  @Prop()
  PARAMS!: ColumnTableParams;
  // @Prop()
  // confidenceIntervalName!: any;
  @Prop({ default: false })
  showDataSetDescription!: boolean;
  @Prop({ default: () => [] })
  selectedDataSet!: any[];
  @Prop({ default: true })
  screen?: boolean;
  @Prop({ default: false })
  handleQuery!: any;
  @Prop()
  queryCriteria!: any;
  @Prop({ default: false })
  geCategory?: any;

  // 筛选器
  form: {
    rowFilter: 'all' | 'noEmpty';
    columnFilter: 'all' | 'noHasEmpty' | 'hasEmpty';
    withConfidenceInterval: false | true;
    containsBadCase: false | true;
    tokenOutputAvg: false | true;
    tikTokenOutputAvg: false | true;
    textLenOutputAvg: false | true;
  } = {
    rowFilter: 'all',
    columnFilter: 'all',
    withConfidenceInterval: false,
    containsBadCase: false,
    tokenOutputAvg: false,
    tikTokenOutputAvg: false,
    textLenOutputAvg: false,
  };

  checkMark = false;
  modelType = 'dataSubSet';
  // 排序
  columnSortOrderData: { rowKey: null | string; order: 'ascending' | 'descending' | '' } = { rowKey: null, order: '' };
  rowSortOrderData: {
    colId: null | string;
    order: 'ascending' | 'descending' | '';
    index: null | number;
  } = { colId: null, order: '', index: null };

  // 表格数据
  $tableData: any[] = [];
  $tableColumn: {
    type?: string;
    title?: string;
    field?: string;
    fixed?: string | null;
    width?: number | null;
    minWidth?: number | null;
    slots?: any;
    hasEmpty?: boolean;
    colId: string;
  }[] = [];

  showTableTips = false;
  contrastRowData: any = null;
  filterModelProperties: any = null;
  filterModelPropertiesMetadata: any = [];

  loading = false;
  worker: ModelColumnTableWorker = null as any;

  SET_TABLE_DATA = 'SET_TABLE_DATA';
  TRANSFORM_TABLE_DATA = 'TRANSFORM_TABLE_DATA';
  FILTER_SORT_TABLE_DATA = 'FILTER_SORT_TABLE_DATA';
  SLICE_TABLE_DATA = 'SLICE_TABLE_DATA';
  GET_CONTRAST_ROW_DATA = 'GET_CONTRAST_ROW_DATA';
  EXPORT_TABLE_DATA = 'EXPORT_TABLE_DATA';
  filterGroup = {
    selectGroup: {
      firstIndex: -1,
      secondIndex: 0,
      title: '',
    },
    type: 'OFF',
  };

  get metaVersionList() {
    return this.modelEvalMetaVersionList.metaVersionList || [];
  }

  handleContainsBadCase() {
    if (this.form.containsBadCase) {
      this.$mtd.message.warning('此功能目前仅支持V5版本的数据，开启后会剔除数据子集中的badcase');
    }
    this.$emit('handleContainsBadCase', !this.form.containsBadCase);
  }

  setContainsBadCase(value) {
    this.form.containsBadCase = value;
  }

  patternComparison: any = null;
  checkboxChange({ llmModelId, modelId, modelType, flag = true }) {
    if (
      (modelType === '对照模型' || modelType === '基线模型') &&
      Object.values(this.patternComparison)
        .map((item: any) => item.modelType)
        .includes(modelType)
    ) {
      this.$mtd.message.warning(`${modelType}只能选择一个`);
      return;
    }

    if (!this.patternComparison[llmModelId]?.checked && !modelType && !this.patternComparison[llmModelId]?.modelType)
      modelType = '评测模型';
    modelType = modelType ? modelType : this.patternComparison[llmModelId]?.modelType;
    if (this.patternComparison[llmModelId]?.checked && flag) modelType = '';
    this.patternComparison = {
      ...this.patternComparison,
      [llmModelId]: {
        ...this.patternComparison[llmModelId],
        checked: flag ? !this.patternComparison[llmModelId]?.checked : true,
        modelType,
        modelId,
      },
    };
  }

  parsePatternComparison(): ComparisonModel {
    let result: ComparisonModel = {
      evalModelList: [],
      baseModel: {},
      baseLineModel: {},
    };

    if (this.unTabelData) return result;

    Object.values(this.patternComparison)
      .filter((item: any) => item.checked)
      .forEach((item: any) => {
        const model = { family: item.family, name: item.name, modelId: item.modelId };
        switch (item.modelType) {
          case '评测模型':
            result.evalModelList.push(model);
            break;
          case '对照模型':
            result.baseModel = model;
            break;
          case '基线模型':
            result.baseLineModel = model;
            break;
        }
      });

    if (JSON.stringify(result.baseLineModel) === '{}') delete result.baseLineModel;
    return result;
  }

  groupChange({ selectGroup, type }) {
    if (selectGroup.firstIndex === this.filterGroup.selectGroup.firstIndex && this.filterGroup.type === 'ON') {
      this.filterGroup = {
        selectGroup: {
          firstIndex: -1,
          secondIndex: 0,
          title: '',
        },
        type: 'OFF',
      };
    }

    this.filterGroup = { selectGroup, type };
    this.setTableData();
  }
  get modelMap() {
    return this.modelSimpleList.llmModelList.reduce((obj, item) => {
      obj[`${item.family}:${item.name}`] = item.label || `${item.family}:${item.name}`;
      return obj;
    }, {});
  }

  get evalDataSizeMetaList() {
    return this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList || [];
  }

  get evalDataSize() {
    if (!this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList?.length) return {};
    const obj: any = {};
    obj.AUTO = 'Auto';
    for (const value of this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList) {
      obj[value.label] = value.name;
    }

    return obj;
  }

  // 行排序
  get isColumnSortable() {
    return this.PARAMS.columnSort || this.PARAMS.columnSort === undefined;
  }

  // 列排序
  get isRowSortable() {
    return this.PARAMS.rowSort || this.PARAMS.rowSort === undefined;
  }

  get showSecondValue() {
    return this.PARAMS.secondTrue;
  }

  get metaVersionId() {
    if (this.PARAMS?.metaVersionId) {
      return this.PARAMS?.metaVersionId;
    }

    return this.$store.state.metaVersion?.id || parseInt(this.$route.params.metaVersionId, 10);
  }

  is$tableDataDirty = false;

  getColumnData(columnIndex) {
    return this.$tableColumn[columnIndex];
  }

  get getRowData() {
    // 脏标记更新
    if (this.is$tableDataDirty) {
      this.is$tableDataDirty = false;
    }

    return (rowid) => this.$tableData[rowid];
  }

  getDiscussion(rowKey, { field, title }) {
    if (!this.kmIframeProps) return;
    const rowData = this.getRowData(rowKey);
    if (!rowData) return null;

    const discussionAnchor = `tableKey:${this.PARAMS.key},modelLabel:${rowData.modelLabel},column:${title}`;
    const refKey = `${rowKey}_${field}`;
    return this.kmIframeProps.discussionList.find((item) => {
      if (item.anchor.discussionAnchor === discussionAnchor) {
        if (!item.dom && this.$refs[refKey]) {
          item.dom = this.$refs[refKey];
        }

        if (item.dom && !this.$refs[refKey]) {
          delete item.dom;
        }

        return true;
      }

      return false;
    });
  }

  @Watch('loading')
  handleLoadingChange() {
    this.$emit('onLoading', this.loading);
  }

  unTabelData = true;

  initTableChange() {
    this.unTabelData = false;
    this.checkMark = false;
    this.patternComparison = null;
    // 新数据列筛选条件清空
    this.filterModelProperties = null;
    this.filterModelPropertiesMetadata = [];
    this.filterGroup = {
      selectGroup: {
        firstIndex: -1,
        secondIndex: 0,
        title: '',
      },
      type: 'OFF',
    };

    // 默认排序
    if (this.PARAMS.isMean || typeof this.PARAMS.isMean === 'undefined') {
      if (this.PARAMS.isMeanSort) {
        this.rowSortOrderData.colId = 'mean';
        this.rowSortOrderData.order = 'descending';
      }

      if (!this.PARAMS.isSortDefault) {
        this.columnSortOrderData = { rowKey: 'row_0', order: 'descending' };
      }
    }

    this.setTableData();
  }

  @Watch('PARAMS.tableDataDataSubSet')
  handleTableDataChange() {
    if (this.modelType !== 'dataSubSet') return;
    if (this.PARAMS.dataSubsetRowComparePage) return;
    if (!this.PARAMS.tableDataDataSubSet) {
      this.unTabelData = true;
      return;
    }

    const modelList = this.PARAMS.tableDataDataSubSet?.evalModelList || [];
    if (modelList.length === 0) {
      this.unTabelData = true;
      this.$tableData = [];
      return;
    }

    this.initTableChange();
  }

  @Watch('PARAMS.tableDataCategory')
  tableDataCategoryChange() {
    if (this.modelType !== 'category') return;
    if (this.PARAMS.dataSubsetRowComparePage) return;

    if (!this.PARAMS.tableDataCategory) {
      this.unTabelData = true;
      return;
    }

    const modelList = this.PARAMS.tableDataCategory?.evalModelList || [];
    if (modelList.length === 0) {
      this.unTabelData = true;
      this.$tableData = [];
      return;
    }

    this.initTableChange();
  }

  changeModelType(type) {
    this.modelType = type;
  }

  async handleRadio(value) {
    // this.loading = true;
    if (value === 'category') {
      if (this.geCategory && !this.PARAMS.tableDataCategory) {
        await this.geCategory();
      } else {
        // 自定义报表
        this.tableDataCategoryChange();
      }
    } else if (value === 'dataSubSet') {
      if (this.handleQuery && !this.PARAMS.tableDataDataSubSet) {
        this.handleQuery(false, void 0, { clear: false });
      } else {
        this.handleTableDataChange();
      }
    } else {
      // 自定义报表
      this.handleTableDataChange();
    }

    this.$emit('handleModelType', value);
    this.loading = false;
  }

  async mounted() {
    await this.action$modelEvalMetaVersionList({});

    this.initWorker();
    if (!this.modelEvalListEvalDataSizeMeta?.evalDataSizeMetaList?.length) await this.action$modelEvalListEvalDataSizeMeta({});
    if (!this.modelEvalUnVersionedListStatMeta?.statMetaList?.length) await this.action$modelEvalUnVersionedListStatMeta({});

    this.handleTableDataChange();
  }

  beforeDestroy() {
    this.worker?.terminate();
  }

  selectInit(bol = true) {
    if (!bol) this.columnSortOrderData = { rowKey: null, order: '' };
    this.contrastRowData = null;
    this.getFilterTableData();
  }

  async handelModelClick(value) {
    try {
      if (!this.PARAMS.isContrastModel) return;
      if (this.contrastRowData?.rowKey === value.rowKey) {
        this.contrastRowData = null;
      } else {
        const { rowData } = await this.worker?.sendMessageSync(this.GET_CONTRAST_ROW_DATA, { rowKey: value.rowKey });
        this.contrastRowData = Object.seal(rowData);
      }
    } catch (err) {
      console.error(err);
    }
  }

  changeColumnSortOrder(rowKey) {
    if (this.columnSortOrderData.rowKey === rowKey) {
      if (this.columnSortOrderData.order === 'ascending') {
        this.columnSortOrderData.order = 'descending';
      } else if (this.columnSortOrderData.order === 'descending') {
        this.columnSortOrderData.rowKey = null;
        this.columnSortOrderData.order = '';
      }
    } else {
      this.columnSortOrderData.rowKey = rowKey;
      this.columnSortOrderData.order = 'ascending';
    }

    this.getFilterTableData();
  }

  changeColSortOrder(colId, index) {
    if (this.rowSortOrderData.colId === colId) {
      if (this.rowSortOrderData.order === 'ascending') {
        this.rowSortOrderData.order = 'descending';
        this.rowSortOrderData.index = index;
      } else if (this.rowSortOrderData.order === 'descending') {
        this.rowSortOrderData.colId = null;
        this.rowSortOrderData.order = '';
        this.rowSortOrderData.index = null;
      }
    } else {
      this.rowSortOrderData.colId = colId;
      this.rowSortOrderData.order = 'ascending';
      this.rowSortOrderData.index = index;
    }

    this.contrastRowData = null;
    this.getFilterTableData();
  }

  keepFilterCss() {
    if (this.filterModelPropertiesMetadata.length === 0) return;
    // 保存列筛选的视觉状态
    this.filterModelPropertiesMetadata.forEach((T: any) => {
      const filtersArr: any = this.$tableColumn.filter((R: any) => R.filters);
      let target: any;
      filtersArr.forEach((R: any) => {
        if (R.filters && T.field === R.field) target = R;
      });

      if (target) {
        const indexTarget = T.filters.findIndex((IT: any) => IT.field === T.field);
        const updatedFilters = target.filters.map((filter) => {
          return {
            ...filter,
            checked: T.filters[indexTarget].values.includes(filter.value),
          };
        });

        const column = this.refXGrid?.getColumnByField(T.field);
        this.refXGrid?.setFilter(column, updatedFilters);
      }
    });
  }

  async handleFilterChange({ field, filters, values }) {
    // 减少不必要的刷新
    if (values.length === 0 && this.filterModelPropertiesMetadata.length === 0) return false;
    // 取需要的数据 没用数据较多
    this.filterModelProperties = {
      values: [...values],
      filters: filters.map((i: any) => {
        if ('column' in i) {
          delete i.column;
        }

        return i;
      }),
      field,
    };

    // 因未能使用 xTable.updateData() 更新数据，需要手动记录所有筛选状态
    if (this.filterModelPropertiesMetadata.length === 0) {
      this.filterModelPropertiesMetadata.push(cloneDeep(this.filterModelProperties));
    } else {
      let found = false;
      for (let i = 0; i < this.filterModelPropertiesMetadata.length; i++) {
        if (field === this.filterModelPropertiesMetadata[i].field) {
          this.filterModelPropertiesMetadata[i] = cloneDeep(this.filterModelProperties);
          found = true;
          break;
        }
      }

      if (!found && values.length !== 0) {
        this.filterModelPropertiesMetadata.push(cloneDeep(this.filterModelProperties));
      }

      // 针对重置筛选的时候
      let deleteIndex = -1;
      this.filterModelPropertiesMetadata.forEach((R, I) => {
        if (values.length === 0 && R.field === field) deleteIndex = I;
        const pushTarget = R.filters.find((RR) => RR.field === R.field);
        if (R.field !== field && R.filters.length > 0) this.filterModelProperties.filters.push(cloneDeep(pushTarget));
      });
      if (deleteIndex !== -1) this.filterModelPropertiesMetadata.splice(deleteIndex, 1);
    }

    await this.getFilterTableData();
  }

  $throttleTimer: any = null;
  handleScroll() {
    if (!this.$throttleTimer) {
      this.$throttleTimer = setTimeout(() => {
        this.getSliceTableData();
        if (this.kmIframeProps) {
          this.kmIframeProps.updateDiscussion = !this.kmIframeProps.updateDiscussion;
        }

        this.$throttleTimer = null;
      }, 200);
    }
  }

  currentRowKeys = '';
  currentColumnIds = '';

  getSliceTableData() {
    const { tableColumn } = this.refXGrid?.getTableColumn();
    const { tableData } = this.refXGrid?.getTableData();
    const rowKeys = tableData.map((item) => item.rowKey).join(',');
    const columnIds = tableColumn.map((item) => item.id).join(',');
    if (columnIds !== this.currentColumnIds || rowKeys !== this.currentRowKeys) {
      this.currentColumnIds = columnIds;
      this.currentRowKeys = rowKeys;
      this.worker.sendMessage(this.SLICE_TABLE_DATA, { rowKeys, columnIds });
    }
  }

  async getFilterTableData() {
    try {
      this.loading = true;
      this.currentRowKeys = '';
      this.currentColumnIds = '';
      const { tableColumn, tableData, patternComparison, childThreadChangeFilterGroup } = await this.worker?.sendMessageSync(
        this.FILTER_SORT_TABLE_DATA,
        {
          PARAMS: Object.fromEntries(
            Object.entries(this.PARAMS).filter(
              (entry) => typeof entry[1] !== 'function' && entry[0] !== 'tableDataDataSubSet' && entry[0] !== 'tableDataCategory'
            )
          ),
          rowSortOrderData: this.rowSortOrderData,
          columnSortOrderData: this.columnSortOrderData,
          // confidenceIntervalName: this.confidenceIntervalName,
          filterModelProperties: this.filterModelProperties,
          patternComparisonWorker: this.patternComparison,
          ...this.form,
        }
      );
      this.$tableColumn = Object.seal(tableColumn);
      this.patternComparison = patternComparison;
      if (childThreadChangeFilterGroup) this.filterGroup = childThreadChangeFilterGroup;

      await this.refXGrid?.reloadColumn(this.$tableColumn);
      await this.refXGrid?.reloadData(Object.seal(tableData));

      this.getSliceTableData();
      this.loading = false;
      this.keepFilterCss();
    } catch (e) {
      console.error(e);
    } finally {
      this.loading = false;
      this.keepFilterCss();
    }
  }

  async setTableData() {
    try {
      this.loading = true;
      this.currentRowKeys = '';
      this.currentColumnIds = '';
      this.$tableData = [];
      this.$tableColumn = [];

      await this.worker?.sendMessageSync(this.TRANSFORM_TABLE_DATA, {
        PARAMS: Object.fromEntries(Object.entries(this.PARAMS).filter((entry) => typeof entry[1] !== 'function')),
        modelMap: this.modelMap,
        evalDataSizeMetaList: this.evalDataSizeMetaList,
        showDataSubSetDescription: this.showDataSetDescription,
        selectedDataSet: this.selectedDataSet,
        // fixedColumnList: [{ a: 1, b: 2 }],
        filterGroup: this.filterGroup,
        modelType: this.modelType,
        checkMark: this.checkMark,
        patternComparisonWorker: this.patternComparison,
        statMetaList: this.modelEvalUnVersionedListStatMeta?.statMetaList || [],
      });
      await this.getFilterTableData();
      this.loading = false;
    } catch (e) {
      console.error(e);
    }
  }

  initWorker() {
    // 初始化worker
    this.worker = Object.seal(new ModelColumnTableWorker());
    let timer: null | number = null;
    this.worker.addListener(this.SLICE_TABLE_DATA, (data) => {
      timer && cancelIdleCallback(timer);
      timer = requestIdleCallback(() => {
        this.$tableData = data.tableData;
        this.is$tableDataDirty = true;
        timer = null;
      });
    });
  }

  @Inject({ default: () => (defaultViewName?: string, detail?: any) => [] }) getSiblingAMethods?: (
    defaultViewName?: string,
    detail?: any
  ) => [];
  async handleValueClick(fieldData, rowData, column) {
    try {
      if (this.PARAMS.handleValueClick) {
        this.PARAMS.handleValueClick(fieldData, rowData);
        return;
      }

      if (!this.PARAMS.isHandleValueClick) return;
      if (this.modelType === 'category') {
        const firstIndex = this.filterGroup.selectGroup.firstIndex === -1 ? fieldData?.groupIndex : this.filterGroup.selectGroup.firstIndex;

        // if ((column?.fixed === 'left' && firstIndex > -1) || (!column && firstIndex === -1)) {
        //   this.$emit('settableDataDataSubSet', rowData.model);
        // } else {
        const obj: any = {
          evalModelList: [rowData.model],
          radio: this.queryCriteria.radio,
          screen: true,
          refreshKey: Date.now(),
        };
        const form = cloneDeep(this.PARAMS.form);
        if (this.queryCriteria.radio === '1') {
          this.$set(obj, 'dataSubSetFilterConfig', form.dataSubSetFilterConfig);
          const instanceFilterConditionTree = form.dataSubSetFilterConfig.instanceFilterConditionTree;
          const leaves = instanceFilterConditionTree?.leaves[1];
          const res = this.queryCriteria.dataSubSetFilterViewMeta.categoryList.find((item) => item.id === fieldData.categoryId);

          if (leaves.type === 'EXPRESSION') {
            leaves.rightParam.data = [
              {
                version: {
                  id: res.metaVersionId,
                  name: this.metaVersionList.find((item) => item.id === res.metaVersionId)?.name,
                  currentTab: '',
                },
                llmEval: {
                  value: [],
                  node: [],
                },
                llmEvalCustom: {
                  value: [],
                  node: [],
                },
              },
            ];
            if (res.type === 'LLM-EVAL') {
              leaves.rightParam.data[0].llmEval.value = [res.id];
              leaves.rightParam.data[0].llmEval.node = [res];
              leaves.rightParam.data[0].version.currentTab = res.type;
            } else {
              leaves.rightParam.data[0].llmEvalCustom.value = [res.id];
              leaves.rightParam.data[0].llmEvalCustom.node = [res];
              leaves.rightParam.data[0].version.currentTab = res.type;
            }
          }
          instanceFilterConditionTree.leaves[1] = leaves;
          obj.dataSubSetFilterConfig.instanceFilterConditionTree = instanceFilterConditionTree;
        } else {
          // const filterGroupViewMetaList =
          const filterGroupConfigList = form.filterGroupConfigList[firstIndex];
          const filterGroup = this.queryCriteria.filterGroupViewMetaList[firstIndex].viewMeta;
          if (filterGroup.clusterType === 'instanceFilter') {
            const instanceFilterConditionTree = filterGroupConfigList.instanceFilterConditionTree;
            const leaves = instanceFilterConditionTree?.leaves[1];
            const res = filterGroup.categoryList.find((item) => item.id === fieldData.categoryId);
            if (leaves.type === 'EXPRESSION') {
              leaves.rightParam.data = [
                {
                  version: {
                    id: res.metaVersionId,
                    name: this.metaVersionList.find((item) => item.id === res.metaVersionId)?.name,
                    currentTab: '',
                  },
                  llmEval: {
                    value: [],
                    node: [],
                  },
                  llmEvalCustom: {
                    value: [],
                    node: [],
                  },
                },
              ];
              if (res.type === 'LLM-EVAL') {
                leaves.rightParam.data[0].llmEval.value = [res.id];
                leaves.rightParam.data[0].llmEval.node = [res];
                leaves.rightParam.data[0].version.currentTab = res.type;
              } else {
                leaves.rightParam.data[0].llmEvalCustom.value = [res.id];
                leaves.rightParam.data[0].llmEvalCustom.node = [res];
                leaves.rightParam.data[0].version.currentTab = res.type;
              }
            }
            instanceFilterConditionTree.leaves[1] = leaves;
            const data = [
              {
                ...filterGroupConfigList,
                instanceFilterConditionTree,
              },
            ];
            this.$set(obj, 'filterGroupConfigList', data);
          } else {
            this.$set(obj, 'filterGroupConfigList', filterGroupConfigList);

            const dataSubSetFilterConditionTree = filterGroupConfigList.dataSubSetFilterConditionTree;
            const res = filterGroup.categoryList.find((item) => item.id === fieldData.categoryId);
            const leaves = this.updateCategoryIdListAndReturn(dataSubSetFilterConditionTree?.leaves, res);
            dataSubSetFilterConditionTree.leaves = leaves;

            const data = [
              {
                ...filterGroupConfigList,
                dataSubSetFilterConditionTree,
              },
            ];
            this.$set(obj, 'filterGroupConfigList', data);
          }
        }

        this.getSiblingAMethods && this.getSiblingAMethods('', obj);
        // }
      } else {
        const firstIndex = this.filterGroup.selectGroup.firstIndex;

        if (firstIndex === -1) return;

        if (column?.fixed === 'left' && firstIndex > -1) return;

        const { valueMap, model } = rowData;
        const params = this.PARAMS.params;
        let statName: string = '';
        let runSpecSetId: any = null;
        let category: any = {};
        if (params.radio === '0' && params.filterGroupConfigList?.length) {
          statName = params.filterGroupConfigList[firstIndex]?.statName;
          runSpecSetId = params.filterGroupConfigList[firstIndex]?.runSpecSetId;
          if (params.filterGroupConfigList[firstIndex]?.instanceFilterConditionTree.leaves?.length >= 2) {
            const leaves = params.filterGroupConfigList[firstIndex]?.instanceFilterConditionTree.leaves[1];
            category.categoryId = leaves.rightParam.data;
            const res = await this.action$modelEvalCategoryGet({
              id: Number(category.categoryId),
              statusFilter: '',
            });
            category.categoryName = res.category.name;
            category.versionID = res.category.metaVersionId;
            category.categoryType = res.category.type;
          }
        }
        if (params.radio === '1') {
          statName = params.dataSubSetFilterConfig.statName;
          runSpecSetId = params.dataSubSetFilterConfig?.runSpecSetId || null;
          if (params.dataSubSetFilterConfig.instanceFilterConditionTree.leaves?.length >= 2) {
            const leaves = params.dataSubSetFilterConfig.instanceFilterConditionTree.leaves[1];
            category.categoryId = leaves.rightParam.data;
            const res = await this.action$modelEvalCategoryGet({
              id: Number(category.categoryId),
              statusFilter: '',
            });
            category.categoryName = res.category.name;
            category.versionID = res.category.metaVersionId;
            category.categoryType = res.category.type;
          }
        }
        const { modelLabel, evalDataSize } = valueMap;
        const evalDataSizeValue =
          (fieldData.diffEvalDataSize?.length &&
            fieldData.diffEvalDataSize.find((item) => item.subSetId === fieldData.dataSubSetId)?.evalDataSize) ||
          fieldData?.evalDataSize ||
          evalDataSize?.value;
        const query: any = {
          dataSubSetId: fieldData.dataSubSetId,
          modelFamily: model.family,
          modelName: fieldData?.master && fieldData?.masterModelName ? fieldData.masterModelName : model.name,
          modelLabel: modelLabel.value,
          modelId: model.modelId,
          label: model.label,
          master: fieldData?.master,
          evalDataSize: this.evalDataSize[evalDataSizeValue] || evalDataSizeValue,
          statName: statName,
          //关于评测项的参数
          category: JSON.stringify(category),
          runSpecSetId: fieldData.runSpecSetId ? +fieldData.runSpecSetId : +runSpecSetId,
        };

        window.open(
          this.$router.resolve({
            name: 'data-sub-set-evaluation-result-detail-new',
            query,
          }).href,
          '_blank',
          'noopener'
        );
      }
    } catch (e) {
      console.error(e);
    }
  }

  updateCategoryIdListAndReturn(tree, res) {
    const recursiveUpdate = (tree) => {
      for (const item of tree) {
        if (item.type === 'EXPRESSION' && item.leftParam.data === 'categoryIdList') {
          item.rightParam.data = [
            {
              version: {
                id: res.metaVersionId,
                name: this.metaVersionList.find((item) => item.id === res.metaVersionId)?.name,
                currentTab: '',
              },
              llmEval: {
                value: [],
                node: [],
              },
              llmEvalCustom: {
                value: [],
                node: [],
              },
            },
          ];
          if (res.type === 'LLM-EVAL') {
            item.rightParam.data[0].llmEval.value = [res.id];
            item.rightParam.data[0].llmEval.node = [res];
            item.rightParam.data[0].version.currentTab = res.type;
          } else {
            item.rightParam.data[0].llmEvalCustom.value = [res.id];
            item.rightParam.data[0].llmEvalCustom.node = [res];
            item.rightParam.data[0].version.currentTab = res.type;
          }
          return true; // 找到第一个后停止查找
        } else if (item.type === 'LOGIC' && item.leaves) {
          const found = recursiveUpdate(item.leaves);
          if (found) return true; // 如果在子节点中找到，停止查找
        }
      }
      return false; // 没有找到，继续查找
    };

    recursiveUpdate(tree);
    return tree; // 返回修改后的树结构
  }

  rowStyle(data) {
    if (this.contrastRowData?.rowKey === data.row.rowKey) {
      return { backgroundColor: '#0d46d92b' };
    }

    if (data.row.highlight) {
      return { backgroundColor: '#ffd200' };
    }
  }

  async handleExport() {
    try {
      this.$mtd.message.warning('正在导出csv');
      const data = {
        PARAMS: Object.fromEntries(
          Object.entries(this.PARAMS).filter(
            (entry) => typeof entry[1] !== 'function' && entry[0] !== 'tableDataCategory' && entry[0] !== 'tableDataDataSubSet'
          )
        ),
        rowSortOrderData: this.rowSortOrderData,
        columnSortOrderData: this.columnSortOrderData,
        // confidenceIntervalName: this.confidenceIntervalName,
        filterModelProperties: this.filterModelProperties,
        ...this.form,
      };
      const { tableData } = await this.worker?.sendMessageSync(this.EXPORT_TABLE_DATA, { showSecondValue: this.showSecondValue, data });
      const csvContent = `data:text/csv;charset=utf-8,\uFEFF${tableData}`;
      const link = document.createElement('a');
      link.href = encodeURI(csvContent);
      link.download = `表格${dayjs().format('MMDDHHmmss')}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (e) {
      console.error(e);
    }
  }
}
</script>

<style lang="scss">
.backColor-240-247-255 {
  background-color: rgb(240, 247, 255);
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.display-none {
  display: none;
}
.display-none-important {
  display: none !important;
}
.margin-right-threeInOne {
  margin-right: 40px !important;
  margin-bottom: 0 !important;
}
.merge-cell-identification {
  width: 10px;
  display: block;
  transform: translateX(-10px);
  // 行高固定
  height: 120px !important;
}
.eval-data-size-tooltip {
  max-width: unset;
  .content {
    font-size: 12px;
  }
  .title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
  }
}
</style>
<style scoped lang="scss">
.arrows {
  position: relative;
  left: -40px;
  transform: rotate(45deg);
  animation: moveX 1.5s linear infinite;
  user-select: none;
}

@keyframes moveX {
  0% {
    transform: translateX(0) rotate(45deg); /* 初始位置 */
  }
  50% {
    transform: translateX(30px) rotate(45deg); /* 向右移动60px */
  }
  100% {
    transform: translateX(0) rotate(45deg); /* 返回初始位置 */
  }
}
.patternComparison {
  line-height: 30px;
  &:hover {
    cursor: pointer;
    color: rgb(75, 156, 238);
    background: rgb(239, 246, 255);
  }
}
.dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #0a70f5;
  border-radius: 50%;
  margin-right: 4px;
}
::v-deep .vxe-cell {
  padding: 0 !important;
}
.three-in-one-table {
  // user-select: none;
}
.cleanDirtyBox {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid rgb(216 216 216);
  .cleanDirty {
    line-height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.vxe-table-container {
  min-height: 40px;
  width: 100%;
  //overflow: hidden;
  font-size: 12px;
  margin-bottom: 10px;

  .spanTD {
    font-size: 12px;
    position: relative;
  }

  .show {
    display: inline-block;
  }

  .showBlock {
    display: block;
  }

  .hid {
    display: none;
  }

  .discussion {
    border-bottom: 2px solid rgba(255, 209, 0, 0.6);
    transition: background-color 0.2s;
    &.discussion-active {
      background-color: rgba(255, 209, 0, 0.25) !important;
    }
  }
  .clickable-value {
    cursor: pointer;
    transition: color 0.2s;

    &:hover {
      color: #1c6cdc;
    }
  }

  .blackTag {
    font-size: 10px;
    flex-shrink: 0;
    padding: 0 2px;
    height: 16px;
    line-height: 12px;
    border-radius: 3px;
    border: 1px solid #000000;
    position: absolute;
    top: 5px;
    left: 5px;
  }

  .mtd-table-sortable {
    cursor: pointer;
    flex-shrink: 0;
    margin-left: 2px;
    margin-right: -5px;
    transform: translateY(-2px);

    &.descending .mtd-table-sortable-descending {
      border-top-color: #1c6cdc;
    }

    &.ascending .mtd-table-sortable-ascending {
      border-bottom-color: #1c6cdc;
    }
  }

  .model-name-cell-col {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .mtdicon-question-circle-o {
      vertical-align: bottom;
      margin-left: 3px;
      color: #1c6cdc;
      font-size: 15px;
    }
  }

  .table-tips {
    margin-left: auto;
    color: #0a70f5;
    line-height: 24px;
    font-size: 12px;
    // user-select: none;
  }

  .modelName {
    min-width: 278px;
    text-align: left;
    font-weight: bold;
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 60px;
    font-size: 12px;
    padding: 0 10px;

    .modelNameTxt {
      // user-select: none;
      flex: 1;
      line-height: 16px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 5;
    }

    .model-btn {
      height: 12px;
      line-height: 12px;
      padding: 0 2px;
      margin-left: 4px;
      vertical-align: text-bottom;

      ::v-deep .mtd-tag-content {
        font-size: 10px;
      }
    }
  }

  ::v-deep {
    .vxe-header--column {
      line-height: 16px;
      position: relative;
    }

    .vxe-thead {
      height: 120px;
      font-size: 12px !important;
    }

    .vxe-body--row {
      transition: background 0.2s;

      .vxe-cell--label {
        font-size: 12px !important;
      }
    }

    .vxe-body--column {
      // 会引起滚动高度异常
      //height: 30px !important;
    }

    .c--ellipsis {
      white-space: pre-wrap !important;
      overflow: scroll !important;
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .vxe-header--column.col--ellipsis > .vxe-cell .vxe-cell--title {
      overflow: visible;
      text-overflow: clip;
      white-space: normal;
    }

    .vxe-table--render-default .vxe-header--column.col--ellipsis > .vxe-cell {
      max-height: 58px !important;
    }
  }
}

.badcase {
  margin-top: 5px;
}
</style>
