<script lang="tsx">
import { defineComponent } from 'vue';

const EditableLabel = defineComponent({
  setup(_, { slots, emit }) {
    return {
      slots,
      emit,
    };
  },
  render(h, hack) {
    return (
      <div onClick={(e) => this.emit('click', e)} onDragstart={(e) => this.emit('onDragstart', e)}>
        DropTarget
        {this.slots.default && this.slots.default()}
      </div>
    );
  },
}) as any;

export default EditableLabel;
</script>
