<template>
  <div class="eval-datasets">
    <mtd-button class="selectWarpBtn" @click="handleShow">{{ `${isEdit ? '选择' : '查看'}评测子集` }}</mtd-button>

    <mtd-modal v-model="modalVisible" width="60%" @close="handleClose">
      <template slot="title">
        <div class="modal-title">
          <span>评测子集选择</span>
          <mtd-radio-group v-model="selectType" @change="handleTypeChange" size="small">
            <mtd-radio-button value="template">从模型选择</mtd-radio-button>
            <mtd-radio-button value="custom">自定义选择</mtd-radio-button>
          </mtd-radio-group>
        </div>
      </template>

      <!-- 弹窗内容区域 -->
      <div class="modal-content">
        <!-- 从模型选择 -->
        <NewModelSelect
          v-if="modalVisible"
          v-show="selectType === 'template'"
          v-model="model.a"
          title="模型选择"
          :type="1"
          :disabled="!isEdit"
          :isViewTable="true"
          :loadOriginData="true"
          :hidEles="['bastCheckPoint']"
          :instertToBody="false"
          ref="modelSelect"
          @change="handleModelChange"
        />

        <!-- 自定义选择 -->
        <DataSubSetSelect
          v-if="modalVisible"
          v-show="selectType === 'custom'"
          v-model="model.b"
          :disabled="!isEdit"
          ref="datasetSelect"
          @change="handleSubChange"
        />
      </div>
      <div slot="footer" class="demo-modal-footer">
        <mtd-button class="cancel-btn" @click="handleClose">取消</mtd-button>
        <mtd-button type="primary" @click="handleConfirm">确定</mtd-button>
      </div>
    </mtd-modal>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Spectra, Vue, Ref } from '@/decorators';
import NewModelSelect from '@/components/NewModelSelect.vue';
import DataSubSetSelect from '@/components/dataset/DataSubSetSelect.vue';

@Component({
  components: {
    DataSubSetSelect,
    NewModelSelect,
  },
})
@Spectra
export default class EvalDatasets extends Vue {
  @Prop({ default: () => ({}) })
  model!: any;
  @Prop()
  isEdit!: boolean;
  @Prop()
  item: any;

  @Ref('modelSelect') modelSelect: any;
  @Ref('datasetSelect') datasetSelect: any;

  selectType = 'template';
  modalVisible = false;

  created() {
    // 初始化 model
    if (!this.model.a) {
      this.$set(this.model, 'a', []);
    }
    if (!this.model.b) {
      this.$set(this.model, 'b', []);
    }
  }

  handleShow() {
    this.modalVisible = true;
    this.handleTypeChange(this.selectType);
  }

  comps = { a: false, b: false };
  handleTypeChange(value: string) {
    // 切换选择类型时打开对应组件的弹窗
    this.$nextTick(() => {
      if (value === 'template' && !this.comps.a) {
        this.comps.a = true;
        this.modelSelect?.handleSelectModel?.();
      } else if (value === 'custom' && !this.comps.b) {
        this.comps.b = true;
        this.datasetSelect?.handleShow?.();
      }
    });
  }

  handleModelChange(val) {
    this.model.a = val || [];
  }

  handleSubChange(val) {
    this.model.b = val || [];
  }

  handleClose() {
    this.comps.a = false;
    this.comps.b = false;
    this.modalVisible = false;
    this.selectType = 'template';
  }

  handleConfirm() {
    this.modelSelect?.$refs.modelSelect.handleSubmit();
    this.datasetSelect?.handleSureSelect();
    this.$nextTick(() => {
      this.handleClose();
    });
  }
}
</script>

<style lang="scss" scoped>
.eval-datasets {
  .selectWarpBtn {
    min-width: 100px;
  }
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 16px;

  span {
    font-size: 16px;
    font-weight: 500;
  }

  .mtd-radio-group {
    margin-left: 16px;
  }
}

.modal-content {
  min-height: 400px;
  > .ModelMultipleRunUnitSelect {
    height: auto;
    line-height: unset;
    ::v-deep > .selectTitle {
      display: none;
    }
    ::v-deep > .selectWarpBtn {
      display: none;
    }
    ::v-deep > div {
      > .mtd-modal-mask {
        display: none;
      }
      > .mtd-modal-wrapper {
        position: unset;
        > .mtd-modal {
          width: 100% !important;
          border: 0;
          box-shadow: none;
          > .mtd-modal-header {
            display: none;
          }
          > .mtd-modal-content-wrapper {
            padding: 16px 0;
          }
          > .mtd-modal-close {
            display: none;
          }
          > .mtd-modal-footer {
            display: none;
          }
        }
      }
    }
  }
  > .model-select-warp {
    ::v-deep > .select {
      display: none;
    }
    ::v-deep > div {
      > .mtd-modal-mask {
        display: none;
      }
      > .mtd-modal-wrapper {
        position: unset;
        > .mtd-modal {
          width: 100% !important;
          border: 0;
          box-shadow: none;
          > .mtd-modal-header {
            display: none;
          }
          > .mtd-modal-content-wrapper {
            padding: 16px 0;
            > .mtd-modal-content {
              .model-list {
                > .header {
                  > div {
                    line-height: 1.5714285714;
                  }
                }
              }
            }
          }
          > .mtd-modal-close {
            display: none;
          }
          > .mtd-modal-footer {
            display: none;
          }
        }
      }
    }
  }
}
</style>
