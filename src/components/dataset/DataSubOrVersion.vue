<template>
  <div class="data-subset-select-container">
    <div class="show-btn-length">
      <div class="show-length">已选择{{ vLength ?? 0 }}项</div>
      <mtd-button @click="handleShow">{{ `${isEdit ? '选择' : '查看'}评测数据源` }}</mtd-button>
    </div>
    <mtd-modal v-model="visible" title="" width="70%" @close="modalClose">
      <template slot="title">
        <span class="font-medium text-base">{{ `评测数据源${isEdit ? '选择' : '查看'}` }} / </span>
        <mtd-radio-group v-model="type" class="ml-4 inline-block" size="small" @input="changeType" :disabled="false">
          <mtd-radio-button :value="1">评测子集</mtd-radio-button>
          <mtd-radio-button :value="2">评测数据集版本</mtd-radio-button>
          <mtd-radio-button :value="3">模型</mtd-radio-button>
        </mtd-radio-group>
      </template>
      <div :class="['modal-switch-container', , isEdit ? '' : 'modal-switch-container-disabled']" v-if="visible">
        <DataSubSetSelect
          ref="subset"
          v-show="type === 1"
          v-model="context.nodeParam.datasetIds"
          :disabled="!isEdit"
          @confirm="subSetChange"
        />
        <DatasetVersionSelect
          ref="version"
          v-show="type === 2"
          :showKeys="[' ']"
          :onlyOne="true"
          :versionId="versionId"
          :disabled="!isEdit"
          :searchParam="searchParam"
          @change="versionChange"
        />

        <div :class="['modal-content', isEdit ? '' : 'modal-content-disabled']">
          <NewModelSelect
            v-show="type === 3"
            v-model="context.nodeParam.modelList"
            title="模型选择"
            :type="1"
            :hidEles="['bastCheckPoint']"
            :instertToBody="false"
            ref="modelSelect"
            @confirm="handleModelChange"
          />
        </div>
      </div>
    </mtd-modal>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Weaver, Ref, CatchError } from '@/decorators';
import DataSubSetSelect from '@/components/dataset/DataSubSetSelect.vue';
import DatasetVersionSelect from '@/views/DataManage/PretrainDataset/components/DatasetVersionSelect.vue';
import datasetManage from '@/store/modules/datasetManage';
import NewModelSelect from '@/components/NewModelSelect.vue';

@Component({ components: { DataSubSetSelect, DatasetVersionSelect, NewModelSelect } })
@Spectra
export default class DataSubOrVersion extends Weaver(datasetManage) {
  @Prop()
  model!: any;
  @Prop()
  context: any;
  @Prop()
  globalContext: any;
  @Prop()
  isEdit!: boolean;
  @Ref('subset') subsetRef;
  @Ref('version') versionRef;
  @Ref('modelSelect') modelRef;

  visible = false;
  type = 1;
  versionId = -1;
  searchParam = { stage: 0 };

  get vLength() {
    const nodeParam = this.context?.nodeParam;
    const { model } = this;
    return (model?.length || nodeParam?.datasetVersionIds?.length || 0) + (this.context.nodeParam?.modelList?.length || 0);
  }

  setVersionId(v?: number) {
    this.$set(this, 'versionId', v ?? null);
    this.$set(this.context.nodeParam, 'datasetVersionIds', v ? [v] : []);
  }

  versionChange(v) {
    if (this.modelRef?.$refs?.modelSelect?.show === true) {
      this.modelRef.$refs.modelSelect.handleSubmit();
    }
    this.$set(this.context.nodeParam, 'datasetIds', []);
    this.setVersionId(v);
    this.modalClose();
  }

  subSetChange(v) {
    if (this.modelRef?.$refs?.modelSelect?.show === true) {
      this.modelRef.$refs.modelSelect.handleSubmit();
    }
    this.setVersionId();
    this.$set(this.context.nodeParam, 'datasetIds', v);
    this.modalClose();
  }

  handleModelChange(v) {
    this.$set(this.context.nodeParam, 'modelList', v);
    this.subsetRef?.visible === true && this.subsetRef?.handleSureSelect();
    this.versionRef?.visible === true && this.versionRef?.handleSureSelect();
  }

  setType() {
    const nodeParam = this.context?.nodeParam;
    this.type = nodeParam && nodeParam?.datasetVersionIds?.length ? 2 : 1;
  }

  changeType() {
    this.$nextTick(() => {
      if (this.type === 1) {
        this.versionRef?.modalClose();
        this.subsetRef?.handleShow();
      } else if (this.type === 2) {
        this.subsetRef?.modalClose();
        this.versionRef?.handleShow();
      } else if (this.type === 3) {
        if (this.modelRef?.$refs?.modelSelect?.show !== true) {
          this.modelRef?.handleSelectModel?.();
        }
      }
    });
  }

  handleShow() {
    const vids = this.context?.nodeParam?.datasetVersionIds;
    this.$set(this, 'versionId', vids?.length ? vids[0] : null);
    this.setType();
    this.visible = true;
    this.changeType();
  }

  modalClose() {
    this.versionRef?.modalClose();
    this.subsetRef?.modalClose();
    this.visible = false;
  }

  @CatchError
  async getPretrainStages() {
    const res = await this.action$dataHubCategoryCategories({
      dimId: '2',
      parentId: '',
    });
    res &&
      res?.findIndex((item) => {
        const flag = item.name === 'eval';
        flag && (this.searchParam.stage = item.id);
        return flag;
      });
  }

  created() {
    this.getPretrainStages();
    this.context.nodeParam?.modelList || this.$set(this.context.nodeParam, 'modelList', []);
  }
  mounted() {
    this.setType();
  }
}
</script>
<style lang="scss" scoped>
.data-subset-select-container {
  width: 100%;
  .show-btn-length {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.modal-switch-container {
  height: fit-content;
  ::v-deep > div {
    height: fit-content;
    .selectTitle {
      display: none;
    }
    .selectWarpBtn {
      display: none;
    }
    .mtd-modal-mask {
      display: none;
    }
    .mtd-modal-wrapper {
      position: unset;
      .mtd-modal {
        width: 100% !important;
        box-shadow: none;
        .mtd-modal-close {
          display: none;
        }
        .mtd-modal-header {
          display: none;
        }
        .mtd-modal-content-wrapper {
          padding: 0;
        }
      }
    }
  }
  ::v-deep .dataset-version-select-container {
    display: block;
    .selectTitle {
      display: none;
    }
  }
  &-disabled {
    ::v-deep .dataset-version-select-container {
      cursor: not-allowed !important;
      .selectHeader {
        pointer-events: none;
        opacity: 0.5;
      }
    }
    .ModelMultipleRunUnitSelect {
      cursor: not-allowed !important;
      ::v-deep .selectHeader {
        pointer-events: none;
        opacity: 0.5;
      }
    }
  }
  > .modal-content {
    > .model-select-warp {
      ::v-deep > .select {
        display: none;
      }
      ::v-deep div {
        > .mtd-modal-wrapper {
          > .mtd-modal {
            > .mtd-modal-content-wrapper {
              max-height: calc(80vh - 100px);
              padding: 16px 0;
              > .mtd-modal-content {
                .model-list {
                  > .header {
                    > div {
                      line-height: 1.5714285714;
                    }
                  }
                }
              }
            }
            > .mtd-modal-footer {
              padding-bottom: 0;
              .cancel-btn {
                display: none;
              }
            }
          }
        }
      }
    }
    &-disabled {
      cursor: not-allowed !important;
      > .model-select-warp {
        pointer-events: none;
      }
      ::v-deep .filter-container {
        opacity: 0.5;
      }
      ::v-deep .mtd-modal-footer {
        display: none;
      }
    }
  }
}
</style>
