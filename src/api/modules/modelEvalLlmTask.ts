import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1347245, catid=227021, projectId=35802, created=2023-12-22 16:42:24, last-modified=2023-12-22 17:22:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmTask/getOriginDataDownloadUrl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1347245)
 */
export function postModelEvalLlmTaskGetOriginDataDownloadUrl(
  params: model.modelEvalLlmTask.IPostModelEvalLlmTaskGetOriginDataDownloadUrlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmTask.IPostModelEvalLlmTaskGetOriginDataDownloadUrlResponse>(
    '/webApi/modelEvalLlmTask/getOriginDataDownloadUrl',
    {
      data: pick(params, ['id']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1296811, catid=227021, projectId=35802, created=2023-10-13 19:27:55, last-modified=2024-09-20 10:08:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmTask/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296811)
 */
export function postModelEvalLlmTaskList(
  params: model.modelEvalLlmTask.IPostModelEvalLlmTaskListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmTask.IPostModelEvalLlmTaskListResponse>('/webApi/modelEvalLlmTask/list', {
    data: pick(params, ['modelList', 'ownerFilter', 'processId', 'runSpecName', 'idListFilter', 'limit', 'offset']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296815, catid=227021, projectId=35802, created=2023-10-13 19:27:55, last-modified=2023-11-21 14:38:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmTask/listLog)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296815)
 */
export function postModelEvalLlmTaskListLog(
  params: model.modelEvalLlmTask.IPostModelEvalLlmTaskListLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmTask.IPostModelEvalLlmTaskListLogResponse>('/webApi/modelEvalLlmTask/listLog', {
    data: pick(params, ['id', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296846, catid=227021, projectId=35802, created=2023-10-13 19:27:57, last-modified=2023-12-22 16:26:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmTask/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296846)
 */
export function postModelEvalLlmTaskGet(
  params: model.modelEvalLlmTask.IPostModelEvalLlmTaskGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmTask.IPostModelEvalLlmTaskGetResponse>('/webApi/modelEvalLlmTask/get', {
    data: pick(params, ['id', 'processId', 'mlpJobId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296847, catid=227021, projectId=35802, created=2023-10-13 19:27:58, last-modified=2023-11-21 14:38:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLlmTask/diagnose)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296847)
 */
export function postModelEvalLlmTaskDiagnose(
  params: model.modelEvalLlmTask.IPostModelEvalLlmTaskDiagnoseParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLlmTask.IPostModelEvalLlmTaskDiagnoseResponse>('/webApi/modelEvalLlmTask/diagnose', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
