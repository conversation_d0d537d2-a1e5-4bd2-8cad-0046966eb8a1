import * as model from '@/model';
import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import http from '../http';

/**
 * @desc id=1430207, catid=227025, projectId=35802, created=2024-07-02 15:07:48, last-modified=2024-07-19 08:41:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalSummary/setMasterForModel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1430207)
 */
export function postModelEvalSummarySetMasterForModel(
  params: model.modelEvalSummary.IPostModelEvalSummarySetMasterForModelParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalSummary.IPostModelEvalSummarySetMasterForModelResponse>('/webApi/modelEvalSummary/setMasterForModel', {
    data: pick(params, ['modelFamily', 'modelName', 'statNameList', 'evalDataSizeList', 'dataSubSetIdList', 'modelId', 'runSpecSetName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1433734, catid=227025, projectId=35802, created=2024-07-09 14:34:11, last-modified=2024-07-09 15:39:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalSummary/modelDataSubSetTableNonnumeric)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433734)
 */
export function postModelEvalSummaryModelDataSubSetTableNonnumeric(
  params: model.modelEvalSummary.IPostModelEvalSummaryModelDataSubSetTableNonnumericParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalSummary.IPostModelEvalSummaryModelDataSubSetTableNonnumericResponse>(
    '/webApi/modelEvalSummary/modelDataSubSetTableNonnumeric',
    {
      data: pick(params, ['modelList', 'statName', 'evalDataSize', 'dataSubSetIdList', 'jsonKey']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1296875, catid=227025, projectId=35802, created=2023-10-13 19:27:59, last-modified=2024-07-01 14:51:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalSummary/modelDataSubSetTable)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296875)
 */
export function postModelEvalSummaryModelDataSubSetTable(
  params: model.modelEvalSummary.IPostModelEvalSummaryModelDataSubSetTableParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalSummary.IPostModelEvalSummaryModelDataSubSetTableResponse>(
    '/webApi/modelEvalSummary/modelDataSubSetTable',
    {
      data: pick(params, [
        'metaVersionId',
        'modelList',
        'statName',
        'parentCategoryIdList',
        'parentCategoryPathList',
        'categoryTagList',
        'subSetTags',
        'evalDataSize',
        'asyncExcelDownload',
        'dataSetStatus',
        'dataSetId',
        'dataSubSetId',
        'dataSubSetIdList',
        'dataSetName',
        'dataSubSetName',
        'queryUuid',
        'heartbeatTtl',
        'withConfidenceInterval',
        'dataSetOpennessStatus',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1296883, catid=227025, projectId=35802, created=2023-10-13 19:27:59, last-modified=2023-12-19 14:56:32
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalSummary/modelCategoryTable)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296883)
 */
export function postModelEvalSummaryModelCategoryTable(
  params: model.modelEvalSummary.IPostModelEvalSummaryModelCategoryTableParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalSummary.IPostModelEvalSummaryModelCategoryTableResponse>('/webApi/modelEvalSummary/modelCategoryTable', {
    data: pick(params, [
      'metaVersionId',
      'modelList',
      'statName',
      'parentCategoryId',
      'parentCategoryPath',
      'categoryTag',
      'subSetTags',
      'evalDataSize',
      'asyncExcelDownload',
      'queryUuid',
      'heartbeatTtl',
      'withConfidenceInterval',
      'dataSetOpennessStatus',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296892, catid=227025, projectId=35802, created=2023-10-13 19:27:59, last-modified=2023-11-21 14:38:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalSummary/modelDiffTable)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296892)
 */
export function postModelEvalSummaryModelDiffTable(
  params: model.modelEvalSummary.IPostModelEvalSummaryModelDiffTableParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalSummary.IPostModelEvalSummaryModelDiffTableResponse>('/webApi/modelEvalSummary/modelDiffTable', {
    data: pick(params, [
      'metaVersionId',
      'evalModel',
      'baseModel',
      'baseLineModel',
      'subSetTags',
      'statName',
      'evalDataSize',
      'asyncExcelDownload',
      'queryUuid',
      'queryHeartbeatTtl',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1311180, catid=227025, projectId=35802, created=2023-10-26 10:59:00, last-modified=2023-12-19 14:58:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalSummary/modelDiffTableMultiple)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1311180)
 */
export function postModelEvalSummaryModelDiffTableMultiple(
  params: model.modelEvalSummary.IPostModelEvalSummaryModelDiffTableMultipleParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalSummary.IPostModelEvalSummaryModelDiffTableMultipleResponse>(
    '/webApi/modelEvalSummary/modelDiffTableMultiple',
    {
      data: pick(params, [
        'metaVersionId',
        'evalModelList',
        'baseModel',
        'baseLineModel',
        'subSetTags',
        'statName',
        'evalDataSize',
        'asyncExcelDownload',
        'queryUuid',
        'heartbeatTtl',
        'dataSetOpennessStatus',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
