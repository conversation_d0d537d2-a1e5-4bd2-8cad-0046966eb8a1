import * as model from '@/model';
import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import http from '../http';

/**
 * @desc id=1326452, catid=231401, projectId=35802, created=2023-11-16 11:00:31, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/notice/simpleList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1326452)
 */
export function postNoticeSimpleList(params: model.notice.IPostNoticeSimpleListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.notice.IPostNoticeSimpleListResponse>('/webApi/notice/simpleList', {
    data: pick(params, ['keyword', 'statusFilter', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1326693, catid=231401, projectId=35802, created=2023-11-16 16:11:39, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/notice/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1326693)
 */
export function postNoticeDelete(params: model.notice.IPostNoticeDeleteParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.notice.IPostNoticeDeleteResponse>('/webApi/notice/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1326483, catid=231401, projectId=35802, created=2023-11-16 11:13:14, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/notice/platformNotice)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1326483)
 */
export function postNoticePlatformNotice(params: model.notice.IPostNoticePlatformNoticeParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.notice.IPostNoticePlatformNoticeResponse>('/webApi/notice/platformNotice', {
    data: pick(params, ['statusFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1326488, catid=231401, projectId=35802, created=2023-11-16 11:23:53, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/notice/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1326488)
 */
export function postNoticeUpsert(params: model.notice.IPostNoticeUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.notice.IPostNoticeUpsertResponse>('/webApi/notice/upsert', {
    data: pick(params, ['noticeContent', 'validTs', 'invalidTs', 'id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1326503, catid=231401, projectId=35802, created=2023-11-16 11:32:25, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/notice/updateNoticeStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1326503)
 */
export function postNoticeUpdateNoticeStatus(
  params: model.notice.IPostNoticeUpdateNoticeStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.notice.IPostNoticeUpdateNoticeStatusResponse>('/webApi/notice/updateNoticeStatus', {
    data: pick(params, ['id', 'statusFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1326492, catid=231401, projectId=35802, created=2023-11-16 11:28:24, last-modified=2023-11-21 14:37:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/notice/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1326492)
 */
export function postNoticeGet(params: model.notice.IPostNoticeGetParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.notice.IPostNoticeGetResponse>('/webApi/notice/get', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
