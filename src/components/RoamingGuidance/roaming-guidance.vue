<script setup lang="ts">
import { arrow, computePosition, flip, offset, shift } from '@floating-ui/vue';
import { Component, computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router/composables';

interface Step {
  target: string;
  title: string;
  content: string | Component;
  placement: 'top' | 'right' | 'bottom' | 'left';
  scrollDelay?: number;
  padding: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  notice?: string | Component;
  noticeClass?: string;
}

const props = defineProps<{
  steps: Step[];
  active: boolean;
}>();

const emit = defineEmits<{
  (e: 'finish'): void;
  (e: 'step-change', index: number): void;
}>();
const route = useRoute();
const currentStep = ref(0);
const tooltipRef = ref<HTMLElement | null>(null);
const arrowRef = ref<HTMLElement | null>(null);
const tooltipStyle = ref({
  position: 'absolute' as const,
  top: '0',
  left: '0',
  width: 'max-content',
  zIndex: '9999',
});
const arrowStyle = ref({
  position: 'absolute' as const,
  width: '8px',
  height: '8px',
  background: 'inherit',
  visibility: 'hidden' as 'hidden' | 'visible',
  left: '',
  top: '',
  right: '',
  bottom: '',
  transform: '',
});

// 高亮区域位置和尺寸
const highlightPosition = ref({
  top: '0px',
  left: '0px',
  width: '0px',
  height: '0px',
});

const isFirstRender = ref(true);

const updatePosition = async (scrollDelay?: number) => {
  if (!props.active || !props.steps.length) return;

  const step = props.steps[currentStep.value];
  const targetElement = document.querySelector(step.target) as HTMLElement;
  if (!targetElement || !tooltipRef.value) return;

  if (scrollDelay) {
    const tmp = currentStep.value;
    await targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    });
    await new Promise((resolve) => setTimeout(resolve, scrollDelay));
    if (tmp !== currentStep.value) return;
  }

  // 计算提示框位置
  const { x, y, placement, middlewareData } = await computePosition(targetElement, tooltipRef.value, {
    placement: step.placement,
    middleware: [offset(18), flip(), shift({ padding: 5 }), arrow({ element: arrowRef.value as HTMLElement })],
  });

  // 更新提示框位置
  tooltipStyle.value = {
    ...tooltipStyle.value,
    left: `${x}px`,
    top: `${y}px`,
  };

  // 更新箭头位置
  if (arrowRef.value && middlewareData.arrow) {
    const { x: arrowX, y: arrowY } = middlewareData.arrow;

    const staticSide = {
      top: 'bottom',
      right: 'left',
      bottom: 'top',
      left: 'right',
    }[placement.split('-')[0]];

    arrowStyle.value = {
      ...arrowStyle.value,
      left: arrowX != null ? `${arrowX}px` : '',
      top: arrowY != null ? `${arrowY}px` : '',
      right: '',
      bottom: '',
      [staticSide as string]: '-4px',
      transform: 'rotate(45deg)',
      visibility: 'visible' as const,
    };
  }

  // 高亮当前步骤元素
  highlightElement(targetElement);
};

// 添加遮罩和高亮效果
const overlayRef = ref<HTMLElement | null>(null);
const highlightElement = (element: HTMLElement) => {
  const rect = element.getBoundingClientRect();
  const scrollTop = window.scrollY || document.documentElement.scrollTop;
  const scrollLeft = window.scrollX || document.documentElement.scrollLeft;

  // 添加间距（像素）
  const padding = props.steps[currentStep.value].padding ?? {
    top: 10,
    bottom: 10,
    left: 10,
    right: 10,
  };

  // 更新高亮区域位置和尺寸，四周添加不同间距
  highlightPosition.value = {
    top: `${rect.top + scrollTop - padding.top}px`,
    left: `${rect.left + scrollLeft - padding.left}px`,
    width: `${rect.width + padding.left + padding.right}px`,
    height: `${rect.height + padding.top + padding.bottom}px`,
  };
};

// 下一步
const nextStep = () => {
  if (currentStep.value < props.steps.length - 1) {
    isFirstRender.value = false;
    currentStep.value++;
    emit('step-change', currentStep.value);
    const step = props.steps[currentStep.value];
    nextTick(() => updatePosition(step.scrollDelay));
  } else {
    finish();
  }
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    const step = props.steps[currentStep.value];
    isFirstRender.value = false;
    currentStep.value--;
    emit('step-change', currentStep.value);
    nextTick(() => updatePosition(step.scrollDelay));
  }
};

// 完成引导
const finish = () => {
  emit('finish');
};

// 监听窗口大小变化，更新位置
let resizeObserver: ResizeObserver | null = null;
onMounted(() => {
  resizeObserver = new ResizeObserver(() => {
    updatePosition();
  });

  window.addEventListener('scroll', () => updatePosition());
  window.addEventListener('resize', () => updatePosition());
});

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  window.removeEventListener('scroll', () => updatePosition());
  window.removeEventListener('resize', () => updatePosition());
});

// 添加检测移动设备的函数
const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// 修改监听 active 变化的 watch
watch(
  () => props.active,
  (newActive) => {
    if (newActive) {
      console.log('route', route);
      console.log('route.query', route.query);
      console.log('roamingGuidanceShare:', route.query.roamingGuidanceShare);

      // 检查路由查询参数，如果 roamingGuidanceShare 为 yes，则不启动引导
      if (route.query.roamingGuidanceShare === 'yes') {
        console.log('不启动引导 - roamingGuidanceShare 为 yes');
        return;
      }

      if (isMobile()) {
        // 在移动端直接结束引导
        finish();
        return;
      }
      currentStep.value = 0;
      isFirstRender.value = true;
      nextTick(() => updatePosition());
    }
  }
);

// 监听步骤变化
watch(
  () => currentStep.value,
  () => {
    emit('step-change', currentStep.value);
  }
);

// 添加一个计算属性来检查是否应该显示引导
const shouldShowGuidance = computed(() => {
  return props.active && !isMobile() && route.query.roamingGuidanceShare !== 'yes';
});
</script>

<template>
  <div v-if="shouldShowGuidance" class="fixed inset-0 pointer-events-none z-[9999]">
    <!-- 半透明遮罩层 - 使用多个div实现遮罩，避开高亮区域 -->
    <div ref="overlayRef" class="fixed inset-0 z-40 pointer-events-auto">
      <!-- 上方遮罩 -->
      <div
        class="absolute bg-black bg-opacity-50"
        :class="{ 'transition-all duration-300 ease-in-out': !isFirstRender }"
        :style="{
          left: '0',
          top: '0',
          width: '100%',
          height: highlightPosition.top,
        }"
      ></div>

      <!-- 左侧遮罩 -->
      <div
        class="absolute bg-black bg-opacity-50"
        :class="{ 'transition-all duration-300 ease-in-out': !isFirstRender }"
        :style="{
          left: '0',
          top: highlightPosition.top,
          width: highlightPosition.left,
          height: highlightPosition.height,
        }"
      ></div>

      <!-- 右侧遮罩 -->
      <div
        class="absolute bg-black bg-opacity-50"
        :class="{ 'transition-all duration-300 ease-in-out': !isFirstRender }"
        :style="{
          left: `calc(${highlightPosition.left} + ${highlightPosition.width})`,
          top: highlightPosition.top,
          width: `calc(100% - ${highlightPosition.left} - ${highlightPosition.width})`,
          height: highlightPosition.height,
        }"
      ></div>

      <!-- 下方遮罩 -->
      <div
        class="absolute bg-black bg-opacity-50"
        :class="{ 'transition-all duration-300 ease-in-out': !isFirstRender }"
        :style="{
          left: '0',
          top: `calc(${highlightPosition.top} + ${highlightPosition.height})`,
          width: '100%',
          height: `calc(100% - ${highlightPosition.top} - ${highlightPosition.height})`,
        }"
      ></div>

      <!-- 高亮区域边框 -->
      <div
        class="absolute border-2 border-white pointer-events-none rounded-lg"
        :class="{ 'transition-all duration-300 ease-in-out': !isFirstRender }"
        :style="{
          top: highlightPosition.top,
          left: highlightPosition.left,
          width: highlightPosition.width,
          height: highlightPosition.height,
          zIndex: 41,
        }"
      ></div>
    </div>

    <!-- 提示框 -->
    <div v-if="steps.length > 0" ref="tooltipRef" :style="tooltipStyle" class="bg-white rounded-lg shadow-lg p-4 pointer-events-auto">
      <div ref="arrowRef" :style="arrowStyle"></div>

      <div class="flex justify-between items-center mb-2">
        <x-title class="font-bold text-lg text-gray-800">{{ steps[currentStep].title }}</x-title>
        <!-- <mtd-icon-button @click="finish" type="secondary" icon="mtdicon mtdicon-close" /> -->
      </div>

      <!-- 根据内容类型渲染不同的内容 -->
      <div class="text-gray-600 mb-4">
        <component v-if="typeof steps[currentStep].content !== 'string'" :is="steps[currentStep].content" />
        <p v-else class="whitespace-pre-line">{{ steps[currentStep].content }}</p>
      </div>

      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500">{{ currentStep + 1 }} / {{ steps.length }}</div>
        <div class="flex space-x-2">
          <mtd-button v-if="currentStep > 0" @click="prevStep"> 上一步 </mtd-button>
          <mtd-button type="primary" @click="nextStep"> {{ currentStep < steps.length - 1 ? '下一步' : '完成' }} </mtd-button>
        </div>
      </div>
      <!--  尾部提示区域 -->
      <div class="flex justify-between items-center mt-1">
        <component v-if="typeof steps[currentStep].notice !== 'string'" :is="steps[currentStep].notice" />
        <p v-else :class="steps[currentStep].noticeClass">{{ steps[currentStep].notice }}</p>
      </div>
    </div>
  </div>
</template>
