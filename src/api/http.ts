import { factory } from '@snfe/flow-http';
import { Confirm, Notification } from '@ss/mtd-vue';
import axios from 'axios';

const swimLane = window.location.host.split('-sl-');
const headers = { 'X-Requested-With': 'XMLHttpRequest' };
switch (process.env.CONFIG_ENV) {
  case 'staging':
    headers['x-gray-set'] = 'preonline';
    break;
  case 'test':
    headers['x-gray-set'] = swimLane.length > 1 ? swimLane[0] : '';
    break;
  default:
    break;
}

const sso_host =
  process.env.CONFIG_ENV === 'production' || process.env.CONFIG_ENV === 'staging'
    ? 'https://ssosv.sankuai.com/sson'
    : 'http://ssosv.it.test.sankuai.com/sson';

const getSSOLoginUrl = () => {
  //登录域名
  const loginOrigin = `${sso_host}/login`;
  //clientId
  const clientId = process.env.CONFIG_ENV === 'production' || process.env.CONFIG_ENV === 'staging' ? 'efa25e6b04' : 'd4575e9875';

  const { origin, href } = window.location;
  const currentPath = href.replace(new RegExp(`^${origin}`), '');

  const sso_callback_url = `${origin}/webApi/sso/callback`;
  // 例：https://ssosv.sankuai.com/sson/login?client_id=com.iph.zhaopin&redirect_uri=http%3A%2F%2Fzhaopin.sankuai.com%2Fsso%2Fcallback%3Foriginal-url%3D%252F
  // !!!注意redirect_uri的encodeURIComponent包含了original-url参数
  return `${loginOrigin}?client_id=${clientId}&redirect_uri=${encodeURIComponent(
    `${sso_callback_url}?original-url=${encodeURIComponent(currentPath)}`
  )}`;
};

const http = factory({
  headers,
  baseURL: process.env.NODE_ENV === 'production' && process.env.CONFIG_ENV === 'staging' ? 'https://model.sankuai.com' : '',
  notifyMessage: Notification.error,
  showErrorMessage: (data) => {
    if (data?.data?.data?.customException) {
      return false;
    }
    return !(data instanceof axios.Cancel);
  },
  loginUrl: () => getSSOLoginUrl(),
  validateResponse(data) {
    if (data?.data?.customException) {
      Confirm({
        title: '失败提示',
        message: data?.data?.message,
        width: 800,
        type: 'error',
        okButtonText: '我知道了',
      });
      return false;
    }

    if (typeof data === 'string' && data.startsWith('data:')) {
      return true;
    }

    return data?.rescode === 0 || data?.rescode === 4291;
  },
  isUnauthorized(data) {
    // 处理staging环境的sso跳转问题

    return data.status === 401 || data.data?.rescode === 4030;
  },
  rethrowError: true,
  extractData: false,
});

export default http;
export { headers };
