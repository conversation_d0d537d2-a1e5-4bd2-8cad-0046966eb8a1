import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1418461, catid=245820, projectId=35802, created=2024-06-07 11:35:34, last-modified=2024-08-27 15:35:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/menu/collection/cancel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418461)
 */
export function postMenuCollectionCancel(params: model.menu.IPostMenuCollectionCancelParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.menu.IPostMenuCollectionCancelResponse>('/webApi/menu/collection/cancel', {
    data: pick(params, ['name', 'title']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1418456, catid=245820, projectId=35802, created=2024-06-07 11:35:31, last-modified=2024-08-27 15:35:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/menu/collection/add)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418456)
 */
export function postMenuCollectionAdd(params: model.menu.IPostMenuCollectionAddParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.menu.IPostMenuCollectionAddResponse>('/webApi/menu/collection/add', {
    data: pick(params, ['name', 'title']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1418463, catid=245820, projectId=35802, created=2024-06-07 11:35:37, last-modified=2024-08-27 15:35:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/menu/collection/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418463)
 */
export function postMenuCollectionList(options?: FlowHttpRequestOptions) {
  return http.post<model.menu.IPostMenuCollectionListResponse>('/webApi/menu/collection/list', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
