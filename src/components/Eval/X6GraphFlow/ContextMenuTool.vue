<template>
  <div class="x6-context-menu-tool" :style="`left:${config.x}px;top:${config.y}px;`">
    <mtd-dropdown-menu>
      <mtd-dropdown-menu-item
        ><mtd-cascader
          v-model="value"
          style="width: 80px"
          placement="right-start"
          :data="options"
          :formatter="formatter"
          :load-data="loadData"
          @change="addNode"
      /></mtd-dropdown-menu-item>
      <mtd-dropdown-menu-item @click="delNode">删除节点</mtd-dropdown-menu-item>
      <mtd-dropdown-menu-item @click="linkNode">链接节点</mtd-dropdown-menu-item>
    </mtd-dropdown-menu>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import EvalFlowVertexDefine from '@/store/modules/evalFlowVertexDefine';
import { Node } from '@antv/x6';

interface IContextMenuToolConfig {
  x: number;
  y: number;
  node: Node;
}
@Component
@Spectra
export default class ContextMenuTool extends Weaver(EvalFlowVertexDefine) {
  @Prop()
  config!: IContextMenuToolConfig;

  formatter() {
    return '新增节点';
  }

  value = [];
  options: {
    value: string;
    label: string;
    isLeaf: boolean;
  }[] = [];

  async addNode(valList) {
    const val = valList.pop();
    this.$mtd.message({ message: '添加中，请稍等', type: 'loading' });
    const res = await this.action$evalFlowVertexDefine({ id: val });
    this.$emit('addNode', {
      id: String(Date.now()),
      vertexDefineId: val,
      label: res.evalFlowVertexDefine.label,
      vertexDefine: res.evalFlowVertexDefine,
      pluginBindList: [],
    });
    this.$mtd.message.closeAll();
    this.value = [];
  }
  delNode() {
    this.$emit('delNode');
  }
  linkNode() {
    this.$emit('linkNode');
  }
  created() {
    this.initOptions();
  }
  async initOptions() {
    await this.action$flowVertexDefineListCategory({});
    this.options = this.flowVertexDefineListCategory.evalFlowVertexDefineCategoryList.map((category) => ({
      value: category,
      label: category,
      isLeaf: false,
    }));
  }
  async loadData(category, callback) {
    await this.action$flowVertexDefineList({
      searchFilter: '',
      ownerFilter: '',
      categoryFilter: category.value,
      statusFilter: '',
      offset: 0,
      limit: 9999,
    });
    callback(
      this.flowVertexDefineList.evalFlowVertexDefineList
        .filter((vertexDefine) => vertexDefine.anchor !== 'DataLoader')
        .map((vertexDefine) => ({
          value: vertexDefine.id,
          label: vertexDefine.label,
          isLeaf: true,
        }))
    );
  }
}
</script>
<style lang="scss">
.x6-context-menu-tool {
  position: absolute;
  z-index: 3;
  width: 100px;
  overflow: hidden;
  background-color: #fff;
  .mtd-input-wrapper.mtd-input-suffix .mtd-input {
    border: 0;
    background-color: inherit;
    margin: 0;
    padding: 0;
    justify-content: center;
    height: 36px;
  }
  .mtd-input-suffix-inner {
    width: 16px;
    height: 36px;
    transform: rotate(270deg);
  }
  .mtd-cascader.focus .mtd-input-suffix-inner {
    transform: rotate(270deg);
  }
}
</style>
