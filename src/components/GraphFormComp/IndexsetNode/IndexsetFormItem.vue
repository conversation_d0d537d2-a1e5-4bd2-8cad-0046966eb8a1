<template>
  <Indexset v-bind="item.indexsetCfg || {}" :value="model" :disabled="!isEdit" @change="handleChange" />
</template>

<script lang="ts">
import Indexset from '@/components/dataset/Indexset.vue';
import { Component, Prop, Spectra, Vue } from '@/decorators';

@Component({ components: { Indexset } })
@Spectra
export default class IndexsetFormItem extends Vue {
  @Prop()
  model!: any;
  @Prop()
  isEdit!: boolean;
  @Prop()
  item: any;

  handleChange(value: any) {
    this.$emit('change', value);
  }
}
</script>
