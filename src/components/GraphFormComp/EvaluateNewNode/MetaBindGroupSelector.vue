<template>
  <div class="common-width">
    <div class="w-full flex">
      <mtd-select
        style="flex: 1"
        v-if="isEdit"
        :value="foundModel"
        :filterable="true"
        @change="change"
        clearable
        :placeholder="placeholder"
      >
        <mtd-option v-for="v in options" :key="v.value" :label="v.label" :value="v.value"> </mtd-option>
      </mtd-select>
      <span v-else>{{ this.options.find((x) => x.value === model)?.label || placeholder }}</span>
      <mtd-button type="text-primary" @click="handleModelMetaJump"
        >预设推理配置
        <mtd-icon name="link-o"></mtd-icon>
      </mtd-button>
    </div>

    <span v-if="!options.length" style="color: red">请先在【模型信息】节点配置【全局推理参数配置】</span>
  </div>
</template>
<script lang="ts">
import { getModelExperimentTaskGetOutputModelMetaId, postModelManagerV2GetModelMetaEvalPluginList } from '@/api/modules/model';
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import modelManage from '@/store/modules/modelManage';
import { useGraphStore } from '@/views/modelManage/components/use-graph-store';

@Component
@Spectra
export default class MetaBindGroupSelector extends Weaver(modelManage) {
  @Prop()
  model!: any;
  @Prop()
  globalContext: any;
  @Prop()
  isEdit!: boolean;
  @Prop()
  context!: any;

  benchMark: any;
  change(v) {
    this.$emit('change', v);
  }
  useGraphStore = useGraphStore();
  options: any[] = [];
  placeholder = '';

  get foundModel() {
    return this.options.find((x) => x.value === this.model)?.value || null;
  }

  async getSourceMetaId() {
    const { edges, nodes } = this.globalContext.getFields('graphData');
    const prevNodeMap = new Map();
    edges.forEach((edge) => {
      prevNodeMap.set(edge.target, edge.source);
    });
    let ptr = this.context.id;
    while (ptr) {
      const node = nodes.find((n) => n.id === ptr);
      // 向前递归找精调模型节点。要适配其他会改meta的节点改这里
      if (node.nodeType === 2) {
        // case1: 精调模型节点上配置了输出模型
        if (node.nodeParam?.outputModelMetaId) return node.nodeParam?.outputModelMetaId;
        // case2: 精调模型节点没配，但是完成执行了，产生了一个新meta
        const nodeInfo = (this.taskStatLists || this.experimentConfigDetail?.status || this.taskDetail?.status)?.nodeStatus?.find(
          (x) => x.nodeId === Number(ptr)
        );
        if (nodeInfo) {
          const res = await getModelExperimentTaskGetOutputModelMetaId({
            taskDetailId: String(nodeInfo.taskDetailId),
          });
          if (res.data.logUrl) return Number(res.data.logUrl);
        }
      }
      ptr = prevNodeMap.get(ptr);
    }
    // case3: 没有改变meta的节点，用实验信息的
    return this.experimentBaseInfo.modelMeta?.metaId || this.useGraphStore.modelInfo?.modelMetaId;
  }

  async created() {
    const res = await postModelManagerV2GetModelMetaEvalPluginList({
      modelMetaId: await this.getSourceMetaId(),
    });
    this.options = res.data.groupList?.map((x) => ({ label: x.name, value: x.id })) || [];
    this.placeholder = `默认(${res.data.groupList?.find((x) => x.defaultFlag)?.name || '-'})`;
  }

  async handleModelMetaJump() {
    const metaId = await this.getSourceMetaId();
    window.open(
      this.$router.resolve({
        name: 'model-manage-model-detail',
        params: {
          metaId,
        },
        query: {
          type: 'global',
        },
      }).href,
      '_blank',
      'noopener'
    );
  }
}
</script>
<style lang="scss"></style>
