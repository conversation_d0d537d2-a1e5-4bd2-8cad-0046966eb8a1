<template>
  <mtd-card body-class="flow-config-card" style="min-width: 350px; max-width: 600px">
    <div class="flow-config-info">
      <div class="flow-config-label-container">
        <div class="flow-config-label">==全局概览==</div>
        <mtd-button type="text-primary" v-if="nodeInfo" size="small" @click="handleViewJobHistory" style="font-size: 15px"
          >[提交历史]</mtd-button
        >
      </div>
      <div style="flex-shrink: 0" v-if="nodeInfo">
        <mtd-button type="text-primary" class="card-button" @click="handleViewJobDetail">提交详情#{{ nodeInfo?.taskDetailId }}</mtd-button>
      </div>
    </div>
    <div class="tag-container">
      <div class="flex justify-between">
        <div>
          <span>数据总量</span>&nbsp;
          <mtd-popover trigger="hover">
            <div slot="content">
              <mtd-tag v-for="item in globalBenchmarkList" :key="item.runSpecSetName">{{ item.runSpecSetName }}</mtd-tag>
            </div>
            <span style="color: #1c6cdc; font-weight: bold">数据集&nbsp;{{ globalBenchmarkList.length }}</span> </mtd-popover
          >&nbsp;

          <mtd-popover trigger="hover" placement="top" :disabled="!globalRunSpecFlowConfigList.filter((x) => x.repeatFlag).length">
            <span class="font-bold"
              >执行单元&nbsp;{{ globalRunSpecFlowConfigList.length }}
              <span v-if="globalRunSpecFlowConfigList.filter((x) => x.repeatFlag).length"
                >(<span style="text-decoration: underline">{{ globalRunSpecFlowConfigList.filter((x) => !x.repeatFlag).length }}</span
                >)</span
              >
            </span>
            <div slot="content" class="repeat-runspec-content">
              配置存在重复，不执行的执行单元：
              <ul style="margin-left: 20px">
                <li v-for="runSpec of globalRunSpecFlowConfigList.filter((x) => x.repeatFlag).slice(0, 10)" :key="runSpec.id">
                  执行单元:#{{ runSpec.runSpecId }}:&nbsp;{{ runSpec.jobBenchmark.runSpecSetName }}@{{
                    getDataSizeLabel(runSpec.evalDataSize)
                  }}&nbsp;=>&nbsp;{{ runSpec.dataSubSetName }}
                </li>
                <li v-if="globalRunSpecFlowConfigList.filter((x) => x.repeatFlag).length > 10">
                  ...共{{ globalRunSpecFlowConfigList.filter((x) => x.repeatFlag).length }}个执行单元
                </li>
              </ul>
            </div>
          </mtd-popover>
        </div>
        <mtd-button v-if="isEdit" style="font-size: 15px" type="text-primary" @click="openModal('PARAM')">
          参数批量配置 <mtd-icon name="setting" />
        </mtd-button>
      </div>
      <div class="flex justify-between">
        <div>
          <span>资源总量&nbsp;</span>
          <span style="color: #9152f5; font-weight: bold" v-for="(item, idx) in globalGpuList" :key="item">
            <span v-if="idx !== 0">,</span>
            {{ item }}
          </span>
        </div>
        <mtd-button v-if="isEdit" style="font-size: 15px" type="text-primary" @click="openModal('RESOURCE')">
          资源批量配置 <mtd-icon name="slider-settings" />
        </mtd-button>
      </div>
    </div>

    <bulk-resource-config-modal
      :isEdit="isEdit"
      :runspecSetContext="globalRunspecSetContext"
      :context="context"
      :openModal.sync="resourceModalOpen"
      :sourceTableData="sourceTableData"
      :defaultTableData="defaultTableData"
      @save="saveBulkResourceConfig"
    />
    <bulk-params-config-modal
      :isEdit="isEdit"
      :runspecSetContext="globalRunspecSetContext"
      :context="context"
      :saveRunspecSetConfig="saveRunspecSetConfig"
      :openModal.sync="paramModalOpen"
      :sourceMetaId="sourceMetaId"
      :isBulk="true"
      @save="saveBulkParamsConfig"
    />
  </mtd-card>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Weaver } from '@/decorators';
import { ContextParam } from '@/model/customEval';
import evalFlow from '@/store/modules/evalFlow';
import evalFlowJob from '@/store/modules/evalFlowJob';
import evalFlowVersion from '@/store/modules/evalFlowVersion';
import modelManage from '@/store/modules/modelManage';
import StatusCountTag from '@/views/modelEval/FlowChartJobExecute/components/StatusCountTag.vue';
import StatusTag from '@/views/modelEval/FlowChartJobExecute/components/StatusTag.vue';
import { StatusConfig } from '@/views/modelEval/utils';
import { cloneDeep, differenceWith, isEqual } from 'lodash';
import BulkParamsConfigModal from './BulkParamsConfigModal.vue';
import BulkResourceConfigModal from './BulkResourceConfigModal.vue';
import GlobalAliasParams from './GlobalAliasParams.vue';
import PluginBindPreview from './PluginBindPreview.vue';

@Component({
  components: {
    GlobalAliasParams,
    PluginBindPreview,
    StatusTag,
    StatusCountTag,
    BulkResourceConfigModal,
    BulkParamsConfigModal,
  },
})
@Spectra
export default class GlobalFlowJobCard extends Weaver(evalFlowJob, evalFlow, evalFlowVersion, modelManage) {
  @Prop()
  isEdit!: boolean;
  @Prop()
  context;
  @Prop({ default: () => 0 })
  sourceMetaId;
  @Prop()
  saveRunspecSetConfig;
  @Prop()
  sourceConfigList;
  @Prop()
  nodeInfo;
  @Prop()
  clusterUniqueKeyMap;

  paramModalOpen = false;
  resourceModalOpen = false;
  globalRunspecSetContext: any = {};
  sourceTableData: any = [];
  defaultTableData: any = [];
  globalGpuList: any = [];

  handleViewJobHistory() {
    window.open(
      this.$router.resolve({
        name: 'flow-chart-job-execute-list',
        query: {
          expId: this.$route.params.experimentId,
          expNodeId: String(this.context.id),
        },
      }).href,
      '_blank',
      'noopener'
    );
  }

  handleViewJobDetail() {
    window.open(
      this.$router.resolve({
        name: 'flow-chart-job-execute-list',
        query: {
          expTaskDetailId: this.nodeInfo?.taskDetailId,
        },
      }).href,
      '_blank',
      'noopener'
    );
  }

  get globalBenchmarkList() {
    return this.sourceConfigList.flatMap((x) => x.benchmarkList);
  }

  get globalRunSpecFlowConfigList() {
    return this.sourceConfigList.flatMap((x) => x.runSpecFlowConfigList);
  }

  genGlobalGpuList() {
    const gpuSpecCount = [...this.clusterUniqueKeyMap.values()].reduce((acc, config) => {
      if (!config.gpuSpec) return acc;
      acc[config.gpuSpec] = (acc[config.gpuSpec] || 0) + config.cost;
      return acc;
    }, {});
    this.globalGpuList = Object.entries(gpuSpecCount).map((x) => x.join(' * '));
  }

  initGlobalRunspecSetContext() {
    this.globalRunspecSetContext = {
      ...cloneDeep(this.sourceConfigList[0]),
      benchmarkList: this.globalBenchmarkList,
      runSpecFlowConfigList: this.globalRunSpecFlowConfigList,
      globalAliasParams: [],
      envGlobalAliasParams: this.initBulkParams(),
      globalAnchorBindMapJson: this.initGlobalAnchorBindMapJson(),
      runSpecAnchorBindMapJson: '[]',
    };
  }

  initBulkParams() {
    const flowJob = cloneDeep(this.sourceConfigList);
    const initParamsMap = new Map<string, ContextParam>();
    flowJob.forEach((job) => {
      job.previewEnvGlobalAliasParams.forEach((params) => {
        if (!initParamsMap.has(params.name)) {
          initParamsMap.set(params.name, { ...params, defaultValue: params.value, value: null });
        } else {
          const mappedParam = initParamsMap.get(params.name)!;
          if (mappedParam.defaultValue !== params.value) {
            mappedParam.defaultValue = '<多值>';
          }
        }
      });
      job.envGlobalAliasParams.forEach((params) => {
        const mappedParam = initParamsMap.get(params.name)!;
        if (!mappedParam.value) {
          mappedParam.value = params.value;
          mappedParam.bulkDefaultValue = params.value;
        }
        if (mappedParam.value !== params.value) {
          mappedParam.value = '<多值>';
          mappedParam.bulkDefaultValue = '<多值>';
        }
      });
    });

    return [...initParamsMap.values()];
  }

  initBulkResourceConfig() {
    const flowJob = cloneDeep(this.sourceConfigList);
    const initResourceMap = new Map<string, ContextParam>();
    const initDefaultResourceMap = new Map<string, ContextParam>();
    flowJob.forEach((job) => {
      job.defaultResourceConfigList.forEach((defaultConfig, idx) => {
        const config = job.resourceConfigList.find((x) => x.clusterName === defaultConfig.clusterName);
        if (!config) return;
        if (!initResourceMap.has(config.clusterName)) {
          initResourceMap.set(config.clusterName, {
            resourceType: defaultConfig.resourceType,
            consumerDefineList: defaultConfig.consumerDefineList,
            ...config,
          });
          initDefaultResourceMap.set(config.clusterName, { ...defaultConfig });
        } else {
          const mappedParam = initResourceMap.get(config.clusterName)!;
          const defaultMappedParam = initDefaultResourceMap.get(config.clusterName)!;
          Object.keys(defaultConfig).forEach((key) => {
            if (key === 'consumerDefineList') return;
            if (String(mappedParam[key]) !== String(config[key])) {
              mappedParam[key] = '多值';
            }
            if (String(defaultMappedParam[key]) !== String(defaultConfig[key])) {
              defaultMappedParam[key] = '多值';
            }
          });
        }
      });
    });
    this.sourceTableData = [...initResourceMap.values()];
    this.defaultTableData = [...initDefaultResourceMap.values()];
  }

  getTheme(status) {
    return StatusConfig[status].theme;
  }

  async openModal(type) {
    await this.action$evalFlowVersion({
      flowId: this.sourceConfigList[0].flowId,
      version: this.sourceConfigList[0].flowVersion,
    });

    this.initGlobalRunspecSetContext();
    if (type === 'PARAM') {
      this.paramModalOpen = true;
    } else {
      this.initBulkResourceConfig();
      this.resourceModalOpen = true;
    }
  }

  saveBulkParamsConfig(bulkConfig) {
    const addedEnvGlobalAliasParams = bulkConfig.envGlobalAliasParams.filter((x) => x.isBulk === true);
    const addedGlobalPluginBindMapJson = bulkConfig.globalAnchorBindMapJson;
    this.sourceConfigList.forEach((flowJobConfig, idx) => {
      // 批量全局环境参数注入
      const changedEnvGlobalAliasParams = [...flowJobConfig.envGlobalAliasParams];
      addedEnvGlobalAliasParams.forEach((addedParams) => {
        const findParams = changedEnvGlobalAliasParams.find((x) => x.name === addedParams.name);
        if (findParams) findParams.value = addedParams.value;
        else changedEnvGlobalAliasParams.push(addedParams);
      });
      // 批量全局插件绑定注入
      let changedGlobalPluginBindMapJson = flowJobConfig.globalAnchorBindMapJson;
      try {
        const changedMap: any = new Map(JSON.parse(addedGlobalPluginBindMapJson));
        const oriMap = new Map(JSON.parse(flowJobConfig.globalAnchorBindMapJson));
        [...changedMap.entries()].forEach(([anchorPath, pluginBindList]) => {
          [...oriMap.keys()].forEach((oriAnchorPath) => {
            if (anchorPath === oriAnchorPath) {
              oriMap.set(oriAnchorPath, pluginBindList);
            }
          });
        });
        changedGlobalPluginBindMapJson = JSON.stringify([...oriMap.entries()]);
      } catch (e) {
        console.log('批量保存绑定错误:', e);
      }
      // save
      this.saveRunspecSetConfig(idx, {
        ...flowJobConfig,
        envGlobalAliasParams: changedEnvGlobalAliasParams,
        globalAnchorBindMapJson: changedGlobalPluginBindMapJson,
      });
    });
  }

  saveBulkResourceConfig(bulkConfig) {
    bulkConfig.forEach((config, i) => {
      Object.entries(config).forEach(([k, v]) => {
        if (v === 'DEFAULT') {
          bulkConfig[i][k] = null;
        }
      });
    });
    this.sourceConfigList.forEach((flowJobConfig, idx) => {
      const changedResourceConfig = flowJobConfig.resourceConfigList.map((resourceConfig) => {
        const changedConfig = bulkConfig.find((x) => x.clusterName === resourceConfig.clusterName);
        if (!changedConfig) return resourceConfig;
        else {
          return { ...resourceConfig, ...changedConfig };
        }
      });
      this.saveRunspecSetConfig(idx, {
        ...flowJobConfig,
        resourceConfigList: changedResourceConfig,
      });
    });
    this.$nextTick(this.genGlobalGpuList);
  }

  initGlobalAnchorBindMapJson() {
    let globalAnchorBindMap = new Map();
    const bindHasDiff = (bind1, bind2) => {
      const res =
        bind1.pluginImplement !== bind2.pluginImplement ||
        bind1.anchorPath !== bind2.anchorPath ||
        !isEqual(
          bind1.contextParams.map(({ name, value }) => ({ name, value })),
          bind2.contextParams.map(({ name, value }) => ({ name, value }))
        );
      return !res;
    };
    this.sourceConfigList.forEach((config, idx) => {
      try {
        const curGlobalAnchorBindMap: any = new Map(JSON.parse(config.globalAnchorBindMapJson));
        if (idx === 0) {
          globalAnchorBindMap = curGlobalAnchorBindMap;
        } else {
          [...globalAnchorBindMap.entries()].forEach(([anchorPath, pluginBindList]) => {
            if (
              !curGlobalAnchorBindMap.get(anchorPath) ||
              differenceWith(pluginBindList, curGlobalAnchorBindMap.get(anchorPath) || [], bindHasDiff).length
            ) {
              globalAnchorBindMap.delete(anchorPath);
            }
          });
        }
      } catch (e) {
        console.log('绑定聚合失败:', e);
      }
    });
    return JSON.stringify([...globalAnchorBindMap.entries()]);
  }
  getDataSizeLabel(dataSize) {
    return this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList.find((x) => x.name === String(dataSize))?.label || '-';
  }
}
</script>
<style lang="less" scoped>
.status-text {
  max-width: 230px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .flow-config-card {
  position: relative;
  cursor: pointer;
  padding: 10px;
}

::v-deep .mtd-badge-text {
  background-color: rgba(255, 0, 0, 0.4);
}

.flow-config-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  overflow: hidden;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.card-button {
  font-size: 19px;
  font-weight: bold;
  flex-shrink: 0;
}
.flow-config-label-container {
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 0;
  overflow: hidden;
  align-items: center;
  ::v-deep .mtd-tooltip-rel {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.flow-config-label {
  font-size: 19px;
  display: flex;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-container {
  font-size: 15px;
}

.repeat-runspec-content {
  padding: 10px;
}
</style>
