<template>
  <div>
    <mtd-radio-group v-model="type" @input="changeType">
      <mtd-radio value="path">默认</mtd-radio>
      <mtd-radio value="version"
        >自定义数据
        <!-- <span v-if="isVersionDef">(已填入默认值)</span> -->
      </mtd-radio>
    </mtd-radio-group>
    <div v-if="type === 'path'" class="flex items-center">
      <a @click="onClickData" class="cursor-pointer">基线数据</a>
      <mtd-tag theme="green" class="ml-2">最新基线数据</mtd-tag>
    </div>
    <DatasetVersionSelect
      class="common-width"
      v-if="type === 'version'"
      ref="datasetVersionSelectRef"
      :versions="context.nodeParam.datasets"
      :disabled="!isEdit"
      :onlyMultiple="true"
      :multipleType="true"
      :nodeParam="context.nodeParam"
      @change="changeVersion"
      :dataStrategyVisible="dataStrategyVisible"
      :snapshotSelectVisible="snapshotSelectVisible"
    />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra } from '@/decorators';
import DatasetVersionSelect from '@/views/DataManage/PretrainDataset/components/DatasetVersionSelect.vue';
import BaseExperimentDetail from '@/views/modelManage/components/BaseExperimentDetail';

import { cloneDeep } from 'lodash';

@Component({ components: { DatasetVersionSelect } })
@Spectra
export default class BaselineDataNew extends BaseExperimentDetail {
  @Prop()
  model!: any;
  @Prop()
  context: any;
  @Prop()
  globalContext: any;
  // @Prop()
  // isEdit!: boolean;
  @Prop()
  item: any;

  get dataStrategyVisible() {
    return this.item?.dataStrategyVisible || '';
  }
  get snapshotSelectVisible() {
    return this.item?.snapshotSelectVisible || '';
  }
  flowDatasetsCfg = { visible: false };
  type = 'path';

  lastBaselineData = '';
  lastVersion: any = [];
  async onClickData() {
    const taskId = this.taskId ?? this.experimentConfigDetail?.taskId?.taskId;
    const taskVersion = this.version ?? this.experimentConfigDetail?.taskId?.version;
    let basedPath: string = '';
    if (taskId && taskVersion) {
      try {
        const result = await this.action$taskNodeResult({
          taskId: taskId,
          nodeId: Number(this.context.id),
          version: Number(taskVersion),
        });
        basedPath = result?.outputs?.find((i) => i.key === 'basedPath')?.value || '';
      } catch (error) {}
    }
    const routerParams: any = {
      name: 'data-dissemination-list',
      query: {
        experimentId: this.experimentId,
      },
    };
    if (basedPath) {
      routerParams.query.basedPath = basedPath;
    }
    const url = this.$router.resolve(routerParams).href;
    window.open(url);
  }
  changeType() {
    const { nodeParam } = this.context;
    if (this.type === 'version') {
      this.lastBaselineData = nodeParam.baselineData;
      this.$set(nodeParam, 'baselineData', '');
      this.isVersionDef = false;
      this.lastVersion?.length && this.$set(nodeParam, 'datasets', cloneDeep(this.lastVersion));
      this.$set(nodeParam, 'defaultPath', false);
    } else {
      this.$set(nodeParam, 'baselineData', '');
      this.$set(nodeParam, 'storageLocation', '');
      this.lastVersion = cloneDeep(nodeParam.datasets);
      this.$set(nodeParam, 'datasets', []);
      this.$set(nodeParam, 'defaultPath', true);
    }
  }

  changeVersion(v) {
    const { nodeParam } = this.context;
    this.isVersionDef = false;
    if (v.length > 1) {
      this.$mtd.message.error('数据集版本只能选择一个,请重新选择');
      this.$set(nodeParam, 'datasets', []);
      return;
    } else {
      this.$set(nodeParam, 'datasets', cloneDeep(v));
    }
  }

  change(v) {
    this.$emit('change', v);
  }

  defVersions: any = null;
  isVersionDef: boolean = false;
  getDefVersion() {
    const { nodeParam } = this.context;
    this.isVersionDef = true;
    if (this.defVersions) {
      this.$set(nodeParam, 'datasets', cloneDeep(this.defVersions));
      return;
    }
    this.action$getDataHubDatasetVersionPostTrainingGetMasterVersion({ type: '0' }).then((res) => {
      if (res) {
        this.defVersions = [
          {
            datasetId: res.datasetId,
            datasetVersionId: res.datasetVersionId,
            snapshotId: res.snapshotId,
          },
        ];
        this.$set(nodeParam, 'datasets', cloneDeep(this.defVersions));
      }
    });
  }

  created() {
    const { nodeParam } = this.context;
    if (nodeParam?.defaultPath) {
      this.type = 'path';
    } else {
      this.type = nodeParam?.datasets?.length || nodeParam?.storageLocation || nodeParam?.baselineData ? 'version' : 'path';
    }

    if (nodeParam.specialData) {
      this.lastBaselineData = nodeParam.specialData;
    }
    if (!nodeParam.datasets?.length) {
      this.$set(nodeParam, 'datasets', []);
    }
    // if (this.type === 'version' && !nodeParam.datasets?.length) {
    //   this.getDefVersion();
    // }
    if (this.type === 'path') {
      this.$set(nodeParam, 'defaultPath', true);
    }
  }
}
</script>
<style lang="scss" scoped></style>
