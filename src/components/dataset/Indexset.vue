<template>
  <div class="inddexset-container">
    <div class="container">
      <div class="exp-text">
        {{ `已选择${((value && value.viewIdList?.length) || 0) + ((value && value.indexInfoList?.length) || 0)}项` }}
      </div>
      <slot name="btn" :click="openModal">
        <mtd-button @click="openModal">{{ `${disabled ? '查看' : '选择'}${setDisabled ? '训练集' : '索引'}` }}</mtd-button>
      </slot>
    </div>
    <mtd-modal
      v-model="visible"
      class="indexset-modal"
      :append-to-container="false"
      :title="setDisabled ? '数据集信息' : '索引集信息'"
      width="720px"
      @closed="closeModal"
    >
      <div v-if="!setDisabled" class="view-tags" style="display: flex; margin-bottom: 3px">
        <div class="label" style="text-wrap: nowrap; width: 128px; flex-shrink: 0">当前已选索引集合：</div>
        <div class="tags-container">
          <mtd-tag
            v-for="(item, index) in viewTags"
            :key="item.id"
            style="margin-right: 6px; margin-bottom: 3px"
            :theme="item.name === null ? 'red' : undefined"
            :type="item.name === null ? 'pure' : undefined"
            closable
            @close="removeTag(index)"
            :disabled="disabled"
            >{{ item.name || item.id }}</mtd-tag
          >
        </div>
      </div>
      <div v-if="!setDisabled" class="view-tags" style="display: flex; margin-bottom: 3px">
        <div class="label" style="text-wrap: nowrap; width: 128px; flex-shrink: 0">当前已选索引：</div>
        <div class="tags-container">
          <mtd-tag
            v-for="(item, index) in indexTags"
            :key="index"
            style="margin-right: 6px; margin-bottom: 3px"
            closable
            @close="removeTag(index, 1)"
            :disabled="disabled"
            >{{ `${item[0]}` }}</mtd-tag
          >
        </div>
      </div>
      <div v-if="!setDisabled" style="display: flex; justify-content: space-between">
        <mtd-radio-group v-model="switchType" style="margin-bottom: 10px" @change="changeSwitchType">
          <mtd-radio-button :value="1">索引集合</mtd-radio-button>
          <mtd-radio-button :value="2">索引</mtd-radio-button>
        </mtd-radio-group>
        <mtd-button v-if="switchType === 1" type="primary" @click="openAddModal()">新建索引集合</mtd-button>
      </div>
      <div class="switch-container" style="height: 350px">
        <div v-show="switchType === 1" class="table-container">
          <mtd-table
            :loading="LS('dataHubDatasetViewPage')"
            row-key="id"
            :selection="viewTags"
            reserve-selection
            :index-of-selection="indexOfSelection"
            :height="320"
            :data="dataHubDatasetViewPage.list"
          >
            <mtd-table-column type="selection" v-show="!disabled" width="50" />
            <mtd-table-column prop="id" label="id" width="60" />
            <mtd-table-column prop="name" label="名称" show-overflow-tooltip width="160" />
            <mtd-table-column prop="datasetEsIndexItemList" show-overflow-tooltip label="索引集合详情">
              <template slot-scope="{ row }">
                {{ getEsIndexLabel(row.datasetEsIndexItemList).join(' ; ') }}
              </template>
            </mtd-table-column>
            <mtd-table-column fixed="right" label="操作" width="90" class-name="operate-class">
              <template slot-scope="{ row }">
                <mtd-button type="text-primary" @click="openAddModal(row)">修改</mtd-button>
                <mtd-popconfirm placement="top" message="确定删除该索引集合吗" @ok="deleteIndexset(row)">
                  <mtd-button type="text-primary" :loading="LS('dataHubDatasetViewDelete')">删除</mtd-button>
                </mtd-popconfirm>
              </template>
            </mtd-table-column>
          </mtd-table>
          <mtd-pagination
            style="float: right"
            size="small"
            :total="dataHubDatasetViewPage.total"
            show-size-changer
            show-total
            :current-page.sync="tableData.pageNumber"
            :page-size.sync="tableData.pageSize"
            @change="searchTableList"
          />
        </div>
        <div v-show="switchType === 2" class="cascader-container">
          <mtd-cascader
            v-model="indexTags"
            multiple
            expand-trigger="hover"
            :data="indexSubList"
            checked-strategy="children"
            filterable
            clearable
            :props="{ label: 'value' }"
            style="width: 260px"
            @change="changeIndexSub"
            :disabled="disabled"
          >
            <template v-if="tagIdx.length" slot-scope="{ node, data }">
              <span>{{ node.label }} </span>
              <mtd-tag v-if="isExped(data)">已完成实验</mtd-tag>
            </template>
          </mtd-cascader>
        </div>
      </div>
      <div v-if="!disabled" slot="footer">
        <mtd-button @click="closeModal">取消</mtd-button>
        <mtd-button type="primary" @click="confrimSetValue">确定</mtd-button>
      </div>
    </mtd-modal>
    <mtd-modal
      v-model="addModal.visible"
      class="index-modal"
      :append-to-container="false"
      :title="`${addModal.titleDic[addModal.type]}索引集合`"
      width="520"
      @closed="closeAddModal"
    >
      <mtd-loading :loading="LS('dataHubDatasetViewUpsert', 'dataHubDatasetViewEsIndex')">
        <mtd-form ref="addForm" :model="addModal.form">
          <mtd-form-item label="索引名称" prop="name" :rules="[{ required: true, message: '索引名称不能为空' }]">
            <mtd-input v-model="addModal.form.name" type="text" style="width: 260px" />
          </mtd-form-item>
          <mtd-form-item label="选择索引" prop="esIndexInfoList" :rules="[{ required: true, message: '索引不能为空' }]">
            <mtd-cascader
              v-model="addModal.form.esIndexInfoList"
              multiple
              expand-trigger="hover"
              :data="indexSubList"
              checked-strategy="children"
              filterable
              clearable
              :props="{ label: 'value' }"
              style="width: 260px"
            />
          </mtd-form-item>
        </mtd-form>
      </mtd-loading>
      <div slot="footer">
        <mtd-button @click="closeAddModal">取消</mtd-button>
        <mtd-button type="primary" @click="confirmAdd">确定</mtd-button>
      </div>
    </mtd-modal>
  </div>
</template>

<script lang="ts">
import { CatchError, Component, Model, Prop, Ref, Spectra, Weaver } from '@/decorators';
import dataManage from '@/store/modules/dataManage';
import { Form } from '@ss/mtd-vue/es/components/form';

interface viewTag {
  id: number;
  name: string;
  [k: string]: unknown;
}

@Component({ components: {} })
@Spectra
export default class Indexset extends Weaver(dataManage) {
  @Model('change')
  value;
  @Prop({ default: false }) disabled;
  @Prop({ default: false }) setDisabled;
  @Prop({ default: false }) singleIndexSub;
  @Prop({ default: () => [] }) tagIdx?: number[];
  @Ref('addForm')
  addFormRef: Form;

  visible = false;
  switchType = 1;
  tableData = {
    form: { name: '', type: 1 },
    list: [],
    total: 0,
    pageSize: 10,
    pageNumber: 1,
  };
  indexSubList = [];

  addModal = {
    visible: false,
    type: 'add',
    form: {
      name: '',
      esIndexInfoList: [],
      type: 1,
    },
    titleDic: Object.freeze({
      add: '新建',
      edit: '修改',
    }),
  };

  viewTags: viewTag[] = [];
  indexTags: string[][] = [];

  // @Watch('indexTags')
  // changeIndexTags(to, from) {
  //   this.changeIndexSub(to, from);
  // }

  // @Watch('addModal.form.esIndexInfoList')
  // changeEsIndexInfoList(to, from) {
  //   this.changeIndexSub(to, from);
  // }

  removeTag(index, type?) {
    type || this.viewTags.splice(index, 1);
    type && this.indexTags.splice(index, 1);
  }

  indexOfSelection(row, selection) {
    return selection.findIndex((item) => {
      const flag = item.id === row.id;
      flag && item.name !== row.name && Object.assign(item, { name: row.name });
      return flag;
    });
  }

  async openModal() {
    this.viewTags = (this.value.viewIdList || []).map((id) => ({ id, name: undefined }));
    this.indexTags = this.value.indexInfoList?.map(({ source }) => [source]) || [];
    this.switchType = 2;
    this.visible = true;
    this.matchViewTags();
    this.changeSwitchType(2);
  }

  async matchViewTags() {
    const errorTags: number[] = [];
    const res = await this.searchTableList({ pageSize: 99999 });
    const dataMap = new Map();
    (res as any).list?.forEach((item) => {
      dataMap.set(item.id, item.name);
    });
    this.viewTags.forEach((item) => {
      const name = dataMap.get(item.id);

      Object.assign(item, { name });
      name === undefined && errorTags.push(item.id);
    });
    errorTags.length && this.$mtd.message.error(`未查询到id为[${errorTags.join('，')}]的索引集，请删除`);
    return errorTags.length;
  }

  closeModal() {
    this.visible = false;
    this.viewTags = [];
    this.indexTags = [];
  }

  async confrimSetValue() {
    if (await this.matchViewTags()) {
      this.changeSwitchType(1);
      return;
    }

    const indexInfoList = this.indexTags.map(([source]) => ({
      source,
    }));
    this.$set(this.value, 'indexInfoList', indexInfoList);

    this.$set(
      this.value,
      'viewIdList',
      this.viewTags.map((tag) => tag.id)
    );
    this.closeModal();
  }

  async searchTableList(data?) {
    const { form, pageSize, pageNumber } = this.tableData;
    const res = await this.action$dataHubDatasetViewPage({ param: form, pageSize, pageNumber, ...(data || {}) });
    return res;
  }

  initSearchTableList() {
    Object.assign(this.tableData, { pageNumber: 1, pageSize: 10 });
    this.searchTableList();
  }

  openAddModal(row?) {
    this.getIndexSubList();
    if (row) {
      const { datasetEsIndexItemList, id, name } = row;
      Object.assign(this.addModal.form, { id, name, esIndexInfoList: this.getEsIndexLabel(datasetEsIndexItemList, true) });
    }

    Object.assign(this.addModal, {
      visible: true,
      type: row ? 'edit' : 'add',
    });
  }

  closeAddModal() {
    this.addFormRef.resetFields();
    Object.assign(this.addModal, {
      visible: false,
      type: 'add',
    });
    this.$set(this.addModal.form, 'id', null);
  }
  @CatchError
  async deleteIndexset(row) {
    await this.action$dataHubDatasetViewDelete({ id: row.id });
    this.$mtd.message.success('删除成功');
    this.initSearchTableList();
  }

  async getIndexSubList() {
    const data = await this.action$dataHubDatasetViewEsIndex({ type: 1 });
    data?.forEach((source) => {
      source.value = source.source;
      // source.esIndexVersionItem?.forEach((version) => {
      //   version.value = version.version;
      // });
      // source.children = source.esIndexVersionItem;
      source.children = [];
    });
    this.$set(this, 'indexSubList', data);
  }

  getEsIndexLabel(list, getIdPath?) {
    const result: (string | string[])[] = [];
    list?.forEach((source) => {
      const idPath = [source.source];
      result.push(getIdPath ? idPath : idPath.join('/'));
      // source.esIndexVersionItem?.forEach((version) => {
      // });
    });
    return result;
  }

  changeSwitchType(v) {
    v === 1 && this.initSearchTableList();
    v === 2 && this.getIndexSubList();
  }

  // changeIndexSub(to, from) {
  //   if (to && from && to.length > 1 && to.length > from.length) {
  //     from.forEach((item) => {
  //       const arr = to.filter((i) => i[0] === item[0]);
  //       if (arr.length > 1) {
  //         const index = arr.findIndex((i) => i[1] === item[1]);
  //         if (index > -1) {
  //           const idx = to.findIndex((e) => e === arr[index]);
  //           idx > -1 && to.splice(idx, 1);
  //         }
  //       }
  //     });
  //   }
  // }

  @CatchError
  async confirmAdd() {
    await this.addFormRef.validate();
    const { form, type, titleDic } = this.addModal;
    const { esIndexInfoList } = form;
    const param = { ...this.addModal.form, esIndexInfoList: esIndexInfoList.map((item) => ({ source: item[0] })) };
    await this.action$dataHubDatasetViewUpsert(param);
    this.$mtd.message.success(`${titleDic[type]}成功`);

    this.initSearchTableList();
    this.closeAddModal();
  }

  changeIndexSub(val, _selectedOptions, _nodes, lastNode) {
    if (this.singleIndexSub && val.length > 1 && lastNode.value) {
      this.$set(this, 'indexTags', [[lastNode.value]]);
    }
  }

  isExped(data) {
    const id = data.esIndexVersionItem[0]?.esIndexItemList[0]?.id;
    return id && this.tagIdx?.includes(id);
  }
}
</script>

<style lang="scss" scoped>
.inddexset-container {
  width: 100%;
  .container {
    display: flex;
    justify-content: space-between;
  }
}
</style>

<style lang="scss" scoped>
.inddexset-container {
  .indexset-modal {
    .mtd-cascader {
      &::v-deep .mtd-multiple-input-rendered {
        overflow-y: scroll;
        max-height: 240px;
      }
    }
  }
  .index-modal {
    .mtd-cascader {
      &::v-deep .mtd-multiple-input-rendered {
        overflow-y: scroll;
        max-height: 80px;
      }
    }
    .mtd-form-item {
      display: block;
      margin-bottom: 16px;
    }
  }
  .tags-container {
    flex-grow: 1;
    display: flex;
    flex-wrap: wrap;
    height: 45px;
    overflow-y: scroll;
    border: 1px solid rgba(0, 0, 0, 0.22);
    border-radius: 3px;
    padding: 1px 2px;
  }
  .mtd-btn {
    min-width: unset;
    margin-right: 0;
    padding-left: 8px;
    padding-right: 8px;
    &.mtd-btn-text-primary {
      padding-left: 0px;
      padding-right: 0px;
    }
  }
}
</style>
