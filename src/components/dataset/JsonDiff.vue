<template>
  <mtd-drawer @close="initmodal" v-model="drawerVisible" width="60vw" :title="`${language.toUpperCase() ?? 'JSON'}对比`">
    <div class="drawer-container" style="height: calc(100vh - 90px)">
      <DiffJson
        style="height: calc(100% - 40px)"
        height="calc(100% - 45px)"
        :oldJson="getObj(oldVal)"
        :newJson="getObj(newVal)"
        :language="language"
      />
      <mtd-button class="float-right" type="primary" @click="confirm">确定</mtd-button>
    </div>
  </mtd-drawer>
</template>

<script lang="ts">
import store from '@/store';
import '@snfe/backend-ui/lib/index.css';
import { Component, Prop, Vue } from 'vue-property-decorator';
import DiffJson from '@/views/DataManage/DataPolicy/components/DiffJson.vue';

@Component({ components: { DiffJson } })
export default class JsonDiff extends Vue {
  @Prop({ default: null }) oldVal?: object;
  @Prop({ default: null }) newVal?: object;
  @Prop({ default: 'json' }) language?: string;
  drawerVisible = false;

  getObj(v) {
    if (typeof v === 'string' && this.language === 'json') {
      return JSON.parse(v ?? '{}');
    }
    return v;
  }

  initmodal(v?: boolean) {
    this.drawerVisible = v ?? false;
    v || this.$emit('cancel', { old: this.oldVal, new: this.newVal });
  }

  confirm() {
    this.$emit('ok', { old: this.oldVal, new: this.newVal });
    this.initmodal();
  }
}

export function diffJson(oldVal: string | object, newVal: string | object, language?: string) {
  const instance: JsonDiff = new JsonDiff({ store, propsData: { oldVal, newVal, language } });
  instance.$mount();
  instance.initmodal(true);

  return new Promise((resolve, reject) => {
    instance.$on('ok', (v) => {
      instance.$destroy();
      return resolve(v);
    });
    instance.$on('cancel', (v) => {
      instance.$destroy();
      return reject(v);
    });
  });
}
</script>

<style lang="scss" scoped></style>
