<!-- 任务详情  路由：train-line-manage-task-detail-->
<template>
  <div>
    <!-- 头部 -->
    <TrainBreadcrumb :items="breadcrumbItems" />

    <!-- 任务信息 -->
    <TaskInfo class="mb-4" v-if="!shareManage" />
    <!-- 模型tab -->
    <TrainLineTab v-model="activeTab" @change="setActiveTab" :tabs="tabs" size="large" v-if="!shareManage" />
    <ModelCard v-show="activeTab === 'modelCard' && !shareManage" />
    <ModelCardPage v-show="activeTab === 'modelCard' && !shareManage" />
    <ManagePanel v-show="activeTab === 'managePanel'" :isShare="!!shareManage" />
    <Lineage v-show="activeTab === 'lineage' && !shareManage" />

    <!-- 节点tab -->
  </div>
</template>

<script setup lang="ts">
import TrainBreadcrumb from '@/views/train-line-manage/share/train-breadcrumb.vue';
import TrainLineTab from '@/views/train-line-manage/share/train-line-tab.vue';
import { useTaskStore } from '@/views/train-line-manage/utils/use-task-store';
import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import Lineage from '../components/lineage.vue';
import ManagePanel from '../components/manage-panel.vue';
import ModelCard from '../components/model-card.vue';
import TaskInfo from '../components/task-info.vue';
import ModelCardPage from '../model-card/model-card-page.vue';
const route = useRoute();
const router = useRouter();

const { activeTab, taskInfo, projectForm } = useTaskStore(Number(route.params.projectId), Number(route.params.recordId));
const tabs = ref([
  { id: 'modelCard', name: '模型卡片' },
  { id: 'managePanel', name: '管理面板' },
  { id: 'lineage', name: '血缘关系' },
]);

let { tabType, shareManage } = route.query;

const setActiveTab = (tab: string) => {
  activeTab.value = tab;
  router.replace({
    query: {
      tabType: tab,
    },
  });
};
onMounted(() => {
  const tab = Array.isArray(tabType) ? tabType[0] ?? '' : tabType ?? '';
  setActiveTab(tab);
});

const breadcrumbItems = ref<any[]>([]);
watch([() => taskInfo.value, () => projectForm.value], (newVal) => {
  breadcrumbItems.value = [
    { label: '训练线管理' },
    { label: projectForm.value.projectName },
    { label: taskInfo.value?.recordName || '--' },
  ];
});
</script>

<style scoped lang="scss">
::v-deep(.mtd-card) {
  border: none;
}
::v-deep(.mtd-card-body) {
  padding-top: 0 !important;
}
::v-deep(.mtd-card-always-shadow) {
  box-shadow: none;
}
</style>
