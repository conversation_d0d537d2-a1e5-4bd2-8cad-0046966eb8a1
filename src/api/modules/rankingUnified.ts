import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc 首页默认查10条
 * @desc id=1464673, catid=243411, projectId=35802, created=2024-08-29 09:01:25, last-modified=2025-02-26 10:41:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/rankingUnified/listRankDate)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1464673)
 */
export function postRankingUnifiedListRankDate(
  params: model.rankingUnified.IPostRankingUnifiedListRankDateParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.rankingUnified.IPostRankingUnifiedListRankDateResponse>('/webApi/rankingUnified/listRankDate', {
    data: pick(params, ['searchType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc 首页默认查10条
 * @desc id=1464666, catid=243411, projectId=35802, created=2024-08-29 08:15:50, last-modified=2025-02-06 14:16:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/rankingUnified/listData)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1464666)
 */
export function postRankingUnifiedListData(
  params: model.rankingUnified.IPostRankingUnifiedListDataParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.rankingUnified.IPostRankingUnifiedListDataResponse>('/webApi/rankingUnified/listData', {
    data: pick(params, ['rankType', 'rankDate', 'sortItemName', 'sortOrder', 'modelSourceListFilter', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc 首页默认查10条
 * @desc id=1482849, catid=243411, projectId=35802, created=2024-10-23 09:46:39, last-modified=2024-10-23 09:48:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/rankingUnified/listRankType)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1482849)
 */
export function postRankingUnifiedListRankType(
  params: model.rankingUnified.IPostRankingUnifiedListRankTypeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.rankingUnified.IPostRankingUnifiedListRankTypeResponse>('/webApi/rankingUnified/listRankType', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc 首页默认查10条
 * @desc id=1505705, catid=243411, projectId=35802, created=2025-02-06 14:24:39, last-modified=2025-02-06 14:26:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/rankingUnified/syncRanking)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505705)
 */
export function postRankingUnifiedSyncRanking(
  params: model.rankingUnified.IPostRankingUnifiedSyncRankingParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.rankingUnified.IPostRankingUnifiedSyncRankingResponse>('/webApi/rankingUnified/syncRanking', {
    data: pick(params, ['rankTypeList', 'rankDate']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1464679, catid=243411, projectId=35802, created=2024-08-29 09:02:56, last-modified=2025-02-17 10:39:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/rankingUnified/getConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1464679)
 */
export function postRankingUnifiedGetConfig(
  params: model.rankingUnified.IPostRankingUnifiedGetConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.rankingUnified.IPostRankingUnifiedGetConfigResponse>('/webApi/rankingUnified/getConfig', {
    data: pick(params, ['rankType', 'rankDate']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc 首页默认查10条
 * @desc id=1505703, catid=243411, projectId=35802, created=2025-02-06 14:19:36, last-modified=2025-02-06 14:23:46
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/rankingUnified/getData)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505703)
 */
export function postRankingUnifiedGetData(
  params: model.rankingUnified.IPostRankingUnifiedGetDataParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.rankingUnified.IPostRankingUnifiedGetDataResponse>('/webApi/rankingUnified/getData', {
    data: pick(params, ['rankType', 'rankDate', 'modelIdList', 'benchmarkModelVersion']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
