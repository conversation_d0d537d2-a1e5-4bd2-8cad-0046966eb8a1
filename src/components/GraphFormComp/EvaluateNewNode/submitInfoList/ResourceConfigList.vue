<template>
  <div>
    <mtd-table :data="tableData" class="resource-table" cell-class="resource-table-ceil">
      <mtd-table-column prop="clusterName" label="消费集群" show-overflow-tooltip :width="100">
        <template #default="{ row }">
          <mtd-tag> {{ row.clusterName.slice(0, 4) }}</mtd-tag
          ><br />
          <mtd-tag v-if="row.resourceType === 'gpuJob'" theme="purple">GPU集群</mtd-tag>
        </template>
      </mtd-table-column>
      <mtd-table-column prop="referencedConsumer" label="包含消费者" :width="220">
        <template #default="{ row }">
          <mtd-tooltip
            v-for="(define, index) in row.consumerDefineList"
            :key="`${define.vertexDefineId}${define.rootAnchor}`"
            size="small"
            placement="top"
            :content="`${vertexMap.get(define.vertexDefineId)?.anchor}/${define.rootAnchor}`"
          >
            <div class="font-bold">{{ `${vertexMap.get(define.vertexDefineId)?.label}/${define.label}` }}</div>
          </mtd-tooltip>
        </template>
      </mtd-table-column>
      <mtd-table-column prop="queue" label="队列" :width="450" show-overflow-tooltip>
        <template #default="{ row }">
          <mtd-tooltip placement="top-start">
            <div slot="content" :class="{ 'job-background': row.queue }">
              {{ row.queue || getDefaultQueue(row) }}
            </div>
            <div v-if="isEdit">
              <mtd-select
                v-model="row.queue"
                style="width: 400px"
                filterable
                @change="setDefaultGpu(row)"
                :placeholder="`(${getDefaultQueue(row)})`"
                :class="{ 'job-background': row.queue }"
              >
                <mtd-option v-if="row.queue" :label="row.queue" :value="row.queue"></mtd-option>
                <mtd-option
                  v-for="v in row.resourceType === 'gpuJob'
                    ? gpuQueueList.filter((x) => x.queue !== row.queue)
                    : cpuQueueList.filter((x) => x.queue !== row.queue)"
                  :key="v.queue"
                  :label="v.queue"
                  :value="v.queue"
                /> </mtd-select
              ><br />
              <mtd-button type="text-primary" v-if="row.queue" @click="setDefault(row, 'queue')"
                >恢复默认({{ getDefaultQueue(row) }})</mtd-button
              >
            </div>
            <div v-else>{{ row.queue || getDefaultQueue(row) }}</div>
          </mtd-tooltip>
        </template>
      </mtd-table-column>
      <!-- <mtd-table-column prop="dockerImage" label="Docker镜像" show-overflow-tooltip :width="150" /> -->
      <mtd-table-column prop="workers" label="实例数量" :width="80">
        <template #default="{ row }" v-if="isEdit">
          <mtd-input-number v-model="row.workers" :controls="false" style="width: 50px" :illegal-clear="true" :min="1" />
        </template>
      </mtd-table-column>
      <mtd-table-column prop="gpuSpec" label="显卡型号" :width="145">
        <template #default="{ row }" v-if="isEdit">
          <mtd-select v-if="row.resourceType === 'gpuJob'" v-model="row.gpuSpec" style="width: 115px">
            <mtd-option v-for="gpuSpec in gpuList(row.queue, row.useOtherGpuQueue)" :key="gpuSpec" :label="gpuSpec" :value="gpuSpec" />
          </mtd-select>
          <span v-else>-</span>
        </template>
        <template #default="{ row }" v-else>
          <span v-if="row.resourceType === 'gpuJob'">{{ row.gpuSpec }}</span>
          <span v-else>-</span>
        </template>
      </mtd-table-column>
      <template v-for="column in columns">
        <mtd-table-column :prop="column.prop" :label="column.label" :width="110">
          <template #default="{ row, $index }">
            <div class="resource-config-cell">
              <div class="input-wrapper">
                <mtd-input-number
                  v-if="(column.prop !== 'workerGpuCore' || row.resourceType === 'gpuJob') && isEdit"
                  v-model="row[column.prop]"
                  :placeholder="`(${defaultTableData[$index][column.prop]})`"
                  :controls="false"
                  :class="{ 'job-background': row[column.prop] }"
                  style="width: 60px"
                  :illegal-clear="true"
                  :min="1"
                />
                <span v-else :class="{ 'job-background': row[column.prop] }">{{
                  row[column.prop] || defaultTableData[$index][column.prop] || '-'
                }}</span>
              </div>
              <mtd-button v-if="row[column.prop] && isEdit" type="text-primary" class="reset-button" @click="setDefault(row, column.prop)">
                恢复默认({{ defaultTableData[$index][column.prop] }})
              </mtd-button>
            </div>
          </template>
        </mtd-table-column>
      </template>
    </mtd-table>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Watch, Weaver } from '@/decorators';
import { PluginBind, VertexDefine } from '@/model/customEval';
import evalCustomPlugin from '@/store/modules/evalCustomPlugin';
import evalFlowVersion from '@/store/modules/evalFlowVersion';
import modelManage from '@/store/modules/modelManage';
import { EvalSource } from '@/utils';
import { generateAnchorBindMap, handleSourceExtends } from '@/views/modelEval/utils';
import CryptoJS from 'crypto-js';
import { cloneDeep, flatten } from 'lodash';
interface ResourceConfig {
  clusterName: string;
  resourceType: string;
  gpuSpec: string;
  workerGpuCore?: number;
  workerCpuCore?: number;
  workerCpuMem?: number;
  workerInstanceCount?: number;
  workers: number;
  queue?: string | null;
  dockerImage: string;
  consumerDefineList: {
    label: string;
    rootAnchor: string;
    vertexDefineId: number;
    anchorPath: string;
  }[];
  useOtherGpuQueue?: boolean;
  groupKey?: string;
  pluginBind?: PluginBind;
  uniqueKey?: string;
  isFromBind?: boolean;
}

interface ConsumerInfo {
  servingType: string;
  rootPluginInterface: string;
  anchorPath: string;
  vertexAnchor: string;
  vertexDefineId: number;
  rootAnchor: string;
  label: string;
}
@Component
@Spectra
export default class ResourceConfigList extends Weaver(evalFlowVersion, modelManage, evalCustomPlugin) {
  @Prop()
  context: any;
  @Prop()
  isEdit!: boolean;
  @Prop()
  nodeContext: any;

  tableData: ResourceConfig[] = [];
  defaultTableData: ResourceConfig[] = [];
  consumerMap: Map<string, ConsumerInfo> = new Map();
  vertexMap: Map<number, VertexDefine> = new Map();

  columns = [
    {
      prop: 'workerGpuCore',
      label: '单机卡数(tp)',
    },
    {
      prop: 'workerCpuCore',
      label: '单机CPU数',
    },
    {
      prop: 'workerCpuMem',
      label: '单机内存(GB)',
    },
    {
      prop: 'workerInstanceCount',
      label: '分片数 (pp)',
    },
  ];

  created() {
    this.initData();
  }

  gpuList(queue, isOther = false) {
    const { gpuJobQueue, otherGpuJobQueue } = this.nodeContext.nodeParam;
    return this.gpuQueueList.find((x) => x.queue === (queue || (isOther ? otherGpuJobQueue || gpuJobQueue : gpuJobQueue)))?.gpuType || [];
  }

  getDefaultQueue(row: ResourceConfig) {
    const { cpuJobQueue, gpuJobQueue, otherGpuJobQueue } = this.nodeContext.nodeParam;
    if (row.resourceType === 'cpuJob') return cpuJobQueue;
    if (row.useOtherGpuQueue) return otherGpuJobQueue || gpuJobQueue;
    return gpuJobQueue;
  }

  /**
   * 由参数列表、全局参数、全局环境变量，实现出cluster聚合
   */
  @Watch('context.globalAnchorBindMapJson', { immediate: true })
  @Watch('context.runSpecAnchorBindMapJson', { immediate: true })
  async initData() {
    function containsOnlyOneSlash(str) {
      return (str.match(/\//g) || []).length === 1;
    }

    // 1. 生成每个runSpec的最终consumer bindList后，打平
    let extendedConsumerAnchorBindList: PluginBind[] = [];
    const globalAnchorBindMap: Map<string, PluginBind[]> = new Map(JSON.parse(this.context.globalAnchorBindMapJson || '[]'));
    const runSpecAnchorBindMap: Map<string, PluginBind[]> = new Map(JSON.parse(this.context.runSpecAnchorBindMapJson || '[]'));
    const pluginBindList = flatten([
      ...globalAnchorBindMap.values(),
      ...runSpecAnchorBindMap.values(),
      ...this.context.globalPluginBindList,
    ]);

    // 1.1 过滤consumer的bindList，以及实现类为DoNothingServer的绑定
    const consumerPluginBindList = pluginBindList
      .filter((bind) => containsOnlyOneSlash(bind.anchorPath))
      .filter((bind) => bind.pluginImplement !== 'DoNothingServer')
      .filter((bind) => !bind.extra?.isSymmetricDifference);

    // 1.2 bindList为执行时需要补充定义
    const defineList: any = await Promise.all(
      consumerPluginBindList
        .filter((bind) => bind.source === EvalSource.JOB)
        .map(async (bind) => {
          const res = await this.action$evalCustomPluginImplementDefine({
            interfaceName: bind.pluginInterface!,
            implementName: bind.pluginImplement!,
          });
          return {
            contextParams: res.evalCustomPluginImplement.contextParamsDefine,
            anchorPath: bind.anchorPath,
            source: EvalSource.PLUGIN_IMPLEMENT,
            pluginImplement: res.evalCustomPluginImplement.implementName,
            orderIndex: 1,
          };
        })
    );
    // 1.3 按照anchorPath聚合成map，然后处理继承
    const consumerAnchorBindMap = generateAnchorBindMap([...consumerPluginBindList, ...defineList]);
    // 1.4 处理完之后打平放入runSpec对应的bind。这里因为根节点一定是单实现，所以能直接flat
    extendedConsumerAnchorBindList.push(
      ...[...consumerAnchorBindMap.values()].map((bindList) => handleSourceExtends(bindList).pluginBindList).flat()
    );

    // 3. 用extendedBindList聚合节点
    const initMap = new Map<string, ResourceConfig>();
    for (const initResourceList of this.context.previewResourceConfigList) {
      initMap.set(initResourceList.clusterName, initResourceList);
    }

    extendedConsumerAnchorBindList.forEach((bind) => {
      // 3.1 生成clusterName
      const consumerInfo = this.consumerMap.get(bind.anchorPath!);
      if (!consumerInfo) return; // 绑定不在图上 虽然不太可能还是兜一下
      const paramsMap = this.generateContextParamsMap(bind);
      const { groupKey, clusterName } = this.generateClusterName(paramsMap, consumerInfo);
      // 3.2 填入聚合map
      const curConfig = initMap.get(clusterName);
      const isCpuJob = paramsMap.get('_RESOURCE_TYPE') === 'cpuJob';
      if (curConfig && curConfig.isFromBind) {
        curConfig.workerCpuCore = Math.max(curConfig.workerCpuCore!, paramsMap.get('_RESOURCE_WORKER_CPU_CORE'));
        curConfig.workerCpuMem = Math.max(curConfig.workerCpuMem!, paramsMap.get('_RESOURCE_WORKER_CPU_MEM'));
        curConfig.workerInstanceCount = Math.max(curConfig.workerInstanceCount!, paramsMap.get('_RESOURCE_WORKER_INSTANCE_COUNT'));
        curConfig.groupKey = groupKey;
        curConfig.pluginBind = bind;
        if (
          !curConfig.consumerDefineList.find(
            (x) => x.rootAnchor === consumerInfo.rootAnchor && x.vertexDefineId === consumerInfo.vertexDefineId
          )
        )
          curConfig.consumerDefineList.push({
            label: consumerInfo.label,
            rootAnchor: consumerInfo.rootAnchor,
            vertexDefineId: consumerInfo.vertexDefineId,
            anchorPath: consumerInfo.anchorPath,
          });
      } else {
        const isOther = consumerInfo.anchorPath !== 'EvalModelInfer/model';
        initMap.set(clusterName, {
          clusterName,
          resourceType: paramsMap.get('_RESOURCE_TYPE'),
          queue: null,
          dockerImage: paramsMap.get('_DOCKER_IMAGE'),
          workers: isCpuJob ? 4 : 1,
          gpuSpec: isCpuJob
            ? null
            : this.gpuList(null, isOther)?.find((x) => x === paramsMap.get('_RESOURCE_GPU_SPEC'))
            ? paramsMap.get('_RESOURCE_GPU_SPEC')
            : this.gpuList(null, isOther)[0],
          workerGpuCore: isCpuJob ? 0 : paramsMap.get('_RESOURCE_WORKER_GPU_CORE'),
          workerCpuCore: paramsMap.get('_RESOURCE_WORKER_CPU_CORE'),
          workerCpuMem: paramsMap.get('_RESOURCE_WORKER_CPU_MEM'),
          workerInstanceCount: paramsMap.get('_RESOURCE_WORKER_INSTANCE_COUNT'),
          consumerDefineList: [
            {
              label: consumerInfo.label,
              rootAnchor: consumerInfo.rootAnchor,
              vertexDefineId: consumerInfo.vertexDefineId,
              anchorPath: consumerInfo.anchorPath,
            },
          ],
          useOtherGpuQueue: isOther,
          groupKey,
          isFromBind: true,
        });
      }
    });

    this.defaultTableData = cloneDeep([...initMap.values()]);
    this.tableData = this.mergeResourceConfigList(
      [...initMap.values()].map(({ clusterName, consumerDefineList, resourceType, workers, gpuSpec, dockerImage }) => ({
        clusterName,
        resourceType,
        workers,
        gpuSpec,
        dockerImage,
        consumerDefineList,
        useOtherGpuQueue: consumerDefineList?.[0]?.vertexDefineId !== 2,
      }))
    );
  }

  /**
   * 把原始值merge到新的clusterMap当中
   */
  mergeResourceConfigList(defaultConfig: ResourceConfig[]) {
    return defaultConfig.map((config) => {
      const foundConfig = this.context.resourceConfigList.find((x) => x.clusterName === config.clusterName);
      const res = {
        ...config,
        ...(!foundConfig.resourceType || foundConfig?.resourceType === config.resourceType ? foundConfig : {}),
      };
      const uniqueKey = this.genUniqueKey(res);
      return {
        ...res,
        uniqueKey,
      };
    });
  }

  genUniqueKey(config: ResourceConfig) {
    const defaultConfig = this.defaultTableData.find((x) => x.clusterName === config.clusterName)!;
    const changedConfig = this.context.resourceConfigList.find((x) => x.clusterName === config.clusterName);
    if (!defaultConfig.groupKey) return;
    const { resourceType, gpuSpec, workerGpuCore, workerCpuCore, workerCpuMem, workerInstanceCount, dockerImage } = {
      ...defaultConfig,
      ...Object.fromEntries(Object.entries(changedConfig || {}).filter(([_, value]) => value !== null)),
    };
    const resourceConfigKey = `rt=${resourceType}||gs=${gpuSpec}||gpu=${workerGpuCore}||cpu=${workerCpuCore}||mem=${workerCpuMem}||ins=${workerInstanceCount}||dc=${dockerImage}`;

    const globalAnchorBindMap: Map<string, PluginBind[]> = new Map(JSON.parse(this.context.globalAnchorBindMapJson || '[]'));
    const pluginBindList: PluginBind[] = flatten([...globalAnchorBindMap.values(), ...this.context.globalPluginBindList]).filter((bind) =>
      bind.anchorPath.startsWith(config.consumerDefineList[0].anchorPath)
    );

    const consumerAnchorBindMap = generateAnchorBindMap(pluginBindList);
    const extendedPluginBindList = [...consumerAnchorBindMap.values()]
      .map((bindList) => handleSourceExtends(bindList).pluginBindList)
      .flat();

    extendedPluginBindList.sort((o1, o2) => {
      const compare = o1.anchorPath!.localeCompare(o2.anchorPath!);
      if (compare === 0) {
        return o1.orderIndex - o2.orderIndex;
      }
      return compare;
    });
    const pluginBindKey = extendedPluginBindList.reduce((prevBindKey, bind) => {
      const params = [...bind.contextParams].filter((x) => !x.name.startsWith('_RESOURCE'));
      params.sort((o1, o2) => {
        return o1.name.localeCompare(o2.name);
      });
      const contextParamKey = params.reduce((prevParamKey, param) => {
        prevParamKey += `<${param.name}=${param.value}>`;
        return prevParamKey;
      }, '');

      prevBindKey += `[[${bind.anchorPath}||${bind.orderIndex}||${bind.pluginImplement}||${contextParamKey}]]`;
      return prevBindKey;
    }, '');

    return `${defaultConfig.groupKey}&&${resourceConfigKey}&&${pluginBindKey}`;
  }

  /**
   * 由流程图定义聚合消费者map
   */
  async generateFlowVertexMap() {
    const {
      evalFlowVersion: { vertexList },
    } = await this.action$evalFlowVersion({
      flowId: this.context.flowId,
      version: this.context.flowVersion,
    });
    const consumerMap: Map<string, ConsumerInfo> = new Map();
    const vertexMap: Map<number, VertexDefine> = new Map();

    vertexList.forEach(({ vertexDefine }) => {
      vertexMap.set(vertexDefine.id, vertexDefine as any);
      vertexDefine.consumerDefineList.forEach((consumer) => {
        const anchorPath = `${vertexDefine.anchor}/${consumer.rootAnchor}`;
        consumerMap.set(anchorPath, {
          servingType: consumer.servingType,
          rootPluginInterface: consumer.rootPluginInterface,
          anchorPath: `${vertexDefine.anchor}/${consumer.rootAnchor}`,
          vertexAnchor: vertexDefine.anchor,
          rootAnchor: consumer.rootAnchor,
          label: consumer.label,
          vertexDefineId: vertexDefine.id,
        });
      });
    });
    this.vertexMap = vertexMap;
    this.consumerMap = consumerMap;
  }

  generateContextParamsMap(pluginBind: PluginBind) {
    const contextParamsMap = new Map();
    pluginBind.contextParams.forEach((params) => {
      // 来源可能是参数本身、底下修改过后的globalAliasParams和envGlobalAliasParams
      switch (params.globalAliasType) {
        case 'USER_INPUT':
          const globalAliasParams = this.context.globalAliasParams.find((x) => x.name === params.globalAliasName)?.value;
          contextParamsMap.set(params.name, globalAliasParams || params.value);
          break;
        case 'ENV':
          const envGlobalAliasParams = this.context.envGlobalAliasParams.find((x) => x.name === params.globalAliasName)?.value;
          contextParamsMap.set(params.name, envGlobalAliasParams); // 这里理论上是一定有的吧
          break;
        default:
          contextParamsMap.set(params.name, params.value);
      }
    });
    return contextParamsMap;
  }

  generateClusterName(paramsMap, consumerInfo: ConsumerInfo) {
    let groupKey = consumerInfo.rootPluginInterface + '||' + consumerInfo.servingType + '||';
    if (consumerInfo.servingType === 'CONSUMER') {
      // 特殊逻辑：不同节点定义的 agent 一定被不同的集群处理，不再能够共享集群。
      if (consumerInfo.rootPluginInterface === 'AgentWrapperI') {
        groupKey += consumerInfo.vertexAnchor + '||';
      }
      groupKey += [
        paramsMap.get('_RESOURCE_TYPE'),
        paramsMap.get('_DOCKER_IMAGE'),
        paramsMap.get('_RESOURCE_GPU_SPEC') || 'null',
        paramsMap.get('_RESOURCE_WORKER_GPU_CORE') || '0',
      ].join('||');
    } else {
      groupKey += consumerInfo.anchorPath;
    }
    return { groupKey, clusterName: CryptoJS.MD5(groupKey).toString() };
  }

  @Watch('tableData', { deep: true })
  tableDataChanged() {
    this.tableData.forEach((x) => (x.uniqueKey = this.genUniqueKey(x)!));
    this.$emit('change', this.tableData, this.defaultTableData);
  }

  setDefaultGpu(row) {
    if (row.resourceType !== 'gpuJob') return;
    row.gpuSpec = this.gpuList(row.queue, row.useOtherGpuQueue)[0];
  }

  setDefault(row, prop) {
    this.$set(row, prop, null);
    if (prop === 'queue') this.setDefaultGpu(row);
  }

  async mounted() {
    if (!this.context.flowId) return;
    await this.generateFlowVertexMap();
    this.initData();
  }
}
</script>
<style lang="less" scoped>
.resource-table {
  width: 100%;
  overflow: scroll;
}
::v-deep .resource-table-ceil {
  vertical-align: top !important;
}

.job-background {
  background-color: rgba(255, 0, 0, 0.3);
  ::v-deep .mtd-input-number {
    background-color: rgba(0, 0, 0, 0);
  }
  ::v-deep .mtd-input {
    background-color: rgba(0, 0, 0, 0);
  }
}
.resource-config-cell {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.input-wrapper {
  height: 32px;
  display: flex;
  align-items: center;
}

.reset-button {
  margin-top: 4px;
  padding: 0;
}
</style>
