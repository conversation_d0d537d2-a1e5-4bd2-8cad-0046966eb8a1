const SET_TABLE_DATA = 'SET_TABLE_DATA';
const TRANSFORM_TABLE_DATA = 'TRANSFORM_TABLE_DATA';
const FILTER_SORT_TABLE_DATA = 'FILTER_SORT_TABLE_DATA';
const SLICE_TABLE_DATA = 'SLICE_TABLE_DATA';
const GET_CONTRAST_ROW_DATA = 'GET_CONTRAST_ROW_DATA';
const EXPORT_TABLE_DATA = 'EXPORT_TABLE_DATA';

const GET_SCATTER_CHART_DATA = 'GET_SCATTER_CHART_DATA';
const GET_BAR_CHART_DATA = 'GET_BAR_CHART_DATA';

// 表格数据
let tableData;
// 表头数据
let tableColumn;
// 固定表头
let fixColumns;
// 固定表头长度
let fixColumnsLength = 0;

// 过滤的表格数据
let filterTableData;
// 过滤的表头数据
let filterTableColumn;
// 裁剪的树结构

// 对比模式
let patternComparison;

// 评测集数据规模中出现的规模，取法如下：遍历所有calculateUnitList取distinct。
let groupValueUniqueArr = [];

let evalDataSizeMetaListCopy;
let statMetaListCopy;

// 子线程修改主线程数据 需要发送回去
let childThreadChangeFilterGroup = false;

function getRandomPaleColor() {
  const hue = Math.floor(Math.random() * 360); // 完全随机化色调
  const saturation = Math.floor(Math.random() * 40) + 10;
  const lightness = Math.floor(Math.random() * 11) + 78;

  return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
}

function assignColorsForDuplicateNames(arr) {
  const nameCounts = {};
  const nameColorMap = {};

  // 统计每个name出现的次数
  arr.forEach((item) => {
    const name = item.name;
    if (nameCounts[name]) {
      nameCounts[name]++;
    } else {
      nameCounts[name] = 1;
    }
  });

  // 查找重复的name并分配颜色
  Object.keys(nameCounts).forEach((name) => {
    if (nameCounts[name] > 1) {
      let color;
      do {
        color = getRandomPaleColor();
      } while (Object.values(nameColorMap).includes(color));

      nameColorMap[name] = color;
    }
  });

  return nameColorMap;
}

// 综合胜率
const cmbinedWinningPercentage = {
  Integration: '综合评测胜率',
  Subjectivity: '主观评测胜率',
  Objectivity: '客观评测准确率',
};

const get = (object, path, defaultValue) => {
  const keys = Array.isArray(path) ? path : path.split('.');
  let result = object;
  for (const key of keys) {
    result = result ? result[key] : undefined;
    if (result === undefined) {
      return defaultValue;
    }
  }

  return result;
};

const setTableData = (data) => {
  const {
    PARAMS,
    evalDataSizeMetaList,
    showDataSubSetDescription,
    filterGroup,
    modelType,
    comparison,
    patternComparisonWorker,
    statMetaList,
  } = data;
  const { highlightModelList, order, evalDataSizeConfiguration, modelTags = [], unfoldDefault = false } = PARAMS;
  let targettableData = {};
  if (modelType === 'dataSubSet') {
    targettableData = PARAMS.tableDataDataSubSet;
  } else if (modelType === 'category') {
    targettableData = PARAMS.tableDataCategory;
  }

  evalDataSizeMetaListCopy = evalDataSizeMetaList;
  statMetaListCopy = statMetaList;

  // 不使用模版
  if (comparison && PARAMS.unTableDataModelEffects) targettableData = PARAMS.unTableDataModelEffects;
  const { modelList = [], evalModelList = [] } = targettableData;

  if (!patternComparisonWorker) patternComparison = null;
  if (!patternComparison && !patternComparisonWorker) {
    patternComparison = {};
    evalModelList.forEach((item) => {
      patternComparison[item.llmModel.id] = {
        family: item.family,
        name: item.name,
        modelId: item.modelId,
        checked: false,
        modelType: '',
        label: item?.label,
        llmModelId: item.llmModel.id,
      };
    });
  } else {
    patternComparison = patternComparisonWorker;
  }

  const collectAllCalculateUnitLists = (obj) => {
    let resultArray = [];

    // const recurse = (currentObject) => {
    //   if (Array.isArray(currentObject)) {
    //     currentObject.forEach((item) => recurse(item));
    //   } else if (typeof currentObject === 'object' && currentObject !== null) {
    //     Object.values(currentObject).forEach((value) => {
    //       if (value && value.calculateUnitList) {
    //         resultArray = resultArray.concat(value.calculateUnitList);
    //       }
    //       recurse(value);
    //     });
    //   }
    // };

    const recurse = (currentObject) => {
      if (currentObject?.valueList) {
        currentObject.valueList.forEach((value) => {
          value.unitList.forEach((item) => {
            resultArray = resultArray.concat(item.result?.evalDataSize);
          });
        });
      } else {
        currentObject.forEach((cur) => {
          cur.groupList.forEach((group) => {
            group.groupValue.unitList.forEach((item) => {
              resultArray = resultArray.concat(item.result?.result.calculateUnitList);
            });
          });
        });
      }
    };

    // 根据传入的对象结构，只会存在 filterGroupResultList 或 dataSubSetFilterResult
    if (obj.filterGroupResultList && obj.filterGroupResultList.length) {
      recurse(obj.filterGroupResultList);
    } else if (obj.dataSubSetFilterResult) {
      recurse(obj.dataSubSetFilterResult);
    }

    return resultArray;
  };

  // 工具函数
  const getEvalDataSize = (evalDataSize) => evalDataSizeMetaList.find((item) => item.name === evalDataSize)?.label || '';
  const getStatName = (statName) => {
    if (!statName) {
      return '平均值';
    }

    const statMeta = statMetaListCopy?.find((item) => item.name === statName);
    return statMeta ? statMeta.label : '平均值';
  };
  const getEvalDataSizeArr = (model) => {
    let calculateUnitList = [];
    // if (model.filterGroupResultList) {
    //   calculateUnitList = collectAllCalculateUnitLists(model.filterGroupResultList);
    // } else if (model.dataSubSetFilterResult) {
    //   calculateUnitList = collectAllCalculateUnitLists(model.dataSubSetFilterResult);
    // }

    calculateUnitList = collectAllCalculateUnitLists(model);

    // const modelEvalDataSize = getEvalDataSize(model.evalDataSize);
    const arr = calculateUnitList.map((item) => getEvalDataSize(item?.evalDataSize ? item.evalDataSize : item)); // 筛选出与 evalDataSize 不同的项
    const filteredArr = arr.filter(Boolean);
    // filteredArr.push(modelEvalDataSize);
    const uniqueArr = [...new Set(filteredArr)];
    groupValueUniqueArr = uniqueArr;
    return uniqueArr;
  };

  const getRunSpecSetId = (res) => {
    if (res?.length) {
      const runSpecSetId = res[0].runSpecSetId;
      return runSpecSetId;
    } else {
      return '';
    }
  };

  const countUnequalCategories = (calculateUnitList, evalDataSize) => {
    if (!calculateUnitList || calculateUnitList?.length === 0) {
      return {};
    }

    const categoryCounts = {};
    // 遍历 calculateUnitList，统计每个种类不相等的数量
    calculateUnitList?.forEach((unit) => {
      // 假设 unit.category 是种类的属性，unit.size 是需要与 evalDataSize 比较的值
      // if (unit.evalDataSize !== evalDataSize) {
      // 如果 categoryCounts 中还没有这个种类，初始化为 0
      if (!categoryCounts[unit.evalDataSize]) {
        categoryCounts[unit.evalDataSize] = 0;
      }

      // 对应种类的计数加 1
      categoryCounts[unit.evalDataSize]++;
      // }
    });

    // 创建一个新对象来存储转换后的键
    const transformedCategoryCounts = {
      groupValueUniqueArr, // 初始化 count 为一个空数组
      count: [],
    };
    for (const key in categoryCounts) {
      // 假设 getEvalDataSize 是一个函数，用于处理 key
      const transformedKey = getEvalDataSize(key);
      transformedCategoryCounts.count.push(transformedKey); // 将转换后的键添加到数组中
      transformedCategoryCounts[transformedKey] = categoryCounts[key]; // 将计数添加到对象中
    }

    transformedCategoryCounts.count.sort((a, b) => a - b);
    transformedCategoryCounts.groupValueUniqueArr = [...new Set(transformedCategoryCounts.groupValueUniqueArr)];
    return transformedCategoryCounts;
  };

  const getToolTipHTML = (fieldData, { isAverage, rowData, index } = {}) => {
    const { calculateUnitList, hasStatSubSetCount, totalSubSetCount } = fieldData;
    if (hasStatSubSetCount === 1 && totalSubSetCount === 1 && calculateUnitList?.[0].totalInstanceCount > 0) {
      return `聚合数据条数: ${calculateUnitList[0]?.instanceCount}`;
    }

    // todo 显示内容等待确认
    let result = `评测子集数量：有效${hasStatSubSetCount} / 总计${totalSubSetCount}`;
    if (hasStatSubSetCount < totalSubSetCount) {
      let children = `${(fieldData.result?.notCalculateDataSubSetList || fieldData.result?.result.notCalculateDataSubSetList || [])
        .map((_) => `<li>${_.dataSetName || ''}:${_.name}</li>`)
        .join('')}`;

      // todo 什么神奇的特殊逻辑吧
      if (isAverage) {
        // todo clean & dirty ????? calculateUnitList
        children = (rowData.groupValue?.unitList[index].result?.result?.notCalculateDataSubSetList || [])
          .map((_) => `<li>${_.dataSetName || ''}:${_.name}</li>`)
          .join('');
      }

      let children1 = (rowData.groupValue?.unitList[index].result?.result?.instanceIncompleteList || [])
        .map((_) => `<li>${_.dataSetName || ''}:${_.name}</li>`)
        .join('');

      result += `
      <details>
      <summary style="cursor: pointer">查看详情</summary>
      <div>无效数据子集：<i onclick="navigator.clipboard.writeText(event.target.parentElement.nextElementSibling.innerText)" style="transform: scale(1.02);cursor: pointer" class="icon mtdicon-copy-o"></i></div>
      <ul style="max-width: 800px; max-height: 100px; overflow: auto;">
      ${children}
      </ul>
       <div>指标未更新数据子集：<i onclick="navigator.clipboard.writeText(event.target.parentElement.nextElementSibling.innerText)" style="transform: scale(1.02);cursor: pointer" class="icon mtdicon-copy-o"></i></div>
      <ul style="max-width: 800px; max-height: 100px; overflow: auto;">
      ${children1}
      </ul>
     </details>
      `;
    } else {
      let children1 = (rowData.groupValue?.unitList[index].result?.result?.instanceIncompleteList || [])
        .map((_) => `<li>${_.dataSetName || ''}:${_.name}</li>`)
        .join('');
      result += `
      <details>
      <summary style="cursor: pointer">查看详情</summary>
       <div>指标未更新数据子集：<i onclick="navigator.clipboard.writeText(event.target.parentElement.nextElementSibling.innerText)" style="transform: scale(1.02);cursor: pointer" class="icon mtdicon-copy-o"></i></div>
      <ul style="max-width: 800px; max-height: 100px; overflow: auto;">
      ${children1}
      </ul>
     </details>
      `;
    }

    return result;
  };

  const getFieldValue = (groupValue, rowData, { isMean: isAverage } = {}) => {
    const fieldData = groupValue?.unitList;
    return fieldData.map((item, i) => {
      return {
        value: item.value || item.mean,
        secondValue: item.secondValue,
        hasStatSubSetCount: item.hasStatSubSetCount,
        totalSubSetCount: item.totalSubSetCount,
        confidenceInterval: item.confidenceInterval,
        subjectivityIsFromModelEval: item.subjectivityIsFromModelEval,
        tokenOutputAvg: item.tokenOutputAvg,
        tikTokenOutputAvg: item.tikTokenOutputAvg,
        textLenOutputAvg: item.textLenOutputAvg,
        // 跳转链接需要
        categoryId: groupValue.categoryId,
        dataSubSetId: groupValue.dataSubSetId,
        groupIndex: groupValue?.groupIndex || 0,
        runSpecSetId: getRunSpecSetId(item?.result?.calculateUnitList || []),
        // tooltip
        toolTipHTML: getToolTipHTML(item, { isAverage, rowData, index: i }),
        // EvalDataSize提示
        ...(evalDataSizeConfiguration && {
          diffCountUnequalCategories: countUnequalCategories(
            item.result?.calculateUnitList || item.result?.result.calculateUnitList,
            rowData.evalDataSize
          ),
          diffEvalDataSize: (item.result?.calculateUnitList || item.result?.result.calculateUnitList)
            ?.map((J) => ({
              evalDataSize: getEvalDataSize(J.evalDataSize),
              dataSetLabel: J.dataSetLabel,
              dataSubSetLabel: J.dataSubSetLabel,
              subSetId: J.subSetId,
            }))
            .sort((a, b) => a.evalDataSize - b.evalDataSize),
        }),
        evalDataSize: item.result?.evalDataSize,
        master: rowData?.master || false,
        masterModelName: rowData?.masterModelName || '',
        // todo 补齐数据
      };
    });
  };

  // 表头处理
  const firstData = evalModelList[0];
  fixColumns = [];
  // 根据参数设置固定表头
  if (order) fixColumns.push({ title: '序号', type: 'seq', fixed: 'left', width: 80, colId: 'seq', field: 'seq' });
  const modelLabelObj = { title: '模型名称', field: 'modelLabel', fixed: 'left', width: 300 };
  const modelLabelSecond = { title: '评测模型名', field: 'modelLabelSecond', fixed: 'left', width: 300 };
  const evalDataSizeObj = { title: '评测集数据规模', field: 'evalDataSize', fixed: 'left' };
  const groupSelect = {
    title: '筛选组',
    field: 'groupSelect backColor-240-247-255',
    fixed: 'left',
    // width: comparison ? 400 : 300,
    group: {
      flag: true,
      selectGroup: filterGroup.selectGroup,
      type: filterGroup.type === 'OFF' ? 'ON' : 'OFF',
    },
  };

  const { filterGroupResultList, dataSubSetFilterResult } = firstData;
  childThreadChangeFilterGroup = false;
  try {
    if (unfoldDefault && filterGroupResultList?.length === 1 && filterGroupResultList[0]?.groupList?.length === 1) {
      let title = '';
      if (modelType === 'dataSubSet') {
        title = `${filterGroupResultList[0].name}${filterGroupResultList[0].groupList[0]?.valueList[0]?.label ? '-' : '-'}${getStatName(
          filterGroupResultList[0]?.statName
        )}`;

        if (comparison && !filterGroupResultList[0].groupList[0]?.groupType)
          title = `${filterGroupResultList[0].name}-${getStatName(filterGroupResultList[0]?.statName)}`;
      } else if (modelType === 'category') {
        title = `${filterGroupResultList[0].name}${filterGroupResultList[0].groupList[0]?.valueList[0]?.label ? '-' : ''}${
          filterGroupResultList[0].groupList[0]?.valueList[0]?.label || ''
        }`;
      }
      filterGroup.selectGroup = {
        firstIndex: 0,
        secondIndex: 0,
        title,
      };

      filterGroup.type = 'ON';
      childThreadChangeFilterGroup = filterGroup;
    } else if (unfoldDefault && dataSubSetFilterResult) {
      filterGroup.type = 'ON';
      filterGroup.selectGroup = {
        firstIndex: 0,
        secondIndex: 0,
        title: '平均值（无权重）',
        valueListLength:
          modelType === 'category' ? dataSubSetFilterResult?.valueList?.length > 1 : dataSubSetFilterResult?.valueList?.length > 0,
      };
      childThreadChangeFilterGroup = filterGroup;
    }
  } catch (e) {
    console.error(e, '<= 默认展开筛选组有问题');
  }

  // 不要删，可能是暂时删除的功能
  // 模型名称
  // if (PARAMS.filterColumn && PARAMS.filterColumn.modelFilter) {
  //   modelLabelObj.filters = evalModelList.reduce(
  //     (acc, item) => {
  //       const { modelInfo } = item.llmModel;
  //       const { modelCheckpoint } = modelInfo;
  //       const { modelName } = modelInfo.modelMeta;
  //       const label = `${modelName} / ${modelCheckpoint}`;
  //       if (!acc.tempSet.has(label)) {
  //         acc.tempSet.add(label);
  //         acc.result.push({ label, value: label });
  //       }

  //       return acc;
  //     },
  //     { tempSet: new Set(), result: [] }
  //   ).result;
  // }

  // 不要删，可能是暂时删除的功能
  // 评测集数据规模
  // if (PARAMS.filterColumn && PARAMS.filterColumn.evalDataSizeFilter) {
  //   const uniqueLabels = new Set();
  //   evalDataSizeObj.filters = evalModelList
  //     .map((item) => {
  //       let label = getEvalDataSizeArr(item)
  //         .map((items) => items?.toString())
  //         .join('/');
  //       if (!label) label = getEvalDataSize(item.evalDataSize);

  //       if (uniqueLabels.has(label)) {
  //         return null;
  //       }

  //       uniqueLabels.add(label);
  //       return { label, value: label };
  //     })
  //     .filter((item) => item !== null);
  // }

  fixColumns.push(modelLabelObj);
  fixColumns.push(modelLabelSecond);
  // if (isEvalDataSize) fixColumns.push(evalDataSizeObj);
  if (filterGroup.selectGroup.firstIndex !== -1) {
    let widthComparison = comparison ? 400 : 300;
    if (firstData.filterGroupResultList && firstData.filterGroupResultList.length) {
      groupSelect.title = `${filterGroup.selectGroup.title}`;
      groupSelect.field = `col_${filterGroup.selectGroup.firstIndex}_${filterGroup.selectGroup.secondIndex} backColor-240-247-255`;
      groupSelect.cleanAndDirty =
        firstData.filterGroupResultList[filterGroup.selectGroup.firstIndex]?.groupList[filterGroup.selectGroup.secondIndex]?.groupValue
          .unitList.length !== 1;

      groupSelect.width =
        firstData.filterGroupResultList[filterGroup.selectGroup.firstIndex]?.groupList[filterGroup.selectGroup.secondIndex]?.groupValue
          .unitList.length !== 1
          ? widthComparison
          : 200;
    } else if (firstData.dataSubSetFilterResult) {
      groupSelect.title = '平均值（无权重）';
      groupSelect.cleanAndDirty = firstData.dataSubSetFilterResult?.groupValue.unitList.length !== 1;
      groupSelect.width = firstData.dataSubSetFilterResult?.groupValue.unitList.length !== 1 ? widthComparison : 200;
      groupSelect.field = `col_${0}_${0} backColor-240-247-255`;
    }

    fixColumns.push(groupSelect);
  }

  // 不要删，可能是暂时删除的功能
  if (Array.isArray(modelTags)) {
    modelTags.forEach((item) => {
      const filterObj = { title: item.title, field: item.tagName };
      if (item.filterFlag === true) {
        const uniqueCombinationSet = new Set();
        filterObj.filters = modelList
          .map((model) => {
            const value = get(model, 'llmModel.tags', [])
              .find((tag) => tag.name === item.tagName)
              ?.values.join();
            return { value, label: value };
          })
          .filter((obj) => {
            const combination = `${obj.value}_${obj.label}`;
            if (uniqueCombinationSet.has(combination)) return false;
            uniqueCombinationSet.add(combination);
            return true;
          });
      }

      fixColumns.push(filterObj);
    });
  }

  // 更新固定列长度
  fixColumnsLength = fixColumns.length;
  fixColumns.forEach((item) => {
    item.slots = { default: 'col', header: 'header' };
    item.isFixedColumn = true;
    item.colId = item.field || item.type;
  });

  let columns = [];
  let WIDTHCOLUMNS = 200;

  // 获取动态表头
  if (!firstData?.winRateValue && filterGroup.selectGroup.firstIndex !== -1) {
    let columnsMaster = [];
    if (firstData.filterGroupResultList && firstData.filterGroupResultList.length) {
      columnsMaster =
        firstData.filterGroupResultList[filterGroup.selectGroup.firstIndex]?.groupList[filterGroup.selectGroup.secondIndex].valueList;
      if (columnsMaster.length > 0 && modelType === 'category') columnsMaster = columnsMaster.slice(1);
    } else if (firstData.dataSubSetFilterResult) {
      columnsMaster = firstData.dataSubSetFilterResult.valueList;
      if (columnsMaster.length > 0 && modelType === 'category') columnsMaster = columnsMaster.slice(1);
    }

    columns = columnsMaster.map((item, index) => {
      const { dataSubSetId = '', label = '', name } = item;

      let targetDataSet;
      if (showDataSubSetDescription) {
        targetDataSet = item;
      }

      const colId = `col_${index}`;
      if (item.unitList.length !== 1) WIDTHCOLUMNS = comparison ? 400 : 300;

      return {
        colId,
        field: colId,
        title: name,
        hasEmpty: false,
        fixed: null,
        width: WIDTHCOLUMNS,
        isfilterGroup: true,
        dataSubSetId: dataSubSetId,
        slots: { default: 'col', header: 'header' },
        showBlackTag: targetDataSet?.publicStatus === 'BLACK',
        description: showDataSubSetDescription && targetDataSet?.description,
        cleanAndDirty: item.unitList.length !== 1,
      };
    });
  } else {
    // 全展示筛选组的时候
    if (firstData.filterGroupResultList && firstData.filterGroupResultList.length) {
      columns = firstData.filterGroupResultList.reduce((pre, item, index) => {
        const groupListLength = item?.groupList.length;
        const arr = item?.groupList.map((J, K) => {
          const { name = '' } = item;
          let title = '';

          if (modelType === 'dataSubSet') {
            title =
              groupListLength === 1
                ? `${name}${J.valueList[0]?.label ? '-' : '-'}${getStatName(item?.statName)}`
                : `${name}-${cmbinedWinningPercentage[J.groupType]}${J.groupType === 'Integration' ? '' : `-${J.runSpecSetName}`}`;

            if (comparison && !J.groupType) title = `${name}-${getStatName(item?.statName)}`;
          } else if (modelType === 'category') {
            title =
              groupListLength === 1
                ? `${name}${J.valueList[0]?.label ? '-' : ''}${J.valueList[0]?.label || ''}`
                : `${name}-${cmbinedWinningPercentage[J.groupType]}${J.groupType === 'Integration' ? '' : `-${J.runSpecSetName}`}${
                    J.valueList?.length >= 1 ? `-${J.valueList[0].label}` : ''
                  }`;
          }

          const colId = `col_${index}_${groupListLength === 1 ? 0 : K} backColor-240-247-255`;
          if (J?.groupValue.unitList.length !== 1) WIDTHCOLUMNS = comparison ? 400 : 300;
          return {
            colId,
            field: colId,
            title,
            hasEmpty: false,
            fixed: null,
            width: WIDTHCOLUMNS,
            slots: { default: 'col', header: 'header' },
            group: {
              flag: true,
              selectGroup: {
                firstIndex: index,
                secondIndex: groupListLength === 1 ? 0 : K,
                title,
                valueListLength: modelType === 'category' ? J.valueList.length > 1 : J?.valueList.length > 0,
              },
              type: filterGroup.type === 'OFF' ? 'ON' : 'OFF',
            },
            isfilterGroup: true,
            cleanAndDirty: J?.groupValue.unitList.length !== 1,
          };
        });

        return pre.concat(arr);
      }, []);
    } else if (firstData.dataSubSetFilterResult) {
      const colId = `col_${0}_${0} backColor-240-247-255`;
      if (firstData.dataSubSetFilterResult?.groupValue.unitList.length !== 1) WIDTHCOLUMNS = comparison ? 400 : 300;
      columns = [
        {
          colId,
          field: colId,
          title: '平均值（无权重）',
          hasEmpty: false,
          fixed: null,
          width: WIDTHCOLUMNS,
          slots: { default: 'col', header: 'header' },
          group: {
            flag: true,
            selectGroup: {
              firstIndex: 0,
              secondIndex: 0,
              title: '平均值（无权重）',
              valueListLength:
                modelType === 'category'
                  ? firstData.dataSubSetFilterResult?.valueList.length > 1
                  : firstData.dataSubSetFilterResult?.valueList.length > 0,
            },
            type: filterGroup.type === 'OFF' ? 'ON' : 'OFF',
          },
          cleanAndDirty: firstData.dataSubSetFilterResult?.groupValue.unitList.length !== 1,
        },
      ];
    }
  }

  tableColumn = [...fixColumns, ...columns];
  const colorenum = {
    HF: 'rgb(47, 198, 200)',
    FT: 'rgb(182, 162, 222)',
    OTHER: 'rgb(255, 209, 0)',
  };

  const colorObj = assignColorsForDuplicateNames(
    evalModelList.map((group) => ({
      name: `${group.llmModel.modelInfo?.modelMeta.modelName} / ${group.llmModel.modelInfo?.modelCheckpoint} / ${group.llmModel.modelInfo?.modelFormat}`,
    }))
  );

  if (filterGroup.selectGroup.firstIndex === -1) {
    tableData = evalModelList.map((group, groupIndex) => {
      const filterGroupResultList = group.filterGroupResultList || [];
      const dataSubSetFilterResult = group.dataSubSetFilterResult || {};
      // 获取固定列的行数据
      const fixedColumnRowData = [];
      // 序号列
      if (order) fixedColumnRowData.push({ type: 'seq', data: { value: dataIndex } });
      fixedColumnRowData.push({
        type: 'modelLabel',
        data: {
          value: {
            modelName: group.llmModel.modelInfo?.modelMeta.modelName,
            modelCheckpoint: group.llmModel.modelInfo?.modelCheckpoint,
            modelFormat: group.llmModel.modelInfo?.modelFormat,
          },
        },
      });
      fixedColumnRowData.push({
        type: 'modelLabelSecond',
        data: { value: group.llmModel.label, modelId: group.modelId, llmModelId: group.llmModel.id, label: group?.label },
      });
      fixedColumnRowData.push({
        type: 'evalDataSize',
        data: { value: getEvalDataSize(group.evalDataSize), diffCalculateUnitList: getEvalDataSizeArr(group) },
      });

      if (filterGroupResultList && filterGroupResultList.length) {
        filterGroupResultList.forEach((item, index) => {
          const { groupList } = item || {};
          const arr = groupList?.map((R, I) => {
            const valueList = R?.valueList || [];
            return {
              type: `col_${index}_${groupList.length === 1 ? 0 : I} backColor-240-247-255`,
              data: {
                value: comparison
                  ? R?.groupValue.unitList
                  : getFieldValue({ ...R?.groupValue, groupIndex: index, categoryId: valueList[0]?.categoryId }, group),
              },
            };
          });

          arr?.forEach((l) => fixedColumnRowData.push(l));
        });
      } else if (dataSubSetFilterResult?.groupValue && dataSubSetFilterResult.valueList) {
        fixedColumnRowData.push({
          type: `col_${0}_${0} backColor-240-247-255`,
          data: { value: getFieldValue(dataSubSetFilterResult?.groupValue, group) },
        });
      }

      let rowDataHasEmpty = false;
      const valueMap = fixedColumnRowData.reduce((obj, item, index) => {
        item.data.isFixedColumn = true;
        obj[item.type] = item.data;

        if (item.data.value && item.data.value.length) {
          // clean & dirty 时取的第一个算 是否完整
          let { hasStatSubSetCount, totalSubSetCount } = item.data.value[0];
          hasStatSubSetCount = Number(hasStatSubSetCount);
          totalSubSetCount = Number(totalSubSetCount);
          if (!(hasStatSubSetCount === 0 && totalSubSetCount === 0) && hasStatSubSetCount < totalSubSetCount) {
            const tableColumnHasEmpty = tableColumn.find((colId) => colId.colId === item.type);
            if (tableColumnHasEmpty) tableColumnHasEmpty.hasEmpty = true;
            rowDataHasEmpty = true;
          }
        }
        return obj;
      }, {});

      const { modelShortName, benchmarking = false, modelName, modelStage } = group.llmModel.modelInfo?.modelMeta;
      return {
        rowKey: `row_${groupIndex}`, // 行key
        groupIndex, // 数据索引
        valueMap, // 动态列数据
        hasEmpty: rowDataHasEmpty,
        modelLabel: {
          modelName: benchmarking || modelStage === 'ORIGIN' ? modelShortName : modelName,
          modelCheckpoint: group.llmModel.modelInfo?.modelCheckpoint,
          modelFormat: group.llmModel.modelInfo?.modelFormat,
        },
        model: { family: group.family, name: group.name, modelId: group.modelId, label: group?.label }, // 模型信息, 后续数据结构可能会变
        backColor: colorObj[`${modelName} / ${group.llmModel.modelInfo?.modelCheckpoint}`],
        hoverAndOther: {
          modelPath: group.llmModel.modelInfo?.modelPath,
          modelFormat: group.llmModel.modelInfo?.modelFormat,
          color: colorenum[group.llmModel.modelInfo?.modelFormat] || colorenum.OTHER,
          hoverValue: modelName,
          modelShortName: group.llmModel.modelInfo?.modelMeta?.modelShortName,
          benchmarking,
        },
        master: group.master,
      };
    });

    return tableData;
  }

  const seletGroup = evalModelList.map((model) => {
    if (model.filterGroupResultList?.length) {
      return {
        ...model,
        ...model.filterGroupResultList[filterGroup.selectGroup.firstIndex]?.groupList[filterGroup.selectGroup.secondIndex],
        name: model.name,
      };
    } else if (model.dataSubSetFilterResult) {
      return {
        ...model,
        ...model.dataSubSetFilterResult,
      };
    }
  });

  // 行数据处理
  tableData = seletGroup.map((model, dataIndex) => {
    let valueList = model.valueList || [];
    if (valueList.length > 0 && modelType === 'category') valueList = valueList.slice(1);
    // 获取固定列的行数据
    const fixedColumnRowData = [];
    if (order) fixedColumnRowData.push({ type: 'seq', data: { value: dataIndex } });
    fixedColumnRowData.push({
      type: 'modelLabel',
      data: {
        value: {
          modelName: model.llmModel.modelInfo?.modelMeta.modelName,
          modelCheckpoint: model.llmModel.modelInfo?.modelCheckpoint,
          modelFormat: model.llmModel.modelInfo?.modelFormat,
        },
      },
    });

    // 评估模型名称
    fixedColumnRowData.push({
      type: 'modelLabelSecond',
      data: { value: model.llmModel.label, modelId: model.modelId, llmModelId: model.llmModel.id },
    });
    fixedColumnRowData.push({
      type: 'evalDataSize',
      data: { value: getEvalDataSize(model.evalDataSize), diffCalculateUnitList: getEvalDataSizeArr(model) },
    });
    fixedColumnRowData.push({
      type: `col_${filterGroup.selectGroup.firstIndex}_${filterGroup.selectGroup.secondIndex} backColor-240-247-255`,
      data: {
        value: comparison
          ? model.groupValue.unitList
          : getFieldValue(
              {
                ...model?.groupValue,
                categoryId: model.valueList[0]?.categoryId,
                name: valueList[0]?.name,
              },
              model
            ),
      },
    });

    if (Array.isArray(modelTags)) {
      modelTags.forEach((item) => {
        fixedColumnRowData.push({
          type: item.tagName,
          data: { value: get(model, 'llmModel.tags', []).find((tag) => tag.name === item.tagName)?.values, customTags: true },
        });
      });
    }

    const valueMap = fixedColumnRowData.reduce((obj, item) => {
      item.data.isFixedColumn = true;
      obj[item.type] = item.data;
      return obj;
    }, {});

    // 行数据是否有空
    let rowDataHasEmpty = false;
    valueList.length &&
      valueList.reduce((obj, valueItem, index) => {
        const colId = `col_${index}`;
        obj[colId] = comparison ? valueItem.unitList : getFieldValue(valueItem, model);
        // 列index
        const columnIndex = fixColumnsLength + index;
        // 列数据是否有空
        // const columnDataHasEmpty = tableColumn[columnIndex]?.hasEmpty;
        const result = tableColumn.find((item) => item.dataSubSetId === valueItem.dataSubSetId);
        const columnDataHasEmpty = result.hasEmpty;
        if (!rowDataHasEmpty || !columnDataHasEmpty) {
          // todo 筛选可能有点问题
          let { hasStatSubSetCount, totalSubSetCount } = valueItem.unitList[0];
          hasStatSubSetCount = Number(hasStatSubSetCount);
          totalSubSetCount = Number(totalSubSetCount);

          // 不能同时为0 且 存在空的数据
          if (!(hasStatSubSetCount === 0 && totalSubSetCount === 0) && hasStatSubSetCount < totalSubSetCount) {
            // eslint-disable-next-line no-multi-assign
            rowDataHasEmpty = tableColumn[columnIndex].hasEmpty = true;
          }
        }

        return obj;
      }, valueMap);

    const { modelShortName, benchmarking = false, modelName, modelStage } = model.llmModel.modelInfo?.modelMeta;
    return {
      rowKey: `row_${dataIndex}`, // 行key
      dataIndex, // 数据索引
      hasEmpty: rowDataHasEmpty, // 行是否有空数据
      modelLabel: {
        modelName: benchmarking || modelStage === 'ORIGIN' ? modelShortName : modelName,
        modelCheckpoint: model.llmModel.modelInfo?.modelCheckpoint,
        modelFormat: model.llmModel.modelInfo?.modelFormat,
      },
      highlight: !!highlightModelList?.find((item) => item.name === model.name && item.family === model.family), // 是否高亮
      valueMap, // 动态列数据
      model: { family: model.family, name: model.name, modelId: model.modelId, label: model?.label }, // 模型信息, 后续数据结构可能会变
      backColor: colorObj[`${modelName} / ${model.llmModel.modelInfo?.modelCheckpoint}`],
      hoverAndOther: {
        modelPath: model.llmModel.modelInfo?.modelPath,
        modelFormat: model.llmModel.modelInfo?.modelFormat,
        color: colorenum[model.llmModel.modelInfo?.modelFormat] || colorenum.OTHER,
        hoverValue: modelName,
        modelShortName: model.llmModel.modelInfo?.modelMeta?.modelShortName,
        benchmarking,
      },
      master: model.master,
    };
  });
};

const filterSortTableData = (data, { exportTableData } = {}) => {
  // exportTableData = true 导出时将 filterTableData 全部数据一同导出，避免筛选时导出无关数据
  const {
    PARAMS,
    rowSortOrderData,
    columnSortOrderData,
    // confidenceIntervalName,
    rowFilter,
    columnFilter,
    filterModelProperties,
    patternComparisonWorker,
  } = data;
  // 对比值判断
  const { modelTags = [] } = PARAMS;

  // 行筛选
  if (rowFilter === 'noEmpty') {
    filterTableData = tableData.filter((item) => !item.hasEmpty);
  } else {
    filterTableData = [...tableData];
  }

  // 行排序
  if (rowSortOrderData.colId !== null) {
    const { order, colId } = rowSortOrderData;
    const columnData = tableColumn.find((item) => item.colId === colId);
    if (columnData) {
      const { field } = columnData;

      filterTableData.sort((a, b) => {
        const aData = Array.isArray(a.valueMap[field])
          ? a.valueMap[field][rowSortOrderData.index]
          : a.valueMap[field].value[rowSortOrderData.index];
        const bData = Array.isArray(b.valueMap[field])
          ? b.valueMap[field][rowSortOrderData.index]
          : b.valueMap[field].value[rowSortOrderData.index];

        if (colId !== 'modelLabel' && colId !== 'modelLabelSecond' && (!aData || !bData)) {
          return a.dataIndex - b.dataIndex;
        }

        const winningList = Object.keys({
          comprehensiveValue: '综合评测胜率',
          objectiveValue: '客观评测准确率',
          subjectiveValue: '主观评测胜率',
        });
        let compareResult;

        const valueA = a.valueMap[field]?.value;
        const valueB = b.valueMap[field]?.value;
        // 字符串列做处理
        if (colId === 'modelLabel') {
          const strA = valueA?.modelName + valueA?.modelCheckpoint + valueA?.modelFormat;
          const strB = valueB?.modelName + valueB?.modelCheckpoint + valueB?.modelFormat;
          compareResult = strA > strB ? 1 : -1;
        } else if (colId === 'evalDataSize' || colId === 'modelLabelSecond') {
          compareResult = valueA > valueB ? 1 : -1;
        } else if (colId === 'mean' || colId === 'macroMean' || winningList.includes(colId)) {
          compareResult = (+aData.value || 0) - (+bData.value || 0);
        } else {
          if (+aData.secondValue !== void 0 || +aData.secondValue !== null) {
            compareResult = (+aData.secondValue || 0) - (+bData.secondValue || 0);
          }

          compareResult = (+aData.value || 0) - (+bData.value || 0);
        }

        return order === 'descending' ? -compareResult : compareResult;
      });
    } else {
      filterTableData.sort((a, b) => a.dataIndex - b.dataIndex);
    }
  } else {
    filterTableData.sort((a, b) => a.dataIndex - b.dataIndex);
  }

  // && filterModelProperties.values.length !== 0
  if (filterModelProperties) {
    const filterFunction = (item) => {
      const valueModelLabel = item.llmModel ? item.llmModel.label : item.modelLabel;
      const { evalDataSize } = item.valueMap;
      let valueEvalDataSize = evalDataSize.value;
      if (evalDataSize.diffCalculateUnitList.length > 0) {
        valueEvalDataSize = evalDataSize.diffCalculateUnitList.join('/');
      }

      return filterModelProperties.filters.every((filterItem) => {
        if (filterItem.field === 'modelLabel') {
          return filterItem.values.some((ii) => valueModelLabel === ii);
        }

        if (filterItem.field === 'evalDataSize') {
          return filterItem.values.some((ii) => valueEvalDataSize === ii);
        }

        // 外部传入 有风险
        try {
          if (modelTags.length !== 0) {
            return modelTags.some((customizeTag) => {
              if (filterItem.field === customizeTag.tagName) {
                return filterItem.values.some((ii) => (item.valueMap[customizeTag.tagName].value || []).join() === (ii || ''));
              }

              return false;
            });
          }
        } catch (err) {
          console.error(err);
          return false;
        }

        return true;
      });
    };

    filterTableData = filterTableData.filter(filterFunction);
  }

  // 列筛选
  if (columnFilter === 'hasEmpty') {
    filterTableColumn = tableColumn.filter((item) => item.isFixedColumn || item.hasEmpty);
  } else if (columnFilter === 'noHasEmpty') {
    filterTableColumn = tableColumn.filter((item) => item.isFixedColumn || !item.hasEmpty);
  } else {
    filterTableColumn = [...tableColumn];
  }

  // 列排序
  if (columnSortOrderData.rowKey !== null) {
    const { rowKey, order } = columnSortOrderData;
    const rowData = filterTableData.find((item) => item.rowKey === rowKey);
    if (rowData) {
      filterTableColumn.sort((a, b) => {
        if (a.isFixedColumn || b.isFixedColumn) {
          return 0;
        }

        const aData = Array.isArray(rowData.valueMap[a.field]) ? rowData.valueMap[a.field][0] : rowData.valueMap[a.field].value[0];
        const bData = Array.isArray(rowData.valueMap[b.field]) ? rowData.valueMap[b.field][0] : rowData.valueMap[b.field].value[0];

        if (!aData || !bData) {
          return a.field - b.field;
        }

        let compareResult = 0;
        if (+aData.secondValue !== void 0 || +aData.secondValue !== null) {
          compareResult = (+aData.secondValue || 0) - (+bData.secondValue || 0);
        }

        compareResult = (+aData.value || 0) - (+bData.value || 0);
        return order === 'descending' ? -compareResult : compareResult;
      });
    } else {
      filterTableColumn.sort((a, b) => (a.isFixedColumn || b.isFixedColumn ? 0 : a.field - b.field));
    }
  } else {
    filterTableColumn.sort((a, b) => (a.isFixedColumn || b.isFixedColumn ? 0 : a.field - b.field));
  }

  if (patternComparisonWorker) patternComparison = patternComparisonWorker;
  const returnObj = {
    patternComparison,
    tableColumn: filterTableColumn,
    tableData: filterTableData.map((item) => ({ rowKey: item.rowKey, ...(item.highlight && { highlight: item.highlight }) })),
    childThreadChangeFilterGroup,
  };

  if (exportTableData) returnObj.filterTableData = filterTableData;
  return returnObj;
};

const sliceTableData = (data) => {
  let { rowKeys, columnIds } = data;
  columnIds = fixColumns.map((item) => item.colId).concat(columnIds.split(','));
  rowKeys = rowKeys.split(',');
  return filterTableData.reduce((obj, item) => {
    if (rowKeys.includes(item.rowKey)) {
      obj[item.rowKey] = {
        ...item,
        valueMap: columnIds.reduce((obj1, item1) => {
          if (item.valueMap[item1]) {
            obj1[item1] = item.valueMap[item1];
          }

          return obj1;
        }, {}),
      };
    }

    return obj;
  }, {});
};

const getContraRowData = (data) => {
  return tableData.find((item) => item.rowKey === data.rowKey);
};

const getExportTableData = ({ showSecondValue = false, data, comparison = false }) => {
  const res = filterSortTableData(data, { exportTableData: true });
  const { withConfidenceInterval, PARAMS, tokenOutputAvg, tikTokenOutputAvg, textLenOutputAvg } = data;
  const { unTableDataModelEffects } = PARAMS;
  const getModelName = (model) => {
    return `${model.llmModel.modelInfo?.modelMeta.modelShortName || '-'} / ${model.llmModel.modelInfo?.modelMeta.modelName} / ${
      model.llmModel.modelInfo?.modelCheckpoint
    } / ${model.llmModel.label}`;
  };

  const getEvalDataSize = (evalDataSize) => evalDataSizeMetaListCopy?.find((item) => item.name === evalDataSize)?.label || '';

  let base = '';
  let baseline = '';
  if (unTableDataModelEffects) {
    base = `对照模型: ${getModelName(unTableDataModelEffects.baseModel)} (${getEvalDataSize(
      unTableDataModelEffects.baseModel.evalDataSize
    )})`;
    baseline = unTableDataModelEffects.baseLineModel
      ? `基线模型: ${getModelName(unTableDataModelEffects.baseLineModel)} (${getEvalDataSize(
          unTableDataModelEffects.baseLineModel.evalDataSize
        )})`
      : '';
  }

  const tableHeader = res.tableColumn
    .map((column) => {
      let cleanAndDirtyStr = '';
      if (column.cleanAndDirty) cleanAndDirtyStr = ' clean/dirty ';
      return `"${column.title} ${cleanAndDirtyStr}"`;
    })
    .join(',');
  const tableBody = res.filterTableData.map((row) =>
    res.tableColumn
      .map((column) => {
        const { value, secondValue, hasStatSubSetCount, totalSubSetCount, confidenceInterval, diffCalculateUnitList } =
          row.valueMap[column.field];

        const secondFlag = secondValue !== void 0 && secondValue !== null;
        const secondValueStr = showSecondValue && secondFlag && secondValue ? `${secondValue}±` : '';
        let valueStr;
        if (column.field === 'seq') {
          valueStr = value + 1;
        } else if (column.field === 'modelLabel') {
          valueStr = `${row?.modelLabel?.modelName}/${row?.modelLabel?.modelCheckpoint}/ ${row?.hoverAndOther?.modelFormat}` || '-';
        } else if (column.field === 'modelLabelSecond') {
          valueStr = value || '-';
        } else if (Array.isArray(value) || Array.isArray(row.valueMap[column.field])) {
          let valueListMaster = [];
          if (Array.isArray(value)) valueListMaster = value;
          if (Array.isArray(row.valueMap[column.field])) valueListMaster = row.valueMap[column.field];
          return `"${valueListMaster
            .map((item) => {
              const {
                value,
                secondValue,
                hasStatSubSetCount,
                totalSubSetCount,
                confidenceInterval,
                basePercentage,
                mean,
                baseLineDiffValue,
              } = item;
              let basePercentageCopy = '';
              let baseLineDiffValueCopy = '';
              if (comparison) {
                basePercentageCopy = ` / ${basePercentage || 'DIV/0!'}`;
                baseLineDiffValueCopy = baseLineDiffValue ? ` (${baseLineDiffValue || ''})` : '';
              }
              const secondValueStr = showSecondValue && secondFlag && secondValue ? `${secondValue}±` : '';
              const confidenceIntervalStr = withConfidenceInterval && confidenceInterval ? `±${confidenceInterval}` : '';
              const hasEmptyStr = hasStatSubSetCount !== totalSubSetCount ? ' *' : '';
              const tokenOutputAvgStr = tokenOutputAvg ? `平均token数：${item?.tokenOutputAvg ?? ''}` : '';
              const tikTokenOutputAvgStr = tikTokenOutputAvg ? `平均tiktoken数：${item?.tikTokenOutputAvg ?? ''}` : '';
              const textLenOutputAvgStr = textLenOutputAvg ? `平均字符数：${item?.textLenOutputAvg ?? ''}` : '';
              const additionalInfo = [tokenOutputAvgStr, tikTokenOutputAvgStr, textLenOutputAvgStr]
                .filter((str) => str.trim() !== '')
                .join('\n');

              return `${secondValueStr}${
                mean || value || '-'
              }${baseLineDiffValueCopy}${basePercentageCopy}${confidenceIntervalStr}${hasEmptyStr}\n${additionalInfo}`;
            })
            .join(' / ')}"`;
        }

        const confidenceIntervalStr = withConfidenceInterval && confidenceInterval ? `±${confidenceInterval}` : '';
        const hasEmptyStr = hasStatSubSetCount !== totalSubSetCount ? ' *' : '';

        return `"${secondValueStr}${valueStr}${confidenceIntervalStr}${hasEmptyStr}"`;
      })
      .join(',')
  );

  if (base) return [base, baseline, tableHeader, ...tableBody].join('\r\n');
  return [tableHeader, ...tableBody].join('\r\n');
};

const getScatterChartData = () => {
  if (tableColumn.length - fixColumnsLength === 1) {
    const column = tableColumn.find((item) => !item.isFixedColumn);
    const { colId } = column;
    const filterData = tableData
      .filter((item) => item.valueMap[colId].value)
      .sort((a, b) => b.valueMap[colId].value - a.valueMap[colId].value);
    return {
      column,
      chartData: filterData.map((item, index) => {
        const toolTipData = {
          index: index + 1,
          modelName: item.modelLabel,
          value: item.valueMap[colId].value,
          percent: `${Math.round(((index + 1) / filterData.length) * 100)}%`,
          tip: `${tableData.length - filterData.length}个模型未参与排名`,
        };
        return [index + 1, item.valueMap[colId].value, toolTipData];
      }),
    };
  }
};

const getBarChartData = () => {
  if (tableColumn.length - fixColumnsLength === 1) {
    const fixDecimal = (i, length = 2) => parseFloat(i.toFixed(length));
    const getRowValue = (range) => ({ range, value: 0, modelList: [] });

    let currentKey = '0';
    const column = tableColumn.find((item) => !item.isFixedColumn);
    const { colId } = column;
    const filterData = tableData.filter((item) => item.valueMap[colId].value);
    const rows = Object.values(
      [...filterData, ...Array.from({ length: 20 }).map((_, i) => fixDecimal(i * 0.05))]
        .sort((a, b) => {
          if (typeof a === 'number' && typeof b === 'number') {
            return a - b;
          }

          if (typeof a === 'number') {
            return a - parseFloat(b.valueMap[colId].value) || 1;
          }

          if (typeof b === 'number') {
            return parseFloat(a.valueMap[colId].value) - b || -1;
          }

          return parseFloat(a.valueMap[colId].value) - parseFloat(b.valueMap[colId].value);
        })
        .reduce(
          (obj, item) => {
            if (typeof item === 'number') {
              const key = `${fixDecimal(item)}-${fixDecimal(item + 0.05)}`;
              obj[key] = getRowValue(key);
              currentKey = key;
            } else {
              obj[currentKey].modelList.push(item);
              obj[currentKey].value++;
            }

            return obj;
          },
          { ['0']: getRowValue('0') }
        )
    );

    return { title: `${column.title} 上各模型的得分分布`, rows };
  }
};

self.addEventListener('message', function (event) {
  let data;
  if (typeof event.data === 'string') {
    data = JSON.parse(event.data);
  } else {
    data = event.data;
  }

  switch (data.type) {
    case SET_TABLE_DATA:
      self.postMessage({ type: SET_TABLE_DATA, messageId: data.messageId });
      break;
    case TRANSFORM_TABLE_DATA:
      setTableData(data);
      self.postMessage({ type: TRANSFORM_TABLE_DATA, messageId: data.messageId });
      break;
    case FILTER_SORT_TABLE_DATA:
      self.postMessage({ type: FILTER_SORT_TABLE_DATA, ...filterSortTableData(data), messageId: data.messageId });
      break;
    case SLICE_TABLE_DATA:
      self.postMessage({ type: SLICE_TABLE_DATA, messageId: data.messageId, tableData: sliceTableData(data) });
      break;
    case GET_CONTRAST_ROW_DATA:
      self.postMessage({ type: GET_CONTRAST_ROW_DATA, messageId: data.messageId, rowData: getContraRowData(data) });
      break;
    case EXPORT_TABLE_DATA:
      self.postMessage({ type: EXPORT_TABLE_DATA, messageId: data.messageId, tableData: getExportTableData(data) });
      break;
    case GET_SCATTER_CHART_DATA:
      self.postMessage({ type: GET_SCATTER_CHART_DATA, messageId: data.messageId, data: getScatterChartData() });
      break;
    case GET_BAR_CHART_DATA:
      self.postMessage({ type: GET_SCATTER_CHART_DATA, messageId: data.messageId, data: getBarChartData() });
      break;
    default:
      if (data.type || data.messageId) {
        self.postMessage({ type: data.type, messageId: data.messageId });
      }

      break;
  }
});
