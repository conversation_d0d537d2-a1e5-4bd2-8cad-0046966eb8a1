<template>
  <div>
    <mtd-switch v-model="type" @change="changeType" />
    <div class="version-name" v-if="type">
      <div class="item common-width" style="margin-bottom: 12px">
        <div class="label">目标数据集：</div>
        <mtd-select v-if="isEdit" :value="model" :filterable="true" @change="change">
          <mtd-option v-for="v in dataSet?.list" :key="v.name" :label="v.name" :value="v.name" />
        </mtd-select>
        <span v-else>{{ model }}</span>
      </div>
      <div class="item common-width">
        <div class="label">版本名称：</div>
        <mtd-input v-if="isEdit" :value="context.nodeParam.targetDatasetVersionName" @change="changeVerName"> </mtd-input>
        <span v-else>{{ context.nodeParam.targetDatasetVersionName }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, <PERSON><PERSON><PERSON>, <PERSON> } from '@/decorators';
import dataManage from '@/store/modules/dataManage';

@Component
@Spectra
export default class DataRegister extends Weaver(dataManage) {
  @Prop()
  model!: any;
  @Prop()
  context: any;
  @Prop()
  globalContext: any;
  @Prop()
  isEdit!: boolean;

  type = true;

  change(v) {
    this.$emit('change', v);
  }

  changeType() {
    const { nodeParam } = this.context;
    this.change('');
    this.$set(nodeParam, 'targetDatasetVersionName', '');
  }

  changeVerName(v) {
    const { nodeParam } = this.context;
    this.$set(nodeParam, 'targetDatasetVersionName', v);
  }

  created() {
    this.action$dataSet({ param: { name: '' }, pageNumber: 1, pageSize: 9999 });
    const { nodeParam } = this.context;
    this.type = !!(this.model || nodeParam.targetDatasetVersionName);
  }
}
</script>
<style lang="scss" scoped>
.item {
  display: flex;
  .label {
    width: 100px;
    text-align: right;
    margin-right: 12px;
  }
  .mtd-select {
    flex-grow: 1;
  }
  .mtd-input-wrapper {
    flex-grow: 1;
  }
}
</style>
