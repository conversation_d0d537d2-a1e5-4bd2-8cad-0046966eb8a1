import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1373241, catid=227021, projectId=35802, created=2024-02-27 14:36:04, last-modified=2024-03-07 15:49:45
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runCustomScriptTemplate/getDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373241)
 */
export function postRunCustomScriptTemplateGetDetail(
  params: model.runCustomScriptTemplate.IPostRunCustomScriptTemplateGetDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runCustomScriptTemplate.IPostRunCustomScriptTemplateGetDetailResponse>(
    '/webApi/runCustomScriptTemplate/getDetail',
    {
      data: pick(params, ['id']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1376926, catid=227021, projectId=35802, created=2024-03-06 11:19:25, last-modified=2024-03-06 11:22:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runCustomScriptTemplate/getDetailByVersion)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1376926)
 */
export function postRunCustomScriptTemplateGetDetailByVersion(
  params: model.runCustomScriptTemplate.IPostRunCustomScriptTemplateGetDetailByVersionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runCustomScriptTemplate.IPostRunCustomScriptTemplateGetDetailByVersionResponse>(
    '/webApi/runCustomScriptTemplate/getDetailByVersion',
    {
      data: pick(params, ['customScriptTemplateId', 'version']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc limit/offSet 不填，默认查询全部，可用于新增评测执行单元集合时获取脚本列表使用
 * @desc id=1373233, catid=227021, projectId=35802, created=2024-02-27 14:33:35, last-modified=2024-03-08 11:28:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runCustomScriptTemplate/simpleList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373233)
 */
export function postRunCustomScriptTemplateSimpleList(
  params: model.runCustomScriptTemplate.IPostRunCustomScriptTemplateSimpleListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runCustomScriptTemplate.IPostRunCustomScriptTemplateSimpleListResponse>(
    '/webApi/runCustomScriptTemplate/simpleList',
    {
      data: pick(params, ['keywordFilter', 'limit', 'offset']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1373254, catid=227021, projectId=35802, created=2024-02-27 14:40:24, last-modified=2024-03-08 14:49:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runCustomScriptTemplate/setStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373254)
 */
export function postRunCustomScriptTemplateSetStatus(
  params: model.runCustomScriptTemplate.IPostRunCustomScriptTemplateSetStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runCustomScriptTemplate.IPostRunCustomScriptTemplateSetStatusResponse>(
    '/webApi/runCustomScriptTemplate/setStatus',
    {
      data: pick(params, ['id', 'statusFilter']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1373264, catid=227021, projectId=35802, created=2024-02-27 14:50:07, last-modified=2024-03-07 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runCustomScriptTemplate/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373264)
 */
export function postRunCustomScriptTemplateUpsert(
  params: model.runCustomScriptTemplate.IPostRunCustomScriptTemplateUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runCustomScriptTemplate.IPostRunCustomScriptTemplateUpsertResponse>('/webApi/runCustomScriptTemplate/upsert', {
    data: pick(params, ['id', 'name', 'content', 'version', 'ownerList', 'saveType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
