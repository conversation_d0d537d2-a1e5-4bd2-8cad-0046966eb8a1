import { postDataHubS3MultipartUploadSign as getMultipartSign, postDataHubS3SingleUploadSign as getSign } from '@/api/modules/dataHub';
import { IPostDataHubS3MultipartUploadSignResponse, IPostDataHubS3SingleUploadSignResponse } from '@/model/modules/dataHub';
import createUpload from '@ai/mss-upload-js';

export async function uploadFile(option) {
  const { file } = option;

  let bKey = '';
  let errMsg = '';

  async function getUploadSign() {
    let res: IPostDataHubS3SingleUploadSignResponse = {};
    res = await getSign({ fileName: file.name });
    bKey = res?.data?.key ?? '';
    res && Object.assign(res, { code: res.rescode });
    return res;
  }
  let uploadInstance = createUpload(
    {
      signatureFunc: getUploadSign,
      isHttps: true,
      bucket: 'llm-data-cube',
      prefix_type: 's3_style',
      accept: ['.xlsx', '.json', '.txt', '.jsonl'],
      hashMode: true,
      exactKey: true,
      file,
      s3_host: 's3plus-bj02.vip.sankuai.com',
      uploadHeaders: {
        'Content-Disposition': 'attachment',
      },
      onProgress(percent) {
        option.onProgress({ percent });
      },
      onStart() {},
      onSuccess(fileUrl) {},
      onError(errorMsg) {},
      onFileInfo(fileInfo) {},
    },
    1
  );
  await uploadInstance.upload();
  if (errMsg) {
    return Promise.reject(errMsg);
  }
  return Promise.resolve({ key: bKey });
}

export async function uploadPartFile(option, key: string) {
  const { file } = option;
  let errMsg = '';

  async function getUploadSign(pureOpts, opts, params) {
    let res: IPostDataHubS3MultipartUploadSignResponse = {};
    res = await getMultipartSign(params);
    res && Object.assign(res, { code: res.rescode });
    return res;
  }

  let uploadInstance = createUpload(
    {
      signatureFunc: getUploadSign,
      isHttps: true,
      bucket: 'llm-data-cube',
      prefix_type: 's3_style',
      accept: ['.xlsx', '.json', '.txt', '.jsonl'],
      hashMode: false,
      file,
      exactKey: true,
      key,
      s3_host: 's3plus-bj02.vip.sankuai.com',
      sliceSize: 1024 * 1024 * 100,
      validateFile(file) {
        return true;
      },
      onProgress(percent) {
        option.onProgress({ percent });
      },
      onStart() {},
      onSuccess(fileUrl) {},
      onError(errorMsg) {},
      onFileInfo(fileInfo) {},
    },
    2
  );
  await uploadInstance.upload();
  if (errMsg) {
    return Promise.reject(errMsg);
  }
  return Promise.resolve({ key });
}
