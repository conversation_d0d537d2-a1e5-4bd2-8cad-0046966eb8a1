import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1383808, catid=240661, projectId=35802, created=2024-03-19 13:10:18, last-modified=2024-04-03 11:07:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLog/getDataSubSetLog)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1383808)
 */
export function postModelEvalLogGetDataSubSetLog(
  params: model.modelEvalLog.IPostModelEvalLogGetDataSubSetLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLog.IPostModelEvalLogGetDataSubSetLogResponse>('/webApi/modelEvalLog/getDataSubSetLog', {
    data: pick(params, [
      'dataSubSetIdList',
      'metaVersionId',
      'actionType',
      'userMis',
      'startTime',
      'endTime',
      'sortConfig',
      'offset',
      'limit',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1383799, catid=240661, projectId=35802, created=2024-03-19 13:06:45, last-modified=2024-09-20 10:08:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLog/getModelLog)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1383799)
 */
export function postModelEvalLogGetModelLog(
  params: model.modelEvalLog.IPostModelEvalLogGetModelLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLog.IPostModelEvalLogGetModelLogResponse>('/webApi/modelEvalLog/getModelLog', {
    data: pick(params, ['modelList', 'actionType', 'userMis', 'startTime', 'endTime', 'sortConfig', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1425779, catid=240661, projectId=35802, created=2024-06-21 16:06:18, last-modified=2024-06-21 16:06:18
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLog/getModelLog_1718957098631)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425779)
 */
export function postModelEvalLogGetModelLog1718957098631(
  params: model.modelEvalLog.IPostModelEvalLogGetModelLog1718957098631Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLog.IPostModelEvalLogGetModelLog1718957098631Response>('/webApi/modelEvalLog/getModelLog_1718957098631', {
    data: pick(params, ['modelList', 'actionType', 'userMis', 'startTime', 'endTime', 'sortConfig', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1383700, catid=240661, projectId=35802, created=2024-03-19 10:01:06, last-modified=2024-04-07 15:20:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalLog/getCategoryLog)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1383700)
 */
export function postModelEvalLogGetCategoryLog(
  params: model.modelEvalLog.IPostModelEvalLogGetCategoryLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalLog.IPostModelEvalLogGetCategoryLogResponse>('/webApi/modelEvalLog/getCategoryLog', {
    data: pick(params, [
      'type',
      'categoryIdsFilter',
      'subSetTags',
      'statsFilter',
      'actionType',
      'userMis',
      'startTime',
      'endTime',
      'sortConfig',
      'offset',
      'limit',
      'metaVersionId',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
