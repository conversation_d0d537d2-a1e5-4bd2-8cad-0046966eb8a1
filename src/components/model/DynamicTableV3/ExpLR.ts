export enum TableCellRenderType {
  TEXT,
  RICH_TEXT,
  IMAGE,
  SEGMENTS,
}
export interface TableData {
  title?: string;
  subTitle?: string;
  colHeader: ColHeader[];
  data: Record<string, CellData>[];
  rowHeaderKeys: string[];
}

export interface ColHeader {
  key: string;
  level?: number;
  name: string | JSX.Element;
  hasFilter?: boolean;
  nextLevelFlag: boolean;
  width?: number;
  textAlign?: 'left' | 'right' | 'center' | 'unset';
  indicatorInfo?: {
    definition: string;
    calFunction: string;
    indicatorUrl: string;
  };
  nextLevelHeader?: ColHeader[];
}

export interface CellData {
  type: TableCellRenderType;
  value: string | CellSegment[];
  color?: string;
  colSpan?: number;
  rowSpan?: number;
}

export interface CellSegment {
  format: string;
  args: {
    argText: string;
    argColor: string;
  }[];
}
