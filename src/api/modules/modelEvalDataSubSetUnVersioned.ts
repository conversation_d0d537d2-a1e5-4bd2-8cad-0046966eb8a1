import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1425634, catid=227034, projectId=35802, created=2024-06-21 11:04:07, last-modified=2025-02-28 10:39:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchSetSubSetCategoryRelation)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425634)
 */
export function postModelEvalDataSubSetUnVersionedBatchSetSubSetCategoryRelation(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchSetSubSetCategoryRelationParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchSetSubSetCategoryRelationResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchSetSubSetCategoryRelation',
    {
      data: pick(params, [
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'categoryIdsListFilter',
        'subSetTags',
        'runSpecSetIdFilter',
        'limit',
        'offset',
        'categoryList',
        'actionType',
        'idList',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1439590, catid=227034, projectId=35802, created=2024-07-22 18:28:31, last-modified=2024-11-28 14:40:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/instanceList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1439590)
 */
export function postModelEvalDataSubSetUnVersionedInstanceList(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedInstanceListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedInstanceListResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/instanceList',
    {
      data: pick(params, ['dataSubSetId', 'evalDataSizeFilter', 'offset', 'limit']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1417702, catid=227034, projectId=35802, created=2024-06-05 21:14:44, last-modified=2025-01-20 10:46:35
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1417702)
 */
export function postModelEvalDataSubSetUnVersionedList(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedListResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/list',
    {
      data: pick(params, [
        'keyword',
        'typeFilter',
        'statTypeFilter',
        'publicStatusFilter',
        'statSourceTypeFilter',
        'categoryIdsListFilter',
        'subSetTags',
        'runSpecSetIdFilter',
        'idList',
        'offset',
        'limit',
        'batchKeywordList',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505188, catid=227034, projectId=35802, created=2025-01-20 11:00:10, last-modified=2025-01-23 16:20:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchUpdateFiled)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505188)
 */
export function postModelEvalDataSubSetUnVersionedBatchUpdateFiled(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchUpdateFiledParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchUpdateFiledResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchUpdateFiled',
    {
      data: pick(params, ['fieldList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505375, catid=227034, projectId=35802, created=2025-01-22 10:17:18, last-modified=2025-01-22 11:23:40
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchUpdatePlugin)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505375)
 */
export function postModelEvalDataSubSetUnVersionedBatchUpdatePlugin(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchUpdatePluginParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchUpdatePluginResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchUpdatePlugin',
    {
      data: pick(params, ['pluginBindList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505367, catid=227034, projectId=35802, created=2025-01-22 10:16:04, last-modified=2025-02-07 15:06:40
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchDeletePlugin)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505367)
 */
export function postModelEvalDataSubSetUnVersionedBatchDeletePlugin(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchDeletePluginParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchDeletePluginResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchDeletePlugin',
    {
      data: pick(params, ['pluginBindList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505365, catid=227034, projectId=35802, created=2025-01-22 10:13:16, last-modified=2025-01-22 11:24:25
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchCreatePlugin)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505365)
 */
export function postModelEvalDataSubSetUnVersionedBatchCreatePlugin(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchCreatePluginParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchCreatePluginResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchCreatePlugin',
    {
      data: pick(params, ['bindingRunSpecList', 'ids']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505180, catid=227034, projectId=35802, created=2025-01-20 10:48:12, last-modified=2025-01-22 15:11:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/listAggregate)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505180)
 */
export function postModelEvalDataSubSetUnVersionedListAggregate(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedListAggregateParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedListAggregateResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/listAggregate',
    {
      data: pick(params, [
        'keyword',
        'typeFilter',
        'statTypeFilter',
        'publicStatusFilter',
        'statSourceTypeFilter',
        'categoryIdsListFilter',
        'subSetTags',
        'runSpecSetIdFilter',
        'idList',
        'offset',
        'limit',
        'batchKeywordList',
        'sortField',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1425718, catid=227034, projectId=35802, created=2024-06-21 13:02:23, last-modified=2025-02-28 10:40:45
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchUpsertField)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425718)
 */
export function postModelEvalDataSubSetUnVersionedBatchUpsertField(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchUpsertFieldParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchUpsertFieldResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchUpsertField',
    {
      data: pick(params, [
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'categoryIdsListFilter',
        'subSetTags',
        'runSpecSetIdFilter',
        'limit',
        'offset',
        'updateField',
        'idList',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1418850, catid=227034, projectId=35802, created=2024-06-10 10:20:33, last-modified=2025-03-13 15:06:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/simpleList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418850)
 */
export function postModelEvalDataSubSetUnVersionedSimpleList(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedSimpleListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedSimpleListResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/simpleList',
    {
      data: pick(params, [
        'keyword',
        'typeFilter',
        'statTypeFilter',
        'publicStatusFilter',
        'categoryIdsListFilter',
        'statSourceTypeFilter',
        'subSetTags',
        'categoryTags',
        'runSpecSetIdFilter',
        'statNameFilter',
        'limit',
        'offset',
        'idList',
        'batchKeywordList',
        'ownerList',
        'instanceTypeFilter',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1425615, catid=227034, projectId=35802, created=2024-06-21 10:53:41, last-modified=2025-02-28 10:38:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchSetSubSetTag)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425615)
 */
export function postModelEvalDataSubSetUnVersionedBatchSetSubSetTag(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchSetSubSetTagParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchSetSubSetTagResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchSetSubSetTag',
    {
      data: pick(params, [
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'categoryIdsListFilter',
        'subSetTags',
        'runSpecSetIdFilter',
        'limit',
        'offset',
        'setSubSetTag',
        'actionType',
        'idList',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1496084, catid=227034, projectId=35802, created=2024-11-28 15:17:07, last-modified=2024-11-28 15:18:29
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/rebuildInstanceEvalDataSize)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1496084)
 */
export function postModelEvalDataSubSetUnVersionedRebuildInstanceEvalDataSize(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedRebuildInstanceEvalDataSizeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedRebuildInstanceEvalDataSizeResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/rebuildInstanceEvalDataSize',
    {
      data: pick(params, ['dataSubSetId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1417700, catid=227034, projectId=35802, created=2024-06-05 21:05:33, last-modified=2025-04-18 14:38:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1417700)
 */
export function postModelEvalDataSubSetUnVersionedUpsert(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedUpsertResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/upsert',
    {
      data: pick(params, [
        'id',
        'type',
        'execType',
        'dataSetName',
        'dataSetLabel',
        'name',
        'label',
        'statType',
        'publicStatus',
        'description',
        'runtimeConfig',
        'statSourceType',
        'instanceType',
        'aggrateType',
        'tags',
        'files',
        'categoryList',
        'autoEvalDataSizeOrderList',
        'bindingRunSpecList',
        'bindingRunSpecSetList',
        'ownerList',
        'evalType',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1417644, catid=227034, projectId=35802, created=2024-06-05 19:53:52, last-modified=2025-04-18 14:39:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1417644)
 */
export function postModelEvalDataSubSetUnVersionedGet(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedGetResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/get',
    {
      data: pick(params, ['id']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1425107, catid=227034, projectId=35802, created=2024-06-20 14:59:28, last-modified=2024-09-10 14:27:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/listTagMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425107)
 */
export function postModelEvalDataSubSetUnVersionedListTagMeta(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedListTagMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedListTagMetaResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/listTagMeta',
    {
      data: params,
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1418886, catid=227034, projectId=35802, created=2024-06-10 21:43:16, last-modified=2024-06-10 21:43:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418886)
 */
export function postModelEvalDataSubSetUnVersionedDelete(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedDeleteResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/delete',
    {
      data: pick(params, ['id']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1507418, catid=227034, projectId=35802, created=2025-02-27 19:02:00, last-modified=2025-02-27 19:02:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchDelete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507418)
 */
export function postModelEvalDataSubSetUnVersionedBatchDelete(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchDeleteResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchDelete',
    {
      data: pick(params, ['idList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1425720, catid=227034, projectId=35802, created=2024-06-21 13:07:39, last-modified=2025-03-03 18:17:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchExportDataSubSetCase)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425720)
 */
export function postModelEvalDataSubSetUnVersionedBatchExportDataSubSetCase(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchExportDataSubSetCaseParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchExportDataSubSetCaseResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchExportDataSubSetCase',
    {
      data: pick(params, [
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'categoryIdsListFilter',
        'subSetTags',
        'runSpecSetIdFilter',
        'limit',
        'offset',
        'idList',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1425725, catid=227034, projectId=35802, created=2024-06-21 13:10:23, last-modified=2025-03-04 10:52:35
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/batchExportDataSubSetDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425725)
 */
export function postModelEvalDataSubSetUnVersionedBatchExportDataSubSetDetail(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchExportDataSubSetDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedBatchExportDataSubSetDetailResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/batchExportDataSubSetDetail',
    {
      data: pick(params, [
        'keyword',
        'statTypeFilter',
        'publicStatusFilter',
        'categoryIdsListFilter',
        'subSetTags',
        'runSpecSetIdFilter',
        'limit',
        'offset',
        'exportCategoryColumnMetaVersionIdList',
        'idList',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1438392, catid=227034, projectId=35802, created=2024-07-18 10:48:21, last-modified=2024-07-18 10:49:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalDataSubSetUnVersioned/exportDataSubSet)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1438392)
 */
export function postModelEvalDataSubSetUnVersionedExportDataSubSet(
  params: model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedExportDataSubSetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalDataSubSetUnVersioned.IPostModelEvalDataSubSetUnVersionedExportDataSubSetResponse>(
    '/webApi/modelEvalDataSubSetUnVersioned/exportDataSubSet',
    {
      data: pick(params, ['sourceCategoryId', 'targetCategoryId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
