<template>
  <pre v-highlightjs="json ? JSON.parse(json) : ''"><code  class="language-JSON" style="overflow-x: hidden;text-wrap: wrap;"></code></pre>
</template>

<script lang="ts">
import { Component, Spectra, Vue, Prop } from '@/decorators';
import hljs from 'highlight.js/lib/core';
import json from 'highlight.js/lib/languages/json';
import 'highlight.js/styles/atom-one-light.css';
hljs.registerLanguage('json', json);

@Component({
  directives: {
    highlightjs: {
      deep: true,
      bind(el, binding) {
        const targets = el.querySelectorAll('code');
        targets.forEach((target) => {
          if (binding.value) {
            target.textContent = JSON.stringify(binding.value, null, 2);
          }
          hljs.highlightElement(target);
        });
      },
      componentUpdated(el, binding) {
        const targets = el.querySelectorAll('code');
        targets.forEach((target) => {
          if (binding.value) {
            target.textContent = JSON.stringify(binding.value, null, 2);
          }
          hljs.highlightElement(target);
        });
      },
    },
  },
} as any)
@Spectra
export default class JsonShow extends Vue {
  @Prop({ default: '' })
  json;
}
</script>

<style lang="scss" scoped></style>
