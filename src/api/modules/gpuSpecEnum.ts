import { FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1474459, catid=246786, projectId=35802, created=2024-09-19 14:36:07, last-modified=2024-09-19 15:00:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/gpuSpecEnum/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1474459)
 */
export function postGpuSpecEnumList(params: model.gpuSpecEnum.IPostGpuSpecEnumListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.gpuSpecEnum.IPostGpuSpecEnumListResponse>('/webApi/gpuSpecEnum/list', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
