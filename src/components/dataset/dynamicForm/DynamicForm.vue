<!-- 这里之前使用element、后修改成MTD 部分组件的属性不同 -->

<template>
  <div class="dynamic-form">
    <mtd-form
      ref="dynamicForm"
      :label-width="formStyle.labelWidth"
      :rules="isPreview ? {} : rules"
      :model="formData"
      :size="formStyle.size"
      label-position="top"
    >
      <mtd-row v-for="(item, index) in formItemList" :key="index" :gutter="formStyle.rowGutter">
        <mtd-col :span="item.span || formStyle.span || 12" :offset="item.offset">
          <p v-if="item.formItemType === 'title'" class="title">
            {{ item.label }}
          </p>
          <mtd-form-item
            :label-width="item.labelWidth"
            :style="{ width: item.width || formStyle.itemWidth }"
            :rules="[{ required: false, message: '请填写拟合配置' }]"
            :prop="item.vModel"
          >
            <!-- 展示了绑定值 -->
            <template slot="label">
              <mtd-tooltip v-if="item.tips" placement="top" :content="item.tips" theme="light">
                <span><i class="mtdicon-warning-circle-o"></i>{{ item.label }}</span>
              </mtd-tooltip>
              <span v-else>{{ item.formItemType === 'el-button' ? '' : item.label }}</span>
              <span style="background-color: #b7d7fb; display: inline-block; color: #2c7fff; margin-left: 20px; padding: 0 15px">
                {{ getFixResult(item) }}
              </span>
            </template>
            <mtd-select
              v-if="getComponentsName(item.formItemType) === 'mtd-select'"
              v-model="formItemProxyList[index].vModelProxy"
              :disabled="isPreview"
              v-bind="getComponentAttribute(item.attr, item.formItemType)"
              :options="item.options"
              :style="[item.style || {}, { marginTop: `5px` }]"
            />
            <component
              :is="getComponentsName(item.formItemType, item)"
              v-if="getComponentsName(item.formItemType) !== 'mtd-select' && getComponentsName(item.formItemType) !== 'richText'"
              v-model="formItemProxyList[index].vModelProxy"
              :disabled="isPreview"
              v-bind="getComponentAttribute(item.attr, item.formItemType, item)"
              :readonly="onlyDisplay"
              :style="[item.style || {}, { marginTop: `5px` }]"
              v-on="item.on"
            >
              <template v-if="item.formItemType === 'el-radio-group' && item.options">
                <mtd-radio
                  v-for="(option, idx) in item.options"
                  v-bind="getComponentAttribute(option, 'el-radio')"
                  :key="idx"
                  tabindex="0"
                  :value="option.label"
                >
                  {{ option.name || option.label }}
                </mtd-radio>
              </template>
              <template v-if="item.formItemType === 'el-checkbox-group' && item.options">
                <mtd-checkbox
                  v-for="(option, idx) in item.options"
                  v-bind="getComponentAttribute(option, 'el-checkbox')"
                  :key="idx"
                  tabindex="0"
                  :value="option.label"
                  >{{ option.name || option.label }}</mtd-checkbox
                >
              </template>
              <template v-if="item.formItemType === 'el-select' && item.options">
                <mtd-option
                  v-for="(option, idx) in item.options"
                  v-bind="getComponentAttribute(option, 'el-option')"
                  :key="idx"
                ></mtd-option>
              </template>
              <template v-if="item.formItemType === 'el-upload'">
                <mtd-button size="small" type="primary">点击上传 </mtd-button>
                <div slot="tip" class="el-upload__tip">支持扩展名.log</div>
              </template>
              <template v-if="item.formItemType === 'el-button'">
                {{ item.label }}
              </template>
            </component>
            <div v-if="getComponentsName(item.formItemType, item) === 'richText'">富文本内容，无法显示</div>
            <!-- <RichText
              class="richText"
              v-if="getComponentsName(item.formItemType, item) === 'richText' && richTextComponentLoaded"
              v-model="formItemProxyList[index].vModelProxy"
            ></RichText> -->
            <slot name="formItem" :formItem="item" :formItemIndex="index"></slot>
          </mtd-form-item>
        </mtd-col>
      </mtd-row>
    </mtd-form>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch, Ref } from 'vue-property-decorator';
import isEmpty from 'lodash/isEmpty';
import cloneDeep from 'lodash/cloneDeep';

@Component({ components: {} })
export default class DynamicForm extends Vue {
  @Prop({ default: false }) isPreview;
  @Prop({ default: () => ({}) })
  formConfig!: any;
  @Prop({ default: () => ({}) })
  formData!: any;
  @Prop({ default: false })
  onlyDisplay!: boolean;
  @Prop({ default: '' })
  formDataId!: string;
  @Prop({ default: () => ({}) })
  fitItemResult: any;
  @Prop({ default: '' })
  prefix!: string;

  @Ref('dynamicForm')
  dynamicFormRef;

  defaultLabelWidth = 0;
  // defaultLabelWidth = 100;
  defaultRowGutter = 0;
  defaultItemWidth = '100%';
  defaultSize = 'medium';
  formItemProxyList = [];
  richTextComponentLoaded = false;
  richTextComponentLoadPromise: any = null;

  @Watch('formData', { immediate: true })
  handleFormDataChange(value) {
    if (this.formItemProxyList.length) {
      this.formItemProxyList.forEach((item: any) => {
        if (Object.getOwnPropertyDescriptor(item, 'vModelProxy')) {
          item.vModelProxy = this.getFormDefaultValue(item.originalFormItem);
        }
      });
    }

    !this.onlyDisplay && this.$emit('update:formData', value);
  }

  getFixResult(item) {
    return this.fitItemResult[this.prefix + item.vModel]?.desc || '';
  }

  @Watch('formItemList', { immediate: true })
  formItemListChange() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;
    this.formItemProxyList = this.formItemList.map((item, formItemIdx) => {
      !isEmpty(item.on) &&
        Object.keys(item.on).forEach((key) => {
          const originCb = item.on[key];
          // eslint-disable-next-line @typescript-eslint/no-this-alias
          const _this = this;
          item.on[key] = function (params) {
            if (originCb) {
              // eslint-disable-next-line prefer-rest-params
              const args = [].slice.call(arguments);
              const finalArgs = [formItemIdx, ...args];
              originCb.apply(_this, finalArgs);
            }

            _this.$emit(key, params);
          };
        });

      const obj: any = { originalFormItem: item };
      if (!isEmpty(item.vModel)) {
        Object.defineProperty(obj, 'vModelProxy', {
          get() {
            return this.originalFormItem.vModel.split('.').reduce((target, seg) => {
              if (target) {
                target = target[seg];
              }

              return target;
            }, self.formData);
          },
          set(value) {
            // hack start 数组排序，为了判断序列后的标注结果是否被修改
            if (
              this.originalFormItem.vModel === 'checkbox' &&
              Array.isArray(value) &&
              value.length > 1 &&
              Array.isArray(this.originalFormItem.options)
            ) {
              const sortedValue = [];
              this.originalFormItem.options.forEach((option: { label: never }) => {
                if (option && option.label && value.includes(option.label)) {
                  sortedValue.push(option.label);
                }
              });
              if (sortedValue.length && JSON.stringify(value) !== JSON.stringify(sortedValue)) {
                Object.assign(value, sortedValue);
              }
            }

            // hack end
            const segments = this.originalFormItem.vModel.split('.');
            segments.reduce((target, seg, length) => {
              if (length === segments.length - 1) {
                self.$set(target, seg, value);
              } else {
                !target[seg] && self.$set(target, seg, {});
              }

              target = target[seg];
              return target;
            }, self.formData);
          },
        });
        obj.vModelProxy = this.getFormDefaultValue(item);
      }

      return obj;
    });
  }

  get formStyle() {
    return {
      labelWidth: this.defaultLabelWidth,
      size: this.defaultSize,
      rowGutter: this.defaultRowGutter,
      itemWidth: this.defaultItemWidth,
      ...this.formConfig.formStyle,
    };
  }
  get formItemList() {
    this.formConfig.formItemList.forEach((item) => {
      // 级联选择器，支持限制是否必须选择叶子结点

      // 这里直接修改props不太好，但暂时没想到其他好的方法，而且改的是对象类型的属性，所以影响还在把控范围内
      // 之前尝试直接深拷贝一份this.formConfig但是出现其他问题失败了
      if (item.formItemType === 'el-cascader') {
        item.on = { change: this.onCascaderChange };
      }
    });
    return this.formConfig.formItemList || [];
  }
  onCascaderChange(formItemIdx, value, selectedOptions, nodes, lastNode) {
    console.log('cascader change', formItemIdx, value, selectedOptions, nodes, lastNode);
    console.log('this.formItemProxyList[formItemIdx]', this.formItemProxyList[formItemIdx]);
    const cascaderChangeParams = {
      formDataId: this.formDataId,
      selectedOptions,
    };
    console.log('this.formConfig', this.formConfig, this.formData);
    this.$emit('onCascaderChange', cascaderChangeParams);
    if (!Array.isArray(selectedOptions[0])) return;
    const targetFormItemIsMustSelectLeafNode = (this.formItemProxyList[formItemIdx] as any)?.originalFormItem?.isMustSelectLeafNode;
    if (targetFormItemIsMustSelectLeafNode) {
      // 如果叶子节点只有1个，selectedOptions不会返回叶子结点，原因未知
      const legalSelectedOptions = selectedOptions.filter(
        (selOpt) => !selOpt?.[selOpt.length - 1]?.children || selOpt?.[selOpt.length - 1]?.children.length === 1
      );
      (this.formItemProxyList[formItemIdx] as any).vModelProxy = legalSelectedOptions.map((lso) => {
        const lastItem = lso[lso.length - 1];
        const result = lso.map((lsoItem) => lsoItem.value);
        return lastItem.children ? [...result, lastItem.children[0]?.value] : result;
      });
    }
  }
  get rules() {
    return this.formItemList.reduce((rules, item) => {
      if (item.rules && item.rules.length > 0) {
        rules[item.vModel] = item.rules;
      }

      return rules;
    }, {});
  }

  get formItemVisibility() {
    return this.formItemList.reduce((visibility, item) => {
      if (item.visible) {
        // visibility[item.vModel] = isEqualWith(Object.assign({}, this.formData, item.visible), this.formData, this.customizer);
        visibility[item.vModel] = this.compareObj(item.visible, this.formData);
      } else {
        visibility[item.vModel] = true;
      }

      return visibility;
    }, {});
  }

  compareObj(visible, formData) {
    if (Array.isArray(visible) && Array.isArray(formData)) {
      // todo 完善当item是对象时的逻辑
      return !visible.find((item) => !formData.includes(item));
    }

    if (typeof visible === 'object' && typeof formData === 'object') {
      return !Object.keys(visible).find((key) => !this.compareObj(visible[key], formData[key]));
    }

    return formData === visible;
  }
  getFormDefaultValue(item) {
    // 循环标注区会循环引用配置
    const formDataValue = item.vModel.split('.').reduce((target, seg) => {
      if (target) {
        target = target[seg];
      }

      return target;
    }, this.formData);
    if (formDataValue !== undefined && formDataValue !== null && formDataValue !== '') {
      return formDataValue;
    }

    if (item.defaultValue) {
      return cloneDeep(item.defaultValue);
    }

    if (item.formItemType === 'daterangeIncrease') {
      return [['', '']];
    }

    if (
      // eslint-disable-next-line eqeqeq
      (formDataValue == '' && item.formItemType === 'el-checkbox-group') ||
      item.type === 'dates' ||
      item.formItemType === 'el-checkbox-group' ||
      item.formItemType === 'el-cascader' ||
      item.formItemType === 'input-plus' ||
      (item.type && item.type.indexOf('range') !== -1)
    ) {
      return [];
    }

    return '';
  }

  getComponentsName(name, item?) {
    switch (name) {
      case 'el-checkbox-group':
        return 'mtd-checkbox-group';
      case 'el-input':
        if (item?.attr?.type === 'textarea') {
          return 'mtd-textarea';
        }

        if (item?.attr?.type === 'texteditor') {
          // 如果有富文本的formItem，再动态加载
          // this.loadTextEditorAsync();
          return 'richText';
        }

        return 'mtd-input';
      case 'el-select':
        return 'mtd-select';
      case 'el-radio-group':
        return 'mtd-radio-group';
      case 'el-input-number':
        return 'mtd-input-number';
      case 'el-color-picker':
        return 'mtd-color-picker';
      case 'el-cascader':
        return 'mtd-cascader';
      default:
        return name;
    }
  }

  loadTextEditorAsync() {
    if (!this.richTextComponentLoaded && !this.richTextComponentLoadPromise) {
      // this.richTextComponentLoadPromise = import('./richText.vue')
      //   .then((res) => {
      //     if (res.default) {
      //       Vue.component('RichText', res.default);
      //       this.richTextComponentLoaded = true;
      //       this.richTextComponentLoadPromise = null;
      //     } else {
      //       return Promise.reject();
      //     }
      //   })
      //   .catch(() => {
      //     this.richTextComponentLoaded = false;
      //     this.richTextComponentLoadPromise = null;
      //   });
    }
  }

  getComponentAttribute(attr, formItemType, item) {
    if (formItemType === 'el-cascader') {
      attr.filterable = item.filterable;
      attr.multiple = item.multiple;
    }

    return attr;
  }

  validate() {
    return new Promise((resolve, reject) => {
      if (this.dynamicFormRef) {
        this.dynamicFormRef
          .validate((v, message) => {
            if (v) {
              resolve(v);
            } else {
              this.$mtd.message.error('表单校验失败!');
              reject(message);
            }
          })
          .catch((err: any) => {
            console.log(err);
          });
      } else {
        reject('表单不存在');
      }
    });
  }

  clearValidate() {
    this.dynamicFormRef && this.dynamicFormRef.clearValidate();
  }
}
</script>
<style lang="scss" scoped>
.dynamic-form {
  .title {
    font-weight: bold;
    color: #222222;
    font-size: 16px;
  }
  .mtd-form-item {
    width: 100%;

    .mtd-form-item-content {
      width: auto;
      > * {
        width: 100%;
      }
      .mtd-btn {
        width: auto;
      }
    }
  }
  .mtd-color-picker {
    width: 32px !important;
  }
}
</style>
