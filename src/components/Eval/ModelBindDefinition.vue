<template>
  <div class="modelPluginBind">
    <div v-if="setUp">
      <mtd-badge style="padding: 0px" :value="pluginBindList.length" class="example-badge">
        <i class="mtdicon mtdicon-setting setting" @click="showModal"></i>
      </mtd-badge>
    </div>
    <div v-else-if="flag">
      {{ pluginBindList.length }}个绑定点【
      <mtd-button type="text-primary" @click="showModal">{{ explicitTitle ? explicitTitle : readonly ? '查看' : '编辑' }}</mtd-button>
      】
    </div>
    <mtd-modal v-model="visible" :width="1500" :closable="false">
      <!-- <mtd-button @click="log">log</mtd-button> -->
      <mtd-tabs v-model="activeTab" style="margin-bottom: 10px">
        <mtd-tab-pane label="总览" value="overview"> </mtd-tab-pane>
        <mtd-tab-pane label="通过节点定义编辑" value="vertex"> </mtd-tab-pane>
        <mtd-tab-pane label="通过流程图预览编辑" value="flow"> </mtd-tab-pane>
      </mtd-tabs>
      <mtd-table v-if="activeTab === 'overview'" :data="tableData">
        <mtd-table-column prop="anchorPath" label="锚点路径"> </mtd-table-column>
        <mtd-table-column prop="bindType" width="150" label="绑定类型"> </mtd-table-column>
        <mtd-table-column prop="pluginBindInfo" width="300" label="插件/参数绑定">
          <template #default="{ row }">
            【<mtd-button type="text-primary" @click="handleVertexJump(row)"> {{ row.bindInfo.label }}</mtd-button
            >】
          </template>
        </mtd-table-column>
        <mtd-table-column width="50">
          <template #default="{ row }">
            <mtd-button :disabled="readonly" type="text-primary" @click="delItem(row)">x</mtd-button>
          </template>
        </mtd-table-column>
      </mtd-table>

      <template v-if="activeTab === 'vertex'">
        <mtd-select
          v-model="vertexId"
          :loading="vertexSelectLoading"
          :filterable="true"
          :remote="true"
          :remote-method="searchVertexList"
          @change="initVertex"
        >
          <mtd-option
            v-for="vertex of flowVertexDefineList.evalFlowVertexDefineList"
            :key="vertex.id"
            :value="vertex.id"
            :label="vertex.label"
          ></mtd-option>
        </mtd-select>
        <node-bind-definition
          v-if="vertex"
          ref="nodeBindDefinition"
          :initData="vertexInitData"
          :source="source"
          :defaultExpandPath="defaultExpandPath"
          :disabled="readonly"
          @nodeUpdated="(e) => addPluginBindList(null, e.anchor, e.currBindType, e.changedBindType)"
        ></node-bind-definition>
      </template>

      <template v-if="activeTab === 'flow'">
        <mtd-form inline>
          <mtd-form-item label="流程图" style="width: 300px">
            <mtd-select
              v-model="flow"
              :loading="flowSelectLoading"
              :filterable="true"
              :remote="true"
              :remote-method="searchFlowList"
              :formatter="({ value }) => value.name"
              @change="selectFlow"
            >
              <mtd-option v-for="flow in evalFlowList.evalFlowList" :key="flow.id" :label="flow.name" :value="flow" />
            </mtd-select>
          </mtd-form-item>
          <mtd-form-item label="流程图版本" style="width: 300px">
            <flow-chart-version :id="flowId" :value.sync="flowVersion" @change="selectFlowVersion" />
          </mtd-form-item>
        </mtd-form>
        <div ref="graph" class="graph-tab">
          <div v-if="!flowId">请选择流程图</div>
          <mtd-loading v-else-if="flowLoading"></mtd-loading>
          <custom-node-flow
            v-else
            :editable="false"
            :nodeComponent="nodeComponent"
            :data="flowData"
            @select="updateSelectedNode"
            @unselect="unselectNode"
          />
          <mtd-drawer
            v-model="openDrawer"
            :width="500"
            :mask="false"
            title="节点详情"
            :mask-closable="false"
            :get-popup-container="getGraphContainer"
            :append-to-container="false"
            :closable="false"
          >
            <node-bind-definition
              v-if="hasSelectedNode"
              ref="nodeBindDefinition"
              :initData="selectedNodeVertexDefine"
              :source="source"
              :disabled="readonly"
              @nodeUpdated="(e) => addPluginBindList(null, e.anchor, e.currBindType, e.changedBindType)"
            />
          </mtd-drawer>
        </div>
      </template>
      <div style="margin-top: 10px; display: flex; align-items: center; justify-content: end">
        <span v-if="bindWarning && !readonly" style="color: red; font-weight: bold">
          不推荐在“EvalModelInfer/model”以外的锚点上进行推理配置绑定！ <br />结果很可能不符合预期，您确定这样做的后果吗？
        </span>
        <mtd-button type="text-primary" v-if="actionButtonVisible" @click="copy" icon="mtdicon mtdicon-copy-o">复制配置</mtd-button>
        <mtd-button type="text-primary" v-if="actionButtonVisible" @click="paste" :disabled="readonly" icon="mtdicon mtdicon-file-import"
          >粘贴配置</mtd-button
        >
        <mtd-button style="margin-left: 10px" :disabled="readonly" type="primary" @click="save">确定</mtd-button>
        <mtd-button style="margin-left: 10px" @click="cancel">取消</mtd-button>
      </div>
    </mtd-modal>
  </div>
</template>

<script lang="ts">
import NodeBindDefinition from '@/components/Eval/NodeBindDefinition.vue';
import { Component, Prop, Ref, Spectra, Watch, Weaver } from '@/decorators';
import { AnchorDefine, BindType, PluginBind, VertexDefine } from '@/model/customEval';
import EvalFlow from '@/store/modules/evalFlow';
import EvalFlowVersion from '@/store/modules/evalFlowVersion';
import EvalFlowVertexDefine from '@/store/modules/evalFlowVertexDefine';
import { EvalSource } from '@/utils';
import EvalFlowNode from '@/views/modelEval/EvalFlow/EvalFlowNode.vue';
import FlowChartVersion from '@/views/modelEval/ModelEvalRunSpecSetSimple/FlowChartVersion.vue';
import { generateAnchorBindMap } from '@/views/modelEval/utils';
import { cloneDeep } from 'lodash';
import CustomNodeFlow from './X6GraphFlow/CustomNodeFlow.vue';

const bindTypeLabelMap = {
  [BindType.APPEND]: '追加',
  [BindType.COVER]: '覆盖',
  [BindType.PARAM]: '参数',
};

const MODEL_DEFAULT_VERTEX = 'EvalModelInfer';
const RUNSPEC_DEFAULT_VERTEX = 'DataLoader';
@Component({ components: { NodeBindDefinition, CustomNodeFlow, FlowChartVersion } })
@Spectra
export default class ModelBindDefinition extends Weaver(EvalFlowVertexDefine, EvalFlow, EvalFlowVersion) {
  @Prop()
  source!: string;
  @Prop()
  value: any;
  @Prop({ default: false })
  readonly?: boolean;
  @Prop({ default: false })
  setUp?: boolean;
  @Prop()
  explicitTitle?: string;
  @Prop({ default: true })
  flag?: boolean;
  @Prop({ default: true })
  actionButtonVisible?: boolean;

  activeTab = 'overview';
  visible = false;

  anchorBindMap: Map<string, PluginBind[]> = new Map();

  pluginBindList: PluginBind[] = [];

  updatePluginBindList() {
    this.pluginBindList = [...this.anchorBindMap.values()].flat();
  }

  tableData: any = [];

  get bindWarning() {
    return this.tableData.some((x) => !x.anchorPath.startsWith('EvalModelInfer/model')) && this.source === EvalSource.MODEL;
  }

  log() {
    console.log(this.pluginBindList, this.anchorBindMap);
  }
  updateTableData() {
    this.tableData = [...this.anchorBindMap.entries()].reduce((prev, [anchorPath, pluginBindList]) => {
      const firstBind = pluginBindList[0]!;
      if (firstBind.bindType !== BindType.PARAM) {
        prev.push({
          anchorPath,
          bindType: bindTypeLabelMap[firstBind.bindType!],
          bindInfo: {
            source: firstBind.source,
            label:
              (firstBind.bindType === BindType.APPEND ? '+ ' : '') +
              (pluginBindList.length > 1 ? `${pluginBindList.length}个绑定` : firstBind.pluginImplementLabel || firstBind.pluginImplement),
          },
        });
      } else {
        prev.push(
          ...pluginBindList.map((bind) => ({
            anchorPath: `${anchorPath}@${bind.pluginImplement}`,
            bindType: bindTypeLabelMap[BindType.PARAM],
            bindInfo: {
              source: firstBind.source,
              label: bind.pluginImplementLabel || bind.pluginImplement,
            },
          }))
        );
      }

      return prev;
    }, [] as any);
  }

  showModal() {
    this.visible = true;
    this.searchVertexList('');
    this.searchFlowList('');
    if (!this.tableData.length) {
      this.activeTab = 'vertex';
    }
    if (this.source === EvalSource.RUN_SPEC) {
      this.initVertex(0, RUNSPEC_DEFAULT_VERTEX);
    } else {
      this.initVertex(0, MODEL_DEFAULT_VERTEX);
    }
  }

  defaultExpandPath = '';

  @Watch('activeTab')
  tabChanged(val) {
    if (val !== 'vertex') this.defaultExpandPath = '';
    this.openDrawer = false;
    if (val === 'flow' && this.flowId && this.flowVersion) this.generateFlowData();
  }

  async handleVertexJump(row) {
    const anchor = row.anchorPath.split('/')[0];

    if (row.bindType === bindTypeLabelMap[BindType.PARAM]) {
      this.defaultExpandPath = row.anchorPath.replace(/@[^@]*$/, '');
    } else this.defaultExpandPath = row.anchorPath;
    const res = await this.action$evalFlowVertexDefineByAnchor({ anchor });

    this.vertexId = res.evalFlowVertexDefine.id;
    this.vertex = res.evalFlowVertexDefine;
    this.activeTab = 'vertex';
  }

  delItem(row) {
    if (row.bindType === bindTypeLabelMap[BindType.PARAM]) {
      const [_, anchorPath, impl] = row.anchorPath.match(/(.*)@([^@]*)$/);
      const oriBindList = this.anchorBindMap.get(anchorPath);
      if (oriBindList?.length === 1) this.anchorBindMap.delete(anchorPath);
      else
        this.anchorBindMap.set(
          anchorPath,
          oriBindList!.filter((bind) => bind.pluginImplement !== impl)
        );
      this.pluginBindList = this.pluginBindList.filter((bind) => bind.anchorPath !== anchorPath || bind.pluginImplement !== impl);
    } else {
      this.anchorBindMap.delete(row.anchorPath);
      this.pluginBindList = this.pluginBindList.filter((bind) => bind.anchorPath !== row.anchorPath);
    }

    this.updateTableData();
  }

  @Watch('value', { immediate: true })
  addPluginBindList(
    pluginBindList: Array<PluginBind> | null = [],
    anchor?: AnchorDefine,
    currBindType?: BindType,
    changedBindType?: boolean
  ) {
    const changedBindList =
      pluginBindList || this.nodeBindDefinition?.getPluginBindList().map((x) => ({ vertexDefineId: this.vertexId, ...x }));
    const changedMap: Map<string, PluginBind[]> = generateAnchorBindMap(changedBindList);

    for (const [anchorPath, bindList] of changedMap.entries()) {
      const curSourceBindList = bindList.filter((anchorBind) => anchorBind.source === this.source);
      if (curSourceBindList.length) {
        const oldBindList = this.anchorBindMap.get(anchorPath);
        const oldBindType = oldBindList?.[0].bindType;

        if (oldBindType === BindType.PARAM && curSourceBindList[0].bindType === BindType.PARAM) {
          let res: PluginBind[] = oldBindList || [];
          curSourceBindList.forEach((bind) => {
            res = [...res!.filter((x) => x.pluginImplement !== bind.pluginImplement), bind];
          });
          this.anchorBindMap.set(anchorPath, res);
        } else {
          this.anchorBindMap.set(anchorPath, curSourceBindList);
        }
      }
    }

    if (currBindType === BindType.EXTEND && changedBindType) {
      this.clearAnchorBind(anchor!);
    }

    this.updateTableData();
    this.updatePluginBindList();
  }

  clearAnchorBind(anchor: AnchorDefine) {
    const oldBindList = this.anchorBindMap.get(anchor.anchorPath!);

    const delAnchorAndChild = () => {
      this.anchorBindMap.delete(anchor.anchorPath!);
      for (const anchorPath of this.anchorBindMap.keys()) {
        if (anchorPath.startsWith(anchor.anchorPath! + '@')) this.anchorBindMap.delete(anchorPath);
      }
    };
    if (!oldBindList) return;
    const oldBindType = oldBindList?.[0].bindType;
    if (oldBindType === BindType.PARAM) {
      let res = oldBindList;
      anchor.pluginBindList.forEach((bind) => (res = res.filter((x) => x.pluginImplement !== bind.pluginImplement)));
      if (!res.length) delAnchorAndChild();
      else this.anchorBindMap.set(anchor.anchorPath!, res);
    } else {
      delAnchorAndChild();
    }
  }

  save() {
    this.$emit('input', this.pluginBindList);
    this.visible = false;
  }

  cancel() {
    this.anchorBindMap.clear();
    this.addPluginBindList(this.value);
    this.updatePluginBindList();
    this.updateTableData();
    this.visible = false;
  }

  mounted() {}

  copy() {
    const sourceText = JSON.stringify({
      data: this.pluginBindList,
      key: 'MODAL_BIND',
    });
    navigator.clipboard.writeText(sourceText);
    this.$mtd.message.success('复制成功');
  }

  paste() {
    navigator.clipboard.readText().then((text) => {
      try {
        const data = JSON.parse(text);
        if (data && data.key === 'MODAL_BIND') {
          this.addPluginBindList(data.data);
          this.$mtd.message.success('粘贴成功');
        } else throw new Error();
      } catch (e) {
        this.$mtd.message.error('粘贴失败，剪贴板不包含合法的可粘贴配置。');
      }
    });
  }

  // 通过节点定义编辑 -- start

  vertexSelectLoading = false;
  vertexId = 0;
  vertex: VertexDefine | null = null;

  @Ref('nodeBindDefinition')
  nodeBindDefinition!: any;

  get vertexInitData() {
    return { ...this.vertex, pluginBindList: [...this.vertex!.pluginBindList, ...this.pluginBindList] };
  }

  async searchVertexList(query) {
    this.vertexSelectLoading = true;
    await this.action$flowVertexDefineList({
      searchFilter: query,
      ownerFilter: '',
      categoryFilter: '',
      statusFilter: '',
      offset: 0,
      limit: 100,
    });
    this.vertexSelectLoading = false;
  }

  async initVertex(vertexId, anchor?) {
    const res = vertexId
      ? await this.action$evalFlowVertexDefine({ id: vertexId })
      : await this.action$evalFlowVertexDefineByAnchor({ anchor });
    if (!vertexId) this.vertexId = res.evalFlowVertexDefine.id;
    // @ts-ignore
    this.vertex = cloneDeep(res.evalFlowVertexDefine);
  }
  // 通过节点定义编辑 -- end

  // 通过流程图预览编辑 -- start

  flowSelectLoading = false;

  flow = {};
  flowId = 0;
  flowVersion = 0;

  flowLoading = true;
  flowData: any = {
    nodes: [],
    edges: [],
  };

  openDrawer = false;
  hasSelectedNode = false;
  selectedNodeData: any = {
    id: -1,
    label: '',
    vertexDefineId: -1,
    pluginBindList: [],
  };
  get selectedNodeVertexDefine() {
    return {
      ...this.selectedNodeData!.vertexDefine,
      pluginBindList: [
        ...this.selectedNodeData!.vertexDefine!.pluginBindList,
        ...this.selectedNodeData!.pluginBindList,
        ...this.pluginBindList,
      ],
    };
  }

  nodeComponent = EvalFlowNode;

  async searchFlowList(query) {
    this.flowSelectLoading = true;
    await this.action$evalFlowList({
      searchFilter: query,
      ownerFilter: '',
      categoryFilter: '',
      offset: 0,
      limit: 100,
    });
    this.flowSelectLoading = false;
  }

  async selectFlow(val) {
    this.action$evalFlowVersionList({ flowId: val.id });

    this.flowId = val.id;
    this.flowVersion = val.latestVersion;
    this.generateFlowData();
  }

  async selectFlowVersion(val) {
    this.generateFlowData();
  }

  async generateFlowData() {
    this.hasSelectedNode && this.unselectNode();
    this.flowLoading = true;
    await this.action$evalFlowVersion({ flowId: this.flowId, version: this.flowVersion });

    const { vertexList, edgeList } = this.evalFlowVersion.evalFlowVersion;

    // 节点定义存入cache
    // @ts-ignore
    this.action$batchSetEvalFlowVertexDefine(vertexList.map((vertex) => vertex.vertexDefine));

    const nodes = vertexList.map((vertex) => ({
      id: String(vertex.id),
      shape: 'custom-vue-node',
      data: {
        ...vertex,
      },
    }));
    const edges = edgeList.map((edge) => ({
      source: String(edge.sourceVertexId),
      target: String(edge.targetVertexId),
      shape: 'data-processing-curve',
    }));
    this.flowData = { nodes, edges };
    this.flowLoading = false;
  }

  getGraphContainer() {
    return this.$refs.graph;
  }

  updateSelectedNode(val) {
    this.hasSelectedNode = true;
    this.openDrawer = true;
    this.selectedNodeData = val.data;
    this.vertexId = val.vertexDefineId;
    this.selectedNodeData.pos = val.pos;
  }
  unselectNode() {
    this.hasSelectedNode = false;
    setTimeout(() => {
      if (!this.hasSelectedNode) this.openDrawer = false;
    }, 50);
  }

  // 通过流程图预览编辑 -- end
}
</script>

<style scoped lang="scss">
.modelPluginBind {
  .a {
    cursor: pointer;
    color: #1c6cdc;
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  padding-top: 200px;
}

::v-deep .graph-tab {
  position: relative;
  height: 600px;
  .mtd-drawer {
    position: absolute;
  }
  .mtd-drawer-wrapper {
    pointer-events: none;
  }
}
.setting {
  cursor: pointer;
  color: #1c6cdc;
  font-size: 28px;
}
</style>
