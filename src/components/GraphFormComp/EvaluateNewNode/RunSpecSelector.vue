<template>
  <div>
    <mtd-modal class="runSpec-modal" v-model="modal" :width="1000" :closable="false">
      <div slot="title">
        <div class="flex justify-between">
          <div>
            {{ runSpecLabel(selectedRunSpec?.runSpecSetId) }}
            <span v-if="getBenchmarkChildCount(selectedRunSpec)" style="color: grey"
              >（{{ getBenchmarkChildCount(selectedRunSpec) }}子集）</span
            >
          </div>
          <div v-if="isEdit">
            <mtd-button
              style="width: 80px"
              @click="
                () => {
                  modal = false;
                  modelChanged();
                }
              "
            >
              取消
            </mtd-button>
            &nbsp;&nbsp;
            <mtd-button
              style="width: 80px"
              type="primary"
              @click="
                () => {
                  modal = false;
                  saveModalData();
                }
              "
              >确定</mtd-button
            >
          </div>
          <div v-else>
            <mtd-button @click="modal = false">关闭</mtd-button>
          </div>
        </div>
      </div>
      <mtd-form v-if="selectedRunSpec">
        <div class="flex">
          <mtd-form-item label="指标维度">
            <mtd-select v-if="isEdit" v-model="selectedRunSpec.statsFilter" collapse-tags multiple style="max-height: 30px; width: 300px">
              <mtd-option
                v-for="stats of getStatsFilter(selectedRunSpec.runSpecSetId)"
                :key="stats"
                :label="getStatLabel(stats)"
                :value="stats"
              />
            </mtd-select>
            <div v-else>{{ selectedRunSpec.statsFilter.map(getStatLabel).join(',') }}</div>
          </mtd-form-item>
          <mtd-form-item label="筛选数据子集" class="ml-5">
            <DataSubSetIdsFilter
              v-model="selectedRunSpec.dataSubSetIdsFilter"
              :runSpecSetId="selectedRunSpec.runSpecSetId"
              :isEdit="isEdit"
              :selectWidth="100"
            />
          </mtd-form-item>
        </div>
        <mtd-form-item label="环境参数配置">
          <div class="flex items-center" v-if="notRecCount">
            <mtd-tag type="pure" style="background: rgb(240, 72, 136); color: white; border: none">非推荐配置: {{ notRecCount }}</mtd-tag
            >&nbsp;
            <mtd-button v-if="isEdit" type="text-primary" @click="paramsSetRefer">全部置为推荐值</mtd-button>
          </div>
          <context-param-definition
            :needSource="false"
            :value="selectedRunSpec.envGlobalAliasParams"
            :curSource="source"
            :canEditGlobalAlias="true"
            :disabled="!isEdit"
            :hasOverrideFlag="selectedBenchmarkHasChild"
          ></context-param-definition>
        </mtd-form-item>
      </mtd-form>
    </mtd-modal>
    <div v-if="isEdit" class="common-width">
      <div class="set-select-container">
        <mtd-select
          class="set-select"
          v-model="runSpecSetIdList"
          multiple
          :filterable="true"
          popper-class="benchmark-select"
          :reserve-keyword="true"
          @blur="change"
          collapse-tags
          :closable="false"
          style="width: 100%; max-height: 32px; overflow: hidden"
        >
          <mtd-option v-for="v in benchmarkNoVersioned.runSpecSetList" :key="v.id" :label="v.name" :value="v.id">
            <span>{{ v.name }}</span
            >&nbsp;
            <mtd-tag v-if="JSON.parse(String(v.execPlanConfig || {})).type === 'FLOW'" theme="red" size="small">支持V2评测框架</mtd-tag
            >&nbsp;
          </mtd-option>
          <template v-slot:tag="scope">
            <mtd-tooltip :content="scope.option.label">
              <mtd-tag theme="" style="max-width: 130px">{{ scope.option.label }}</mtd-tag>
            </mtd-tooltip>
          </template>
        </mtd-select>
        <mtd-button type="primary" @click="copyBenchmark(runSpecSetList)">复制配置</mtd-button>
        <mtd-button @click="pasteBenchmark">粘贴配置</mtd-button>
      </div>
      <div class="flex">
        <RunSpecSelectorBatchParam :runSpecSetList="runSpecSetList" @change="saveData" />
        <mtd-button type="text-primary" @click="setAllCacheParams('True')">禁用全部缓存</mtd-button>
        <mtd-button type="text-primary" @click="setAllCacheParams('False')">启用全部缓存</mtd-button>
      </div>
    </div>
    <div v-else>
      已选择{{ runSpecSetList.length }}个数据集
      <mtd-button type="primary" @click="copyBenchmark(runSpecSetList)">复制配置</mtd-button>
    </div>
    <mtd-announcement
      style="margin-left: -100px; width: 100%"
      v-if="context.nodeParam.nodeHasChanged && foundBenchmark().jobId"
      title="评测配置有变更。请考虑 [禁用全部缓存] 或修改“评估模型名”"
      type="warning"
      show-icon
    />
    <mtd-loading v-if="loading" />
    <div class="common-width run-spec-grid" v-else>
      <mtd-card
        v-for="(runSpecSet, idx) in runSpecSetList"
        :key="runSpecSet.runSpecSetId"
        body-class="run-spec-card"
        :class="{
          'selected-card': modal && runSpecSet.runSpecSetId === selectedRunSpec.runSpecSetId,
        }"
        style="max-width: 500px"
        @click.native="openEditor(runSpecSet.runSpecSetId)"
      >
        <div class="run-spec-info">
          <div class="run-spec-label-container">
            <mtd-tooltip placement="top" :content="runSpecLabel(runSpecSet.runSpecSetId)">
              <div class="run-spec-label">
                {{ runSpecLabel(runSpecSet.runSpecSetId)
                }}<span v-if="getBenchmarkChildCount(runSpecSet)" style="color: grey"
                  >（{{ getBenchmarkChildCount(runSpecSet) }}子集）</span
                >
              </div>
            </mtd-tooltip>
            <mtd-button type="text-primary" style="flex-shrink: 0">
              <div class="flex items-center">
                <span v-if="paramOverrideCount(runSpecSet)" class="badge-dot">{{ paramOverrideCount(runSpecSet) }}</span>
                参数配置
                <mtd-icon class="ml-1" name="setting" />
                <mtd-icon class="ml-1" name="copy-o" @click.stop="copyBenchmark([runSpecSet])" />
              </div>
            </mtd-button>
            <mtd-button
              type="text-primary"
              v-if="foundBenchmark().jobId"
              style="margin-left: 0; flex-shrink: 0"
              @click.stop="handleViewBenchmarkDetail(runSpecSet)"
              >查看指标
              <mtd-icon name="barschart-o" />
            </mtd-button>
          </div>
          <div style="flex-shrink: 0" v-if="isEdit">
            <mtd-tooltip placement="top" content="删除">
              <mtd-icon-button
                class="card-button delete-btn ml-2"
                @click.stop="delRunSpecSet(idx)"
                icon="mtdicon mtdicon-delete-o"
                size="small"
              />
            </mtd-tooltip>
          </div>
        </div>
        <div v-if="showTag" class="tag-container" style="display: flex; overflow: hidden">
          <mtd-tag theme="green" style="flex-shrink: 0">{{ getBenchmarkEvalDataSizeLabel(runSpecSet) }}</mtd-tag>
          <mtd-tag theme="purple" style="flex-shrink: 0">{{ getBenchmarkBatch(runSpecSet) }}</mtd-tag>
          <mtd-tag v-if="getBenchmarkNoCache(runSpecSet)" theme="red" style="flex-shrink: 0">NoCache</mtd-tag>
          <mtd-dropdown v-if="runSpecSet.statsFilter.length > 1" trigger="hover" style="align-items: start; line-height: 18px">
            <mtd-tag>
              {{ getStatLabel(runSpecSet.statsFilter[0]) }}...
              <i class="mtdicon mtdicon-down" />
            </mtd-tag>
            <mtd-dropdown-menu slot="dropdown">
              <mtd-dropdown-menu-item style="max-width: 240px">
                <mtd-tag v-for="stats in runSpecSet.statsFilter" :key="stats" style="flex-shrink: 0">
                  {{ getStatLabel(stats) }}
                </mtd-tag></mtd-dropdown-menu-item
              >
            </mtd-dropdown-menu>
          </mtd-dropdown>
          <mtd-tag v-else style="flex-shrink: 0">
            {{ getStatLabel(runSpecSet.statsFilter[0]) }}
          </mtd-tag>
        </div>
      </mtd-card>
    </div>
  </div>
</template>
<script lang="ts">
import { getModelExperimentTaskGetOutputModelMetaId } from '@/api/modules/model';
import ContextParamDefinition from '@/components/Eval/ContextParamDefinition.vue';
import { Component, Prop, ProvideReactive, Spectra, Watch, Weaver } from '@/decorators';
import { ContextParam } from '@/model/customEval';
import EvalFlowJob from '@/store/modules/evalFlowJob';
import ModelEval from '@/store/modules/modelEval';
import modelEvalDataSubSetUnVersioned from '@/store/modules/modelEvalDataSubSetUnVersioned';
import modelManage from '@/store/modules/modelManage';
import { EvalSource } from '@/utils';
import { useGraphStore } from '@/views/modelManage/components/use-graph-store';
import { cloneDeep, differenceWith, isEqual } from 'lodash';
import DataSubSetIdsFilter from './DataSubSetIdsFilter.vue';
import RunSpecSelectorBatchParam from './RunSpecSelectorBatchParam.vue';
interface RunSpecSetConfig {
  runSpecSetId: number;
  statsFilter: string[];
  dataSubSetIdsFilter: number[];
  envGlobalAliasParams: ContextParam[];
}
@Component({
  components: {
    DataSubSetIdsFilter,
    ContextParamDefinition,
    RunSpecSelectorBatchParam,
  },
})
@Spectra
export default class RunSpecSelector extends Weaver(modelEvalDataSubSetUnVersioned, ModelEval, EvalFlowJob, modelManage) {
  @Prop()
  model!: any;
  @Prop()
  context!: any;
  @Prop()
  globalContext: any;
  @Prop()
  isEdit!: boolean;
  @Prop({ default: true })
  showTag?: boolean;
  @Prop({ default: () => [] })
  runSpecSet?: any;
  @ProvideReactive('modelMetaSize')
  modelMetaSize = '';
  modelMetaSizeInitd = false;

  runSpecSetIdList: number[] = [];
  runSpecSetList: RunSpecSetConfig[] = [];
  selectedRunSpec: RunSpecSetConfig = {
    runSpecSetId: -1,
    statsFilter: [],
    dataSubSetIdsFilter: [],
    envGlobalAliasParams: [],
  };

  source = EvalSource.GLOBAL_ALIAS_JOB;

  loading = false;
  modal = false;
  originDataSize = '';
  labelList: { name: string; label: string }[] = [];
  initData = true;
  modelMetaId = 0;
  stashedContext: any = {};

  get notRecCount() {
    return this.selectedRunSpec.envGlobalAliasParams.filter((x) => x.value !== x.referValue).length;
  }

  @Watch('model', { immediate: true })
  async modelChanged() {
    if (!this.modelMetaId) {
      this.modelMetaId = await this.getSourceMetaId();
    }

    if (!this.model) return;

    if (this.initData) {
      await this.action$evalFlowJobPreviewBenchmark({
        runSpecSetIdList: [...new Set([...this.model.map((x) => x.runSpecSetId), ...this.runSpecSet])]?.filter(Boolean),
        modelMetaId: this.modelMetaId,
      });
      this.initData = false;
    }

    const mergedBenchmarkList = this.mergeBenchmarkParam(this.evalFlowJobPreviewBenchmark.benchmarkList, this.model);
    this.runSpecSetList = this.model.map((x) => {
      return {
        ...x,
        envGlobalAliasParams:
          mergedBenchmarkList.find((benchmark) => benchmark.runSpecSetId === x.runSpecSetId)?.envGlobalAliasParams || [],
      };
    });
    this.runSpecSetIdList = this.model.map((x) => x.runSpecSetId);
    this.loading = false;
  }

  @Watch('context', { deep: true })
  contextChanged(v) {
    const differences = differenceWith(Object.entries(v.nodeParam), Object.entries(this.stashedContext.nodeParam), isEqual).filter(
      ([k, v]) => !['submitInfoList', 'benchmarkList', 'advancedConfig', 'nodeHasChanged'].includes(k)
    );
    if (differences.length) {
      this.$set(this.context.nodeParam, 'nodeHasChanged', true);
    }
  }

  get selectedBenchmarkHasChild() {
    return this.evalFlowJobPreviewBenchmark?.benchmarkList.some((x) => x.rootRunSpecSetId === this.selectedRunSpec.runSpecSetId);
  }

  getBenchmarkChildCount(benchmark) {
    return this.evalFlowJobPreviewBenchmark?.benchmarkList.filter((x) => x.rootRunSpecSetId === benchmark.runSpecSetId).length;
  }

  getStatLabel(v) {
    if (!this.labelList.length) {
      this.modelEvalUnVersionedListStatMeta.statMetaList.forEach((x) => {
        x.children?.forEach((y) => {
          this.labelList.push(y);
        });
        this.labelList.push(x);
      });
    }
    return this.labelList.find((x) => x.name === v)?.label || v;
  }

  getBenchmarkEvalDataSizeLabel(benchmark) {
    const dataSize = benchmark.envGlobalAliasParams.find((x) => x.name === '_ENV_RUN_SPEC_SET_EVAL_DATA_SIZE')?.value;
    if (dataSize === '-1') return 'AUTO';
    return this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList.find((x) => x.name === dataSize)?.label || '-';
  }

  getBenchmarkBatch(benchmark) {
    const batchSize = benchmark.envGlobalAliasParams.find((x) => x.name === '_ENV_RUN_SPEC_SET_BATCH_SIZE')?.value || '-';
    return 'Batch ' + batchSize;
  }
  getBenchmarkNoCache(benchmark) {
    return benchmark.envGlobalAliasParams.find((x) => x.name === '_ENV_RUN_SPEC_SET_DISABLE_CACHE')?.value === 'True';
  }

  async change(v = this.runSpecSetIdList) {
    this.loading = true;
    const sourceList = this.runSpecSetList;
    if (
      !isEqual(
        (this.model || []).map((x) => x.runSpecSetId),
        v
      )
    ) {
      this.$set(this.context.nodeParam, 'nodeHasChanged', true);
    }
    v = v?.filter((item) => typeof item === 'number');
    await this.action$evalFlowJobPreviewBenchmark({
      runSpecSetIdList: [...new Set([...v, ...this.runSpecSet])]?.filter(Boolean),
      modelMetaId: this.modelMetaId,
    });

    this.runSpecSetList = v.map((id) => {
      const source = sourceList.find((x) => x.runSpecSetId === id);
      if (source) return source;

      const preview = cloneDeep(this.evalFlowJobPreviewBenchmark.benchmarkList).find((x) => x.runSpecSetId === id);
      const envGlobalAliasParams =
        preview?.envGlobalAliasParams.map((x) => ({
          ...x,
          source: EvalSource.GLOBAL_ALIAS_JOB,
          defaultSource: EvalSource.GLOBAL_ALIAS_JOB,
          defaultValue: x.defaultValue || x.value,
          overrideFlag: ['_ENV_RUN_SPEC_SET_DISABLE_CACHE', '_ENV_RUN_SPEC_SET_DISABLE_SCORE_CACHE'].includes(x.name)
            ? 'ALL_SUB'
            : x.overrideFlag,
        })) || [];
      return {
        runSpecSetId: id,
        // @ts-ignore
        statsFilter: preview?.statNames?.length ? preview.statNames : this.getStatsFilter(id),
        dataSubSetIdsFilter: [],
        envGlobalAliasParams,
      } as any;
    });
    this.saveData();
    this.loading = false;
  }

  runSpecLabel(id) {
    return this.benchmarkNoVersioned.runSpecSetList.find((x) => x.id === id)?.name || '-';
  }

  getStatsFilter(v) {
    const foundItem = this.benchmarkNoVersioned.runSpecSetList.find((item) => item.id === Number(v?.id || v));
    return foundItem?.stats || [];
  }

  useGraphStore = useGraphStore();
  async openEditor(id) {
    this.modal = true;
    this.selectedRunSpec = this.runSpecSetList.find((x) => x.runSpecSetId === id)!;
    this.originDataSize = String(this.selectedRunSpec.envGlobalAliasParams.find((x) => x.type === 'evalDataSize')?.value);
    if (!this.modelMetaSizeInitd) {
      const metaInfo = await this.action$getModelMeta({ modelMetaId: String(this.modelMetaId) });
      this.modelMetaSize = metaInfo.autoEvalDataSize || '';
      this.modelMetaSizeInitd = true;
    }
  }

  created() {
    this.action$benchmarkNoVersioned({
      flowIdExistFilter: 'TRUE',
      limit: 10 ** 5,
      offset: 0,
    });
    this.action$modelEvalUnVersionedListStatMeta({});
    this.action$modelEvalListEvalDataSizeMeta({});
    this.stashedContext = cloneDeep(this.context);
  }

  saveModalData() {
    if (!this.selectedRunSpec.statsFilter.length) {
      this.$mtd.message.error('指标维度不能为空！');
      return;
    }

    this.saveData();
  }

  saveData() {
    this.$emit(
      'change',
      this.runSpecSetList.map((x) => ({
        ...x,
        envGlobalAliasParams: x.envGlobalAliasParams.filter((param) => param.source === this.source),
      }))
    );
  }

  mergeBenchmarkParam(ori, merge) {
    return cloneDeep(ori).map((oriBenchmark) => {
      const mergeBenchmark = merge.find((x) => x.runSpecSetId === oriBenchmark.runSpecSetId);
      if (mergeBenchmark) {
        oriBenchmark.envGlobalAliasParams.forEach((oriParam) => {
          const mergeParam = mergeBenchmark.envGlobalAliasParams.find((x) => x.name === oriParam.name);
          oriParam.defaultSource = EvalSource.GLOBAL_ALIAS_JOB;
          oriParam.source = EvalSource.GLOBAL_ALIAS_JOB;
          oriParam.defaultValue = oriParam.value;
          oriParam.referValue = oriParam.value;
          if (mergeParam) {
            oriParam.value = mergeParam.value;
            oriParam.source = EvalSource.GLOBAL_ALIAS_JOB;
            oriParam.overrideFlag = mergeParam.overrideFlag;
          }
        });
      }
      return oriBenchmark;
    });
  }
  foundBenchmark() {
    return (
      (this.taskStatLists || this.experimentConfigDetail?.status || this.taskDetail?.status)?.nodeStatus
        ?.find((x) => x.nodeId === Number(this.context.id))
        ?.extraInfo?.evaluateFlowExtraInfoVos?.find((x: any) => x.benchmarkList) || {}
    );
  }

  delRunSpecSet(idx: number) {
    this.runSpecSetIdList.splice(idx, 1);
    this.change(this.runSpecSetIdList);
  }

  getFilterGroupConfigList(benchmark) {
    const filterGroupConfigList: any = [];
    if (benchmark?.statsFilter) {
      const statsFilter = benchmark.statsFilter;
      const evalDataSizeParam = benchmark.envGlobalAliasParams.find((x) => x.type === 'evalDataSize')!;
      const evalDataSize = evalDataSizeParam.value === '-1' ? 'AUTO' : evalDataSizeParam.value;
      const overrideFlag = evalDataSizeParam.overrideFlag === 'ALL_SUB';
      const baseObj = {
        runSpecSetId: benchmark.runSpecSetId,
        evalDataSize: !overrideFlag && this.getBenchmarkChildCount(benchmark) ? 'AUTO' : evalDataSize,
      };

      if (statsFilter.length) {
        statsFilter.forEach((item, index) => {
          filterGroupConfigList.push({
            ...baseObj,
            statName: [item],
            name: `${this.runSpecLabel(benchmark.runSpecSetId)}-${this.getStatLabel(item)}-${this.getBenchmarkEvalDataSizeLabel(
              benchmark
            )}`,
          });
        });
      } else {
        filterGroupConfigList.push({
          ...baseObj,
          statName: statsFilter,
          name: `${this.runSpecLabel(benchmark.runSpecSetId)}-${this.getBenchmarkEvalDataSizeLabel(benchmark)}`,
        });
      }
    }
    return filterGroupConfigList;
  }

  openBenchmarkDetailPage(filterGroupConfigList) {
    const [evalModelFamily, evalModelName] = this.context.nodeParam.evaluateName?.split('/') || [];
    const query = {
      evalModelList: [
        {
          family: evalModelFamily,
          name: evalModelName,
        },
      ],
      filterGroupConfigList,
      radio: '0',
    };
    const encodedQuery = encodeURIComponent(JSON.stringify(query));
    window.open(
      this.$router.resolve({
        name: 'three-in-one-model',
        query: {
          experimentManageFilterGroup: encodedQuery,
        },
      }).href,
      '_blank',
      'noopener'
    );
  }

  handleViewBenchmarkDetail(benchmark) {
    const filterGroupConfigList = this.getFilterGroupConfigList(benchmark);
    this.openBenchmarkDetailPage(filterGroupConfigList);
  }

  handleViewAllBenchmarkDetail() {
    const filterGroupConfigList = this.model.flatMap((x) => this.getFilterGroupConfigList(x));
    this.openBenchmarkDetailPage(filterGroupConfigList);
  }

  copyBenchmark(benchmarkList) {
    const sourceText = JSON.stringify({
      data: benchmarkList,
      key: 'EVAL_BENCHMARKLIST',
    });
    navigator.clipboard.writeText(sourceText);
    this.$mtd.message.success('复制成功');
  }

  pasteBenchmark() {
    navigator.clipboard.readText().then((text) => {
      try {
        const data = JSON.parse(text);
        if (data && data.key === 'EVAL_BENCHMARKLIST') {
          this.mergeBenchmarkListConfig(data.data);
          this.change();
        } else throw new Error('invalid key');
      } catch (e) {
        this.$mtd.message.error('粘贴失败，剪贴板不包含合法的可粘贴配置。');
      }
    });
  }

  mergeBenchmarkListConfig(benchmarkList) {
    const oriLength = this.runSpecSetIdList.length;
    benchmarkList.forEach((benchmark) => {
      const { runSpecSetId } = benchmark;
      if (!this.runSpecSetIdList.includes(runSpecSetId)) {
        this.runSpecSetIdList.push(runSpecSetId);
        this.runSpecSetList.push(benchmark);
      } else {
        const foundIdx = this.runSpecSetList.findIndex((x) => x.runSpecSetId === runSpecSetId);
        this.runSpecSetList.splice(foundIdx, 1, benchmark);
      }
    });
    const addLength = this.runSpecSetIdList.length - oriLength;
    this.$mtd.message.success(`粘贴成功，新增${addLength}个，覆盖${benchmarkList.length - addLength}个`);
  }

  setAllCacheParams(value) {
    this.runSpecSetList.forEach((benchmark) => {
      const cacheParam = benchmark.envGlobalAliasParams.find((x) => x.name === '_ENV_RUN_SPEC_SET_DISABLE_CACHE');
      if (cacheParam) cacheParam.value = value;
    });
    this.$mtd.message.success(`已${value === 'True' ? '禁用' : '启用'}全部数据集缓存`);
    this.saveData();
  }

  paramsSetRefer() {
    this.selectedRunSpec.envGlobalAliasParams.forEach((params) => (params.value = params.referValue));
  }

  paramOverrideCount(benchmark) {
    const count = benchmark.envGlobalAliasParams.filter((x) => x.value !== x.referValue).length;
    if (!benchmark.referCount) benchmark.referCount = count;
    else if (benchmark.referCount !== count) {
      this.$set(this.context.nodeParam, 'nodeHasChanged', true);
    }
    return count;
  }

  async getSourceMetaId() {
    const { edges, nodes } = this.globalContext.getFields('graphData');
    const prevNodeMap = new Map();
    edges.forEach((edge) => {
      prevNodeMap.set(edge.target, edge.source);
    });
    let ptr = this.context.id;
    while (ptr) {
      const node = nodes.find((n) => n.id === ptr);
      // 向前递归找精调模型节点。要适配其他会改meta的节点改这里
      if (node.nodeType === 2) {
        // case1: 精调模型节点上配置了输出模型
        if (node.nodeParam?.outputModelMetaId) return node.nodeParam?.outputModelMetaId;
        // case2: 精调模型节点没配，但是完成执行了，产生了一个新meta
        const nodeInfo = (this.taskStatLists || this.experimentConfigDetail?.status || this.taskDetail?.status)?.nodeStatus?.find(
          (x) => x.nodeId === Number(ptr)
        );
        if (nodeInfo) {
          const res = await getModelExperimentTaskGetOutputModelMetaId({
            taskDetailId: String(nodeInfo.taskDetailId),
          });
          if (res.data.logUrl) return Number(res.data.logUrl);
        }
      }
      ptr = prevNodeMap.get(ptr);
    }
    // case3: 没有改变meta的节点，用实验信息的
    return this.experimentBaseInfo.modelMeta?.metaId || this.useGraphStore.modelInfo?.modelMetaId;
  }
}
</script>
<style lang="less" scoped>
.selected-card {
  box-shadow: 0 0 5px blue;
}
.runSpec-modal {
  &::v-deep .mtd-modal {
    margin-left: 8vw;
  }
}
.set-select-container {
  display: flex;
  .set-select {
    flex: 1;
  }
}

.run-spec-grid {
  margin-left: -100px;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 6px;
}
::v-deep .run-spec-card {
  position: relative;
  cursor: pointer;
  padding: 10px;
}
.run-spec-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 20px;
  width: 100%;
  .badge-dot {
    margin-right: 3px;
    flex-shrink: 0;
    display: inline-block;
    color: white;
    background-color: #ff000099;
    border-radius: 6px;
    height: 12px;
    padding: 0 3px;
    font-size: 12px;
    line-height: 12px;
  }
}
.card-button {
  flex-shrink: 0;
}
.run-spec-label-container {
  display: flex;
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 0;
  align-items: center;
  ::v-deep .mtd-tooltip-rel {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.run-spec-label {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-container {
  margin-top: 8px;
  display: flex;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
