import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1377081, catid=227021, projectId=35802, created=2024-03-06 19:47:02, last-modified=2024-03-14 19:41:34
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSet/simpleListOnline)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1377081)
 */
export function postRunSpecSetSimpleListOnline(
  params: model.runSpecSet.IPostRunSpecSetSimpleListOnlineParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSet.IPostRunSpecSetSimpleListOnlineResponse>('/webApi/runSpecSet/simpleListOnline', {
    data: pick(params, ['keywordFilter', 'limit', 'offset']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1377494, catid=227021, projectId=35802, created=2024-03-08 11:47:01, last-modified=2024-03-08 11:48:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSet/listByRunSpecId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1377494)
 */
export function postRunSpecSetListByRunSpecId(
  params: model.runSpecSet.IPostRunSpecSetListByRunSpecIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSet.IPostRunSpecSetListByRunSpecIdResponse>('/webApi/runSpecSet/listByRunSpecId', {
    data: pick(params, ['runSpecIdFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1373159, catid=227021, projectId=35802, created=2024-02-27 10:41:22, last-modified=2024-03-06 16:29:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSet/simpleList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373159)
 */
export function postRunSpecSetSimpleList(params: model.runSpecSet.IPostRunSpecSetSimpleListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.runSpecSet.IPostRunSpecSetSimpleListResponse>('/webApi/runSpecSet/simpleList', {
    data: pick(params, ['keywordFilter', 'metaVersionId', 'statusFilter', 'execTypeFilter', 'limit', 'offset']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1373206, catid=227021, projectId=35802, created=2024-02-27 14:13:18, last-modified=2024-07-03 19:30:21
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSet/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373206)
 */
export function postRunSpecSetUpsert(params: model.runSpecSet.IPostRunSpecSetUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.runSpecSet.IPostRunSpecSetUpsertResponse>('/webApi/runSpecSet/upsert', {
    data: pick(params, [
      'id',
      'metaVersionId',
      'name',
      'status',
      'execType',
      'paramOverrideConfig',
      'execPlanConfig',
      'bindingRunSpecList',
      'ownerList',
      'authType',
      'authMisList',
      'authOrgList',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1373161, catid=227021, projectId=35802, created=2024-02-27 11:00:18, last-modified=2024-06-26 14:41:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSet/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373161)
 */
export function postRunSpecSetGet(params: model.runSpecSet.IPostRunSpecSetGetParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.runSpecSet.IPostRunSpecSetGetResponse>('/webApi/runSpecSet/get', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1490921, catid=227021, projectId=35802, created=2024-11-15 16:10:51, last-modified=2024-11-15 16:55:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSet/queryDefaultRunSpecSetList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1490921)
 */
export function postRunSpecSetQueryDefaultRunSpecSetList(
  params: model.runSpecSet.IPostRunSpecSetQueryDefaultRunSpecSetListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.runSpecSet.IPostRunSpecSetQueryDefaultRunSpecSetListResponse>('/webApi/runSpecSet/queryDefaultRunSpecSetList', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1373183, catid=227021, projectId=35802, created=2024-02-27 11:17:59, last-modified=2024-03-07 17:05:16
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpecSet/setStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373183)
 */
export function postRunSpecSetSetStatus(params: model.runSpecSet.IPostRunSpecSetSetStatusParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.runSpecSet.IPostRunSpecSetSetStatusResponse>('/webApi/runSpecSet/setStatus', {
    data: pick(params, ['id', 'statusFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
