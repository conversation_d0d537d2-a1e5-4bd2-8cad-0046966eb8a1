import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1296953, catid=227034, projectId=35802, created=2023-10-13 19:28:03, last-modified=2024-06-10 14:43:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalMetaVersion/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296953)
 */
export function postModelEvalMetaVersionList(
  params: model.modelEvalMetaVersion.IPostModelEvalMetaVersionListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalMetaVersion.IPostModelEvalMetaVersionListResponse>('/webApi/modelEvalMetaVersion/list', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296978, catid=227034, projectId=35802, created=2023-10-13 19:28:04, last-modified=2023-11-21 14:38:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalMetaVersion/clone)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296978)
 */
export function postModelEvalMetaVersionClone(
  params: model.modelEvalMetaVersion.IPostModelEvalMetaVersionCloneParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalMetaVersion.IPostModelEvalMetaVersionCloneResponse>('/webApi/modelEvalMetaVersion/clone', {
    data: pick(params, ['originId', 'newName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297059, catid=227034, projectId=35802, created=2023-10-13 19:28:06, last-modified=2023-11-21 14:38:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalMetaVersion/setStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297059)
 */
export function postModelEvalMetaVersionSetStatus(
  params: model.modelEvalMetaVersion.IPostModelEvalMetaVersionSetStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalMetaVersion.IPostModelEvalMetaVersionSetStatusResponse>('/webApi/modelEvalMetaVersion/setStatus', {
    data: pick(params, ['id', 'status']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1297065, catid=227034, projectId=35802, created=2023-10-13 19:28:07, last-modified=2023-11-21 14:38:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalMetaVersion/setName)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1297065)
 */
export function postModelEvalMetaVersionSetName(
  params: model.modelEvalMetaVersion.IPostModelEvalMetaVersionSetNameParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalMetaVersion.IPostModelEvalMetaVersionSetNameResponse>('/webApi/modelEvalMetaVersion/setName', {
    data: pick(params, ['id', 'name']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
