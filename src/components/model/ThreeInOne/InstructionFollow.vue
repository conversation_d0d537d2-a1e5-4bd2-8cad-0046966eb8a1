<template>
  <div class="instruction-follow">
    <div class="instruction-follow-top">
      <div class="instruction-follow-filter">
        <mtd-select v-model="dataSubSetId" style="width: 300px" @change="handleDataSubSetId">
          <mtd-option v-for="item in dataSubSetOptions" :key="item.id" :label="item.dataSetName" :value="item.id" />
        </mtd-select>

        <div class="category">
          <span class="red">*</span>
          评测项
          <ThreeInOneModelEvalCategory
            ref="threeInOneModelEvalCategory"
            :value.sync="form.categoryIdsFilter"
            style="width: 200px"
            :treeList="treeList"
            @save="save"
            @setChartShow="setChartShow"
            :disabled="isDisabled"
          ></ThreeInOneModelEvalCategory>
        </div>
      </div>
      <mtd-button class="btn-demo-item" @click="downloadFile" v-if="isShow">下载文件</mtd-button>
    </div>

    <mtd-loading :loading="loading">
      <InstructionFollowChart
        ref="instructionFollowChart"
        :PARAMS="PARAMS"
        :InstructionFollowChartText="InstructionFollowChartText"
      ></InstructionFollowChart>
    </mtd-loading>
  </div>
</template>

<script lang="ts">
import { meta } from '@/api';
import ThreeInOneModelEvalCategory from '@/components/model/ThreeInOne/ThreeInOneModelEvalCategory.vue';
import { Component, Prop, Ref, Spectra, Weaver } from '@/decorators';
import ModelEvalIUnified from '@/store/modules/modelEvalIUnified';
import type { ColumnLineChartParams } from '@/types/modelCommonComponent';
import InstructionFollowChart from './InstructionFollowChart.vue';

@Component({
  components: {
    ThreeInOneModelEvalCategory,
    InstructionFollowChart,
  },
})
@Spectra
export default class InstructionFollow extends Weaver(ModelEvalIUnified) {
  @Ref('chart')
  $chart: any;
  @Ref('instructionFollowChart')
  instructionFollowChartRef: any;
  @Ref('threeInOneModelEvalCategory')
  threeInOneModelEvalCategoryRef: any;
  @Prop()
  PARAMS!: ColumnLineChartParams;
  loading = false;
  form = {
    categoryIdsFilter: [],
  };
  isShow = false;
  dataSubSetId: any = null;
  dataSubSetOptions: any = [];
  dataSubSetName = '';
  treeList: any = [];
  async downloadFile() {
    const filterGroupConfigList = this.PARAMS.params?.filterGroupConfigList;

    const runSpecSetId = filterGroupConfigList?.[0]?.runSpecSetId;
    const obj = {
      evalModelList: this.PARAMS.params.evalModelList,
      dataSubSetId: this.dataSubSetId,
      runSpecSetId,
    };
    await this.action$modelEvalIUnifiedModelDataSubSetCapabilityDownload(obj);
    this.$mtd.message({ type: 'warning', message: '稍后会将下载链接推送至大象，请等待大象通知' });
  }
  allDel() {}
  async save(evalCategoryTree) {
    const obj = {
      evalModelList: this.PARAMS.params.evalModelList,
      dataSubSetId: this.dataSubSetId,
      evalCategoryTree: evalCategoryTree,
    };
    this.loading = true;
    await this.action$modelEvalIUnifiedModelDataSubSetCapabilityView(obj);
    const modelDataSunSetCapabilityList = this.modelEvalIUnifiedModelDataSubSetCapabilityView?.modelDataSunSetCapabilityList || [];
    this.loading = false;
    this.instructionFollowChartRef.handleTable(modelDataSunSetCapabilityList);
  }

  handleDataSubSetId() {
    this.form.categoryIdsFilter = [];
    this.threeInOneModelEvalCategoryRef.allDel();
    this.getCategoryTree();
    this.setChartShow();
  }

  async mounted() {
    this.setDownloadFile();
    this.init();
  }
  setChartShow() {
    this.instructionFollowChartRef.showChart = false;
  }
  async setDownloadFile() {
    if (this.PARAMS.params.radio === '1') {
      this.isShow = false;
    } else {
      const filterGroupConfigList = this.PARAMS.params?.filterGroupConfigList;

      const runSpecSetId = filterGroupConfigList?.[0]?.runSpecSetId;
      if (runSpecSetId) {
        await this.action$runSpecSetUnVersionedGet({ id: runSpecSetId });
        if (this.runSpecSetUnVersionedGet.runSpecSetNonversion.publicStatus === 'BLACK') {
          const res = await meta.postMetaPermissionAuthAny({
            authUnitList: [{ entity: 'modelEvalBlackDataSubSetInstance', action: 'select', value: '' }],
          });
          this.isShow = res.data.hasAuth;
        } else {
          this.isShow = true;
        }
      }
    }
  }
  async init() {
    const filterGroupConfigList = this.PARAMS.params.filterGroupConfigList;
    if (this.PARAMS.params.radio === '0') {
      const obj = {
        filterGroupConfig: filterGroupConfigList?.[0] || {},
        evalModelList: this.PARAMS.params.evalModelList,
      };
      await this.action$modelEvalIUnifiedModelDataSubSetListView(obj);
      this.dataSubSetOptions = this.modelEvalIUnifiedModelDataSubSetListView;
      if (this.dataSubSetOptions.length) {
        this.dataSubSetId = this.dataSubSetOptions[0].id;
      } else {
        this.dataSubSetId = null;
      }
    } else {
      const dataSubSetFilterConfig = this.PARAMS.params.dataSubSetFilterConfig;
      this.dataSubSetId = dataSubSetFilterConfig?.dataSubSetIdList?.[0];
      const runSpecSetIdFilter: any = null;

      const obj = {
        keyword: '',
        typeFilter: '',
        statTypeFilter: '',
        publicStatusFilter: '',
        statSourceTypeFilter: '',
        categoryIdsListFilter: [],
        subSetTags: [],
        idList: dataSubSetFilterConfig?.dataSubSetIdList,
        limit: 10000,
        offset: 0,
        batchKeywordList: [],
        categoryTags: [],
        ownerList: [],
        runSpecSetIdFilter,
      };
      await this.action$modelEvalDataSubSetUnVersionedSimpleList(obj);
      this.dataSubSetOptions = this.modelEvalDataSubSetUnVersionedSimpleList.dataSubSetList || [];
      this.dataSubSetId = this.modelEvalDataSubSetUnVersionedSimpleList.dataSubSetList[0].id;
    }

    if (this.dataSubSetId) {
      this.getCategoryTree();
    }
  }
  isDisabled = false;
  InstructionFollowChartText = '请选择数据子集和评测项';
  async getCategoryTree() {
    const evalModelList = this.PARAMS.params.evalModelList;
    this.isDisabled = false;
    this.InstructionFollowChartText = '请选择数据子集和评测项';

    await this.action$modelEvalIUnifiedModelDataSubSetCapabilityEvalCategoryTreeView({
      evalModelList,
      dataSubSetId: this.dataSubSetId,
    });
    const res: any = this.modelEvalIUnifiedModelDataSubSetCapabilityEvalCategoryTreeView;
    this.isDisabled;
    if (res.data || res?.length) {
      this.treeList = this.modelEvalIUnifiedModelDataSubSetCapabilityEvalCategoryTreeView;
    } else {
      this.treeList = [];
      this.isDisabled = true;
      this.InstructionFollowChartText = '当前子集没有capability_report.json文件，请切换子集';
      return;
    }
  }
}
</script>
<style scoped lang="less">
.instruction-follow {
  .instruction-follow-top {
    display: flex;
    justify-content: space-between;
  }
  .instruction-follow-filter {
    display: flex;
    align-items: center;
    .category {
      margin-left: 20px;
      display: flex;
      align-items: center;
    }
  }
  .red {
    color: red;
    margin-right: 5px;
    margin-top: 5px;
  }
}
</style>
