import { pick, FlowHttpRequestOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1286003, catid=224560, projectId=35802, created=2023-09-25 10:37:38, last-modified=2023-11-21 14:37:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/projectGroup/listQueue)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1286003)
 */
export function getProjectGroupListQueue(params: model.projectGroup.IGetProjectGroupListQueueParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.projectGroup.IGetProjectGroupListQueueResponse>('/webApi/projectGroup/listQueue', {
    params: pick(params, ['projectGroup']),
    ...options,
  });
}
