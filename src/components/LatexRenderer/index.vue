<template>
  <div class="latex-renderer markdown-body" v-html="renderedContent"></div>
</template>

<script lang="ts">
import { hasLatex } from '@/utils/latex';
import '@/views/DataManage/PretrainDataset/components/SftDiff/ChartMdRender/github-markdown.scss';
import '@/views/DataManage/PretrainDataset/components/SftDiff/ChartMdRender/style.scss';
import 'katex/dist/katex.min.css';
import MarkdownIt from 'markdown-it';
import footNote from 'markdown-it-footnote';
import markdownItKatexGpt from 'markdown-it-katex-gpt';
import mila from 'markdown-it-link-attributes';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class LatexRenderer extends Vue {
  @Prop({ required: true }) content!: string;
  @Prop({ default: '' }) docId?: string;

  private md = new MarkdownIt({
    linkify: true,
    html: true,
    breaks: true,
  })
    .use(mila, { attrs: { target: '_blank', rel: 'noopener' } })
    .use(markdownItKatexGpt, {
      delimiters: [
        { left: '\\[', right: '\\]' },
        { left: '\\(', right: '\\)' },
        { left: '$$', right: '$$' },
        { left: '$', right: '$' },
      ],
      throwOnError: false,
      trust: true,
      macros: {
        '\\cdots': '\\cdots',
        '\\theta': '\\theta',
        '\\text': '\\text',
        '\\quad': '\\quad',
        '\\qquad': '\\qquad',
        '\\begin': '\\begin',
        '\\end': '\\end',
      },
    })
    .use(footNote);

  // 取消锚点返回按钮
  constructor() {
    super();
    this.md.renderer.rules.footnote_anchor = () => '';
  }

  private renderContent(content: string): string {
    if (!content || typeof content !== 'string') return content;

    try {
      // 预处理内容，处理特殊的 LaTeX 命令
      const processedContent = content
        // 处理换行符，确保在数学环境中正确显示
        .replace(/\n/g, ' ')
        .replace(/\\theta/g, '\\theta')
        .replace(/\\cos/g, '\\cos')
        .replace(/\\cdots/g, '\\cdots')
        // 处理双反斜杠（换行符）
        .replace(/\\\\/g, '\\\\\\\\')
        // 处理 cases 环境
        .replace(/\\begin\{cases\}/g, '\\begin{cases} ')
        .replace(/\\end\{cases\}/g, ' \\end{cases}')
        // 处理 \quad 命令
        .replace(/\\quad/g, '\\quad ')
        // 处理嵌套的数学环境
        .replace(/\\text\{([^}]*)\$([^}]*)\$([^}]*)\}/g, '\\text{$1}\\text{$2}\\text{$3}')
        // 处理其他 text 命令
        .replace(/\\text\{([^}]+)\}/g, '\\text{$1}');

      return this.md.render(processedContent, {
        docId: this.docId,
      });
    } catch (error) {
      console.error('Markdown rendering error:', error);
      return content;
    }
  }

  get renderedContent(): string {
    return hasLatex(this.content) ? this.renderContent(this.content) : this.content;
  }
}
</script>

<style lang="scss" scoped>
.latex-renderer {
  :deep(.markdown-body) {
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica, Arial, sans-serif;
    font-size: 12px;
    line-height: 1.6;
    word-wrap: break-word;
    padding: 0;
    background: transparent;
  }

  :deep(.katex-display) {
    margin: 0.5em 0;
    overflow-x: auto;
    overflow-y: hidden;
    text-align: left !important;
  }

  :deep(.katex) {
    font-size: 12px !important;
    text-align: left;
    max-width: 100%;
    overflow-x: auto;
  }

  :deep(.katex-html) {
    white-space: normal;
    max-width: 100%;
    overflow-x: auto;
  }

  :deep(.katex-error) {
    color: inherit;
    background: none;
  }

  :deep(p) {
    margin: 0;
    white-space: pre-wrap;
    font-size: 12px;
  }

  :deep(img) {
    max-width: 100%;
  }

  :deep(.katex-mathml) {
    display: none;
  }

  :deep(.katex .base) {
    margin: 0;
    padding: 0;
  }

  :deep(.katex .strut) {
    height: 0 !important;
  }
}
</style>
