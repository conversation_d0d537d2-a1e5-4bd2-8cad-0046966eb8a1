<template>
  <div>
    <div v-if="valueList?.length > 1">数据子集>1时无法展示</div>
    <div v-else>
      <div v-if="filterGroup.selectGroup.firstIndex === -1">请在上面表格里点击展开想查看的筛选组</div>
      <div class="showFilter" v-else-if="showChart">
        <mtd-button class="btn-demo-item" ghost type="primary">{{ filterGroup.selectGroup.title }}</mtd-button>
        <div class="filterOptionContent-name" v-if="stateOption?.length">选择展示内容</div>
        <mtd-select v-model="conditionValue" v-if="stateOption?.length" style="width: 180px" :append-to-container="false">
          <mtd-option v-for="item in stateOption" :key="item.value" :label="item.value" :value="item.key" />
        </mtd-select>
      </div>
      <chart v-if="showChart" :extends="echartsBaseOption" :height="500" :data="options" render="canvas" :theme="$C.ChartTheme" />
      <ThreeInOneScatterChart :PARAMS="PARAMS" :filterGroup="filterGroup" style="margin-top: 40px"></ThreeInOneScatterChart>
    </div>
  </div>
</template>

<script lang="ts">
import Chart from '@ss/mtdv-charts';

import type { ColumnLineChartParams } from '@/types/modelCommonComponent';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import ThreeInOneScatterChart from './ThreeInOneScatterChart.vue';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { TOOLTIP_BODY, TOOLTIP_COL, TOOLTIP_HEADER, TOOLTIP_ROW, TOOLTIP_ROW_VAL } from '@ss/mtdv-charts-core';

@Component({
  components: { Chart, ThreeInOneScatterChart },
})
export default class ThreeInOneBarChart extends Vue {
  @Prop()
  PARAMS!: ColumnLineChartParams;
  @Prop()
  title!: string;
  @Prop({
    default: () => ({
      selectGroup: {
        firstIndex: -1,
        secondIndex: 0,
        title: '',
      },
      type: 'OFF',
    }),
  })
  filterGroup!: any;

  baseOption = {
    type: 'bar',
    toolbox: {
      show: true,
      feature: {
        saveAsImage: {},
      },
    },
    tooltipConfig: {
      trigger: 'item',
      formatter: (params) => {
        const { name, value, marker } = params;
        return `<div class="${TOOLTIP_HEADER}">${marker.replace('width:10px;height:10px;', 'width:3px;height:10px;')}区间 ${name}</div>
                  <div class="${TOOLTIP_BODY}">
                    <div class="${TOOLTIP_COL}">
                      <div class="${TOOLTIP_ROW}__name-con ${TOOLTIP_ROW}__name">模型数量</div>
                    </div>
                    <div style="flex:1" class="${TOOLTIP_COL}">
                      <div class="${TOOLTIP_ROW_VAL}">${value[1]}</div>
                    </div>
                  </div>`;
      },
    },
    columns: ['range', 'clean', 'dirty'],
    title: '',
    rows: [],
  };
  showChart = false;
  echartsBaseOption = {};
  options = {};

  created() {
    this.options = { ...this.baseOption };
  }

  beforeDestroy() {
    this.showChart = false;
  }

  stateOption1 = [
    { value: '仅clean', key: 'CLEAN' },
    { value: '仅dirty', key: 'DIRTY' },
    { value: 'clean和dirty同时显示', key: 'CLEAN&DIRTY' },
  ];
  conditionValue = '';
  stateOption: any = [];
  seriesClean: any = [];
  seriesDirty: any = [];

  valueList: any = [];
  columnsFilter: any[] = [];
  mergedSeries: any[] = [];

  @Watch('conditionValue')
  handleConditionValue(value) {
    if (value === 'CLEAN&DIRTY') {
      if (this.mergedSeries?.length) {
        this.options = {
          ...this.baseOption,
          title: `${this.valueList[0].name} 上各模型的得分分布`,
          rows: this.mergedSeries,
        };
        this.showChart = true;
      } else {
        this.showChart = false;
      }
    }

    if (value === 'CLEAN') {
      if (this.mergedSeries?.length) {
        this.options = {
          ...this.baseOption,
          title: `${this.valueList[0].name} 上各模型的得分分布`,
          rows: this.seriesClean,
          columns: ['range', 'clean'],
        };
        this.showChart = true;
      } else {
        this.showChart = false;
      }
    }

    if (value === 'DIRTY') {
      if (this.seriesDirty?.length) {
        this.options = {
          ...this.baseOption,
          title: `${this.valueList[0].name} 上各模型的得分分布`,
          rows: this.seriesDirty,
          columns: ['range', 'dirty'],
        };
        this.showChart = true;
      } else {
        this.showChart = false;
      }
    }
  }

  @Watch('filterGroup', { deep: true })
  @Watch('PARAMS.tableData', { immediate: true, deep: false })
  handleTableDataChange() {
    const firstIndex = this.filterGroup.selectGroup.firstIndex;
    if (firstIndex > -1) {
      this.handleTable();
    } else {
      this.showChart = false;
      this.clearData();
    }
  }

  clearData() {
    this.stateOption = [];
    this.seriesDirty = [];
    this.seriesClean = [];
    this.mergedSeries = [];
  }

  handleTable() {
    this.clearData();
    const seriesDirty: any = [];
    const seriesClean: any = [];
    const firstIndex = this.filterGroup.selectGroup.firstIndex;
    const secondIndex = this.filterGroup.selectGroup.secondIndex;
    const tableData = this.PARAMS.tableDataDataSubSet;
    if (!tableData || tableData.evalModelList.length === 0) return;
    const evalModelList = tableData.evalModelList;
    if (evalModelList[0]?.filterGroupResultList) {
      this.valueList = evalModelList[0].filterGroupResultList[firstIndex].groupList[secondIndex].valueList;
    } else {
      this.valueList = evalModelList[0].dataSubSetFilterResult.valueList;
    }
    // 按模型排序

    const rows: any[] = [];
    this.columnsFilter = [];
    this.conditionValue = '';
    evalModelList.map((model, dataIndex) => {
      let label = `${model.llmModel.modelInfo?.modelMeta.modelName}/${model.llmModel.modelInfo?.modelCheckpoint}/${model.llmModel.label}`;
      this.columnsFilter.push({
        label,
        key: `row_${dataIndex}`,
      });
      let valueList: any = [];
      if (model.filterGroupResultList) {
        valueList = model.filterGroupResultList[firstIndex]?.groupList[secondIndex].valueList;
      } else {
        valueList = model.dataSubSetFilterResult.valueList;
      }
      valueList.forEach((item) => {
        if (item.unitList?.length === 2) {
          this.stateOption = this.stateOption1;
          this.conditionValue = 'CLEAN&DIRTY';
        }
        item.unitList.forEach((key, index) => {
          if (key.dataSetOpennessStatus === 'CLEAN' || key.dataSetOpennessStatus === '') {
            const row = {
              value: key.value,
              modelList: {
                label: model.label,
                modelCheckpoint: model.llmModel.modelInfo.modelCheckpoint,
                modelName: model.llmModel.modelInfo.modelMeta.modelName,
                evalDataSize: model.evalDataSize || this.PARAMS.evalDataSizeLineChart,
                dataSubSetId: item.dataSubSetId,
                dataStatus: key.dataSetOpennessStatus,
              },
            };
            seriesClean.push(row);
          }
          if (key.dataSetOpennessStatus === 'DIRTY') {
            const row = {
              value: key.value,
              modelList: {
                label: model.label,
                modelCheckpoint: model.llmModel.modelInfo.modelCheckpoint,
                modelName: model.llmModel.modelInfo.modelMeta.modelName,
                evalDataSize: model.evalDataSize || this.PARAMS.evalDataSizeLineChart,
                dataSubSetId: item.dataSubSetId,
                dataStatus: key.dataSetOpennessStatus,
              },
            };
            seriesDirty.push(row);
          }
        });
      });
    });
    this.seriesClean = this.getBarChartData(seriesClean, 'clean');
    this.seriesDirty = this.getBarChartData(seriesDirty, 'dirty');
    let rangeMap: any = {};

    this.seriesClean.forEach((item) => {
      rangeMap[item.range] = {
        range: item.range,
        clean: item.clean ?? 0,
        dirty: 0,
        modelList: {
          clean: item.modelList,
          dirty: [],
        },
      };
    });

    this.seriesDirty.forEach((item) => {
      if (rangeMap[item.range]) {
        rangeMap[item.range].dirty = item.dirty ?? 0;
        rangeMap[item.range].modelList.dirty = item.modelList;
      } else {
        rangeMap[item.range] = {
          range: item.range,
          clean: 0,
          dirty: item.dirty ?? 0,
          modelList: {
            clean: [],
            dirty: item.modelList,
          },
        };
      }
    });

    for (const key in rangeMap) {
      if (rangeMap.hasOwnProperty(key)) {
        this.mergedSeries.push(rangeMap[key]);
      }
    }

    if (this.mergedSeries?.length) {
      this.$nextTick(() => {
        this.options = {
          ...this.baseOption,
          title: `${this.valueList[0].name} 上各模型的得分分布`,
          rows: this.mergedSeries,
        };
      });

      this.showChart = true;
    } else {
      this.showChart = false;
    }
  }

  getBarChartData(data, name) {
    const fixDecimal = (i, length = 2) => parseFloat(i.toFixed(length));
    const getRowValue = (range) => ({ range, [name]: 0, modelList: [] });

    // 将所有值为空、null或undefined的项转换为0，并将其转换为浮点数
    const parsedData = data.map((item) => ({
      ...item,
      [name]: parseFloat(item.value) || 0,
    }));

    // 配置区间范围
    const ranges = Array.from({ length: 20 }, (_, i) => fixDecimal(i * 0.05));

    // 合并 parsedData 和 ranges
    const combinedData = [...parsedData, ...ranges];

    // 对合并后的数据进行排序
    const sortedData = combinedData.sort((a, b) => {
      if (typeof a === 'number' && typeof b === 'number') {
        return a - b;
      } else if (typeof a === 'number') {
        return a - b[name] || 1;
      } else if (typeof b === 'number') {
        return a[name] - b || -1;
      } else {
        return a[name] - b[name];
      }
    });

    // 使用 reduce 来分组和汇总数据
    const rows = sortedData.reduce((obj, item) => {
      if (typeof item === 'number') {
        const key = `${fixDecimal(item, 2)}-${fixDecimal(item + 0.05, 2)}`;
        if (!obj[key]) {
          obj[key] = getRowValue(key);
        }
      } else {
        const start = fixDecimal(Math.floor(item[name] / 0.05) * 0.05, 2);
        const end = fixDecimal(start + 0.05, 2);
        const key = `${start}-${end}`;
        if (!obj[key]) {
          obj[key] = getRowValue(key);
        }
        obj[key].modelList.push(item);
        obj[key][name]++;
      }
      return obj;
    }, {});
    return Object.values(rows);
  }
}
</script>
<style scoped lang="less">
.showFilter {
  text-align: left;
  margin-left: 30px;
  display: flex;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
}
.filterOptionContent-name {
  margin-left: 20px;
  margin-right: 10px;
}
</style>
