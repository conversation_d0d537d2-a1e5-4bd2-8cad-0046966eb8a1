import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1505419, catid=255612, projectId=35802, created=2025-01-22 16:15:32, last-modified=2025-01-22 16:32:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/getOnlineInstanceNumByVersionList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505419)
 */
export function getDataHubDatasetVersionPostTrainingGetOnlineInstanceNumByVersionList(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingGetOnlineInstanceNumByVersionListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingGetOnlineInstanceNumByVersionListResponse>(
    '/webApi/data-hub/dataset-version/post-training/getOnlineInstanceNumByVersionList',
    {
      params: pick(params, ['datasetVersionIdList']),
      ...options,
    }
  );
}

/**
 * @desc id=1503820, catid=255612, projectId=35802, created=2025-01-06 15:59:17, last-modified=2025-01-08 11:19:45
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/diff/statistic)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503820)
 */
export function getDataHubDatasetVersionPostTrainingDiffStatistic(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingDiffStatisticParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingDiffStatisticResponse>(
    '/webApi/data-hub/dataset-version/post-training/diff/statistic',
    {
      params: pick(params, ['reportId', 'statisticType', 'dataStatus', 'countField']),
      ...options,
    }
  );
}

/**
 * @desc id=1505254, catid=255612, projectId=35802, created=2025-01-20 16:50:52, last-modified=2025-01-22 17:20:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/getMasterVersion)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505254)
 */
export function getDataHubDatasetVersionPostTrainingGetMasterVersion(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingGetMasterVersionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingGetMasterVersionResponse>(
    '/webApi/data-hub/dataset-version/post-training/getMasterVersion',
    {
      params: pick(params, ['type']),
      ...options,
    }
  );
}

/**
 * @desc id=1505277, catid=255612, projectId=35802, created=2025-01-20 20:07:21, last-modified=2025-01-20 20:09:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/sftText/getMergeCheckGroup)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505277)
 */
export function getDataHubDatasetVersionPostTrainingSftTextGetMergeCheckGroup(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingSftTextGetMergeCheckGroupResponse>(
    '/webApi/data-hub/dataset-version/post-training/sftText/getMergeCheckGroup',
    {
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505203, catid=255612, projectId=35802, created=2025-01-20 14:35:14, last-modified=2025-02-07 14:22:30
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/sftText/mainVersionFlow)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505203)
 */
export function postDataHubDatasetVersionPostTrainingSftTextMainVersionFlow(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingSftTextMainVersionFlowParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingSftTextMainVersionFlowResponse>(
    '/webApi/data-hub/dataset-version/post-training/sftText/mainVersionFlow',
    {
      data: pick(params, ['pageSize', 'pageNumber']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1498760, catid=255612, projectId=35802, created=2024-12-03 14:00:40, last-modified=2025-01-13 15:19:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/snapshotPage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498760)
 */
export function postDataHubDatasetVersionPostTrainingSnapshotPage(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingSnapshotPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingSnapshotPageResponse>(
    '/webApi/data-hub/dataset-version/post-training/snapshotPage',
    {
      data: pick(params, ['param', 'pageSize', 'pageNumber']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1503557, catid=255612, projectId=35802, created=2024-12-30 16:30:34, last-modified=2024-12-30 16:31:16
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/downloadExcel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503557)
 */
export function getDataHubDatasetVersionPostTrainingDownloadExcel(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingDownloadExcelParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingDownloadExcelResponse>(
    '/webApi/data-hub/dataset-version/post-training/downloadExcel',
    {
      params: pick(params, ['snapshotId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1499475, catid=255612, projectId=35802, created=2024-12-09 10:09:02, last-modified=2024-12-09 10:33:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/taskDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499475)
 */
export function getDataHubDatasetVersionPostTrainingTaskDetail(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingTaskDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingTaskDetailResponse>(
    '/webApi/data-hub/dataset-version/post-training/taskDetail',
    {
      params: pick(params, ['taskId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1506593, catid=255612, projectId=35802, created=2025-02-18 18:05:28, last-modified=2025-02-27 17:27:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/instanceCanEdit)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506593)
 */
export function postDataHubDatasetVersionPostTrainingInstanceCanEdit(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingInstanceCanEditParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingInstanceCanEditResponse>(
    '/webApi/data-hub/dataset-version/post-training/instanceCanEdit',
    {
      data: pick(params, ['datasetVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1506600, catid=255612, projectId=35802, created=2025-02-18 18:07:25, last-modified=2025-02-20 10:31:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/versionCanDelete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506600)
 */
export function postDataHubDatasetVersionPostTrainingVersionCanDelete(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingVersionCanDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingVersionCanDeleteResponse>(
    '/webApi/data-hub/dataset-version/post-training/versionCanDelete',
    {
      data: pick(params, ['datasetVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1502214, catid=255612, projectId=35802, created=2024-12-23 14:55:53, last-modified=2024-12-23 15:11:31
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/instanceDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502214)
 */
export function getDataHubDatasetVersionPostTrainingInstanceDetail(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingInstanceDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingInstanceDetailResponse>(
    '/webApi/data-hub/dataset-version/post-training/instanceDetail',
    {
      params: pick(params, ['instanceId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1498766, catid=255612, projectId=35802, created=2024-12-03 15:14:06, last-modified=2025-01-13 15:13:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/fork)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498766)
 */
export function postDataHubDatasetVersionPostTrainingFork(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingForkParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingForkResponse>(
    '/webApi/data-hub/dataset-version/post-training/fork',
    {
      data: pick(params, ['datasetName', 'datasetVersionName', 'snapshotName', 'labelType', 'sourceSnapshotId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1498873, catid=255612, projectId=35802, created=2024-12-04 10:01:24, last-modified=2024-12-04 10:03:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/syncDataPoolToVersion)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498873)
 */
export function getDataHubDatasetVersionPostTrainingSyncDataPoolToVersion(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingSyncDataPoolToVersionResponse>(
    '/webApi/data-hub/dataset-version/post-training/syncDataPoolToVersion',
    {
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1496346, catid=255612, projectId=35802, created=2024-11-29 16:34:18, last-modified=2025-01-13 15:13:18
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1496346)
 */
export function postDataHubDatasetVersionPostTrainingUpsert(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingUpsertResponse>(
    '/webApi/data-hub/dataset-version/post-training/upsert',
    {
      data: pick(params, [
        'insertType',
        'comment',
        'datasetName',
        'datasetVersionName',
        'snapshotName',
        's3KeyList',
        'storagePath',
        'labelType',
        'instanceTypeCode',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1503713, catid=255612, projectId=35802, created=2025-01-03 10:12:36, last-modified=2025-01-03 18:01:39
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/upsertByIdSelection)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503713)
 */
export function postDataHubDatasetVersionPostTrainingUpsertByIdSelection(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingUpsertByIdSelectionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingUpsertByIdSelectionResponse>(
    '/webApi/data-hub/dataset-version/post-training/upsertByIdSelection',
    {
      data: pick(params, ['snapshotId', 'editType', 's3Key', 'datasetId', 'datasetVersionName', 'labelType']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505531, catid=255612, projectId=35802, created=2025-01-23 12:46:03, last-modified=2025-02-11 10:01:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/sftText/getBaseLineData)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505531)
 */
export function getDataHubDatasetVersionPostTrainingSftTextGetBaseLineData(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingSftTextGetBaseLineDataParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingSftTextGetBaseLineDataResponse>(
    '/webApi/data-hub/dataset-version/post-training/sftText/getBaseLineData',
    {
      params: pick(params, ['experimentId', 'taskId', 'taskVersion']),
      ...options,
    }
  );
}

/**
 * @desc id=1502330, catid=255612, projectId=35802, created=2024-12-23 16:10:57, last-modified=2025-01-03 14:15:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/diff/detailPage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502330)
 */
export function postDataHubDatasetVersionPostTrainingDiffDetailPage(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingDiffDetailPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingDiffDetailPageResponse>(
    '/webApi/data-hub/dataset-version/post-training/diff/detailPage',
    {
      data: pick(params, ['param', 'pageSize', 'pageNumber']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1499088, catid=255612, projectId=35802, created=2024-12-05 10:31:21, last-modified=2025-02-18 19:00:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/snapshotInstancePage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499088)
 */
export function postDataHubDatasetVersionPostTrainingSnapshotInstancePage(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingSnapshotInstancePageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingSnapshotInstancePageResponse>(
    '/webApi/data-hub/dataset-version/post-training/snapshotInstancePage',
    {
      data: pick(params, ['pageNumber', 'pageSize', 'param']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1502344, catid=255612, projectId=35802, created=2024-12-23 16:41:13, last-modified=2024-12-23 16:53:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/diff/listQuery)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502344)
 */
export function postDataHubDatasetVersionPostTrainingDiffListQuery(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingDiffListQueryParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingDiffListQueryResponse>(
    '/webApi/data-hub/dataset-version/post-training/diff/listQuery',
    {
      data: pick(params, ['reportId', 'filed', 'userInput']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1502199, catid=255612, projectId=35802, created=2024-12-23 14:44:11, last-modified=2024-12-25 11:03:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/diff/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502199)
 */
export function postDataHubDatasetVersionPostTrainingDiffPage(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingDiffPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingDiffPageResponse>(
    '/webApi/data-hub/dataset-version/post-training/diff/page',
    {
      data: pick(params, ['param', 'pageSize', 'pageNumber']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505213, catid=255612, projectId=35802, created=2025-01-20 15:35:19, last-modified=2025-02-06 10:57:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/sftText/release)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505213)
 */
export function postDataHubDatasetVersionPostTrainingSftTextRelease(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingSftTextReleaseParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingSftTextReleaseResponse>(
    '/webApi/data-hub/dataset-version/post-training/sftText/release',
    {
      params: pick(params, ['snapshotId', 'name']),
      data: pick(params, ['name', 'snapshotId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1496427, catid=255612, projectId=35802, created=2024-11-29 18:45:32, last-modified=2025-01-13 16:00:21
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/versionTaskPage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1496427)
 */
export function postDataHubDatasetVersionPostTrainingVersionTaskPage(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingVersionTaskPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingVersionTaskPageResponse>(
    '/webApi/data-hub/dataset-version/post-training/versionTaskPage',
    {
      data: pick(params, ['param', 'pageSize', 'pageNumber']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1498870, catid=255612, projectId=35802, created=2024-12-04 09:54:27, last-modified=2024-12-15 17:36:21
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/snapshotOnlineControl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498870)
 */
export function getDataHubDatasetVersionPostTrainingSnapshotOnlineControl(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingSnapshotOnlineControlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingSnapshotOnlineControlResponse>(
    '/webApi/data-hub/dataset-version/post-training/snapshotOnlineControl',
    {
      params: pick(params, ['snapshotId', 'status', 'datasetVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1499466, catid=255612, projectId=35802, created=2024-12-06 19:00:47, last-modified=2024-12-11 10:25:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/viewJson)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499466)
 */
export function getDataHubDatasetVersionPostTrainingViewJson(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingViewJsonParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingViewJsonResponse>(
    '/webApi/data-hub/dataset-version/post-training/viewJson',
    {
      params: pick(params, ['instanceId', 'snapshotId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1502221, catid=255612, projectId=35802, created=2024-12-23 15:05:06, last-modified=2024-12-27 10:09:31
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/diff/generateReport)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502221)
 */
export function postDataHubDatasetVersionPostTrainingDiffGenerateReport(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingDiffGenerateReportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingDiffGenerateReportResponse>(
    '/webApi/data-hub/dataset-version/post-training/diff/generateReport',
    {
      data: pick(params, [
        'sourceDatasetVersionId',
        'sourceDatasetSnapshotId',
        'targetDatasetVersionId',
        'targetDatasetSnapshotId',
        'isReGenerate',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1496215, catid=255612, projectId=35802, created=2024-11-28 19:33:29, last-modified=2024-12-09 15:59:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/queryGroupUser)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1496215)
 */
export function getDataHubDatasetVersionPostTrainingQueryGroupUser(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingQueryGroupUserParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingQueryGroupUserResponse>(
    '/webApi/data-hub/dataset-version/post-training/queryGroupUser',
    {
      params: pick(params, ['groupName']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1499157, catid=255612, projectId=35802, created=2024-12-05 11:05:06, last-modified=2025-01-13 15:13:30
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/updateSnapshotInstance)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499157)
 */
export function postDataHubDatasetVersionPostTrainingUpdateSnapshotInstance(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingUpdateSnapshotInstanceParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingUpdateSnapshotInstanceResponse>(
    '/webApi/data-hub/dataset-version/post-training/updateSnapshotInstance',
    {
      data: pick(params, [
        'editType',
        'snapshotName',
        'snapshotId',
        'editInstanceList',
        'datasetVersionName',
        'datasetName',
        'delInstanceList',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1501292, catid=255612, projectId=35802, created=2024-12-18 19:35:33, last-modified=2024-12-18 19:57:40
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/taskStageList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1501292)
 */
export function getDataHubDatasetVersionPostTrainingTaskStageList(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingTaskStageListResponse>(
    '/webApi/data-hub/dataset-version/post-training/taskStageList',
    {
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1506361, catid=255612, projectId=35802, created=2025-02-17 14:35:49, last-modified=2025-02-17 14:41:40
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/dataQueue)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506361)
 */
export function getDataHubDatasetVersionPostTrainingDataQueue(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingDataQueueResponse>(
    '/webApi/data-hub/dataset-version/post-training/dataQueue',
    {
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1503189, catid=255612, projectId=35802, created=2024-12-25 10:35:11, last-modified=2024-12-25 10:39:24
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/getStageInstanceTypeRelation)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503189)
 */
export function getDataHubDatasetVersionPostTrainingGetStageInstanceTypeRelation(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingGetStageInstanceTypeRelationResponse>(
    '/webApi/data-hub/dataset-version/post-training/getStageInstanceTypeRelation',
    {
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1496222, catid=255612, projectId=35802, created=2024-11-28 19:41:56, last-modified=2024-12-05 14:46:40
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/getFeatureList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1496222)
 */
export function getDataHubDatasetVersionPostTrainingGetFeatureList(
  params: model.dataHub.IGetDataHubDatasetVersionPostTrainingGetFeatureListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionPostTrainingGetFeatureListResponse>(
    '/webApi/data-hub/dataset-version/post-training/getFeatureList',
    {
      params: pick(params, ['snapshotId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1506607, catid=255612, projectId=35802, created=2025-02-18 19:09:51, last-modified=2025-02-27 17:27:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/post-training/instanceCanEditOnSource)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506607)
 */
export function postDataHubDatasetVersionPostTrainingInstanceCanEditOnSource(
  params: model.dataHub.IPostDataHubDatasetVersionPostTrainingInstanceCanEditOnSourceParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPostTrainingInstanceCanEditOnSourceResponse>(
    '/webApi/data-hub/dataset-version/post-training/instanceCanEditOnSource',
    {
      data: pick(params, ['datasetVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1498753, catid=256019, projectId=35802, created=2024-12-03 11:38:26, last-modified=2024-12-15 15:36:29
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/s3/multipart_upload_sign)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498753)
 */
export function postDataHubS3MultipartUploadSign(
  params: model.dataHub.IPostDataHubS3MultipartUploadSignParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubS3MultipartUploadSignResponse>('/webApi/data-hub/s3/multipart_upload_sign', {
    data: pick(params, ['contentType', 'type', 'partNumber', 'uploadId', 'key']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1498751, catid=256019, projectId=35802, created=2024-12-03 11:38:04, last-modified=2024-12-15 15:36:40
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/s3/single_upload_sign)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498751)
 */
export function postDataHubS3SingleUploadSign(
  params: model.dataHub.IPostDataHubS3SingleUploadSignParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubS3SingleUploadSignResponse>('/webApi/data-hub/s3/single_upload_sign', {
    data: pick(params, ['fileName', 'randomName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1498757, catid=256019, projectId=35802, created=2024-12-03 11:38:48, last-modified=2024-12-03 12:59:31
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/s3/temp_url)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498757)
 */
export function postDataHubS3TempUrl(params: model.dataHub.IPostDataHubS3TempUrlParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubS3TempUrlResponse>('/webApi/data-hub/s3/temp_url', {
    data: pick(params, ['key']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1498750, catid=256019, projectId=35802, created=2024-12-03 11:38:01, last-modified=2024-12-03 12:47:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/s3/generate_key)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498750)
 */
export function postDataHubS3GenerateKey(params: model.dataHub.IPostDataHubS3GenerateKeyParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubS3GenerateKeyResponse>('/webApi/data-hub/s3/generate_key', {
    data: pick(params, ['fileName', 'randomName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1503353, catid=256545, projectId=35802, created=2024-12-26 17:27:49, last-modified=2024-12-27 11:37:40
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/release/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503353)
 */
export function postDataHubReleasePage(params: model.dataHub.IPostDataHubReleasePageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubReleasePageResponse>('/webApi/data-hub/release/page', {
    data: pick(params, ['pageSize', 'pageNumber', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1503352, catid=256545, projectId=35802, created=2024-12-26 17:26:58, last-modified=2024-12-26 17:27:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/release/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503352)
 */
export function getDataHubReleaseDelete(params: model.dataHub.IGetDataHubReleaseDeleteParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubReleaseDeleteResponse>('/webApi/data-hub/release/delete', {
    params: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1503343, catid=256545, projectId=35802, created=2024-12-26 17:24:54, last-modified=2024-12-26 17:26:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/release/create)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503343)
 */
export function postDataHubReleaseCreate(params: model.dataHub.IPostDataHubReleaseCreateParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubReleaseCreateResponse>('/webApi/data-hub/release/create', {
    data: pick(params, ['releaseName', 'datasetVersionId', 'snapshotId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1503410, catid=256545, projectId=35802, created=2024-12-27 11:38:07, last-modified=2024-12-27 16:01:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/release/write_enable)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503410)
 */
export function getDataHubReleaseWriteEnable(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubReleaseWriteEnableResponse>('/webApi/data-hub/release/write_enable', {
    ...options,
  });
}

/**
 * @desc id=1503351, catid=256545, projectId=35802, created=2024-12-26 17:26:29, last-modified=2024-12-26 17:26:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/release/update)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503351)
 */
export function postDataHubReleaseUpdate(params: model.dataHub.IPostDataHubReleaseUpdateParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubReleaseUpdateResponse>('/webApi/data-hub/release/update', {
    data: pick(params, ['releaseName', 'datasetVersionId', 'snapshotId', 'id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506395, catid=256597, projectId=35802, created=2025-02-17 16:13:02, last-modified=2025-02-18 15:20:32
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/config/scenePage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506395)
 */
export function postDataHubConfigScenePage(params: model.dataHub.IPostDataHubConfigScenePageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubConfigScenePageResponse>('/webApi/data-hub/config/scenePage', {
    data: pick(params, ['pageSize', 'pageNumber', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506489, catid=256597, projectId=35802, created=2025-02-18 10:06:41, last-modified=2025-02-18 10:17:46
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/config/sceneDel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506489)
 */
export function getDataHubConfigSceneDel(params: model.dataHub.IGetDataHubConfigSceneDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubConfigSceneDelResponse>('/webApi/data-hub/config/sceneDel', {
    params: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506485, catid=256597, projectId=35802, created=2025-02-17 19:45:42, last-modified=2025-02-18 10:16:18
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/config/sceneUpsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506485)
 */
export function postDataHubConfigSceneUpsert(
  params: model.dataHub.IPostDataHubConfigSceneUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubConfigSceneUpsertResponse>('/webApi/data-hub/config/sceneUpsert', {
    data: pick(params, ['id', 'name', 'format', 'defaultValue']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506496, catid=256597, projectId=35802, created=2025-02-18 10:17:49, last-modified=2025-02-18 10:18:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/config/templateDel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506496)
 */
export function getDataHubConfigTemplateDel(params: model.dataHub.IGetDataHubConfigTemplateDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubConfigTemplateDelResponse>('/webApi/data-hub/config/templateDel', {
    params: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1503750, catid=256597, projectId=35802, created=2025-01-03 18:59:10, last-modified=2025-02-18 10:35:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/config/template/page1)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1503750)
 */
export function postDataHubConfigTemplatePage1(
  params: model.dataHub.IPostDataHubConfigTemplatePage1Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubConfigTemplatePage1Response>('/webApi/data-hub/config/template/page1', {
    data: pick(params, ['pageSize', 'pageNumber', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506391, catid=256597, projectId=35802, created=2025-02-17 15:49:27, last-modified=2025-02-19 10:09:44
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/config/templatePage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506391)
 */
export function postDataHubConfigTemplatePage(
  params: model.dataHub.IPostDataHubConfigTemplatePageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubConfigTemplatePageResponse>('/webApi/data-hub/config/templatePage', {
    data: pick(params, ['pageSize', 'pageNumber', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506412, catid=256597, projectId=35802, created=2025-02-17 17:49:44, last-modified=2025-02-17 17:51:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/config/templateUpsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506412)
 */
export function postDataHubConfigTemplateUpsert(
  params: model.dataHub.IPostDataHubConfigTemplateUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubConfigTemplateUpsertResponse>('/webApi/data-hub/config/templateUpsert', {
    data: pick(params, ['id', 'name', 'sceneId', 'content']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506555, catid=256597, projectId=35802, created=2025-02-18 16:51:30, last-modified=2025-02-18 16:52:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/config/getCubeImage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506555)
 */
export function getDataHubConfigGetCubeImage(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubConfigGetCubeImageResponse>('/webApi/data-hub/config/getCubeImage', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1505112, catid=256772, projectId=35802, created=2025-01-17 10:54:58, last-modified=2025-01-17 16:08:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/quality-inspection/fillInspectionRes)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505112)
 */
export function postDataHubQualityInspectionFillInspectionRes(
  params: model.dataHub.IPostDataHubQualityInspectionFillInspectionResParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubQualityInspectionFillInspectionResResponse>(
    '/webApi/data-hub/quality-inspection/fillInspectionRes',
    {
      data: pick(params, ['id', 'qualityInspectionRes']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1505109, catid=256772, projectId=35802, created=2025-01-17 10:53:50, last-modified=2025-02-08 17:27:18
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/quality-inspection/search)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505109)
 */
export function postDataHubQualityInspectionSearch(
  params: model.dataHub.IPostDataHubQualityInspectionSearchParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubQualityInspectionSearchResponse>('/webApi/data-hub/quality-inspection/search', {
    data: pick(params, ['pageSize', 'pageNumber', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1501341, catid=254898, projectId=35802, created=2024-12-19 10:53:02, last-modified=2024-12-30 10:38:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/framework-report/taskDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1501341)
 */
export function getDataHubFrameworkReportTaskDetail(
  params: model.dataHub.IGetDataHubFrameworkReportTaskDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubFrameworkReportTaskDetailResponse>('/webApi/data-hub/framework-report/taskDetail', {
    params: pick(params, ['taskId']),
    ...options,
  });
}

/**
 * @desc id=1488703, catid=254898, projectId=35802, created=2024-11-11 10:54:35, last-modified=2024-11-12 16:14:56
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/framework-report/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1488703)
 */
export function postDataHubFrameworkReportPage(
  params: model.dataHub.IPostDataHubFrameworkReportPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubFrameworkReportPageResponse>('/webApi/data-hub/framework-report/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1501331, catid=254898, projectId=35802, created=2024-12-19 10:46:54, last-modified=2024-12-19 16:07:28
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/framework-report/status)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1501331)
 */
export function getDataHubFrameworkReportStatus(
  params: model.dataHub.IGetDataHubFrameworkReportStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubFrameworkReportStatusResponse>('/webApi/data-hub/framework-report/status', {
    params: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1419712, catid=243537, projectId=35802, created=2024-06-12 16:34:10, last-modified=2024-06-12 16:35:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/instance/viewJson)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1419712)
 */
export function getDataHubDataPoolInstanceViewJson(
  params: model.dataHub.IGetDataHubDataPoolInstanceViewJsonParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataPoolInstanceViewJsonResponse>('/webApi/data-hub/data-pool/instance/viewJson', {
    params: pick(params, ['dataId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1499819, catid=243537, projectId=35802, created=2024-12-10 18:32:51, last-modified=2024-12-10 18:33:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/revertDataPoolCommit)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499819)
 */
export function postDataHubDataPoolV2RevertDataPoolCommit(
  params: model.dataHub.IPostDataHubDataPoolV2RevertDataPoolCommitParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolV2RevertDataPoolCommitResponse>('/webApi/data-hub/data-pool/v2/revertDataPoolCommit', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1499759, catid=243537, projectId=35802, created=2024-12-10 16:42:02, last-modified=2024-12-15 17:58:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/searchForDataPoolCommitFrontPage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1499759)
 */
export function postDataHubDataPoolV2SearchForDataPoolCommitFrontPage(
  params: model.dataHub.IPostDataHubDataPoolV2SearchForDataPoolCommitFrontPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolV2SearchForDataPoolCommitFrontPageResponse>(
    '/webApi/data-hub/data-pool/v2/searchForDataPoolCommitFrontPage',
    {
      data: pick(params, ['pageNumber', 'pageSize', 'param']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1502104, catid=243537, projectId=35802, created=2024-12-20 14:57:11, last-modified=2024-12-20 15:27:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/getInstanceDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1502104)
 */
export function getDataHubDataPoolV2GetInstanceDetail(
  params: model.dataHub.IGetDataHubDataPoolV2GetInstanceDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataPoolV2GetInstanceDetailResponse>('/webApi/data-hub/data-pool/v2/getInstanceDetail', {
    params: pick(params, ['dataId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1404011, catid=243537, projectId=35802, created=2024-05-08 21:06:27, last-modified=2024-12-03 16:59:34
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/instance/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1404011)
 */
export function postDataHubDataPoolInstancePage(
  params: model.dataHub.IPostDataHubDataPoolInstancePageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolInstancePageResponse>('/webApi/data-hub/data-pool/instance/page', {
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1496197, catid=243537, projectId=35802, created=2024-11-28 18:47:09, last-modified=2025-02-18 19:02:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/search)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1496197)
 */
export function postDataHubDataPoolV2Search(params: model.dataHub.IPostDataHubDataPoolV2SearchParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDataPoolV2SearchResponse>('/webApi/data-hub/data-pool/v2/search', {
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1404062, catid=243537, projectId=35802, created=2024-05-08 22:35:15, last-modified=2024-05-11 17:19:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/stage/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1404062)
 */
export function postDataHubDataPoolStagePage(
  params: model.dataHub.IPostDataHubDataPoolStagePageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolStagePageResponse>('/webApi/data-hub/data-pool/stage/page', {
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506564, catid=243537, projectId=35802, created=2025-02-18 17:06:28, last-modified=2025-02-18 17:13:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/instanceCanDelete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506564)
 */
export function getDataHubDataPoolV2InstanceCanDelete(
  params: model.dataHub.IGetDataHubDataPoolV2InstanceCanDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataPoolV2InstanceCanDeleteResponse>('/webApi/data-hub/data-pool/v2/instanceCanDelete', {
    params: pick(params, ['instanceId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1404060, catid=243537, projectId=35802, created=2024-05-08 22:31:57, last-modified=2024-05-09 03:49:18
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/stage/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1404060)
 */
export function postDataHubDataPoolStageDelete(
  params: model.dataHub.IPostDataHubDataPoolStageDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolStageDeleteResponse>('/webApi/data-hub/data-pool/stage/delete', {
    data: pick(params, ['stagingCfgId', 'idList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1498821, catid=243537, projectId=35802, created=2024-12-03 17:13:46, last-modified=2024-12-03 19:43:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/instance/evalDatasetVersionBadCaseMark)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498821)
 */
export function postDataHubDataPoolInstanceEvalDatasetVersionBadCaseMark(
  params: model.dataHub.IPostDataHubDataPoolInstanceEvalDatasetVersionBadCaseMarkParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolInstanceEvalDatasetVersionBadCaseMarkResponse>(
    '/webApi/data-hub/data-pool/instance/evalDatasetVersionBadCaseMark',
    {
      data: pick(params, ['datasetVersionId', 'instanceId', 'badCase']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1500564, catid=243537, projectId=35802, created=2024-12-11 16:11:58, last-modified=2024-12-15 18:46:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/dataPoolCommitDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1500564)
 */
export function postDataHubDataPoolV2DataPoolCommitDetail(
  params: model.dataHub.IPostDataHubDataPoolV2DataPoolCommitDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolV2DataPoolCommitDetailResponse>('/webApi/data-hub/data-pool/v2/dataPoolCommitDetail', {
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1500560, catid=243537, projectId=35802, created=2024-12-11 16:10:46, last-modified=2024-12-11 17:04:19
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/dataPoolCommitDetailRevert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1500560)
 */
export function postDataHubDataPoolV2DataPoolCommitDetailRevert(
  params: model.dataHub.IPostDataHubDataPoolV2DataPoolCommitDetailRevertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolV2DataPoolCommitDetailRevertResponse>(
    '/webApi/data-hub/data-pool/v2/dataPoolCommitDetailRevert',
    {
      data: pick(params, ['idList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1404022, catid=243537, projectId=35802, created=2024-05-08 21:26:37, last-modified=2024-06-12 16:33:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/insert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1404022)
 */
export function postDataHubDataPoolInsert(params: model.dataHub.IPostDataHubDataPoolInsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDataPoolInsertResponse>('/webApi/data-hub/data-pool/insert', {
    data: pick(params, [
      'type',
      'generalType',
      'singleText',
      'tags',
      'comment',
      'datasetVersionName',
      's3UrlList',
      'datasetId',
      'datasetName',
      'storageType',
      'storagePath',
      'stagingCfgId',
      'sourceCodeUrl',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1404046, catid=243537, projectId=35802, created=2024-05-08 21:55:27, last-modified=2024-05-09 04:01:16
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/instance/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1404046)
 */
export function getDataHubDataPoolInstanceDetail(
  params: model.dataHub.IGetDataHubDataPoolInstanceDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataPoolInstanceDetailResponse>('/webApi/data-hub/data-pool/instance/detail', {
    params: pick(params, ['dataId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1404053, catid=243537, projectId=35802, created=2024-05-08 22:19:17, last-modified=2024-05-09 14:12:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/instance/update)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1404053)
 */
export function postDataHubDataPoolInstanceUpdate(
  params: model.dataHub.IPostDataHubDataPoolInstanceUpdateParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolInstanceUpdateResponse>('/webApi/data-hub/data-pool/instance/update', {
    data: pick(params, ['dataId', 'tags', 'creator', 'comment']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1498764, catid=243537, projectId=35802, created=2024-12-03 14:47:42, last-modified=2024-12-16 14:09:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/featureKeyListByScope)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498764)
 */
export function postDataHubDataPoolV2FeatureKeyListByScope(
  params: model.dataHub.IPostDataHubDataPoolV2FeatureKeyListByScopeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolV2FeatureKeyListByScopeResponse>(
    '/webApi/data-hub/data-pool/v2/featureKeyListByScope',
    {
      data: pick(params, ['scopeList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1404064, catid=243537, projectId=35802, created=2024-05-09 03:41:11, last-modified=2024-05-13 19:10:19
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/stage/add)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1404064)
 */
export function postDataHubDataPoolStageAdd(params: model.dataHub.IPostDataHubDataPoolStageAddParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDataPoolStageAddResponse>('/webApi/data-hub/data-pool/stage/add', {
    data: pick(params, ['dataPoolQuery', 'stagingCfgId', 'dataIdList', 'allSelect']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1500567, catid=243537, projectId=35802, created=2024-12-11 16:14:23, last-modified=2024-12-13 16:58:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/featureModifyHistory)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1500567)
 */
export function postDataHubDataPoolV2FeatureModifyHistory(
  params: model.dataHub.IPostDataHubDataPoolV2FeatureModifyHistoryParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolV2FeatureModifyHistoryResponse>('/webApi/data-hub/data-pool/v2/featureModifyHistory', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1496204, catid=243537, projectId=35802, created=2024-11-28 18:58:27, last-modified=2025-02-20 15:49:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/modifyInstanceAndFeature)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1496204)
 */
export function postDataHubDataPoolV2ModifyInstanceAndFeature(
  params: model.dataHub.IPostDataHubDataPoolV2ModifyInstanceAndFeatureParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolV2ModifyInstanceAndFeatureResponse>(
    '/webApi/data-hub/data-pool/v2/modifyInstanceAndFeature',
    {
      data: params,
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1457642, catid=243537, projectId=35802, created=2024-08-22 20:58:30, last-modified=2024-11-21 15:35:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/instance/updateDatasetVersionInstance)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1457642)
 */
export function postDataHubDataPoolInstanceUpdateDatasetVersionInstance(
  params: model.dataHub.IPostDataHubDataPoolInstanceUpdateDatasetVersionInstanceParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolInstanceUpdateDatasetVersionInstanceResponse>(
    '/webApi/data-hub/data-pool/instance/updateDatasetVersionInstance',
    {
      data: pick(params, ['editType', 'datasetVersionId', 'editInstanceList', 'datasetVersionName', 'datasetId', 'delInstanceList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1404055, catid=243537, projectId=35802, created=2024-05-08 22:26:46, last-modified=2024-05-13 19:12:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/stage/getStageInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1404055)
 */
export function getDataHubDataPoolStageGetStageInfo(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDataPoolStageGetStageInfoResponse>('/webApi/data-hub/data-pool/stage/getStageInfo', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1457650, catid=243537, projectId=35802, created=2024-08-22 21:08:38, last-modified=2024-08-23 15:05:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/eval/checkRelevantSubset)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1457650)
 */
export function getDataHubDatasetVersionTrainEvalCheckRelevantSubset(
  params: model.dataHub.IGetDataHubDatasetVersionTrainEvalCheckRelevantSubsetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainEvalCheckRelevantSubsetResponse>(
    '/webApi/data-hub/dataset-version/train/eval/checkRelevantSubset',
    {
      params: pick(params, ['datasetVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1485057, catid=243537, projectId=35802, created=2024-11-01 14:32:41, last-modified=2024-11-01 14:34:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/instance/getModelEvalDataSubSetFileUrl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1485057)
 */
export function getDataHubDataPoolInstanceGetModelEvalDataSubSetFileUrl(
  params: model.dataHub.IGetDataHubDataPoolInstanceGetModelEvalDataSubSetFileUrlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataPoolInstanceGetModelEvalDataSubSetFileUrlResponse>(
    '/webApi/data-hub/data-pool/instance/getModelEvalDataSubSetFileUrl',
    {
      params: pick(params, ['datasetId', 'datasetVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1506570, catid=243537, projectId=35802, created=2025-02-18 17:21:28, last-modified=2025-02-18 17:23:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/v2/instanceRelatedDatasetAndVersion)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506570)
 */
export function getDataHubDataPoolV2InstanceRelatedDatasetAndVersion(
  params: model.dataHub.IGetDataHubDataPoolV2InstanceRelatedDatasetAndVersionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataPoolV2InstanceRelatedDatasetAndVersionResponse>(
    '/webApi/data-hub/data-pool/v2/instanceRelatedDatasetAndVersion',
    {
      params: pick(params, ['instanceId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1410161, catid=234905, projectId=35802, created=2024-05-17 17:01:08, last-modified=2024-05-17 17:13:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/recalculate)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1410161)
 */
export function postDataHubDataUnderstandingRecalculate(
  params: model.dataHub.IPostDataHubDataUnderstandingRecalculateParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingRecalculateResponse>('/webApi/data-hub/data-understanding/recalculate', {
    data: pick(params, ['datasetId', 'datasetVersionId', 'metaVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1348350, catid=234905, projectId=35802, created=2023-12-27 14:48:42, last-modified=2024-05-28 10:30:34
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/singleDataText)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1348350)
 */
export function postDataHubDataUnderstandingSingleDataText(
  params: model.dataHub.IPostDataHubDataUnderstandingSingleDataTextParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingSingleDataTextResponse>(
    '/webApi/data-hub/data-understanding/singleDataText',
    {
      data: pick(params, ['datasetId', 'datasetVersionId', 'metaVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1357037, catid=234905, projectId=35802, created=2024-01-18 17:21:05, last-modified=2024-01-23 17:00:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/detail/fieldValues)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1357037)
 */
export function postDataHubDataUnderstandingDetailFieldValues(
  params: model.dataHub.IPostDataHubDataUnderstandingDetailFieldValuesParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingDetailFieldValuesResponse>(
    '/webApi/data-hub/data-understanding/detail/fieldValues',
    {
      data: pick(params, ['datasetId', 'datasetVersionId', 'fieldName', 'sort']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1356987, catid=234905, projectId=35802, created=2024-01-18 16:33:19, last-modified=2024-02-29 10:31:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/detail/query)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1356987)
 */
export function postDataHubDataUnderstandingDetailQuery(
  params: model.dataHub.IPostDataHubDataUnderstandingDetailQueryParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingDetailQueryResponse>('/webApi/data-hub/data-understanding/detail/query', {
    data: pick(params, [
      'datasetId',
      'datasetVersionId',
      'inputList',
      'targetList',
      'outputList',
      'tagList',
      'sourceList',
      'page',
      'pageSize',
      'highlightEnable',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1357027, catid=234905, projectId=35802, created=2024-01-18 17:14:55, last-modified=2024-01-18 17:31:45
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/detail/queryCategory)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1357027)
 */
export function getDataHubDataUnderstandingDetailQueryCategory(
  params: model.dataHub.IGetDataHubDataUnderstandingDetailQueryCategoryParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataUnderstandingDetailQueryCategoryResponse>(
    '/webApi/data-hub/data-understanding/detail/queryCategory',
    {
      params: pick(params, ['dimId', 'level']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1413983, catid=234905, projectId=35802, created=2024-05-27 10:21:13, last-modified=2024-05-27 10:25:25
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/tab)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1413983)
 */
export function getDataHubDataUnderstandingTab(
  params: model.dataHub.IGetDataHubDataUnderstandingTabParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataUnderstandingTabResponse>('/webApi/data-hub/data-understanding/tab', {
    params: pick(params, ['metaVersionId']),
    ...options,
  });
}

/**
 * @desc id=1348391, catid=234905, projectId=35802, created=2023-12-27 16:01:09, last-modified=2024-05-17 16:34:25
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/multipleCompareText)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1348391)
 */
export function postDataHubDataUnderstandingMultipleCompareText(
  params: model.dataHub.IPostDataHubDataUnderstandingMultipleCompareTextParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingMultipleCompareTextResponse>(
    '/webApi/data-hub/data-understanding/multipleCompareText',
    {
      data: pick(params, ['datasetId', 'datasetVersionId', 'compareList', 'metaVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1348458, catid=234905, projectId=35802, created=2023-12-27 16:34:17, last-modified=2024-05-17 16:40:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/dataDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1348458)
 */
export function postDataHubDataUnderstandingDataDetail(
  params: model.dataHub.IPostDataHubDataUnderstandingDataDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingDataDetailResponse>('/webApi/data-hub/data-understanding/dataDetail', {
    data: pick(params, ['datasetId', 'datasetVersionId', 'tableId', 'categoryId', 'compareList', 'metaVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1348444, catid=234905, projectId=35802, created=2023-12-27 16:19:28, last-modified=2024-05-17 16:38:21
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/dataBarChart)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1348444)
 */
export function postDataHubDataUnderstandingDataBarChart(
  params: model.dataHub.IPostDataHubDataUnderstandingDataBarChartParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingDataBarChartResponse>('/webApi/data-hub/data-understanding/dataBarChart', {
    data: pick(params, ['datasetId', 'datasetVersionId', 'tableId', 'categoryId', 'compareList', 'metaVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1348405, catid=234905, projectId=35802, created=2023-12-27 16:08:59, last-modified=2024-05-17 17:25:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/queryCategoryList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1348405)
 */
export function postDataHubDataUnderstandingQueryCategoryList(
  params: model.dataHub.IPostDataHubDataUnderstandingQueryCategoryListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingQueryCategoryListResponse>(
    '/webApi/data-hub/data-understanding/queryCategoryList',
    {
      data: pick(params, ['tableId', 'datasetId', 'datasetVersionId', 'metaVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1414131, catid=234905, projectId=35802, created=2024-05-27 14:56:05, last-modified=2024-05-28 14:38:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/tagVersionConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1414131)
 */
export function getDataHubDataUnderstandingTagVersionConfig(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDataUnderstandingTagVersionConfigResponse>(
    '/webApi/data-hub/data-understanding/tagVersionConfig',
    {
      ...options,
    }
  );
}

/**
 * @desc id=1429117, catid=234905, projectId=35802, created=2024-06-27 14:58:10, last-modified=2024-06-27 15:37:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/modelEvalMetaVersion/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1429117)
 */
export function postDataHubDataUnderstandingModelEvalMetaVersionList(
  params: model.dataHub.IPostDataHubDataUnderstandingModelEvalMetaVersionListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingModelEvalMetaVersionListResponse>(
    '/webApi/data-hub/data-understanding/modelEvalMetaVersion/list',
    {
      data: pick(params, ['datasetVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1348454, catid=234905, projectId=35802, created=2023-12-27 16:27:57, last-modified=2024-05-17 16:39:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-understanding/dataPieChart)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1348454)
 */
export function postDataHubDataUnderstandingDataPieChart(
  params: model.dataHub.IPostDataHubDataUnderstandingDataPieChartParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataUnderstandingDataPieChartResponse>('/webApi/data-hub/data-understanding/dataPieChart', {
    data: pick(params, ['datasetId', 'datasetVersionId', 'tableId', 'categoryId', 'compareList', 'metaVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1393175, catid=242106, projectId=35802, created=2024-04-11 16:37:29, last-modified=2024-04-12 18:29:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/view/esIndex)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1393175)
 */
export function postDataHubDatasetViewEsIndex(
  params: model.dataHub.IPostDataHubDatasetViewEsIndexParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetViewEsIndexResponse>('/webApi/data-hub/dataset/view/esIndex', {
    data: pick(params, ['type']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1393155, catid=242106, projectId=35802, created=2024-04-11 16:35:13, last-modified=2024-07-17 17:36:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/view/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1393155)
 */
export function postDataHubDatasetViewUpsert(
  params: model.dataHub.IPostDataHubDatasetViewUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetViewUpsertResponse>('/webApi/data-hub/dataset/view/upsert', {
    data: pick(params, ['id', 'name', 'esIndexInfoList', 'type']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1393159, catid=242106, projectId=35802, created=2024-04-11 16:36:05, last-modified=2024-04-12 11:25:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/view/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1393159)
 */
export function postDataHubDatasetViewDelete(
  params: model.dataHub.IPostDataHubDatasetViewDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetViewDeleteResponse>('/webApi/data-hub/dataset/view/delete', {
    params: pick(params, ['id']),
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1393166, catid=242106, projectId=35802, created=2024-04-11 16:37:00, last-modified=2024-04-12 18:30:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/view/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1393166)
 */
export function postDataHubDatasetViewPage(params: model.dataHub.IPostDataHubDatasetViewPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDatasetViewPageResponse>('/webApi/data-hub/dataset/view/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506706, catid=231924, projectId=35802, created=2025-02-18 19:27:41, last-modified=2025-02-18 19:28:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/datasetAndVersion/getCurUserDataStage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506706)
 */
export function getDataHubDatasetTrainDatasetAndVersionGetCurUserDataStage(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetTrainDatasetAndVersionGetCurUserDataStageResponse>(
    '/webApi/data-hub/dataset/train/datasetAndVersion/getCurUserDataStage',
    {
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1485048, catid=231924, projectId=35802, created=2024-11-01 14:20:27, last-modified=2024-11-01 14:28:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/eval/relevantSubsetPage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1485048)
 */
export function getDataHubDatasetVersionTrainEvalRelevantSubsetPage(
  params: model.dataHub.IGetDataHubDatasetVersionTrainEvalRelevantSubsetPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainEvalRelevantSubsetPageResponse>(
    '/webApi/data-hub/dataset-version/train/eval/relevantSubsetPage',
    {
      params: pick(params, ['datasetId', 'datasetVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1490044, catid=231924, projectId=35802, created=2024-11-13 19:50:18, last-modified=2024-11-28 10:45:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/train/leakage/instance/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1490044)
 */
export function postDataHubDataPoolTrainLeakageInstancePage(
  params: model.dataHub.IPostDataHubDataPoolTrainLeakageInstancePageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolTrainLeakageInstancePageResponse>(
    '/webApi/data-hub/data-pool/train/leakage/instance/page',
    {
      data: pick(params, ['param', 'pageSize', 'pageNumber']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1490923, catid=231924, projectId=35802, created=2024-11-15 16:23:32, last-modified=2024-11-28 10:45:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/train/leakage/instance/statistic)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1490923)
 */
export function postDataHubDataPoolTrainLeakageInstanceStatistic(
  params: model.dataHub.IPostDataHubDataPoolTrainLeakageInstanceStatisticParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolTrainLeakageInstanceStatisticResponse>(
    '/webApi/data-hub/data-pool/train/leakage/instance/statistic',
    {
      data: pick(params, ['datasetId', 'datasetVersionId', 'experimentId', 'taskId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1359109, catid=231924, projectId=35802, created=2024-01-22 11:01:06, last-modified=2024-01-22 11:01:31
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1359109)
 */
export function getDataHubDatasetTrainDel(params: model.dataHub.IGetDataHubDatasetTrainDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetTrainDelResponse>('/webApi/data-hub/dataset/train/del', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1490046, catid=231924, projectId=35802, created=2024-11-13 19:57:51, last-modified=2024-11-28 10:45:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/train/leakage/instance/re-check)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1490046)
 */
export function postDataHubDataPoolTrainLeakageInstanceReCheck(
  params: model.dataHub.IPostDataHubDataPoolTrainLeakageInstanceReCheckParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolTrainLeakageInstanceReCheckResponse>(
    '/webApi/data-hub/data-pool/train/leakage/instance/re-check',
    {
      data: pick(params, ['id', 'markType']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1490019, catid=231924, projectId=35802, created=2024-11-13 19:17:26, last-modified=2024-11-28 10:44:32
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/train/leakage/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1490019)
 */
export function postDataHubDataPoolTrainLeakagePage(
  params: model.dataHub.IPostDataHubDataPoolTrainLeakagePageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolTrainLeakagePageResponse>('/webApi/data-hub/data-pool/train/leakage/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1490042, catid=231924, projectId=35802, created=2024-11-13 19:39:15, last-modified=2024-11-29 14:20:17
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-pool/train/leakage/indexList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1490042)
 */
export function postDataHubDataPoolTrainLeakageIndexList(
  params: model.dataHub.IPostDataHubDataPoolTrainLeakageIndexListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataPoolTrainLeakageIndexListResponse>('/webApi/data-hub/data-pool/train/leakage/indexList', {
    data: pick(params, ['datasetId', 'datasetVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1363707, catid=231924, projectId=35802, created=2024-01-30 14:39:14, last-modified=2024-01-31 11:36:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/manage/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1363707)
 */
export function postDataHubDatasetVersionTrainManagePage(
  params: model.dataHub.IPostDataHubDatasetVersionTrainManagePageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionTrainManagePageResponse>('/webApi/data-hub/dataset-version/train/manage/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426339, catid=231924, projectId=35802, created=2024-06-24 15:57:35, last-modified=2025-01-23 18:13:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/datasetAndVersion/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426339)
 */
export function postDataHubDatasetTrainDatasetAndVersionPage(
  params: model.dataHub.IPostDataHubDatasetTrainDatasetAndVersionPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetTrainDatasetAndVersionPageResponse>(
    '/webApi/data-hub/dataset/train/datasetAndVersion/page',
    {
      data: pick(params, ['param', 'pageSize', 'pageNumber']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1426979, catid=231924, projectId=35802, created=2024-06-25 17:48:24, last-modified=2024-07-04 10:32:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/datasetAndVersion/searchIntroduce)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426979)
 */
export function postDataHubDatasetTrainDatasetAndVersionSearchIntroduce(
  params: model.dataHub.IPostDataHubDatasetTrainDatasetAndVersionSearchIntroduceParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetTrainDatasetAndVersionSearchIntroduceResponse>(
    '/webApi/data-hub/dataset/train/datasetAndVersion/searchIntroduce',
    {
      params: pick(params, ['id']),
      data: pick(params, ['type', 'dataId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1426989, catid=231924, projectId=35802, created=2024-06-25 17:55:18, last-modified=2024-07-04 10:32:17
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/datasetAndVersion/insertIntroduce)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426989)
 */
export function postDataHubDatasetTrainDatasetAndVersionInsertIntroduce(
  params: model.dataHub.IPostDataHubDatasetTrainDatasetAndVersionInsertIntroduceParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetTrainDatasetAndVersionInsertIntroduceResponse>(
    '/webApi/data-hub/dataset/train/datasetAndVersion/insertIntroduce',
    {
      params: pick(params, ['id']),
      data: pick(params, ['type', 'dataId', 'introduce']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1464432, catid=231924, projectId=35802, created=2024-08-28 11:09:24, last-modified=2024-08-28 11:11:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/datasetAndVersion/checkUserOwnerRole)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1464432)
 */
export function getDataHubDatasetTrainDatasetAndVersionCheckUserOwnerRole(
  params: model.dataHub.IGetDataHubDatasetTrainDatasetAndVersionCheckUserOwnerRoleParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetTrainDatasetAndVersionCheckUserOwnerRoleResponse>(
    '/webApi/data-hub/dataset/train/datasetAndVersion/checkUserOwnerRole',
    {
      params: pick(params, ['datasetId', 'datasetVersionId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1329701, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2024-10-29 14:49:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329701)
 */
export function postDataHubDatasetTrainUpsert(
  params: model.dataHub.IPostDataHubDatasetTrainUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetTrainUpsertResponse>('/webApi/data-hub/dataset/train/upsert', {
    data: pick(params, ['name', 'stage', 'type', 'language', 'source', 'resourceType', 'comment', 'baseDatasetId', 'id', 'permission']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1468224, catid=231924, projectId=35802, created=2024-09-04 15:10:30, last-modified=2024-09-04 15:13:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/batch_upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1468224)
 */
export function postDataHubDatasetVersionTrainBatchUpsert(
  params: model.dataHub.IPostDataHubDatasetVersionTrainBatchUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionTrainBatchUpsertResponse>(
    '/webApi/data-hub/dataset-version/train/batch_upsert',
    {
      data: params,
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1329695, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2024-07-01 18:31:28
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/categoriesIn)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329695)
 */
export function getDataHubCategoryCategoriesIn(
  params: model.dataHub.IGetDataHubCategoryCategoriesInParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubCategoryCategoriesInResponse>('/webApi/data-hub/category/categoriesIn', {
    params: pick(params, ['dimId', 'parentIds', 'parentId']),
    ...options,
  });
}

/**
 * @desc id=1329702, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2023-11-21 14:37:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dashboard/dataset/daily_work)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329702)
 */
export function getDataHubDashboardDatasetDailyWork(
  params: model.dataHub.IGetDataHubDashboardDatasetDailyWorkParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDashboardDatasetDailyWorkResponse>('/webApi/data-hub/dashboard/dataset/daily_work', {
    params: pick(params, ['date']),
    ...options,
  });
}

/**
 * @desc id=1329705, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2024-12-03 14:30:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329705)
 */
export function getDataHubDatasetTrainDetail(
  params: model.dataHub.IGetDataHubDatasetTrainDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetTrainDetailResponse>('/webApi/data-hub/dataset/train/detail', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1329707, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2024-01-03 16:14:29
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329707)
 */
export function postDataHubDatasetTrainPage(params: model.dataHub.IPostDataHubDatasetTrainPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDatasetTrainPageResponse>('/webApi/data-hub/dataset/train/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329710, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2024-09-02 16:05:19
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329710)
 */
export function postDataHubDatasetVersionTrainUpsert(
  params: model.dataHub.IPostDataHubDatasetVersionTrainUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionTrainUpsertResponse>('/webApi/data-hub/dataset-version/train/upsert', {
    data: pick(params, [
      'storageType',
      'id',
      'name',
      'datasetId',
      'storageLocation',
      'storageSpace ',
      'quantityValue',
      'quantityUnit',
      'generateTime',
      'bases',
      'extra',
      'comment',
      'type',
      'format',
      'tokenNum',
      'category',
      'associatedModel',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329719, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2023-11-21 14:37:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329719)
 */
export function getDataHubDatasetVersionTrainDel(
  params: model.dataHub.IGetDataHubDatasetVersionTrainDelParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainDelResponse>('/webApi/data-hub/dataset-version/train/del', {
    params: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329728, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2025-01-20 15:41:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329728)
 */
export function getDataHubDatasetVersionTrainDetail(
  params: model.dataHub.IGetDataHubDatasetVersionTrainDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainDetailResponse>('/webApi/data-hub/dataset-version/train/detail', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1329735, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2024-07-05 15:58:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329735)
 */
export function postDataHubDatasetVersionTrainPage(
  params: model.dataHub.IPostDataHubDatasetVersionTrainPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionTrainPageResponse>('/webApi/data-hub/dataset-version/train/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329741, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2023-11-21 14:37:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/types)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329741)
 */
export function getDataHubDatasetVersionTypes(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTypesResponse>('/webApi/data-hub/dataset-version/types', {
    ...options,
  });
}

/**
 * @desc id=1329743, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2023-11-21 14:37:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dashboard/dataset/overview)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329743)
 */
export function getDataHubDashboardDatasetOverview(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDashboardDatasetOverviewResponse>('/webApi/data-hub/dashboard/dataset/overview', {
    ...options,
  });
}

/**
 * @desc id=1329751, catid=231924, projectId=35802, created=2023-11-21 14:37:50, last-modified=2023-11-21 14:37:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dashboard/dataset/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329751)
 */
export function getDataHubDashboardDatasetDetail(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDashboardDatasetDetailResponse>('/webApi/data-hub/dashboard/dataset/detail', {
    ...options,
  });
}

/**
 * @desc id=1329755, catid=231924, projectId=35802, created=2023-11-21 14:37:50, last-modified=2023-11-21 14:37:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/categories)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329755)
 */
export function getDataHubCategoryCategories(
  params: model.dataHub.IGetDataHubCategoryCategoriesParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubCategoryCategoriesResponse>('/webApi/data-hub/category/categories', {
    params: pick(params, ['dimId', 'parentId']),
    ...options,
  });
}

/**
 * @desc id=1495982, catid=233248, projectId=35802, created=2024-11-27 17:18:17, last-modified=2024-11-27 17:24:19
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/createAndSaveExperiment)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1495982)
 */
export function postDataHubExperimentCreateAndSaveExperiment(
  params: model.dataHub.IPostDataHubExperimentCreateAndSaveExperimentParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentCreateAndSaveExperimentResponse>(
    '/webApi/data-hub/experiment/createAndSaveExperiment',
    {
      data: pick(params, ['createRequest', 'saveRequest']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1489289, catid=233248, projectId=35802, created=2024-11-12 16:53:42, last-modified=2025-04-01 17:12:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/templates/sft_merge)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1489289)
 */
export function getDataHubExperimentTemplatesSftMerge(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubExperimentTemplatesSftMergeResponse>('/webApi/data-hub/experiment/templates/sft_merge', {
    ...options,
  });
}

/**
 * @desc id=1332125, catid=233248, projectId=35802, created=2023-11-23 21:11:20, last-modified=2025-04-01 17:12:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332125)
 */
export function postDataHubExperimentTaskPage(
  params: model.dataHub.IPostDataHubExperimentTaskPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskPageResponse>('/webApi/data-hub/experiment/task/page', {
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329757, catid=233248, projectId=35802, created=2023-11-21 14:37:51, last-modified=2025-04-01 17:12:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/types)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329757)
 */
export function getDataHubExperimentTypes(params: model.dataHub.IGetDataHubExperimentTypesParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubExperimentTypesResponse>('/webApi/data-hub/experiment/types', {
    params: pick(params, ['businessType']),
    ...options,
  });
}

/**
 * @desc id=1355340, catid=233248, projectId=35802, created=2024-01-15 15:41:57, last-modified=2025-04-01 17:12:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/runFrom)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1355340)
 */
export function postDataHubExperimentTaskRunFrom(
  params: model.dataHub.IPostDataHubExperimentTaskRunFromParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskRunFromResponse>('/webApi/data-hub/experiment/task/runFrom', {
    data: pick(params, ['description', 'nodeId', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1387678, catid=233248, projectId=35802, created=2024-03-29 17:48:06, last-modified=2025-04-01 17:12:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/run_external)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1387678)
 */
export function postDataHubExperimentTaskRunExternal(
  params: model.dataHub.IPostDataHubExperimentTaskRunExternalParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskRunExternalResponse>('/webApi/data-hub/experiment/task/run_external', {
    data: pick(params, ['commonContext', 'description', 'experimentId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1387679, catid=233248, projectId=35802, created=2024-03-29 17:48:07, last-modified=2025-04-01 17:12:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/status_external)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1387679)
 */
export function postDataHubExperimentTaskStatusExternal(
  params: model.dataHub.IPostDataHubExperimentTaskStatusExternalParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskStatusExternalResponse>('/webApi/data-hub/experiment/task/status_external', {
    data: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1389166, catid=233248, projectId=35802, created=2024-04-08 11:30:52, last-modified=2025-04-01 17:12:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/scene)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1389166)
 */
export function getDataHubExperimentScene(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubExperimentSceneResponse>('/webApi/data-hub/experiment/scene', {
    ...options,
  });
}

/**
 * @desc id=1329761, catid=233248, projectId=35802, created=2023-11-21 14:37:51, last-modified=2025-04-01 17:12:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329761)
 */
export function postDataHubExperimentUpsert(params: model.dataHub.IPostDataHubExperimentUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubExperimentUpsertResponse>('/webApi/data-hub/experiment/upsert', {
    data: pick(params, ['experimentDescription', 'experimentName', 'experimentType', 'resourceId', 'resourceType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329766, catid=233248, projectId=35802, created=2023-11-21 14:37:51, last-modified=2025-04-01 17:12:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/fork)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329766)
 */
export function postDataHubExperimentFork(params: model.dataHub.IPostDataHubExperimentForkParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubExperimentForkResponse>('/webApi/data-hub/experiment/fork', {
    data: pick(params, ['baseExperimentId', 'experimentDescription', 'experimentName', 'resourceId', 'resourceType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329770, catid=233248, projectId=35802, created=2023-11-21 14:37:51, last-modified=2025-04-01 17:12:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329770)
 */
export function getDataHubExperimentDel(params: model.dataHub.IGetDataHubExperimentDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubExperimentDelResponse>('/webApi/data-hub/experiment/del', {
    params: pick(params, ['experimentId']),
    ...options,
  });
}

/**
 * @desc id=1329777, catid=233248, projectId=35802, created=2023-11-21 14:37:52, last-modified=2025-04-01 17:12:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/baseinfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329777)
 */
export function postDataHubExperimentBaseinfo(
  params: model.dataHub.IPostDataHubExperimentBaseinfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentBaseinfoResponse>('/webApi/data-hub/experiment/baseinfo', {
    data: pick(params, ['businessType', 'experimentId', 'experimentName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329783, catid=233248, projectId=35802, created=2023-11-21 14:37:52, last-modified=2025-04-01 17:12:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/conf/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329783)
 */
export function getDataHubExperimentConfDetail(
  params: model.dataHub.IGetDataHubExperimentConfDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubExperimentConfDetailResponse>('/webApi/data-hub/experiment/conf/detail', {
    params: pick(params, ['experimentId']),
    ...options,
  });
}

/**
 * @desc id=1332122, catid=233248, projectId=35802, created=2023-11-23 21:08:35, last-modified=2025-04-01 17:12:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332122)
 */
export function postDataHubExperimentPage(params: model.dataHub.IPostDataHubExperimentPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubExperimentPageResponse>('/webApi/data-hub/experiment/page', {
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329784, catid=233248, projectId=35802, created=2023-11-21 14:37:52, last-modified=2025-04-01 17:12:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/conf/save)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329784)
 */
export function postDataHubExperimentConfSave(
  params: model.dataHub.IPostDataHubExperimentConfSaveParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentConfSaveResponse>('/webApi/data-hub/experiment/conf/save', {
    data: pick(params, ['edges', 'experimentId', 'nodes']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329792, catid=233248, projectId=35802, created=2023-11-21 14:37:52, last-modified=2025-04-01 17:12:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/node_conf)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329792)
 */
export function postDataHubExperimentTaskNodeConf(
  params: model.dataHub.IPostDataHubExperimentTaskNodeConfParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskNodeConfResponse>('/webApi/data-hub/experiment/task/node_conf', {
    data: pick(params, ['node', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329800, catid=233248, projectId=35802, created=2023-11-21 14:37:52, last-modified=2025-04-01 17:12:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/run)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329800)
 */
export function postDataHubExperimentTaskRun(
  params: model.dataHub.IPostDataHubExperimentTaskRunParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskRunResponse>('/webApi/data-hub/experiment/task/run', {
    data: pick(params, ['commonContext', 'description', 'experimentId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329805, catid=233248, projectId=35802, created=2023-11-21 14:37:52, last-modified=2025-04-01 17:12:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/stop)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329805)
 */
export function postDataHubExperimentTaskStop(
  params: model.dataHub.IPostDataHubExperimentTaskStopParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskStopResponse>('/webApi/data-hub/experiment/task/stop', {
    data: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329807, catid=233248, projectId=35802, created=2023-11-21 14:37:53, last-modified=2025-04-01 17:12:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329807)
 */
export function postDataHubExperimentTaskDetail(
  params: model.dataHub.IPostDataHubExperimentTaskDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskDetailResponse>('/webApi/data-hub/experiment/task/detail', {
    data: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329816, catid=233248, projectId=35802, created=2023-11-21 14:37:53, last-modified=2025-04-01 17:12:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/status)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329816)
 */
export function postDataHubExperimentTaskStatus(
  params: model.dataHub.IPostDataHubExperimentTaskStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskStatusResponse>('/webApi/data-hub/experiment/task/status', {
    data: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329821, catid=233248, projectId=35802, created=2023-11-21 14:37:53, last-modified=2025-04-01 17:12:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/recover)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329821)
 */
export function postDataHubExperimentTaskRecover(
  params: model.dataHub.IPostDataHubExperimentTaskRecoverParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskRecoverResponse>('/webApi/data-hub/experiment/task/recover', {
    data: pick(params, ['taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329828, catid=233248, projectId=35802, created=2023-11-21 14:37:53, last-modified=2025-04-01 17:12:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/rerun)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329828)
 */
export function postDataHubExperimentTaskRerun(
  params: model.dataHub.IPostDataHubExperimentTaskRerunParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskRerunResponse>('/webApi/data-hub/experiment/task/rerun', {
    data: pick(params, ['description', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329832, catid=233248, projectId=35802, created=2023-11-21 14:37:53, last-modified=2025-04-01 17:12:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/node/result)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329832)
 */
export function postDataHubExperimentTaskNodeResult(
  params: model.dataHub.IPostDataHubExperimentTaskNodeResultParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskNodeResultResponse>('/webApi/data-hub/experiment/task/node/result', {
    data: pick(params, ['nodeId', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329834, catid=233248, projectId=35802, created=2023-11-21 14:37:53, last-modified=2025-04-01 17:12:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/node/mlp_log)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329834)
 */
export function postDataHubExperimentTaskNodeMlpLog(
  params: model.dataHub.IPostDataHubExperimentTaskNodeMlpLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskNodeMlpLogResponse>('/webApi/data-hub/experiment/task/node/mlp_log', {
    data: pick(params, ['nodeId', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329838, catid=233248, projectId=35802, created=2023-11-21 14:37:53, last-modified=2025-04-01 17:12:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/node/hope_log)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329838)
 */
export function postDataHubExperimentTaskNodeHopeLog(
  params: model.dataHub.IPostDataHubExperimentTaskNodeHopeLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubExperimentTaskNodeHopeLogResponse>('/webApi/data-hub/experiment/task/node/hope_log', {
    data: pick(params, ['nodeId', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1433210, catid=233497, projectId=35802, created=2024-07-08 11:20:25, last-modified=2025-04-01 17:11:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/model-tagging)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1433210)
 */
export function getDataHubDatasetVersionTrainModelTagging(
  params: model.dataHub.IGetDataHubDatasetVersionTrainModelTaggingParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainModelTaggingResponse>(
    '/webApi/data-hub/dataset-version/train/model-tagging',
    {
      params: pick(params, ['datasetVersionId']),
      ...options,
    }
  );
}

/**
 * @desc id=1452039, catid=233497, projectId=35802, created=2024-08-05 16:08:58, last-modified=2025-04-01 17:11:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/accept-snapshot)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1452039)
 */
export function getDataHubDatasetVersionTrainAcceptSnapshot(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainAcceptSnapshotResponse>(
    '/webApi/data-hub/dataset-version/train/accept-snapshot',
    {
      ...options,
    }
  );
}

/**
 * @desc id=1336624, catid=233497, projectId=35802, created=2023-12-05 17:45:53, last-modified=2025-04-01 17:11:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/storage/formats)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1336624)
 */
export function getDataHubDatasetVersionTrainStorageFormats(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainStorageFormatsResponse>(
    '/webApi/data-hub/dataset-version/train/storage/formats',
    {
      ...options,
    }
  );
}

/**
 * @desc id=1390267, catid=233497, projectId=35802, created=2024-04-08 17:19:13, last-modified=2025-04-01 17:11:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/feature-info)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1390267)
 */
export function getDataHubDatasetVersionTrainFeatureInfo(
  params: model.dataHub.IGetDataHubDatasetVersionTrainFeatureInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainFeatureInfoResponse>('/webApi/data-hub/dataset-version/train/feature-info', {
    params: pick(params, ['datasetVersionId', 'featureType']),
    ...options,
  });
}

/**
 * @desc id=1390219, catid=233497, projectId=35802, created=2024-04-08 16:07:44, last-modified=2025-04-01 17:11:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/detail-tab)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1390219)
 */
export function getDataHubDatasetVersionTrainDetailTab(
  params: model.dataHub.IGetDataHubDatasetVersionTrainDetailTabParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainDetailTabResponse>('/webApi/data-hub/dataset-version/train/detail-tab', {
    params: pick(params, ['datasetVersionId']),
    ...options,
  });
}

/**
 * @desc id=1332018, catid=232378, projectId=35802, created=2023-11-23 18:25:23, last-modified=2024-01-02 15:09:38
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/queryCategoryTree)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332018)
 */
export function getDataHubCategoryQueryCategoryTree(
  params: model.dataHub.IGetDataHubCategoryQueryCategoryTreeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubCategoryQueryCategoryTreeResponse>('/webApi/data-hub/category/queryCategoryTree', {
    params: pick(params, ['name', 'dimId']),
    ...options,
  });
}

/**
 * @desc id=1332313, catid=232378, projectId=35802, created=2023-11-24 14:00:21, last-modified=2024-01-02 15:09:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/queryCategoryList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332313)
 */
export function getDataHubCategoryQueryCategoryList(
  params: model.dataHub.IGetDataHubCategoryQueryCategoryListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubCategoryQueryCategoryListResponse>('/webApi/data-hub/category/queryCategoryList', {
    params: pick(params, ['id', 'code', 'level', 'dimId']),
    ...options,
  });
}

/**
 * @desc id=1332030, catid=232378, projectId=35802, created=2023-11-23 18:39:52, last-modified=2024-01-02 15:13:25
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332030)
 */
export function postDataHubCategoryUpsert(params: model.dataHub.IPostDataHubCategoryUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubCategoryUpsertResponse>('/webApi/data-hub/category/upsert', {
    data: pick(params, ['id', 'parentId', 'name', 'dimId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1332041, catid=232378, projectId=35802, created=2023-11-23 18:46:42, last-modified=2024-01-02 15:14:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332041)
 */
export function getDataHubCategoryDel(params: model.dataHub.IGetDataHubCategoryDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubCategoryDelResponse>('/webApi/data-hub/category/del', {
    params: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1332180, catid=232378, projectId=35802, created=2023-11-24 10:48:27, last-modified=2023-12-04 16:38:37
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/category/queryPie)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332180)
 */
export function getDataHubDatasetCategoryQueryPie(
  params: model.dataHub.IGetDataHubDatasetCategoryQueryPieParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetCategoryQueryPieResponse>('/webApi/data-hub/dataset/category/queryPie', {
    params: pick(params, ['id', 'code', 'level', 'time']),
    ...options,
  });
}

/**
 * @desc id=1332388, catid=232378, projectId=35802, created=2023-11-24 14:41:39, last-modified=2023-12-11 15:18:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/queryStatisticsPage)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332388)
 */
export function postDataHubCategoryQueryStatisticsPage(
  params: model.dataHub.IPostDataHubCategoryQueryStatisticsPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubCategoryQueryStatisticsPageResponse>('/webApi/data-hub/category/queryStatisticsPage', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1332613, catid=232378, projectId=35802, created=2023-11-24 17:15:32, last-modified=2023-11-30 16:05:31
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/download)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332613)
 */
export function getDataHubCategoryDownload(params: model.dataHub.IGetDataHubCategoryDownloadParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubCategoryDownloadResponse>('/webApi/data-hub/category/download', {
    params: pick(params, ['id', 'code']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1332615, catid=232378, projectId=35802, created=2023-11-24 17:16:46, last-modified=2023-11-30 11:03:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/detailsDownload)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332615)
 */
export function postDataHubCategoryDetailsDownload(
  params: model.dataHub.IPostDataHubCategoryDetailsDownloadParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubCategoryDetailsDownloadResponse>('/webApi/data-hub/category/detailsDownload', {
    data: pick(params, ['startTime', 'endTime', 'timeUnit', 'firstCategoryId', 'secondCategoryId', 'thirdCategoryId', 'ext']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1473299, catid=252508, projectId=35802, created=2024-09-19 11:14:05, last-modified=2024-09-24 19:21:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/noticeReview)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1473299)
 */
export function getDataHubDataStrategyNoticeReview(
  params: model.dataHub.IGetDataHubDataStrategyNoticeReviewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataStrategyNoticeReviewResponse>('/webApi/data-hub/data-strategy/noticeReview', {
    params: pick(params, ['reviewId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1505640, catid=252508, projectId=35802, created=2025-02-05 16:32:36, last-modified=2025-02-07 17:47:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/sftTextBaselineMerge)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1505640)
 */
export function getDataHubDataStrategySftTextBaselineMerge(
  params: model.dataHub.IGetDataHubDataStrategySftTextBaselineMergeParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataStrategySftTextBaselineMergeResponse>(
    '/webApi/data-hub/data-strategy/sftTextBaselineMerge',
    {
      params: pick(params, ['strategyId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1471614, catid=252508, projectId=35802, created=2024-09-14 14:42:31, last-modified=2025-03-25 14:37:52
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1471614)
 */
export function postDataHubDataStrategyUpsert(
  params: model.dataHub.IPostDataHubDataStrategyUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataStrategyUpsertResponse>('/webApi/data-hub/data-strategy/upsert', {
    data: pick(params, [
      'name',
      'type',
      'comment',
      'id',
      'isReviewRequired',
      'reviewerList',
      'reviewComment',
      'diffId',
      'sharedStatus',
      'version',
      'strategyContent',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1472208, catid=252508, projectId=35802, created=2024-09-19 10:28:16, last-modified=2024-09-24 19:20:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/reviewSubmit)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1472208)
 */
export function postDataHubDataStrategyReviewSubmit(
  params: model.dataHub.IPostDataHubDataStrategyReviewSubmitParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataStrategyReviewSubmitResponse>('/webApi/data-hub/data-strategy/reviewSubmit', {
    data: pick(params, ['reviewId', 'status', 'reviewComment']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1498879, catid=252508, projectId=35802, created=2024-12-04 10:04:01, last-modified=2024-12-04 10:09:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/mergeToPool)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498879)
 */
export function getDataHubDataStrategyMergeToPool(
  params: model.dataHub.IGetDataHubDataStrategyMergeToPoolParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataStrategyMergeToPoolResponse>('/webApi/data-hub/data-strategy/mergeToPool', {
    params: pick(params, ['strategyId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506739, catid=252508, projectId=35802, created=2025-02-19 16:03:40, last-modified=2025-02-19 16:04:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/submit)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506739)
 */
export function postDataHubDataStrategySubmit(
  params: model.dataHub.IPostDataHubDataStrategySubmitParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataStrategySubmitResponse>('/webApi/data-hub/data-strategy/submit', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1506729, catid=252508, projectId=35802, created=2025-02-19 15:25:30, last-modified=2025-02-19 15:27:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/sftDiff)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1506729)
 */
export function getDataHubDataStrategySftDiff(
  params: model.dataHub.IGetDataHubDataStrategySftDiffParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataStrategySftDiffResponse>('/webApi/data-hub/data-strategy/sftDiff', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1516468, catid=252508, projectId=35802, created=2025-03-25 10:58:26, last-modified=2025-03-25 15:29:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/revokeCheck)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516468)
 */
export function getDataHubDataStrategyRevokeCheck(
  params: model.dataHub.IGetDataHubDataStrategyRevokeCheckParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataStrategyRevokeCheckResponse>('/webApi/data-hub/data-strategy/revokeCheck', {
    params: pick(params, ['strategyId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1516920, catid=252508, projectId=35802, created=2025-04-01 17:46:13, last-modified=2025-04-02 14:20:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/transStrategyJson)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516920)
 */
export function postDataHubDataStrategyTransStrategyJson(
  params: model.dataHub.IPostDataHubDataStrategyTransStrategyJsonParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDataStrategyTransStrategyJsonResponse>('/webApi/data-hub/data-strategy/transStrategyJson', {
    data: pick(params, ['sourceJson']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1471627, catid=252508, projectId=35802, created=2024-09-14 14:43:53, last-modified=2025-03-25 14:38:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1471627)
 */
export function getDataHubDataStrategyDetail(
  params: model.dataHub.IGetDataHubDataStrategyDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDataStrategyDetailResponse>('/webApi/data-hub/data-strategy/detail', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1471630, catid=252508, projectId=35802, created=2024-09-14 14:44:15, last-modified=2024-09-14 15:55:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1471630)
 */
export function getDataHubDataStrategyDel(params: model.dataHub.IGetDataHubDataStrategyDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDataStrategyDelResponse>('/webApi/data-hub/data-strategy/del', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1471621, catid=252508, projectId=35802, created=2024-09-14 14:43:08, last-modified=2024-09-23 10:49:24
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1471621)
 */
export function postDataHubDataStrategyPage(params: model.dataHub.IPostDataHubDataStrategyPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDataStrategyPageResponse>('/webApi/data-hub/data-strategy/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1471639, catid=252508, projectId=35802, created=2024-09-14 14:46:24, last-modified=2024-09-24 13:13:31
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/data-strategy/fork)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1471639)
 */
export function postDataHubDataStrategyFork(params: model.dataHub.IPostDataHubDataStrategyForkParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDataStrategyForkResponse>('/webApi/data-hub/data-strategy/fork', {
    data: pick(params, ['baseStrategyId', 'name']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1484678, catid=227933, projectId=35802, created=2024-10-30 18:33:21, last-modified=2024-10-30 20:16:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/glance/esClusterRelation)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1484678)
 */
export function getDataHubGlanceEsClusterRelation(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubGlanceEsClusterRelationResponse>('/webApi/data-hub/glance/esClusterRelation', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1296448, catid=227933, projectId=35802, created=2023-10-13 17:25:25, last-modified=2023-11-21 14:37:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/glance/values)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296448)
 */
export function getDataHubGlanceValues(params: model.dataHub.IGetDataHubGlanceValuesParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubGlanceValuesResponse>('/webApi/data-hub/glance/values', {
    params: pick(params, ['field', 'sort']),
    ...options,
  });
}

/**
 * @desc id=1296464, catid=227933, projectId=35802, created=2023-10-13 17:25:25, last-modified=2023-11-21 14:37:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/glance/fields)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296464)
 */
export function getDataHubGlanceFields(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubGlanceFieldsResponse>('/webApi/data-hub/glance/fields', {
    ...options,
  });
}

/**
 * @desc id=1296457, catid=227933, projectId=35802, created=2023-10-13 17:25:25, last-modified=2024-11-19 15:47:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/glance/search)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296457)
 */
export function postDataHubGlanceSearch(params: model.dataHub.IPostDataHubGlanceSearchParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubGlanceSearchResponse>('/webApi/data-hub/glance/search', {
    data: pick(params, [
      'empty',
      'fields',
      'from',
      'highlightEnable',
      'page',
      'pageSize',
      'query',
      'queryType',
      'sources',
      'versions',
      'indexList',
      'viewIdList',
      'expression',
      'customDSL',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1439295, catid=231941, projectId=35802, created=2024-07-22 15:42:47, last-modified=2024-07-22 16:27:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/accept/index)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1439295)
 */
export function postDataHubTaskReportAcceptIndex(
  params: model.dataHub.IPostDataHubTaskReportAcceptIndexParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubTaskReportAcceptIndexResponse>('/webApi/data-hub/task_report/accept/index', {
    data: pick(params, ['datasetIdList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1439417, catid=231941, projectId=35802, created=2024-07-22 16:29:14, last-modified=2024-07-22 16:52:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/accept/modelStageAcceptDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1439417)
 */
export function postDataHubTaskReportAcceptModelStageAcceptDetail(
  params: model.dataHub.IPostDataHubTaskReportAcceptModelStageAcceptDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubTaskReportAcceptModelStageAcceptDetailResponse>(
    '/webApi/data-hub/task_report/accept/modelStageAcceptDetail',
    {
      data: pick(params, ['datasetIdList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1439445, catid=231941, projectId=35802, created=2024-07-22 16:38:08, last-modified=2024-07-22 16:40:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/accept/downloadModelStageAcceptDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1439445)
 */
export function postDataHubTaskReportAcceptDownloadModelStageAcceptDetail(
  params: model.dataHub.IPostDataHubTaskReportAcceptDownloadModelStageAcceptDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubTaskReportAcceptDownloadModelStageAcceptDetailResponse>(
    '/webApi/data-hub/task_report/accept/downloadModelStageAcceptDetail',
    {
      data: pick(params, ['datasetIdList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1439404, catid=231941, projectId=35802, created=2024-07-22 16:21:18, last-modified=2024-07-22 16:53:21
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/accept/modelAcceptDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1439404)
 */
export function postDataHubTaskReportAcceptModelAcceptDetail(
  params: model.dataHub.IPostDataHubTaskReportAcceptModelAcceptDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubTaskReportAcceptModelAcceptDetailResponse>(
    '/webApi/data-hub/task_report/accept/modelAcceptDetail',
    {
      data: pick(params, ['datasetIdList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1439403, catid=231941, projectId=35802, created=2024-07-22 16:21:15, last-modified=2024-07-22 16:47:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/accept/stageAcceptDetail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1439403)
 */
export function postDataHubTaskReportAcceptStageAcceptDetail(
  params: model.dataHub.IPostDataHubTaskReportAcceptStageAcceptDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubTaskReportAcceptStageAcceptDetailResponse>(
    '/webApi/data-hub/task_report/accept/stageAcceptDetail',
    {
      data: pick(params, ['datasetIdList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1329842, catid=231941, projectId=35802, created=2023-11-21 14:37:53, last-modified=2023-11-21 14:37:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/export)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329842)
 */
export function postDataHubTaskReportExport(params: model.dataHub.IPostDataHubTaskReportExportParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubTaskReportExportResponse>('/webApi/data-hub/task_report/export', {
    data: pick(params, ['businessId', 'taskTypeId', 'taskConfigId', 'taskTag']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329844, catid=231941, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329844)
 */
export function postDataHubTaskReportPage(params: model.dataHub.IPostDataHubTaskReportPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubTaskReportPageResponse>('/webApi/data-hub/task_report/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329851, catid=231941, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/line_chart_export)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329851)
 */
export function postDataHubTaskReportLineChartExport(
  params: model.dataHub.IPostDataHubTaskReportLineChartExportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubTaskReportLineChartExportResponse>('/webApi/data-hub/task_report/line_chart_export', {
    data: pick(params, ['businessId', 'timeUnit', 'taskTypeId', 'taskConfigId', 'taskTagId', 'startTime', 'endTime']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329857, catid=231941, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/line_chart)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329857)
 */
export function postDataHubTaskReportLineChart(
  params: model.dataHub.IPostDataHubTaskReportLineChartParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubTaskReportLineChartResponse>('/webApi/data-hub/task_report/line_chart', {
    data: pick(params, ['businessId', 'timeUnit', 'taskTypeId', 'taskConfigId', 'taskTagId', 'startTime', 'endTime']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329860, catid=231941, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/overview)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329860)
 */
export function getDataHubTaskReportOverview(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubTaskReportOverviewResponse>('/webApi/data-hub/task_report/overview', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1432706, catid=231945, projectId=35802, created=2024-07-05 10:59:14, last-modified=2024-07-05 15:00:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/upload_file)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1432706)
 */
export function postDataHubUploadFile(params: FormData, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubUploadFileResponse>('/webApi/data-hub/upload_file', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'multipart/form-data' }),
  });
}

/**
 * @desc id=1329873, catid=231945, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/upload/data)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329873)
 */
export function postDataHubBookUploadData(params: model.dataHub.IPostDataHubBookUploadDataParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubBookUploadDataResponse>('/webApi/data-hub/book/upload/data', {
    data: pick(params, ['url', 'dataType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329890, catid=231945, projectId=35802, created=2023-11-21 14:37:55, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/download/data)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329890)
 */
export function postDataHubBookDownloadData(params: model.dataHub.IPostDataHubBookDownloadDataParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubBookDownloadDataResponse>('/webApi/data-hub/book/download/data', {
    params: pick(params, ['llmDataCategoryOneId', 'llmDataCategoryTwoId', 'obtainStatus', 'language', 'dataType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1329888, catid=231945, projectId=35802, created=2023-11-21 14:37:55, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/delete/data)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329888)
 */
export function getDataHubBookDeleteData(params: model.dataHub.IGetDataHubBookDeleteDataParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubBookDeleteDataResponse>('/webApi/data-hub/book/delete/data', {
    params: pick(params, ['metadataId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329875, catid=231945, projectId=35802, created=2023-11-21 14:37:55, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/create/data)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329875)
 */
export function postDataHubBookCreateData(params: model.dataHub.IPostDataHubBookCreateDataParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubBookCreateDataResponse>('/webApi/data-hub/book/create/data', {
    data: pick(params, [
      'metadataId',
      'isbn',
      'title',
      'author',
      'llmDataCategoryOneId',
      'llmDataCategoryTwoId',
      'dataType',
      'obtainStatus',
      'pubdate',
      'press',
      'language',
      'authority',
      'heat',
      'keyword',
      'remark',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329881, catid=231945, projectId=35802, created=2023-11-21 14:37:55, last-modified=2024-02-22 19:01:25
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/query/data/details)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329881)
 */
export function postDataHubBookQueryDataDetails(
  params: model.dataHub.IPostDataHubBookQueryDataDetailsParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubBookQueryDataDetailsResponse>('/webApi/data-hub/book/query/data/details', {
    params: pick(params, [
      'pageNumber',
      'pageSize',
      'llmDataCategoryOneId',
      'llmDataCategoryTwoId',
      'obtainStatus',
      'language',
      'dataType',
    ]),
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329864, catid=231945, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/query/data/count)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329864)
 */
export function postDataHubBookQueryDataCount(
  params: model.dataHub.IPostDataHubBookQueryDataCountParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubBookQueryDataCountResponse>('/webApi/data-hub/book/query/data/count', {
    params: pick(params, ['pageNumber', 'pageSize', 'param']),
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329872, catid=231945, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/upload)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329872)
 */
export function postDataHubUpload(params: FormData, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubUploadResponse>('/webApi/data-hub/upload', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'multipart/form-data' }),
  });
}

/**
 * @desc id=1329891, catid=231949, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/op/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329891)
 */
export function getDataHubOpDel(params: model.dataHub.IGetDataHubOpDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubOpDelResponse>('/webApi/data-hub/op/del', {
    params: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1329895, catid=231949, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/op/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329895)
 */
export function postDataHubOpUpsert(params: model.dataHub.IPostDataHubOpUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubOpUpsertResponse>('/webApi/data-hub/op/upsert', {
    data: pick(params, ['id', 'name', 'type', 'content', 'comment', 'current_version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329904, catid=231949, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/op/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329904)
 */
export function postDataHubOpPage(params: model.dataHub.IPostDataHubOpPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubOpPageResponse>('/webApi/data-hub/op/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329908, catid=231949, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/op/types)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329908)
 */
export function getDataHubOpTypes(params: model.dataHub.IGetDataHubOpTypesParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubOpTypesResponse>('/webApi/data-hub/op/types', {
    params: pick(params, ['scopeCode']),
    ...options,
  });
}

/**
 * @desc id=1342648, catid=231953, projectId=35802, created=2023-12-12 20:58:40, last-modified=2024-07-25 16:18:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/uploadVersion)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1342648)
 */
export function postDataHubDatasetVersionUploadVersion(
  params: model.dataHub.IPostDataHubDatasetVersionUploadVersionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionUploadVersionResponse>('/webApi/data-hub/dataset-version/uploadVersion', {
    data: pick(params, ['datasetId', 's3UrlList', 'dataAdaptorExcel', 'annotation_include']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1488508, catid=231953, projectId=35802, created=2024-11-07 16:14:41, last-modified=2024-11-07 16:15:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/sdk_install)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1488508)
 */
export function getDataHubDatasetTrainSdkInstall(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetTrainSdkInstallResponse>('/webApi/data-hub/dataset/train/sdk_install', {
    ...options,
  });
}

/**
 * @desc id=1329913, catid=231953, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/download)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329913)
 */
export function postDataHubDatasetVersionDownload(
  params: model.dataHub.IPostDataHubDatasetVersionDownloadParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionDownloadResponse>('/webApi/data-hub/dataset-version/download', {
    data: pick(params, ['dataset_version_ids']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1344498, catid=231953, projectId=35802, created=2023-12-15 20:31:54, last-modified=2024-08-02 14:29:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/downloadV1)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344498)
 */
export function postDataHubDatasetVersionDownloadV1(
  params: model.dataHub.IPostDataHubDatasetVersionDownloadV1Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionDownloadV1Response>('/webApi/data-hub/dataset-version/downloadV1', {
    data: pick(params, ['dataset_version_ids', 'downloadTypeList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329922, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/task_types)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329922)
 */
export function getDataHubDatasetTaskTypes(params: model.dataHub.IGetDataHubDatasetTaskTypesParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetTaskTypesResponse>('/webApi/data-hub/dataset/task_types', {
    params: pick(params, ['type_name']),
    ...options,
  });
}

/**
 * @desc id=1329925, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/task_configs)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329925)
 */
export function getDataHubDatasetTaskConfigs(
  params: model.dataHub.IGetDataHubDatasetTaskConfigsParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetTaskConfigsResponse>('/webApi/data-hub/dataset/task_configs', {
    params: pick(params, ['task_type_id', 'keyword']),
    ...options,
  });
}

/**
 * @desc id=1447659, catid=231953, projectId=35802, created=2024-07-24 17:12:17, last-modified=2024-07-25 15:45:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/deprecationStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1447659)
 */
export function postDataHubDatasetDeprecationStatus(
  params: model.dataHub.IPostDataHubDatasetDeprecationStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetDeprecationStatusResponse>('/webApi/data-hub/dataset/deprecationStatus', {
    data: pick(params, ['datasetId', 'deprecation_status']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1465201, catid=231953, projectId=35802, created=2024-09-02 15:50:59, last-modified=2024-09-03 16:26:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/listDatasetCategory)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1465201)
 */
export function getDataHubDatasetVersionTrainListDatasetCategory(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainListDatasetCategoryResponse>(
    '/webApi/data-hub/dataset-version/train/listDatasetCategory',
    {
      ...options,
    }
  );
}

/**
 * @desc id=1451341, catid=231953, projectId=35802, created=2024-08-01 15:31:22, last-modified=2024-08-01 15:35:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/listAdoptPhase)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1451341)
 */
export function getDataHubDatasetVersionTrainListAdoptPhase(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainListAdoptPhaseResponse>(
    '/webApi/data-hub/dataset-version/train/listAdoptPhase',
    {
      ...options,
    }
  );
}

/**
 * @desc id=1504501, catid=231953, projectId=35802, created=2025-01-11 14:34:16, last-modified=2025-01-11 14:36:17
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/delReport)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1504501)
 */
export function getDataHubDatasetVersionTrainDelReport(
  params: model.dataHub.IGetDataHubDatasetVersionTrainDelReportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainDelReportResponse>('/webApi/data-hub/dataset-version/train/delReport', {
    params: pick(params, ['reportId']),
    ...options,
  });
}

/**
 * @desc id=1504504, catid=231953, projectId=35802, created=2025-01-11 14:40:58, last-modified=2025-01-11 14:42:39
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/delAnalysis)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1504504)
 */
export function getDataHubDatasetVersionTrainDelAnalysis(
  params: model.dataHub.IGetDataHubDatasetVersionTrainDelAnalysisParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainDelAnalysisResponse>('/webApi/data-hub/dataset-version/train/delAnalysis', {
    params: pick(params, ['analysisId']),
    ...options,
  });
}

/**
 * @desc id=1425340, catid=231953, projectId=35802, created=2024-06-20 16:24:48, last-modified=2024-10-23 15:41:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/beforeUpsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425340)
 */
export function postDataHubDatasetBeforeUpsert(
  params: model.dataHub.IPostDataHubDatasetBeforeUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetBeforeUpsertResponse>('/webApi/data-hub/dataset/beforeUpsert', {
    data: pick(params, [
      'taskIds',
      'businessType',
      'name',
      'benchMarkName',
      'benchMarkId',
      'version',
      'taskConfigId',
      'modelId',
      'surplusTaskConfigId',
      'splitFile',
      'splitField',
      'splitType',
      'surplusName',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1427404, catid=231953, projectId=35802, created=2024-06-26 15:08:31, last-modified=2024-06-26 15:27:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/afterUpsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1427404)
 */
export function postDataHubDatasetAfterUpsert(
  params: model.dataHub.IPostDataHubDatasetAfterUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetAfterUpsertResponse>('/webApi/data-hub/dataset/afterUpsert', {
    data: pick(params, ['businessType', 'name', 'datasetId', 'taskConfigId', 'datasetVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1507937, catid=231953, projectId=35802, created=2025-03-04 09:59:51, last-modified=2025-03-04 10:33:30
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/changeOnlineStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507937)
 */
export function getDataHubDatasetVersionTrainChangeOnlineStatus(
  params: model.dataHub.IGetDataHubDatasetVersionTrainChangeOnlineStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainChangeOnlineStatusResponse>(
    '/webApi/data-hub/dataset-version/train/changeOnlineStatus',
    {
      params: pick(params, ['id', 'onlineStatus']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1427740, catid=231953, projectId=35802, created=2024-06-26 17:10:18, last-modified=2024-09-19 13:54:39
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/calculation)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1427740)
 */
export function postDataHubTaskCalculation(params: model.dataHub.IPostDataHubTaskCalculationParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubTaskCalculationResponse>('/webApi/data-hub/task/calculation', {
    data: pick(params, ['modelName', 'benchMarkName', 'datasetId', 'taskConfigId', 'datasetVersionId', 'modelId', 'surplusDatasetId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329930, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-data/staging)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329930)
 */
export function postDataHubDatasetDataStaging(
  params: model.dataHub.IPostDataHubDatasetDataStagingParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetDataStagingResponse>('/webApi/data-hub/dataset-data/staging', {
    data: pick(params, ['dataset_version_data_id', 'content', 'tagging_result', 'staging_version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1416362, catid=231953, projectId=35802, created=2024-06-03 16:30:09, last-modified=2024-06-14 14:33:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/oneKeyUpsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1416362)
 */
export function postDataHubDatasetOneKeyUpsert(
  params: model.dataHub.IPostDataHubDatasetOneKeyUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetOneKeyUpsertResponse>('/webApi/data-hub/dataset/oneKeyUpsert', {
    data: pick(params, ['query', 'businessType', 'name']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329932, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2024-07-31 15:24:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329932)
 */
export function postDataHubDatasetVersionPage(
  params: model.dataHub.IPostDataHubDatasetVersionPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionPageResponse>('/webApi/data-hub/dataset-version/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1342657, catid=231953, projectId=35802, created=2023-12-12 21:13:22, last-modified=2023-12-13 10:46:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/mergeVersion)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1342657)
 */
export function postDataHubDatasetVersionMergeVersion(
  params: model.dataHub.IPostDataHubDatasetVersionMergeVersionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionMergeVersionResponse>('/webApi/data-hub/dataset-version/mergeVersion', {
    data: pick(params, ['datasetId', 'datasetVersionIdList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329935, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-27 16:29:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-data/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329935)
 */
export function postDataHubDatasetDataPage(params: model.dataHub.IPostDataHubDatasetDataPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDatasetDataPageResponse>('/webApi/data-hub/dataset-data/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329942, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329942)
 */
export function postDataHubDatasetUpsert(params: FormData, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDatasetUpsertResponse>('/webApi/data-hub/dataset/upsert', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'multipart/form-data' }),
  });
}

/**
 * @desc id=1344477, catid=231953, projectId=35802, created=2023-12-15 19:05:35, last-modified=2025-01-11 16:59:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/upsertV1)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1344477)
 */
export function postDataHubDatasetUpsertV1(params: model.dataHub.IPostDataHubDatasetUpsertV1Parameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDatasetUpsertV1Response>('/webApi/data-hub/dataset/upsertV1', {
    data: pick(params, [
      'id',
      'snapshotId',
      'business_id',
      'task_type_id',
      'task_config_id',
      'name',
      'init_type',
      'parent_dataset_version_id',
      'tagging_data_builder',
      'tagging_data_validator',
      'tagging_data_parser',
      'training_data_builder',
      'training_data_builder_excel',
      'data_adaptor',
      'extra',
      'comment',
      'tagging_standard',
      'op_flag',
      'category_id',
      's3UrlList',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329944, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329944)
 */
export function postDataHubDatasetVersionUpsert(
  params: model.dataHub.IPostDataHubDatasetVersionUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionUpsertResponse>('/webApi/data-hub/dataset-version/upsert', {
    data: pick(params, ['parent_dataset_version_id', 'comment', 'id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1488492, catid=231953, projectId=35802, created=2024-11-07 15:49:37, last-modified=2025-01-20 16:37:21
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/latest_milestone)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1488492)
 */
export function getDataHubDatasetVersionTrainLatestMilestone(
  params: model.dataHub.IGetDataHubDatasetVersionTrainLatestMilestoneParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainLatestMilestoneResponse>(
    '/webApi/data-hub/dataset-version/train/latest_milestone',
    {
      params: pick(params, ['type']),
      ...options,
    }
  );
}

/**
 * @desc id=1448618, catid=231953, projectId=35802, created=2024-07-25 17:06:37, last-modified=2024-08-01 10:56:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/delivery)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1448618)
 */
export function postDataHubDatasetVersionDelivery(
  params: model.dataHub.IPostDataHubDatasetVersionDeliveryParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetVersionDeliveryResponse>('/webApi/data-hub/dataset-version/delivery', {
    data: pick(params, ['datasetId', 'datasetVersionName', 'type', 'currentDatasetVersionId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1501349, catid=231953, projectId=35802, created=2024-12-19 13:55:31, last-modified=2025-01-11 16:46:25
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/analysis)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1501349)
 */
export function getDataHubDatasetVersionTrainAnalysis(
  params: model.dataHub.IGetDataHubDatasetVersionTrainAnalysisParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainAnalysisResponse>('/webApi/data-hub/dataset-version/train/analysis', {
    params: pick(params, ['datasetVersionId', 'snapshotId']),
    ...options,
  });
}

/**
 * @desc id=1329946, catid=231953, projectId=35802, created=2023-11-21 14:38:04, last-modified=2024-08-05 18:27:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329946)
 */
export function postDataHubDatasetPage(params: model.dataHub.IPostDataHubDatasetPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubDatasetPageResponse>('/webApi/data-hub/dataset/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329952, catid=231953, projectId=35802, created=2023-11-21 14:38:04, last-modified=2023-11-21 14:38:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/simple_page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329952)
 */
export function postDataHubDatasetSimplePage(
  params: model.dataHub.IPostDataHubDatasetSimplePageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetSimplePageResponse>('/webApi/data-hub/dataset/simple_page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1504496, catid=231953, projectId=35802, created=2025-01-11 11:28:35, last-modified=2025-01-11 16:53:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/analysisByReportIdList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1504496)
 */
export function getDataHubDatasetVersionTrainAnalysisByReportIdList(
  params: model.dataHub.IGetDataHubDatasetVersionTrainAnalysisByReportIdListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainAnalysisByReportIdListResponse>(
    '/webApi/data-hub/dataset-version/train/analysisByReportIdList',
    {
      params: pick(params, ['reportIdList']),
      ...options,
    }
  );
}

/**
 * @desc id=1507575, catid=231953, projectId=35802, created=2025-03-03 14:25:28, last-modified=2025-03-04 16:58:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/analysisBySnapshotId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1507575)
 */
export function getDataHubDatasetVersionTrainAnalysisBySnapshotId(
  params: model.dataHub.IGetDataHubDatasetVersionTrainAnalysisBySnapshotIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainAnalysisBySnapshotIdResponse>(
    '/webApi/data-hub/dataset-version/train/analysisBySnapshotId',
    {
      params: pick(params, ['snapshotId']),
      ...options,
    }
  );
}

/**
 * @desc id=1504500, catid=231953, projectId=35802, created=2025-01-11 11:33:47, last-modified=2025-01-11 16:22:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/listReportByDatasetVersionId)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1504500)
 */
export function getDataHubDatasetVersionTrainListReportByDatasetVersionId(
  params: model.dataHub.IGetDataHubDatasetVersionTrainListReportByDatasetVersionIdParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubDatasetVersionTrainListReportByDatasetVersionIdResponse>(
    '/webApi/data-hub/dataset-version/train/listReportByDatasetVersionId',
    {
      params: pick(params, ['datasetVersionId']),
      ...options,
    }
  );
}

/**
 * @desc id=1447997, catid=231953, projectId=35802, created=2024-07-25 10:27:45, last-modified=2024-08-05 15:54:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/accept-info)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1447997)
 */
export function postDataHubDatasetAcceptInfo(
  params: model.dataHub.IPostDataHubDatasetAcceptInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetAcceptInfoResponse>('/webApi/data-hub/dataset/accept-info', {
    data: pick(params, ['datasetIdList', 'startDate', 'endDate']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1448012, catid=231953, projectId=35802, created=2024-07-25 10:42:15, last-modified=2024-07-25 10:47:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/accept-detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1448012)
 */
export function postDataHubDatasetAcceptDetail(
  params: model.dataHub.IPostDataHubDatasetAcceptDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubDatasetAcceptDetailResponse>('/webApi/data-hub/dataset/accept-detail', {
    data: pick(params, ['datasetIdList', 'startDate', 'endDate']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1350702, catid=231959, projectId=35802, created=2024-01-05 17:44:20, last-modified=2024-03-12 17:00:51
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/queryUser)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1350702)
 */
export function getDataHubTaskQueryUser(params: model.dataHub.IGetDataHubTaskQueryUserParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubTaskQueryUserResponse>('/webApi/data-hub/task/queryUser', {
    params: pick(params, ['name', 'roleId', 'roleType']),
    ...options,
  });
}

/**
 * @desc id=1329953, catid=231959, projectId=35802, created=2023-11-21 14:38:04, last-modified=2023-11-21 14:38:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/report)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329953)
 */
export function getDataHubTaskReport(params: model.dataHub.IGetDataHubTaskReportParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubTaskReportResponse>('/webApi/data-hub/task/report', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1427159, catid=231959, projectId=35802, created=2024-06-26 11:25:44, last-modified=2024-07-31 19:20:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/getAutoTaskConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1427159)
 */
export function getDataHubTaskGetAutoTaskConfig(
  params: model.dataHub.IGetDataHubTaskGetAutoTaskConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubTaskGetAutoTaskConfigResponse>('/webApi/data-hub/task/getAutoTaskConfig', {
    params: pick(params, ['businessType', 'dataType']),
    ...options,
  });
}

/**
 * @desc id=1427262, catid=231959, projectId=35802, created=2024-06-26 14:11:54, last-modified=2024-07-25 16:17:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/getOneKeyConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1427262)
 */
export function getDataHubTaskGetOneKeyConfig(
  params: model.dataHub.IGetDataHubTaskGetOneKeyConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubTaskGetOneKeyConfigResponse>('/webApi/data-hub/task/getOneKeyConfig', {
    params: pick(params, ['businessType', 'taskConfigId']),
    ...options,
  });
}

/**
 * @desc id=1372524, catid=231959, projectId=35802, created=2024-02-26 17:32:41, last-modified=2024-02-26 17:33:21
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/trigger)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1372524)
 */
export function getDataHubTaskTrigger(options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubTaskTriggerResponse>('/webApi/data-hub/task/trigger', {
    ...options,
  });
}

/**
 * @desc id=1388612, catid=231959, projectId=35802, created=2024-04-03 16:07:04, last-modified=2024-04-26 15:53:18
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/queryTaskConfig)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1388612)
 */
export function getDataHubTaskQueryTaskConfig(
  params: model.dataHub.IGetDataHubTaskQueryTaskConfigParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubTaskQueryTaskConfigResponse>('/webApi/data-hub/task/queryTaskConfig', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1395298, catid=231959, projectId=35802, created=2024-04-16 11:27:37, last-modified=2024-04-16 16:58:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/getTaskConfigInfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1395298)
 */
export function getDataHubTaskGetTaskConfigInfo(
  params: model.dataHub.IGetDataHubTaskGetTaskConfigInfoParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubTaskGetTaskConfigInfoResponse>('/webApi/data-hub/task/getTaskConfigInfo', {
    params: pick(params, ['taskConfigId']),
    ...options,
  });
}

/**
 * @desc id=1393050, catid=231959, projectId=35802, created=2024-04-11 15:31:29, last-modified=2024-04-26 15:20:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/getTaskAccuracy)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1393050)
 */
export function getDataHubTaskGetTaskAccuracy(
  params: model.dataHub.IGetDataHubTaskGetTaskAccuracyParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubTaskGetTaskAccuracyResponse>('/webApi/data-hub/task/getTaskAccuracy', {
    params: pick(params, ['taskId']),
    ...options,
  });
}

/**
 * @desc id=1329962, catid=231959, projectId=35802, created=2023-11-21 14:38:04, last-modified=2023-11-21 14:38:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/cancel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329962)
 */
export function getDataHubTaskCancel(params: model.dataHub.IGetDataHubTaskCancelParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubTaskCancelResponse>('/webApi/data-hub/task/cancel', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1329970, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2024-06-05 10:46:18
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329970)
 */
export function postDataHubTaskUpsert(params: model.dataHub.IPostDataHubTaskUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubTaskUpsertResponse>('/webApi/data-hub/task/upsert', {
    data: pick(params, [
      'project_id',
      'dataset_version_id',
      'selective_inspect_percent',
      'selective_inspect_type',
      'accuracy_threshold',
      'sample_type',
      'range_start',
      'range_end',
      'is_scheduled',
      'exam_question_version_id',
      'exam_question_percent',
      'exam_question_bound',
      'batch_count',
      'batches',
      'tagging_workers',
      'tagging_group_workers',
      'inspect_workers',
      'wb_inspect_workers',
      'sync_type',
      'sycn_deadline',
      'isMulti',
      'multiCount',
      'matchQualityInspectionOperators',
      'mismatchQualityInspectionOperators',
      'matchingOp',
      'blindExaminationConfigs',
      'noticeNum',
      'noticeDuration',
      'taskName',
      'resColsFit',
      'loopResColsFit',
      'blindVersion',
      'samplingRatio',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329968, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2024-07-16 14:38:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/sync)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329968)
 */
export function postDataHubTaskSync(params: model.dataHub.IPostDataHubTaskSyncParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubTaskSyncResponse>('/webApi/data-hub/task/sync', {
    data: pick(params, ['id', 'userMis', 'fitResult', 'syncType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329975, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/workers)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329975)
 */
export function getDataHubTaskWorkers(params: model.dataHub.IGetDataHubTaskWorkersParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubTaskWorkersResponse>('/webApi/data-hub/task/workers', {
    params: pick(params, ['worker_name', 'project_id', 'role_id']),
    ...options,
  });
}

/**
 * @desc id=1329980, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2024-07-15 16:03:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329980)
 */
export function postDataHubTaskPage(params: model.dataHub.IPostDataHubTaskPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubTaskPageResponse>('/webApi/data-hub/task/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329984, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/page_1700200339642)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329984)
 */
export function postDataHubTaskPage1700200339642(
  params: model.dataHub.IPostDataHubTaskPage1700200339642Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubTaskPage1700200339642Response>('/webApi/data-hub/task/page_1700200339642', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329985, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/projects)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329985)
 */
export function getDataHubTaskProjects(params: model.dataHub.IGetDataHubTaskProjectsParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubTaskProjectsResponse>('/webApi/data-hub/task/projects', {
    params: pick(params, ['project_name']),
    ...options,
  });
}

/**
 * @desc id=1348588, catid=231959, projectId=35802, created=2023-12-28 11:32:04, last-modified=2024-05-07 16:12:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/group/queryGroup)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1348588)
 */
export function getDataHubGroupQueryGroup(params: model.dataHub.IGetDataHubGroupQueryGroupParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubGroupQueryGroupResponse>('/webApi/data-hub/group/queryGroup', {
    params: pick(params, ['name', 'roleId', 'roleType', 'groupId']),
    ...options,
  });
}

/**
 * @desc id=1329993, catid=231968, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329993)
 */
export function getDataHubAuthDel(params: model.dataHub.IGetDataHubAuthDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubAuthDelResponse>('/webApi/data-hub/auth/del', {
    params: pick(params, ['resource_type', 'resource_id', 'mis']),
    ...options,
  });
}

/**
 * @desc id=1488518, catid=231968, projectId=35802, created=2024-11-07 17:19:18, last-modified=2024-11-07 17:22:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/v1/authRole/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1488518)
 */
export function postDataHubAuthV1AuthRoleList(
  params: model.dataHub.IPostDataHubAuthV1AuthRoleListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.dataHub.IPostDataHubAuthV1AuthRoleListResponse>('/webApi/data-hub/auth/v1/authRole/list', {
    data: pick(params, ['keyword', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1452494, catid=231968, projectId=35802, created=2024-08-06 14:53:51, last-modified=2024-08-07 16:00:30
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/v1/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1452494)
 */
export function postDataHubAuthV1Upsert(params: model.dataHub.IPostDataHubAuthV1UpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubAuthV1UpsertResponse>('/webApi/data-hub/auth/v1/upsert', {
    data: pick(params, ['datasetId', 'permission']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329998, catid=231968, projectId=35802, created=2023-11-21 14:38:06, last-modified=2023-11-21 14:38:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329998)
 */
export function postDataHubAuthUpsert(params: model.dataHub.IPostDataHubAuthUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubAuthUpsertResponse>('/webApi/data-hub/auth/upsert', {
    data: pick(params, ['resource_type', 'resource_id', 'mis', 'role']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1452527, catid=231968, projectId=35802, created=2024-08-06 15:11:37, last-modified=2024-08-06 15:15:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/v1/getPermission)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1452527)
 */
export function getDataHubAuthV1GetPermission(
  params: model.dataHub.IGetDataHubAuthV1GetPermissionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubAuthV1GetPermissionResponse>('/webApi/data-hub/auth/v1/getPermission', {
    params: pick(params, ['datasetId']),
    ...options,
  });
}

/**
 * @desc id=1498820, catid=231968, projectId=35802, created=2024-12-03 17:06:05, last-modified=2024-12-04 11:10:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/v1/authRole/eval_bad_case/check)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1498820)
 */
export function getDataHubAuthV1AuthRoleEvalBadCaseCheck(
  params: model.dataHub.IGetDataHubAuthV1AuthRoleEvalBadCaseCheckParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubAuthV1AuthRoleEvalBadCaseCheckResponse>(
    '/webApi/data-hub/auth/v1/authRole/eval_bad_case/check',
    {
      params: pick(params, ['datasetId']),
      ...options,
    }
  );
}

/**
 * @desc id=1501291, catid=231968, projectId=35802, created=2024-12-18 17:27:25, last-modified=2024-12-18 17:28:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/v1/authRole/data_pool_operator/check)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1501291)
 */
export function getDataHubAuthV1AuthRoleDataPoolOperatorCheck(
  params: model.dataHub.IGetDataHubAuthV1AuthRoleDataPoolOperatorCheckParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<model.dataHub.IGetDataHubAuthV1AuthRoleDataPoolOperatorCheckResponse>(
    '/webApi/data-hub/auth/v1/authRole/data_pool_operator/check',
    {
      params: pick(params, ['operatorType']),
      ...options,
    }
  );
}

/**
 * @desc id=1330007, catid=231968, projectId=35802, created=2023-11-21 14:38:06, last-modified=2023-11-21 14:38:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1330007)
 */
export function postDataHubAuthPage(params: model.dataHub.IPostDataHubAuthPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.dataHub.IPostDataHubAuthPageResponse>('/webApi/data-hub/auth/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1330012, catid=231968, projectId=35802, created=2023-11-21 14:38:06, last-modified=2023-11-21 14:38:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1330012)
 */
export function getDataHubAuthList(params: model.dataHub.IGetDataHubAuthListParameter, options?: FlowHttpRequestOptions) {
  return http.get<model.dataHub.IGetDataHubAuthListResponse>('/webApi/data-hub/auth/list', {
    params: pick(params, ['resource_type']),
    ...options,
  });
}
