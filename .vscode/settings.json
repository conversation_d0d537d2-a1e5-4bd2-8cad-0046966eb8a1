{"typescript.tsdk": "node_modules/typescript/lib", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll.eslint": "explicit"}, "editor.formatOnSaveMode": "file", "editor.formatOnType": true, "editor.formatOnPaste": false, "[typescript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll.eslint": "explicit"}, "editor.formatOnSaveMode": "file", "editor.formatOnType": true, "editor.formatOnPaste": false}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "cSpell.words": ["huggingface", "modelscope", "pretrained"], "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "packages/": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "packages/": true}, "commentTranslate.multiLineMerge": true}