<script lang="ts">
import { Component, Spectra } from '@/decorators';
import GpuQueueSelector from './GpuQueueSelector.vue';

@Component
@Spectra
export default class OtherGpuQueueSelector extends GpuQueueSelector {
  created() {
    this.action$cpuQueueList({ projectGroup: this.experimentBaseInfo?.experimentConfig?.projectGroup });
  }

  get optionList() {
    return this.cpuQueueList || [];
  }

  isOverride(resource) {
    return resource.resourceType === 'cpuJob' && resource.queue;
  }
}
</script>
<style lang="scss"></style>
