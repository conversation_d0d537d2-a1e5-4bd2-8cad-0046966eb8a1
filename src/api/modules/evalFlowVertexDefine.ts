import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1426479, catid=246786, projectId=35802, created=2024-06-24 19:14:30, last-modified=2024-07-15 14:55:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVertexDefine/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426479)
 */
export function postEvalFlowVertexDefineList(
  params: model.evalFlowVertexDefine.IPostEvalFlowVertexDefineListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVertexDefine.IPostEvalFlowVertexDefineListResponse>('/webApi/evalFlowVertexDefine/list', {
    data: pick(params, ['searchFilter', 'ownerFilter', 'categoryFilter', 'statusFilter', 'offset', 'limit']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426507, catid=246786, projectId=35802, created=2024-06-24 19:43:17, last-modified=2024-06-24 19:44:01
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVertexDefine/listCategory)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426507)
 */
export function postEvalFlowVertexDefineListCategory(
  params: model.evalFlowVertexDefine.IPostEvalFlowVertexDefineListCategoryParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVertexDefine.IPostEvalFlowVertexDefineListCategoryResponse>('/webApi/evalFlowVertexDefine/listCategory', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426490, catid=246786, projectId=35802, created=2024-06-24 19:39:59, last-modified=2024-06-24 19:40:40
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVertexDefine/delete)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426490)
 */
export function postEvalFlowVertexDefineDelete(
  params: model.evalFlowVertexDefine.IPostEvalFlowVertexDefineDeleteParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVertexDefine.IPostEvalFlowVertexDefineDeleteResponse>('/webApi/evalFlowVertexDefine/delete', {
    data: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426499, catid=246786, projectId=35802, created=2024-06-24 19:40:42, last-modified=2024-06-24 19:41:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVertexDefine/setStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426499)
 */
export function postEvalFlowVertexDefineSetStatus(
  params: model.evalFlowVertexDefine.IPostEvalFlowVertexDefineSetStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVertexDefine.IPostEvalFlowVertexDefineSetStatusResponse>('/webApi/evalFlowVertexDefine/setStatus', {
    data: pick(params, ['id', 'status']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426480, catid=246786, projectId=35802, created=2024-06-24 19:22:49, last-modified=2024-08-06 17:20:40
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVertexDefine/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426480)
 */
export function postEvalFlowVertexDefineGet(
  params: model.evalFlowVertexDefine.IPostEvalFlowVertexDefineGetParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVertexDefine.IPostEvalFlowVertexDefineGetResponse>('/webApi/evalFlowVertexDefine/get', {
    data: pick(params, ['id', 'anchor']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426485, catid=246786, projectId=35802, created=2024-06-24 19:36:33, last-modified=2024-07-26 14:21:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVertexDefine/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426485)
 */
export function postEvalFlowVertexDefineUpsert(
  params: model.evalFlowVertexDefine.IPostEvalFlowVertexDefineUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVertexDefine.IPostEvalFlowVertexDefineUpsertResponse>('/webApi/evalFlowVertexDefine/upsert', {
    data: pick(params, ['id', 'type', 'anchor', 'category', 'label', 'status', 'ownerList', 'consumerDefineList', 'pluginBindList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
