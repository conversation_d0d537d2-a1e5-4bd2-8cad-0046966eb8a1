<template>
  <mtd-table-column :label="col.name" :align="alignType">
    <template v-for="item of col.nextLevelHeader">
      <column-item v-if="item.nextLevelHeader" :key="item.key" :col="item" />
      <mtd-table-column v-else :key="item.key" :label="item.name" :prop="item.key" :align="alignType">
        <template slot-scope="scope">
          {{ scope.row[item.key]?.value }}
        </template>
      </mtd-table-column>
    </template>
    <template slot-scope="scope">
      {{ scope.row[col.key]?.value }}
    </template>
  </mtd-table-column>
</template>

<script lang="ts">
import { Compo<PERSON>, <PERSON><PERSON>, <PERSON>, Spectra } from '@/decorators';

@Component({ name: 'ColumnItem' })
@Spectra
export default class ColumnItem extends Weaver() {
  @Prop(Object) col!: any;
  @Prop(String) alignType!: any;
}
</script>
