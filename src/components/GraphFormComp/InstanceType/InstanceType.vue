<template>
  <mtd-select :value="model" :filterable="true" @change="onChange" v-if="isEdit" class="form-item-input-width" clearable>
    <mtd-option v-for="v in instanceOptions" :key="v.code" :label="v.description" :value="v.code"></mtd-option>
  </mtd-select>
  <div v-else>{{ instanceOptions.find((v) => v.code === model)?.description }}</div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Watch, Weaver } from '@/decorators';
import dataManage from '@/store/modules/dataManage';
import dataPool from '@/store/modules/dataPool';

@Component
@Spectra
export default class InstanceType extends Weaver(dataPool, dataManage) {
  @Prop()
  isEdit!: boolean;
  @Prop()
  model!: any;
  @Prop()
  context!: any;

  instanceOptions: any[] = [];

  onChange(v) {
    this.$emit('change', v);
  }

  @Watch('context.nodeParam.datasetId', { immediate: true })
  async onDatasetIdChange(datasetId: number) {
    console.log('datasetId', datasetId);
    if (!datasetId) {
      this.instanceOptions = [];
      return;
    }

    const res = await this.action$getInstanceTypeRelation();
    if (!res) return;

    const stageName = this.dataSet?.list?.find((item) => item.id === datasetId)?.stageName;

    if (res && stageName) {
      this.instanceOptions = res[stageName] as any[];
    } else {
      this.instanceOptions = [];
    }
  }
}
</script>
