<template>
  <div type="line-scale" message="" class="model-eval-category-tree-select">
    <!-- <div>
      <div v-for="(R, I) in showEvaluatingList" :key="I" style="margin-bottom: 10px">
        <mtd-button style="margin-right: 10px" @click="handleShow(I, R)">
          <span v-if="R?.version.name" style="margin-right: 10px">{{ R?.version.name }}</span>
          <span>已选择{{ selectedItemCount(R) }}项</span>
        </mtd-button>
        <i class="mtdicon mtdicon-close del" @click="delEvaluating(I)" style="margin-right: 5px"></i>
      </div>
    </div> -->
    <div class="add-box">
      <div class="add">
        <mtd-icon-button class="demo-icon-btn" icon="mtdicon mtdicon-add" @click="addEvaluating" :disabled="disabled" />
      </div>
      <div class="tags" v-if="checkedNodes.length">
        <div v-for="(item, i) in checkedNodes">
          <mtd-tooltip :content="getPath(item?.path)" placement="top">
            <mtd-tag theme="gray" type="pure" :key="item.id" style="margin-left: 5px" @close="removeTag(i)" closable>{{
              item.name
            }}</mtd-tag>
          </mtd-tooltip>
        </div>
      </div>
    </div>

    <mtd-modal
      v-model="visible"
      width="800px"
      :append-to-container="false"
      style="z-index: 9999"
      @close="modalClose"
      title="评测项选择"
      class="VersionModelEvalCategoryTreeSelectModal"
      v-if="visible"
    >
      <div slot="title">
        <div class="model-eval-category-tree-node-container tree-node-checked-tip">
          <span class="tree-node-checked-all mtd-checkbox-inner" /><span style="margin-right: 30px">勾选单项</span>
          <i class="mtdicon tree-node-checked-all mtdicon-success-o" /> <span>全选子集</span>
        </div>
      </div>
      <div style="margin-top: 20px">
        <mtd-input
          v-model="search"
          style="width: 60%; margin-bottom: 10px"
          type="text"
          clearable
          suffix-icon="mtdicon mtdicon-search"
          placeholder="输入评测项名称查找"
        />
        <mtd-tree
          ref="tree"
          :baseIndent="16"
          :check-strictly="checkStrictly"
          :props="treeProps"
          :data="treeData"
          node-key="id"
          checkable
          checked-strategy="all"
          :expanded-keys.sync="expandedKeys"
          :checked-keys.sync="checkedValue"
          class="model-eval-category-tree"
          @toggle-checked="handleChecked"
          style="height: 500px; overflow-y: auto"
          :auto-expand-parent="true"
          :filter-node-method="filterNode"
          filter-with-expand
        >
          <template #default="{ data, node }">
            <div class="model-eval-category-tree-node-container">
              <i
                class="mtdicon tree-node-checked-all mtdicon-success-o"
                @click.stop="handleNodeClick(node)"
                v-if="showCheckAll && data?.children?.length"
              />
              <span>{{ data.name }}</span>
            </div>
          </template>
        </mtd-tree>
      </div>
      <div slot="footer" class="demo-modal-footer">
        <mtd-button style="margin: 0 10px" @click="allDel" :disabled="disabled">全部清空</mtd-button>
        <mtd-button type="primary" @click="save" :disabled="disabled">保存</mtd-button>
      </div>
    </mtd-modal>
  </div>
</template>

<script lang="ts">
import { Component, Prop, PropSync, Ref, Spectra, Watch, Weaver } from '@/decorators';
import DataManage from '@/store/modules/dataManage';
import ModelEvalCategory from '@/store/modules/modelEvalCategory';
import ModelEvalMetaVersion from '@/store/modules/modelEvalMetaVersion';
import { cloneDeep } from 'lodash';

interface TreeNode {
  id: number;
  parentId: number;
  parent?: TreeNode;
  name: string;
  children?: TreeNode[];
}

@Component
@Spectra
export default class ThreeInOneModelEvalCategory extends Weaver(ModelEvalCategory, ModelEvalMetaVersion, DataManage) {
  @Ref('tree')
  treeRef: any;
  @Prop({ default: false })
  disabled!: boolean;
  @Prop({ default: '请选择评测项' })
  placeholder!: string;
  @Prop({ default: true })
  collapseTags!: boolean;
  @Prop({ default: true })
  checkStrictly!: boolean;
  @Prop({ default: true })
  showCheckAll!: boolean;
  @PropSync('value', { default: () => [] })
  innerValue!: any;
  @Prop({ default: true })
  isEditAuth!: boolean;
  @Prop({ default: '' })
  dataSubSetDetailNew!: string;
  @Prop({ default: false })
  dataSubSetListNewBatch!: boolean;
  @Prop({ default: true })
  multiple!: boolean;
  @Prop({ default: '' })
  type!: string;
  @Prop()
  runSpecSetId!: number;
  @Prop()
  treeList!: any;

  evaluatingList: any = [];
  showEvaluatingList: any = [];
  visible = false;
  editIndex = 0;
  options = [];

  treeProps = {
    title: 'name',
    children: 'children',
  };

  expandedKeys = [];
  checkedNodes: any[] = [];
  checkedValue = [];

  search = '';
  @Watch('innerValue', { immediate: true })
  handleValueChange(value) {
    this.checkedValue = cloneDeep(this.innerValue);
  }
  @Watch('search')
  handleSearchChange(val) {
    this.treeRef.filter(val);
  }

  removeTag(i) {
    this.checkedValue.splice(i, 1);
    this.checkedNodes.splice(i, 1);
    if (this.checkedValue.length > 0) {
      this.save();
    } else {
      this.$emit('setChartShow');
    }
  }

  getPath(path) {
    if (path && path?.length) {
      let str = path.join('->');
      return str;
    }
    return '';
  }

  filterNode(value, data) {
    if (!value) return true;
    return data.name.indexOf(value) !== -1;
  }

  get treeData() {
    if (this.treeList.length > 0) {
      const enhancedTreeData = this.assignIds(this.treeList);
      return enhancedTreeData;
    }
  }

  assignIds(data, parentId = '') {
    return data.map((node, index) => {
      // 生成当前节点的唯一 ID
      const nodeId = parentId ? `${parentId}-${index}` : `${index}`;

      // 在当前节点上增加 ID
      node.id = nodeId;

      // 递归分配子节点的 IDs，如果存在子节点
      if (node.children && node.children.length > 0) {
        node.children = this.assignIds(node.children, nodeId);
      }

      return node;
    });
  }

  addEvaluating() {
    this.visible = true;
  }

  modalClose() {
    this.visible = false;
  }
  handleChecked(value: any, { nodes }) {
    this.checkedValue = value;
    this.checkedNodes = nodes
      .filter((item) => item && item.data)
      .map((item) => ({
        id: item.data.id,
        name: item.data.name,
        path: this.generatePath(item),
      }));
  }
  handleNodeClick(node) {
    const isChecked = node.checked;
    const instance = this.treeRef.$refs.wrappedInstance;
    this.$set(node, 'checked', !isChecked);
    this.updateChildChecked(node, !isChecked);
    this.checkedValue = instance.getCheckedNodes(instance.root).map((n) => n.key);
    this.checkedNodes = instance.getCheckedNodes(instance.root).map((item) => ({
      id: item.data.id,
      name: item.data.name,
      path: this.generatePath(item),
    }));

    // this.checkAllDisabled = false;
  }
  generatePath(node: any): string[] {
    const pathArray: string[] = [];

    let currentNode = node;

    // 循环向上查找父节点，直到没有父节点为止
    while (currentNode) {
      // 优先从 data 中获取 name，如果没有再从节点本身获取
      const name = currentNode?.data?.name || currentNode?.name;

      // 确保 name 存在才添加到路径中
      if (name !== undefined && name !== null) {
        pathArray.unshift(name); // 添加到数组开头
      }

      // 获取父节点（优先从 data.parent，然后是 $parent）
      currentNode = currentNode?.data?.parent || currentNode?.$parent || null;
    }

    return pathArray; // 返回完整路径数组（保留所有节点，包括重名节点）
  }

  updateChildChecked(node, isChecked) {
    const { children } = node;
    if (children) {
      children.forEach((item) => {
        if (!item.disableCheckbox) {
          this.$set(item, 'checked', isChecked);
          this.updateChildChecked(item, isChecked);
        }
      });
    }
  }
  allDel() {
    this.checkedValue = [];
    this.checkedNodes = [];
    this.expandedKeys = [];
  }
  save() {
    if (this.checkedValue?.length <= 10) {
      const query = this.buildQueryTree(this.treeData, this.checkedValue);
      this.$emit('save', query);
      this.$emit('change', this.checkedValue);
      this.modalClose();
    } else if (this.checkedValue?.length > 10) {
      this.$mtd.message.warning('评测项选择数量控制在10个');
    } else {
      this.$mtd.message.warning('请选择评测项');
    }
  }
  buildQueryTree(treeData, innerValues) {
    const valueSet = new Set(innerValues);

    // 递归遍历并构建符合条件的树结构
    function traverse(node) {
      // 如果是叶子节点
      if (!node.children) {
        return valueSet.has(node.id)
          ? { ...node, select: true } // 选中叶子节点
          : null; // 未选中的叶子节点直接过滤
      }

      // 处理子节点
      const children = node.children
        .map(traverse) // 递归处理子节点
        .filter((child) => child !== null); // 过滤掉未选中的分支

      // 如果没有子节点且自身未被选中，则过滤掉
      if (children.length === 0 && !valueSet.has(node.id)) {
        return null;
      }

      // 返回当前节点（父节点），select 仅由自身是否在 innerValues 中决定
      return {
        ...node,
        select: valueSet.has(node.id), // 父节点 select 仅由自身决定
        children,
      };
    }

    // 处理整个树并过滤空节点
    return treeData.map(traverse).filter((node) => node !== null);
  }
}
</script>

<style scoped lang="less">
.model-eval-category-tree-select {
  width: 100%;
}
.model-eval-category-tree-select {
  .del,
  .add {
    color: #1c6cdc;
    cursor: pointer;
    font-size: 14px;
    border-radius: 50%;
    &:hover {
      background: #e5e4e4;
    }
  }
  .delete-icon-btn {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.38);
    .mtdicon {
      color: rgba(0, 0, 0, 0.38);
      -webkit-text-stroke-width: 0;
    }
    &.invalid .mtdicon {
      color: rgba(#f5483b, 0.8);
    }
  }
  .mtd-dropdown {
    vertical-align: top;
  }
  .del {
    color: #000;
  }

  .mtd-select,
  .mtd-dropdown {
    width: 100%;
  }

  .mtd-select-choice {
    max-width: calc(40% - 1px);
  }

  .mtd-select-search-field {
    width: 1px !important;
    margin: 0;
  }
}

.model-eval-category-tree {
  .mtd-tree-node-content {
    padding-right: 15px;
  }
}

.model-eval-category-tree-node-container {
  display: flex;
  align-items: center;
  &.tree-node-checked-tip {
    padding: 9px 5px 5px 36px;
    justify-content: unset;
    color: rgba(0, 0, 0, 0.6);
    background-color: #f9f9f9;
    .tree-node-checked-all {
      cursor: default;
      color: #1c6cdc;
    }
  }

  .tree-node-checked-all {
    color: rgba(0, 0, 0, 0.12);
    cursor: pointer;
    font-size: 22px;
    width: 22px;
    height: 22px;
    line-height: 22px;
    margin-right: 5px;
    transition: color 0.2s;
    &.mtd-checkbox-inner {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    &:hover {
      color: #1c6cdc;
    }
  }
}
.mtd-select-tags-ul {
  max-height: 600px;
  overflow: auto;
  width: 400px;
}

.category-table {
  width: 100%;
  height: auto;
  .thead {
    display: flex;
    width: 100%;
    div {
      background-color: #f2f2f2;
      border: 1px solid rgba(0, 0, 0, 0.09);
      padding: 8px;
    }
    .version {
      width: 150px;
    }
    .selected {
      width: calc(100% - 210px);
    }
    .btn-del {
      width: 50px;
    }
  }
  .tbody {
    display: flex;
    max-height: 100px;
    div {
      background-color: #ffffff;
    }
    .version {
      width: 150px;
      padding: 8px;
      border: 1px solid rgba(0, 0, 0, 0.09);
    }
    .selected {
      width: calc(100% - 210px);
      display: flex;
      justify-content: space-around;
      flex-wrap: nowrap;
      border: 1px solid rgba(0, 0, 0, 0.09);

      .category-tag {
        width: 80%;
        overflow-y: scroll;
      }
    }
    .btn-del {
      width: 50px;
      padding: 8px;
      border: 1px solid rgba(0, 0, 0, 0.09);
    }
  }
}

.VersionModelEvalCategoryTreeSelectModal {
  /deep/.mtd-input-invalid .mtd-input {
    border-color: rgba(0, 0, 0, 0.12);
  }
}
.add-box {
  display: flex;
  align-items: center;
}
.tags {
  display: flex;
  margin-left: 10px;
}
</style>
