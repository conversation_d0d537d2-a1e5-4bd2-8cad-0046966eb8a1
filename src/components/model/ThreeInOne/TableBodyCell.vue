<script lang="tsx">
import citadelOpenSDK from '@it/citadel-open-sdk';

export default {
  // 函数式组件减少性能消耗
  functional: true,
  props: {
    discussion: Object,
    tableKey: String,
    rowKey: String,
    column: Object,
    vColumnIndex: Number,
    rowData: Object,
    contrastRowData: Object,
    columnSortOrderData: Object,
    isColumnSortable: Boolean,
    showSecondValue: Boolean,
    evalDataSizeConfiguration: Boolean,
    getToolTipHTML: Function,
    withConfidenceInterval: Boolean,
    filterGroup: Object,
    patternComparison: Object,
    checkMark: Boolean,
    tokenOutputAvg: Boolean,
    tikTokenOutputAvg: Boolean,
    textLenOutputAvg: Boolean,
  },
  render(h, ctx: any) {
    const {
      discussion,
      tableKey,
      rowKey,
      column,
      rowData,
      columnSortOrderData,
      isColumnSortable,
      // contrastRowData,
      evalDataSizeConfiguration,
      withConfidenceInterval,
      tokenOutputAvg,
      tikTokenOutputAvg,
      textLenOutputAvg,
      filterGroup,
      patternComparison,
      checkMark,
    } = ctx.props;
    ctx.listeners.cellRender?.();
    if (!rowData) return <span class="spanTD">loading...</span>;
    // 列数据
    const { field, title } = column;
    // 单元格数据
    const fieldData = rowData.valueMap[field];
    if (!fieldData) return <span class="spanTD">loading...</span>;
    const discussionAnchor = `tableKey:${tableKey},modelLabel:${rowData.modelLabel},column:${title}`;
    const refKey = `${rowKey}_${field}`;
    // 当前是否是对比行
    // const isContrastRow = contrastRowData?.rowKey === rowKey;

    // 固定区
    if (field === 'modelLabel') {
      return (
        <mtd-tooltip placement="top">
          <span slot="content" style="font-size: 12px" domPropsInnerHTML={rowData.hoverAndOther.hoverValue || '-'}></span>
          <div class="modelName">
            <div
              class={{
                'merge-cell-identification': true,
              }}
              style={{ backgroundColor: rowData.backColor || '#ffffff' }}
            ></div>
            <div class="modelNameTxt">
              <span style="margin-right: 4px">{rowData.modelLabel.modelName}</span>
              <mtd-tag size="small">{rowData.modelLabel.modelCheckpoint}</mtd-tag>

              <span
                style={{
                  background: rowData.hoverAndOther.color,
                  color: '#ffffff',
                  margin: '2px 5px',
                  padding: '1px 8px',
                  borderRadius: '2px',
                  whiteSpace: 'nowrap',
                  position: 'relative',
                  bottom: '-1.5px',
                }}
              >
                {rowData.hoverAndOther.modelFormat}
              </span>
            </div>
            {isColumnSortable && (
              <div
                class={{
                  'mtd-table-sortable': true,
                  [columnSortOrderData.order]: columnSortOrderData.rowKey === rowKey,
                }}
                style="transform: rotate(-90deg)"
                onClick={(e) => {
                  e.stopPropagation();
                  ctx.listeners.sortClick?.(rowKey);
                }}
              >
                <i class="mtd-table-sortable-icon mtd-table-sortable-ascending" />
                <i class="mtd-table-sortable-icon mtd-table-sortable-descending" />
              </div>
            )}
          </div>
        </mtd-tooltip>
      );
    }

    const handleDiscussionClick = (e) => {
      if (discussion) {
        citadelOpenSDK.quoteComment.activateDiscussion(discussion.discussionId);
        e.stopPropagation();
      }
    };

    const sortByArray = (arr1, arr2) => {
      const orderMap = arr1.reduce((map, val, index) => {
        map[val] = index;
        return map;
      }, {});

      return arr2?.sort((a, b) => {
        return orderMap[a.evalDataSize] - orderMap[b.evalDataSize];
      });
    };

    const diffEvalDataSizeData = () => {
      const { value, diffCalculateUnitList } = fieldData;
      if (!diffCalculateUnitList || diffCalculateUnitList.length === 0) {
        return value;
      }

      diffCalculateUnitList.unshift(value);
      const uniqueList: any = [...new Set(diffCalculateUnitList)].filter((item) => item !== '' && item !== null && item !== undefined);

      const itemsStr = uniqueList.map((item) => item.toString()).join('/');
      return itemsStr;
    };

    if (field === 'evalDataSize') {
      return (
        <span
          data-discussion-anchor={discussionAnchor}
          onClick={handleDiscussionClick}
          ref={refKey}
          class={[
            'spanTD',
            discussion && 'discussion',
            discussion?.active && 'discussion-active',
            discussion?.hoverActive && 'discussion-active',
          ]}
        >
          <span style="font-weight: bold">{diffEvalDataSizeData()}</span>
        </span>
      );
    }

    const { value, modelId, llmModelId } = fieldData;

    const options = [
      { label: '评测模型', value: '评测模型', color: 'blue' },
      { label: '对照模型', value: '对照模型', color: 'orange' },
      { label: '基线模型', value: '基线模型', color: 'green' },
    ];

    const color = {
      评测模型: 'blue',
      对照模型: 'orange',
      基线模型: 'green',
    };

    if (field === 'modelLabelSecond') {
      return (
        <div style="display: flex; align-items: center">
          <mtd-checkbox
            style="margin-left: 4px"
            class={{ 'display-none': !checkMark && modelId && llmModelId }}
            size="small"
            value={patternComparison[llmModelId]?.checked}
            v-on:change={() => ctx.listeners.checkboxChange?.({ llmModelId, modelId })}
          />
          <mtd-tooltip placement="top">
            <span slot="content" style="font-size: 12px" domPropsInnerHTML={value}></span>
            <div class="flex-center modelNameTxt" style="font-size: 12px; justify-content: start; padding-left: 5px">
              <span style="margin: 0 10px; text-align: left">{value}</span>
            </div>
          </mtd-tooltip>
          <mtd-popover
            class={{ 'display-none': !checkMark && modelId && llmModelId }}
            trigger="hover"
            placement="bottom-start"
            style="margin-right: 10px"
          >
            <mtd-tag theme={color[patternComparison[llmModelId]?.modelType] || 'gray'} class="model-btn" size="small">
              {patternComparison[llmModelId]?.modelType || '请选择'}
            </mtd-tag>
            <template slot="content">
              {options.map((item) => (
                <div
                  class="patternComparison"
                  onClick={() => ctx.listeners.checkboxChange?.({ llmModelId, modelId, modelType: item.value, flag: false })}
                >
                  {item.label}
                </div>
              ))}
            </template>
          </mtd-popover>
        </div>
      );
    }

    if (field === 'groupSelect backColor-240-247-255') {
      return (
        <div>
          <div class="flex-center">
            {value &&
              value.map((item, index) => {
                const { diffCountUnequalCategories, diffEvalDataSize, subjectivityIsFromModelEval } = item;
                const sortList: any = [];
                for (const evalDataSizeItem in diffCountUnequalCategories) {
                  if (evalDataSizeItem !== 'groupValueUniqueArr' && evalDataSizeItem !== 'count') {
                    sortList.push({
                      name: evalDataSizeItem,
                      value: diffCountUnequalCategories[evalDataSizeItem],
                    });
                  }
                }

                const evalList = sortList.sort((a, b) => a.value - b.value).map((item) => item.name);
                const list = sortByArray(evalList, diffEvalDataSize);
                const evalDataSizeTooltip = evalDataSizeConfiguration && diffEvalDataSize?.length > 0 && (
                  <mtd-tooltip placement="bottom" popper-class="eval-data-size-tooltip">
                    <i class="mtdicon mtdicon-warning-circle-o" style="font-size: 12px;color: #0a70f5;cursor: pointer;"></i>
                    <div slot="content" class="content" style="max-height:500px;overflow: scroll;">
                      <div class="title">数据集规模如下：</div>
                      {diffCountUnequalCategories?.count.length > 0 &&
                        evalList.map((unit, index) => (
                          <span key={index} style="font-size: 16px;margin-bottom: 10px">
                            {unit}*{diffCountUnequalCategories[unit]}个；
                          </span>
                        ))}
                      {list.map((item) => (
                        <div>
                          {item.evalDataSize}:{item.dataSetLabel}:{item.dataSubSetLabel}
                        </div>
                      ))}
                    </div>
                  </mtd-tooltip>
                );
                return (
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      flexDirection: 'column',
                    }}
                  >
                    <div
                      class="flex-center"
                      style={{
                        flex: '1 0',
                        textAlign: 'center',
                        borderLeft: index > 0 ? '1px solid rgb(216 216 216)' : '',
                      }}
                    >
                      <mtd-tooltip class={{ 'display-none': !Boolean(rowData.master) }} placement="top">
                        <span slot="content" style="font-size: 12px" domPropsInnerHTML={`master`}></span>
                        <span class="dot"></span>
                      </mtd-tooltip>
                      <mtd-tooltip placement="top">
                        <span slot="content" style="font-size: 12px" domPropsInnerHTML={item.toolTipHTML}></span>
                        <span
                          data-discussion-anchor={discussionAnchor}
                          ref={refKey}
                          class={[
                            'spanTD',
                            'clickable-value',
                            discussion && 'discussion',
                            discussion?.active && 'discussion-active',
                            discussion?.hoverActive && 'discussion-active',
                          ]}
                          style="white-space: nowrap"
                          onClick={(e) => {
                            handleDiscussionClick(e);
                            ctx.listeners.valueClick?.(item, rowData, column);
                          }}
                        >
                          {item.secondValue && <span>{item.secondValue}±</span>}
                          <span>{item.value || item.mean || '-'}</span>
                          {withConfidenceInterval && !item.secondValue && item.confidenceInterval && (
                            <span>±{item.confidenceInterval}</span>
                          )}
                          {item.hasStatSubSetCount !== item.totalSubSetCount && <span style="color: red">*</span>}
                        </span>
                      </mtd-tooltip>
                      {subjectivityIsFromModelEval === true && (
                        <mtd-tooltip placement="top">
                          <span slot="content" style="font-size: 12px" domPropsInnerHTML={` 主观评测指标来自 modeleval 数值 `}></span>
                          <span style="color: red;cursor: pointer;">※</span>
                        </mtd-tooltip>
                      )}
                      {evalDataSizeTooltip}
                    </div>
                    <div style="font-size: 12px;">{tokenOutputAvg && `平均token数：${item?.tokenOutputAvg ?? ''}`}</div>
                    <div style="font-size: 12px;">{tikTokenOutputAvg && `平均tiktoken数：${item?.tikTokenOutputAvg ?? ''}`}</div>
                    <div style="font-size: 12px;">{textLenOutputAvg && `平均字符数：${item?.textLenOutputAvg ?? ''}`}</div>
                  </div>
                );
              })}
          </div>
        </div>
      );
    }

    if (field === 'seq') {
      return (
        <span
          data-discussion-anchor={discussionAnchor}
          ref={refKey}
          class={[
            'spanTD',
            discussion && 'discussion',
            discussion?.active && 'discussion-active',
            discussion?.hoverActive && 'discussion-active',
          ]}
          style="white-space: nowrap"
          onClick={handleDiscussionClick}
        >
          <span>{value + 1}</span>
        </span>
      );
    }

    if (fieldData.customTags) {
      return (
        <span
          data-discussion-anchor={discussionAnchor}
          ref={refKey}
          class={[
            'spanTD',
            discussion && 'discussion',
            discussion?.active && 'discussion-active',
            discussion?.hoverActive && 'discussion-active',
          ]}
          style="white-space: nowrap"
          onClick={handleDiscussionClick}
        >
          <span>{value}</span>
        </span>
      );
    }

    let useValue = value;
    if (Array.isArray(fieldData)) useValue = fieldData;
    return (
      <div>
        <div class="flex-center">
          {useValue &&
            useValue.map((item, index) => {
              const { diffCountUnequalCategories, diffEvalDataSize, subjectivityIsFromModelEval } = item;
              const sortList: any = [];
              for (const evalDataSizeItem in diffCountUnequalCategories) {
                if (evalDataSizeItem !== 'groupValueUniqueArr' && evalDataSizeItem !== 'count') {
                  sortList.push({
                    name: evalDataSizeItem,
                    value: diffCountUnequalCategories[evalDataSizeItem],
                  });
                }
              }

              const evalList = sortList.sort((a, b) => a.value - b.value).map((item) => item.name);
              const list = sortByArray(evalList, diffEvalDataSize);
              const evalDataSizeTooltip = evalDataSizeConfiguration && diffEvalDataSize?.length > 0 && (
                <mtd-tooltip placement="bottom" popper-class="eval-data-size-tooltip">
                  <i class="mtdicon mtdicon-warning-circle-o" style="font-size: 12px;color: #0a70f5;cursor: pointer;"></i>
                  <div slot="content" class="content" style="max-height:500px;overflow: scroll;">
                    <div class="title">数据集规模如下：</div>
                    {diffCountUnequalCategories?.count.length > 0 &&
                      evalList.map((unit, index) => (
                        <span key={index} style="font-size: 16px;margin-bottom: 10px">
                          {unit}*{diffCountUnequalCategories[unit]}个；
                        </span>
                      ))}
                    {list.map((item) => (
                      <div>
                        {item.evalDataSize}:{item.dataSetLabel}:{item.dataSubSetLabel}
                      </div>
                    ))}
                  </div>
                </mtd-tooltip>
              );

              return (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'column',
                  }}
                >
                  <div
                    class="flex-center"
                    style={{
                      flex: '1 0',
                      textAlign: 'center',
                      borderLeft: index > 0 ? '1px solid rgb(216 216 216)' : '',
                    }}
                  >
                    <mtd-tooltip class={{ 'display-none': !Boolean(rowData.master) || !value }} placement="top">
                      <span slot="content" style="font-size: 12px" domPropsInnerHTML={`master`}></span>
                      <span class="dot"></span>
                    </mtd-tooltip>
                    <mtd-tooltip placement="top">
                      <span slot="content" style="font-size: 12px" domPropsInnerHTML={item.toolTipHTML}></span>
                      <span
                        data-discussion-anchor={discussionAnchor}
                        ref={refKey}
                        class={[
                          'spanTD',
                          'clickable-value',
                          discussion && 'discussion',
                          discussion?.active && 'discussion-active',
                          discussion?.hoverActive && 'discussion-active',
                        ]}
                        style="white-space: nowrap"
                        onClick={(e) => {
                          handleDiscussionClick(e);
                          ctx.listeners.valueClick?.(item, rowData, column);
                        }}
                      >
                        {item.secondValue && <span>{item.secondValue}±</span>}
                        <span>{item.mean || item.value || '-'}</span>
                        {withConfidenceInterval && !item.secondValue && item.confidenceInterval && <span>±{item.confidenceInterval}</span>}
                        {item.hasStatSubSetCount !== item.totalSubSetCount && <span style="color: red">*</span>}
                      </span>
                    </mtd-tooltip>
                    {subjectivityIsFromModelEval === true && (
                      <mtd-tooltip placement="top">
                        <span slot="content" style="font-size: 12px" domPropsInnerHTML={` 主观评测指标来自 modeleval 数值 `}></span>
                        <span style="color: red;cursor: pointer;">※</span>
                      </mtd-tooltip>
                    )}
                    {evalDataSizeTooltip}
                  </div>
                  <div style="font-size: 12px;">{tokenOutputAvg && `平均token数：${item?.tokenOutputAvg ?? ''}`}</div>
                  <div style="font-size: 12px;">{tikTokenOutputAvg && `平均tiktoken数：${item?.tikTokenOutputAvg ?? ''}`}</div>
                  <div style="font-size: 12px">{textLenOutputAvg && `平均字符数：${item?.textLenOutputAvg ?? ''}`}</div>
                </div>
              );
            })}
        </div>
      </div>
    );
  },
};
</script>
