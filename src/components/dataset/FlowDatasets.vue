<template>
  <div class="flow-datasets-container">
    <div class="container">
      <div>{{ `已选择${datasets.length}项` }}</div>
      <mtd-button @click="config.visible = true">{{ `${disabled ? '查看' : '选择'}数据集` }}</mtd-button>
    </div>
    <mtd-modal v-model="config.visible" title="数据集信息" width="auto" destroy-on-close @closed="closeModal" @open="openModal">
      <div slot="title">
        <span style="font-size: 18px; font-weight: 500">数据集信息</span>
        <mtd-button v-if="!disabled" type="text-primary" :disabled="limit && tableData.length >= limit" @click="add">新增</mtd-button>
      </div>

      <mtd-table v-if="showTable" show-overflow-tooltip :height="450" :data="tableData">
        <mtd-table-column prop=" " label=" " width="40">
          <template slot-scope="scope">
            <span> {{ scope.$index + 1 }}</span>
          </template>
        </mtd-table-column>
        <mtd-table-column v-for="(item, index) in headers" :key="index" :prop="item.key" :label="item.label" width="200">
          <template slot-scope="scope">
            <mtd-button
              v-if="['datasetId', 'datasetVersionId'].includes(item.key)"
              type="text-primary"
              @click="goToDetail(scope.row, item.key)"
              >{{ getCellContent(scope.row, item) }}</mtd-button
            >
            <span v-else> {{ getCellContent(scope.row, item) }}</span>
          </template>
        </mtd-table-column>
        <mtd-table-column fixed="right" label="操作" width="90">
          <template slot-scope="scope">
            <mtd-button type="text-primary" @click="edit(scope.row)">编辑</mtd-button>
            <mtd-button type="text-primary" @click="deleteItem(scope)">删除</mtd-button>
          </template>
        </mtd-table-column>
      </mtd-table>
      <div v-if="!disabled" slot="footer">
        <mtd-button @click="closeModal">取消</mtd-button>
        <mtd-button type="primary" @click="confirm">暂存</mtd-button>
      </div>
    </mtd-modal>
    <mtd-modal
      v-model="editModal.visible"
      :title="dics.editModalTitle[editModal.type]"
      width="auto"
      :append-to-container="false"
      destroy-on-close
      @close="closeEditModal"
    >
      <mtd-form ref="editForm" style="margin-right: 60px; min-height: 300px" :label-width="200" :model="editModal.form">
        <FormItems :form="editModal.form" :config="{ itemInfoList: headers }"></FormItems>
      </mtd-form>
      <div slot="footer">
        <mtd-button @click="closeEditModal">取消</mtd-button>
        <mtd-button type="primary" @click="editConfirm">确定</mtd-button>
      </div>
    </mtd-modal>
  </div>
</template>

<script lang="ts">
import FormItems from '@/components/dataset/FormItems.vue';
import { Component, Prop, Ref, Spectra, Weaver } from '@/decorators';
import dataManage from '@/store/modules/dataManage';

import { cloneDeep } from 'lodash';
interface configType {
  visible?: boolean;
  [k: string]: unknown;
}
interface headersType {
  key: string;
  label: string;
  [k: string]: unknown;
}

@Component({ components: { FormItems } })
@Spectra
export default class FlowDatasets extends Weaver(dataManage) {
  @Prop({ default: () => ({ visible: false }) })
  config!: configType;
  @Prop({ default: () => [] })
  headers!: headersType[];
  @Prop({ default: () => [] })
  datasets!: object[];
  @Prop({ default: () => false })
  disabled!: boolean;
  @Prop({ default: () => null })
  limit!: number | null;
  @Ref('editForm')
  editFormRef;

  tableData: object[] = [];
  showTable = true;

  dics: any = {
    datasetId: [],
    datasetVersionId: [],
    progressiveMixGroup1: [
      { value: 1, label: '是' },
      { value: 0, label: '否' },
    ],
    editModalTitle: {
      add: '新增',
      edit: '修改',
    },
  };

  maps = {
    datasetId: new Map(),
    datasetVersion: new Map(),
    progressiveMixGroup1: new Map(),
  };

  editModal = {
    type: 'add',
    visible: false,
    form: {},
    originForm: {},
  };

  closeModal() {
    this.config.visible = false;
    this.tableData = [];
  }
  openModal() {
    this.tableData = cloneDeep(this.datasets);
  }

  async confirm() {
    this.datasets.length = 0;
    this.datasets.push(...this.tableData);
    this.$emit('doConfirm', this.datasets);
    this.closeModal();
  }

  openEditModal(type, data) {
    Object.assign(this.editModal, {
      type,
      visible: true,
      form: cloneDeep(data),
      originForm: data,
    });
  }

  getNewItem() {
    const newItem = { comment: '' };
    this.headers.forEach((item) => (newItem[item.key] = undefined));
    return newItem;
  }

  closeEditModal() {
    Object.assign(this.editModal, {
      visible: false,
      form: this.getNewItem(),
    });
  }

  async editConfirm() {
    const { type, form, originForm } = this.editModal;
    try {
      await this.editFormRef.validate();
      if (type === 'add') {
        this.tableData.push(form);
      } else {
        Object.assign(originForm, form);
      }

      this.closeEditModal();
    } catch (error) {
      console.log(error);
    }
  }

  add() {
    const newItem = this.getNewItem();
    this.openEditModal('add', newItem);
  }

  edit(row) {
    this.openEditModal('edit', row);
  }

  deleteItem(scope) {
    if (this.tableData.length < 1) return;
    this.tableData.splice(scope.$index, 1);
  }

  async getDatasetList(name = '', pageSize = 20) {
    await this.action$dataSet({ param: { name }, pageNumber: 1, pageSize });
    if (this.dataSet && this.dataSet.list) {
      const list = this.dataSet.list || [];
      list.forEach((item) => {
        item.label = item.name;
        item.value = item.id;
        this.maps.datasetId.set(item.id, item);
      });
      this.dics.datasetId = list;
    }

    this.showTable = false;
    this.$nextTick(() => {
      this.showTable = true;
    });
  }

  getCellContent(row, item) {
    const { key } = item;
    const { maps } = this;
    if (key === 'datasetVersionId') {
      return row.comment ? `${row[key]}-${row.comment}` : row[key];
    }

    return maps[key] ? maps[key].get(row[key])?.label || row[key] : row[key];
  }

  goToDetail(dataset, key) {
    const query: { [k: string]: null } = {};
    const value = dataset[key];
    key.includes('atasetId') && Object.assign(query, { id: value });
    if (key.includes('atasetVersionId')) {
      const value0 = dataset[key.replace('Version', '')];
      Object.assign(query, { id: value0, vid: value });
    }

    const routeData = this.$router.resolve({ name: `pretrain-dataset-list`, query });
    window.open(routeData.href, '_blank');
  }

  initProgressiveMixGroup1Map() {
    this.dics.progressiveMixGroup1.forEach((item) => {
      this.maps.progressiveMixGroup1.set(item.value, item);
    });
  }

  initEditModalForm() {
    const newItem = this.getNewItem();
    this.editModal.form = newItem;
  }

  created() {
    this.getDatasetList('', 9999);
    this.initEditModalForm();
  }

  mounted() {
    this.initProgressiveMixGroup1Map();
  }
}
</script>

<style lang="scss">
.flow-datasets-container {
  width: 100%;
  .container {
    display: flex;
    justify-content: space-between;
  }
}
</style>
