<template>
  <div>
    <mtd-select
      v-if="isEdit"
      :value="value"
      class="common-width"
      multiple
      :filterable="true"
      popper-class="benchmark-select"
      :style="{ width: selectWidth ? `${selectWidth}%` : '180px' }"
      @change="change"
    >
      <mtd-option
        v-for="v in modelEvalDataSubSetUnVersionedSimpleList.dataSubSetList"
        :key="v.id"
        :label="`${v.dataSetLabel || v.dataSetName}${v.name ? ':' + v.name : ''}`"
        :value="v.id"
      >
      </mtd-option>
    </mtd-select>
    <span v-else>{{ dataSetLabel }}</span>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Spectra, Watch, Weaver } from '@/decorators';
import modelEvalDataSubSetUnVersioned from '@/store/modules/modelEvalDataSubSetUnVersioned';

@Component
@Spectra
export default class DataSubSetIdsFilter extends Weaver(modelEvalDataSubSetUnVersioned) {
  @Prop()
  value!: any;
  @Prop()
  runSpecSetId!: number;
  @Prop()
  isEdit!: boolean;
  @Prop()
  selectWidth?: number;

  benchMark: any;
  change(v) {
    this.$emit('input', v);
  }

  get dataSetLabel() {
    if (!this.value || this.value.length === 0) {
      return '';
    }
    const labels = this.value
      .map((modelId) => {
        const item = this.modelEvalDataSubSetUnVersionedSimpleList.dataSubSetList.find((v) => v.id === modelId);
        if (item) {
          return `${item.dataSetLabel || item.dataSetName}${item.name ? ' : ' + item.name : ''}`;
        }
        return '';
      })
      .filter((label) => label !== ''); // 过滤掉未找到的项
    return labels.join(', ');
  }

  @Watch('runSpecSetId', { immediate: true })
  getData(val) {
    if (val) {
      this.action$modelEvalDataSubSetUnVersionedSimpleList({
        categoryIdsListFilter: [],
        statTypeFilter: '',
        keyword: '',
        publicStatusFilter: '',
        runSpecSetIdFilter: Number(val),
        subSetTags: [],
        typeFilter: '',
        categoryTags: [],
        limit: 10 ** 3,
        offset: 0,
        idList: [],
        batchKeywordList: [],
        ownerList: [],
      });
    }
  }
}
</script>
<style lang="scss"></style>
