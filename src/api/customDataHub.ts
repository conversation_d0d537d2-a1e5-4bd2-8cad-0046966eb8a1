import * as dataHub from '@/model/customDataHub';
import { FlowHttpRequestOptions, mixHeadersOptions, pick } from '@snfe/flow-yapi-helpers';
import http from './http';

/**
 * @desc id=1332018, catid=232378, projectId=35802, created=2023-11-23 18:25:23, last-modified=2023-11-24 14:07:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/data-hub/dataset/category/queryCategoryByName)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332018)
 */
export function getDataHubDatasetCategoryQueryCategoryByName(
  params: dataHub.IGetDataHubDatasetCategoryQueryCategoryByNameParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<dataHub.IGetDataHubDatasetCategoryQueryCategoryByNameResponse>('/data-hub/dataset/category/queryCategoryByName', {
    params: pick(params, ['name']),
    ...options,
  });
}

/**
 * @desc id=1332313, catid=232378, projectId=35802, created=2023-11-24 14:00:21, last-modified=2023-11-24 14:14:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/data-hub/dataset/category/queryTree_1700814636201)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332313)
 */
export function getDataHubDatasetCategoryQueryTree1700814636201(
  params: dataHub.IGetDataHubDatasetCategoryQueryTree1700814636201Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<dataHub.IGetDataHubDatasetCategoryQueryTree1700814636201Response>('/data-hub/dataset/category/queryTree_1700814636201', {
    params: pick(params, ['id', 'code', 'level']),
    ...options,
  });
}

/**
 * @desc id=1332030, catid=232378, projectId=35802, created=2023-11-23 18:39:52, last-modified=2023-11-23 18:45:30
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332030)
 */
export function postDataHubCategory(params: dataHub.IPostDataHubCategoryParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubCategoryResponse>('/webApi/data-hub/category', {
    data: pick(params, ['id', 'parentId', 'name']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1332041, catid=232378, projectId=35802, created=2023-11-23 18:46:42, last-modified=2023-11-23 18:47:57
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category_1700745416587)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332041)
 */
export function getDataHubCategory1700745416587(
  params: dataHub.IGetDataHubCategory1700745416587Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<dataHub.IGetDataHubCategory1700745416587Response>('/webApi/data-hub/category_1700745416587', {
    params: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1332180, catid=232378, projectId=35802, created=2023-11-24 10:48:27, last-modified=2023-11-24 14:18:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/data-hub/dataset/category/queryTree_1700803122148)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332180)
 */
export function getDataHubDatasetCategoryQueryTree1700803122148(
  params: dataHub.IGetDataHubDatasetCategoryQueryTree1700803122148Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<dataHub.IGetDataHubDatasetCategoryQueryTree1700803122148Response>('/data-hub/dataset/category/queryTree_1700803122148', {
    params: pick(params, ['id', 'code', 'level', 'time']),
    ...options,
  });
}

/**
 * @desc id=1332388, catid=232378, projectId=35802, created=2023-11-24 14:41:39, last-modified=2023-11-24 14:58:32
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/page_1700817114249)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332388)
 */
export function postDataHubTaskReportPage1700817114249(
  params: dataHub.IPostDataHubTaskReportPage1700817114249Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubTaskReportPage1700817114249Response>('/webApi/data-hub/task_report/page_1700817114249', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1332613, catid=232378, projectId=35802, created=2023-11-24 17:15:32, last-modified=2023-11-24 17:19:35
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/export_1700826346771)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332613)
 */
export function postDataHubTaskReportExport1700826346771(
  params: dataHub.IPostDataHubTaskReportExport1700826346771Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubTaskReportExport1700826346771Response>('/webApi/data-hub/task_report/export_1700826346771', {
    data: pick(params, ['businessId', 'taskTypeId', 'taskConfigId', 'taskTag']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1332615, catid=232378, projectId=35802, created=2023-11-24 17:16:46, last-modified=2023-11-24 17:22:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/export_1700826346771_1700826421206)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332615)
 */
export function postDataHubTaskReportExport17008263467711700826421206(
  params: dataHub.IPostDataHubTaskReportExport17008263467711700826421206Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubTaskReportExport17008263467711700826421206Response>(
    '/webApi/data-hub/task_report/export_1700826346771_1700826421206',
    {
      data: pick(params, ['startTime', 'endTime', 'timeUnit', 'firstCategoryId', 'secondCategoryId', 'thirdCategoryId', 'ext']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1329757, catid=232554, projectId=35802, created=2023-11-21 14:37:51, last-modified=2023-11-27 20:02:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/types)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329757)
 */
export function getDataHubExperimentTypes(params: dataHub.IGetDataHubExperimentTypesParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubExperimentTypesResponse>('/webApi/data-hub/experiment/types', {
    params: pick(params, ['businessType']),
    ...options,
  });
}

/**
 * @desc id=1332125, catid=232554, projectId=35802, created=2023-11-23 21:11:20, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332125)
 */
export function postDataHubExperimentTaskPage(params: dataHub.IPostDataHubExperimentTaskPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubExperimentTaskPageResponse>('/webApi/data-hub/experiment/task/page', {
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329761, catid=232554, projectId=35802, created=2023-11-21 14:37:51, last-modified=2023-11-27 20:02:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329761)
 */
export function postDataHubExperimentUpsert(params: dataHub.IPostDataHubExperimentUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubExperimentUpsertResponse>('/webApi/data-hub/experiment/upsert', {
    data: pick(params, ['experimentDescription', 'experimentName', 'experimentType', 'operator', 'resourceId', 'resourceType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329766, catid=232554, projectId=35802, created=2023-11-21 14:37:51, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/fork)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329766)
 */
export function postDataHubExperimentFork(params: dataHub.IPostDataHubExperimentForkParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubExperimentForkResponse>('/webApi/data-hub/experiment/fork', {
    data: pick(params, ['baseExperimentId', 'experimentDescription', 'experimentName', 'operator']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329770, catid=232554, projectId=35802, created=2023-11-21 14:37:51, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329770)
 */
export function getDataHubExperimentDel(params: dataHub.IGetDataHubExperimentDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubExperimentDelResponse>('/webApi/data-hub/experiment/del', {
    params: pick(params, ['experimentId']),
    ...options,
  });
}

/**
 * @desc id=1329777, catid=232554, projectId=35802, created=2023-11-21 14:37:52, last-modified=2023-11-27 20:02:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/baseinfo)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329777)
 */
export function postDataHubExperimentBaseinfo(params: dataHub.IPostDataHubExperimentBaseinfoParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubExperimentBaseinfoResponse>('/webApi/data-hub/experiment/baseinfo', {
    data: pick(params, ['businessType', 'experimentId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329783, catid=232554, projectId=35802, created=2023-11-21 14:37:52, last-modified=2023-11-27 20:02:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/conf/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329783)
 */
export function getDataHubExperimentConfDetail(params: dataHub.IGetDataHubExperimentConfDetailParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubExperimentConfDetailResponse>('/webApi/data-hub/experiment/conf/detail', {
    params: pick(params, ['experimentId']),
    ...options,
  });
}

/**
 * @desc id=1332122, catid=232554, projectId=35802, created=2023-11-23 21:08:35, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1332122)
 */
export function postDataHubExperimentPage(params: dataHub.IPostDataHubExperimentPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubExperimentPageResponse>('/webApi/data-hub/experiment/page', {
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329784, catid=232554, projectId=35802, created=2023-11-21 14:37:52, last-modified=2023-11-27 20:02:08
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/conf/save)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329784)
 */
export function postDataHubExperimentConfSave(params: dataHub.IPostDataHubExperimentConfSaveParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubExperimentConfSaveResponse>('/webApi/data-hub/experiment/conf/save', {
    data: pick(params, ['edges', 'experimentId', 'nodes', 'operator']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329792, catid=232554, projectId=35802, created=2023-11-21 14:37:52, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/node_conf)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329792)
 */
export function postDataHubExperimentTaskNodeConf(
  params: dataHub.IPostDataHubExperimentTaskNodeConfParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubExperimentTaskNodeConfResponse>('/webApi/data-hub/experiment/task/node_conf', {
    data: pick(params, ['node', 'operator', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329800, catid=232554, projectId=35802, created=2023-11-21 14:37:52, last-modified=2023-11-27 20:02:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/run)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329800)
 */
export function postDataHubExperimentTaskRun(params: dataHub.IPostDataHubExperimentTaskRunParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubExperimentTaskRunResponse>('/webApi/data-hub/experiment/task/run', {
    data: pick(params, ['description', 'experimentId', 'commonContext']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329805, catid=232554, projectId=35802, created=2023-11-21 14:37:52, last-modified=2023-11-27 20:02:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/stop)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329805)
 */
export function postDataHubExperimentTaskStop(params: dataHub.IPostDataHubExperimentTaskStopParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubExperimentTaskStopResponse>('/webApi/data-hub/experiment/task/stop', {
    data: pick(params, ['operator', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329807, catid=232554, projectId=35802, created=2023-11-21 14:37:53, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329807)
 */
export function postDataHubExperimentTaskDetail(
  params: dataHub.IPostDataHubExperimentTaskDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubExperimentTaskDetailResponse>('/webApi/data-hub/experiment/task/detail', {
    data: pick(params, ['operator', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329816, catid=232554, projectId=35802, created=2023-11-21 14:37:53, last-modified=2023-11-27 20:02:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/status)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329816)
 */
export function postDataHubExperimentTaskStatus(
  params: dataHub.IPostDataHubExperimentTaskStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubExperimentTaskStatusResponse>('/webApi/data-hub/experiment/task/status', {
    data: pick(params, ['operator', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329821, catid=232554, projectId=35802, created=2023-11-21 14:37:53, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/recover)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329821)
 */
export function postDataHubExperimentTaskRecover(
  params: dataHub.IPostDataHubExperimentTaskRecoverParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubExperimentTaskRecoverResponse>('/webApi/data-hub/experiment/task/recover', {
    data: pick(params, ['operator', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329828, catid=232554, projectId=35802, created=2023-11-21 14:37:53, last-modified=2023-11-27 20:02:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/rerun)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329828)
 */
export function postDataHubExperimentTaskRerun(params: dataHub.IPostDataHubExperimentTaskRerunParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubExperimentTaskRerunResponse>('/webApi/data-hub/experiment/task/rerun', {
    data: pick(params, ['description', 'operator', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329832, catid=232554, projectId=35802, created=2023-11-21 14:37:53, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/node/result)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329832)
 */
export function postDataHubExperimentTaskNodeResult(
  params: dataHub.IPostDataHubExperimentTaskNodeResultParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubExperimentTaskNodeResultResponse>('/webApi/data-hub/experiment/task/node/result', {
    data: pick(params, ['nodeId', 'operator', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329834, catid=232554, projectId=35802, created=2023-11-21 14:37:53, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/node/mlp_log)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329834)
 */
export function postDataHubExperimentTaskNodeMlpLog(
  params: dataHub.IPostDataHubExperimentTaskNodeMlpLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubExperimentTaskNodeMlpLogResponse>('/webApi/data-hub/experiment/task/node/mlp_log', {
    data: pick(params, ['nodeId', 'operator', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329838, catid=232554, projectId=35802, created=2023-11-21 14:37:53, last-modified=2023-11-27 20:02:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/experiment/task/node/hope_log)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329838)
 */
export function postDataHubExperimentTaskNodeHopeLog(
  params: dataHub.IPostDataHubExperimentTaskNodeHopeLogParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubExperimentTaskNodeHopeLogResponse>('/webApi/data-hub/experiment/task/node/hope_log', {
    data: pick(params, ['nodeId', 'operator', 'taskId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329719, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2023-11-21 14:37:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329719)
 */
export function getDataHubDatasetVersionTrainDel(
  params: dataHub.IGetDataHubDatasetVersionTrainDelParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<dataHub.IGetDataHubDatasetVersionTrainDelResponse>('/webApi/data-hub/dataset-version/train/del', {
    params: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329702, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2023-11-21 14:37:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dashboard/dataset/daily_work)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329702)
 */
export function getDataHubDashboardDatasetDailyWork(
  params: dataHub.IGetDataHubDashboardDatasetDailyWorkParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<dataHub.IGetDataHubDashboardDatasetDailyWorkResponse>('/webApi/data-hub/dashboard/dataset/daily_work', {
    params: pick(params, ['date']),
    ...options,
  });
}

/**
 * @desc id=1329743, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2023-11-21 14:37:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dashboard/dataset/overview)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329743)
 */
export function getDataHubDashboardDatasetOverview(options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubDashboardDatasetOverviewResponse>('/webApi/data-hub/dashboard/dataset/overview', {
    ...options,
  });
}

/**
 * @desc id=1329705, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2023-11-21 14:37:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329705)
 */
export function getDataHubDatasetTrainDetail(params: dataHub.IGetDataHubDatasetTrainDetailParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubDatasetTrainDetailResponse>('/webApi/data-hub/dataset/train/detail', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1329707, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2023-11-21 14:37:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329707)
 */
export function postDataHubDatasetTrainPage(params: dataHub.IPostDataHubDatasetTrainPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubDatasetTrainPageResponse>('/webApi/data-hub/dataset/train/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329701, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2023-11-21 14:37:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/train/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329701)
 */
export function postDataHubDatasetTrainUpsert(params: dataHub.IPostDataHubDatasetTrainUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubDatasetTrainUpsertResponse>('/webApi/data-hub/dataset/train/upsert', {
    data: pick(params, ['name', 'stage', 'type', 'language', 'source', 'resourceType', 'comment', 'baseDatasetId', 'id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329751, catid=231924, projectId=35802, created=2023-11-21 14:37:50, last-modified=2023-11-21 14:37:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dashboard/dataset/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329751)
 */
export function getDataHubDashboardDatasetDetail(options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubDashboardDatasetDetailResponse>('/webApi/data-hub/dashboard/dataset/detail', {
    ...options,
  });
}

/**
 * @desc id=1329728, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2023-11-21 14:37:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/detail)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329728)
 */
export function getDataHubDatasetVersionTrainDetail(
  params: dataHub.IGetDataHubDatasetVersionTrainDetailParameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<dataHub.IGetDataHubDatasetVersionTrainDetailResponse>('/webApi/data-hub/dataset-version/train/detail', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1329735, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2023-11-21 14:37:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329735)
 */
export function postDataHubDatasetVersionTrainPage(
  params: dataHub.IPostDataHubDatasetVersionTrainPageParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubDatasetVersionTrainPageResponse>('/webApi/data-hub/dataset-version/train/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329741, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2023-11-21 14:37:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/types)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329741)
 */
export function getDataHubDatasetVersionTypes(options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubDatasetVersionTypesResponse>('/webApi/data-hub/dataset-version/types', {
    ...options,
  });
}

/**
 * @desc id=1329710, catid=231924, projectId=35802, created=2023-11-21 14:37:49, last-modified=2023-11-21 14:37:49
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/train/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329710)
 */
export function postDataHubDatasetVersionTrainUpsert(
  params: dataHub.IPostDataHubDatasetVersionTrainUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubDatasetVersionTrainUpsertResponse>('/webApi/data-hub/dataset-version/train/upsert', {
    data: pick(params, [
      'storageType',
      'id',
      'datasetId',
      'storageLocation',
      'storageSpace ',
      'quantityValue',
      'quantityUnit',
      'generateTime',
      'bases',
      'extra',
      'comment',
      'type',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329755, catid=231924, projectId=35802, created=2023-11-21 14:37:50, last-modified=2023-11-21 14:37:50
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/categories)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329755)
 */
export function getDataHubCategoryCategories(params: dataHub.IGetDataHubCategoryCategoriesParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubCategoryCategoriesResponse>('/webApi/data-hub/category/categories', {
    params: pick(params, ['dimId', 'parentId']),
    ...options,
  });
}

/**
 * @desc id=1329695, catid=231924, projectId=35802, created=2023-11-21 14:37:48, last-modified=2023-11-21 14:37:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/category/categories_1700200376333)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329695)
 */
export function getDataHubCategoryCategories1700200376333(
  params: dataHub.IGetDataHubCategoryCategories1700200376333Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.get<dataHub.IGetDataHubCategoryCategories1700200376333Response>('/webApi/data-hub/category/categories_1700200376333', {
    params: pick(params, ['dimId', 'parentId']),
    ...options,
  });
}

/**
 * @desc id=1296448, catid=227933, projectId=35802, created=2023-10-13 17:25:25, last-modified=2023-11-21 14:37:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/glance/values)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296448)
 */
export function getDataHubGlanceValues(params: dataHub.IGetDataHubGlanceValuesParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubGlanceValuesResponse>('/webApi/data-hub/glance/values', {
    params: pick(params, ['field', 'sort']),
    ...options,
  });
}

/**
 * @desc id=1296464, catid=227933, projectId=35802, created=2023-10-13 17:25:25, last-modified=2023-11-21 14:37:47
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/glance/fields)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296464)
 */
export function getDataHubGlanceFields(options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubGlanceFieldsResponse>('/webApi/data-hub/glance/fields', {
    ...options,
  });
}

/**
 * @desc id=1296457, catid=227933, projectId=35802, created=2023-10-13 17:25:25, last-modified=2023-11-21 14:37:48
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/glance/search)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1296457)
 */
export function postDataHubGlanceSearch(params: dataHub.IPostDataHubGlanceSearchParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubGlanceSearchResponse>('/webApi/data-hub/glance/search', {
    data: pick(params, ['empty', 'fields', 'from', 'highlightEnable', 'page', 'pageSize', 'query', 'queryType', 'sources', 'versions']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329842, catid=231941, projectId=35802, created=2023-11-21 14:37:53, last-modified=2023-11-21 14:37:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/export)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329842)
 */
export function postDataHubTaskReportExport(params: dataHub.IPostDataHubTaskReportExportParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubTaskReportExportResponse>('/webApi/data-hub/task_report/export', {
    data: pick(params, ['businessId', 'taskTypeId', 'taskConfigId', 'taskTag']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329844, catid=231941, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329844)
 */
export function postDataHubTaskReportPage(params: dataHub.IPostDataHubTaskReportPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubTaskReportPageResponse>('/webApi/data-hub/task_report/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329851, catid=231941, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/line_chart_export)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329851)
 */
export function postDataHubTaskReportLineChartExport(
  params: dataHub.IPostDataHubTaskReportLineChartExportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubTaskReportLineChartExportResponse>('/webApi/data-hub/task_report/line_chart_export', {
    data: pick(params, ['businessId', 'timeUnit', 'taskTypeId', 'taskConfigId', 'taskTagId', 'startTime', 'endTime']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329857, catid=231941, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/line_chart)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329857)
 */
export function postDataHubTaskReportLineChart(params: dataHub.IPostDataHubTaskReportLineChartParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubTaskReportLineChartResponse>('/webApi/data-hub/task_report/line_chart', {
    data: pick(params, ['businessId', 'timeUnit', 'taskTypeId', 'taskConfigId', 'taskTagId', 'startTime', 'endTime']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329860, catid=231941, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task_report/overview)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329860)
 */
export function getDataHubTaskReportOverview(options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubTaskReportOverviewResponse>('/webApi/data-hub/task_report/overview', {
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1329873, catid=231945, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/upload/data)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329873)
 */
export function postDataHubBookUploadData(params: dataHub.IPostDataHubBookUploadDataParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubBookUploadDataResponse>('/webApi/data-hub/book/upload/data', {
    data: pick(params, ['url', 'dataType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329890, catid=231945, projectId=35802, created=2023-11-21 14:37:55, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/download/data)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329890)
 */
export function postDataHubBookDownloadData(params: dataHub.IPostDataHubBookDownloadDataParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubBookDownloadDataResponse>('/webApi/data-hub/book/download/data', {
    params: pick(params, ['llmDataCategoryOneId', 'llmDataCategoryTwoId', 'obtainStatus', 'language', 'dataType']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1329888, catid=231945, projectId=35802, created=2023-11-21 14:37:55, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/delete/data)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329888)
 */
export function getDataHubBookDeleteData(params: dataHub.IGetDataHubBookDeleteDataParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubBookDeleteDataResponse>('/webApi/data-hub/book/delete/data', {
    params: pick(params, ['metadataId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329875, catid=231945, projectId=35802, created=2023-11-21 14:37:55, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/create/data)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329875)
 */
export function postDataHubBookCreateData(params: dataHub.IPostDataHubBookCreateDataParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubBookCreateDataResponse>('/webApi/data-hub/book/create/data', {
    data: pick(params, [
      'metadataId',
      'isbn',
      'title',
      'author',
      'llmDataCategoryOneId',
      'llmDataCategoryTwoId',
      'dataType',
      'obtainStatus',
      'pubdate',
      'press',
      'language',
      'authority',
      'heat',
      'keyword',
      'remark',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329881, catid=231945, projectId=35802, created=2023-11-21 14:37:55, last-modified=2023-11-21 14:37:55
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/query/data/details)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329881)
 */
export function postDataHubBookQueryDataDetails(
  params: dataHub.IPostDataHubBookQueryDataDetailsParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubBookQueryDataDetailsResponse>('/webApi/data-hub/book/query/data/details', {
    params: pick(params, [
      'pageNumber',
      'pageSize',
      'llmDataCategoryOneId',
      'llmDataCategoryTwoId',
      'obtainStatus',
      'language',
      'dataType',
    ]),
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329864, catid=231945, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/book/query/data/count)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329864)
 */
export function postDataHubBookQueryDataCount(params: dataHub.IPostDataHubBookQueryDataCountParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubBookQueryDataCountResponse>('/webApi/data-hub/book/query/data/count', {
    params: pick(params, ['pageNumber', 'pageSize', 'param']),
    data: pick(params, ['pageNumber', 'pageSize', 'param']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329872, catid=231945, projectId=35802, created=2023-11-21 14:37:54, last-modified=2023-11-21 14:37:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/upload)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329872)
 */
export function postDataHubUpload(params: FormData, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubUploadResponse>('/webApi/data-hub/upload', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'multipart/form-data' }),
  });
}

/**
 * @desc id=1329891, catid=231949, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/op/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329891)
 */
export function getDataHubOpDel(params: dataHub.IGetDataHubOpDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubOpDelResponse>('/webApi/data-hub/op/del', {
    params: pick(params, ['id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/x-www-form-urlencoded' }),
  });
}

/**
 * @desc id=1329895, catid=231949, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/op/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329895)
 */
export function postDataHubOpUpsert(params: dataHub.IPostDataHubOpUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubOpUpsertResponse>('/webApi/data-hub/op/upsert', {
    data: pick(params, ['id', 'name', 'type', 'content', 'comment', 'current_version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329904, catid=231949, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/op/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329904)
 */
export function postDataHubOpPage(params: dataHub.IPostDataHubOpPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubOpPageResponse>('/webApi/data-hub/op/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329908, catid=231949, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/op/types)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329908)
 */
export function getDataHubOpTypes(params: dataHub.IGetDataHubOpTypesParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubOpTypesResponse>('/webApi/data-hub/op/types', {
    params: pick(params, ['scopeCode']),
    ...options,
  });
}

/**
 * @desc id=1329913, catid=231953, projectId=35802, created=2023-11-21 14:38:02, last-modified=2023-11-21 14:38:02
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/download)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329913)
 */
export function postDataHubDatasetVersionDownload(
  params: dataHub.IPostDataHubDatasetVersionDownloadParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubDatasetVersionDownloadResponse>('/webApi/data-hub/dataset-version/download', {
    data: pick(params, ['dataset_version_ids']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329922, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/task_types)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329922)
 */
export function getDataHubDatasetTaskTypes(params: dataHub.IGetDataHubDatasetTaskTypesParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubDatasetTaskTypesResponse>('/webApi/data-hub/dataset/task_types', {
    params: pick(params, ['type_name']),
    ...options,
  });
}

/**
 * @desc id=1329925, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/task_configs)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329925)
 */
export function getDataHubDatasetTaskConfigs(params: dataHub.IGetDataHubDatasetTaskConfigsParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubDatasetTaskConfigsResponse>('/webApi/data-hub/dataset/task_configs', {
    params: pick(params, ['task_type_id', 'keyword']),
    ...options,
  });
}

/**
 * @desc id=1329930, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-data/staging)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329930)
 */
export function postDataHubDatasetDataStaging(params: dataHub.IPostDataHubDatasetDataStagingParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubDatasetDataStagingResponse>('/webApi/data-hub/dataset-data/staging', {
    data: pick(params, ['dataset_version_data_id', 'content', 'tagging_result', 'staging_version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329932, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329932)
 */
export function postDataHubDatasetVersionPage(params: dataHub.IPostDataHubDatasetVersionPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubDatasetVersionPageResponse>('/webApi/data-hub/dataset-version/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329935, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-27 16:29:54
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-data/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329935)
 */
export function postDataHubDatasetDataPage(params: dataHub.IPostDataHubDatasetDataPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubDatasetDataPageResponse>('/webApi/data-hub/dataset-data/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329942, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329942)
 */
export function postDataHubDatasetUpsert(params: FormData, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubDatasetUpsertResponse>('/webApi/data-hub/dataset/upsert', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'multipart/form-data' }),
  });
}

/**
 * @desc id=1329944, catid=231953, projectId=35802, created=2023-11-21 14:38:03, last-modified=2023-11-21 14:38:03
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset-version/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329944)
 */
export function postDataHubDatasetVersionUpsert(
  params: dataHub.IPostDataHubDatasetVersionUpsertParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubDatasetVersionUpsertResponse>('/webApi/data-hub/dataset-version/upsert', {
    data: pick(params, ['parent_dataset_version_id', 'comment', 'id']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329946, catid=231953, projectId=35802, created=2023-11-21 14:38:04, last-modified=2023-11-27 16:31:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329946)
 */
export function postDataHubDatasetPage(params: dataHub.IPostDataHubDatasetPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubDatasetPageResponse>('/webApi/data-hub/dataset/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329952, catid=231953, projectId=35802, created=2023-11-21 14:38:04, last-modified=2023-11-21 14:38:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/dataset/simple_page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329952)
 */
export function postDataHubDatasetSimplePage(params: dataHub.IPostDataHubDatasetSimplePageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubDatasetSimplePageResponse>('/webApi/data-hub/dataset/simple_page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329953, catid=231959, projectId=35802, created=2023-11-21 14:38:04, last-modified=2023-11-21 14:38:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/report)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329953)
 */
export function getDataHubTaskReport(params: dataHub.IGetDataHubTaskReportParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubTaskReportResponse>('/webApi/data-hub/task/report', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1329962, catid=231959, projectId=35802, created=2023-11-21 14:38:04, last-modified=2023-11-21 14:38:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/cancel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329962)
 */
export function getDataHubTaskCancel(params: dataHub.IGetDataHubTaskCancelParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubTaskCancelResponse>('/webApi/data-hub/task/cancel', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1329968, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/sync)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329968)
 */
export function getDataHubTaskSync(params: dataHub.IGetDataHubTaskSyncParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubTaskSyncResponse>('/webApi/data-hub/task/sync', {
    params: pick(params, ['id']),
    ...options,
  });
}

/**
 * @desc id=1329970, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329970)
 */
export function postDataHubTaskUpsert(params: dataHub.IPostDataHubTaskUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubTaskUpsertResponse>('/webApi/data-hub/task/upsert', {
    data: pick(params, [
      'project_id',
      'dataset_version_id',
      'selective_inspect_percent',
      'selective_inspect_type',
      'accuracy_threshold',
      'sample_type',
      'range_start',
      'range_end',
      'is_scheduled',
      'exam_question_version_id',
      'exam_question_percent',
      'exam_question_bound',
      'batch_count',
      'batches',
      'tagging_workers',
      'inspect_workers',
      'sync_type',
      'sycn_deadline',
      'isMulti',
      'multiCount',
      'matchQualityInspectionOperators',
      'mismatchQualityInspectionOperators',
      'matchingOp',
      'blindExaminationConfigs',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329975, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/workers)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329975)
 */
export function getDataHubTaskWorkers(params: dataHub.IGetDataHubTaskWorkersParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubTaskWorkersResponse>('/webApi/data-hub/task/workers', {
    params: pick(params, ['worker_name', 'project_id', 'role_id']),
    ...options,
  });
}

/**
 * @desc id=1329980, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-23 14:07:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329980)
 */
export function postDataHubTaskPage(params: dataHub.IPostDataHubTaskPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubTaskPageResponse>('/webApi/data-hub/task/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329984, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/page_1700200339642)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329984)
 */
export function postDataHubTaskPage1700200339642(
  params: dataHub.IPostDataHubTaskPage1700200339642Parameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<dataHub.IPostDataHubTaskPage1700200339642Response>('/webApi/data-hub/task/page_1700200339642', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1329985, catid=231959, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/task/projects)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329985)
 */
export function getDataHubTaskProjects(params: dataHub.IGetDataHubTaskProjectsParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubTaskProjectsResponse>('/webApi/data-hub/task/projects', {
    params: pick(params, ['project_name']),
    ...options,
  });
}

/**
 * @desc id=1329993, catid=231968, projectId=35802, created=2023-11-21 14:38:05, last-modified=2023-11-21 14:38:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/del)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329993)
 */
export function getDataHubAuthDel(params: dataHub.IGetDataHubAuthDelParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubAuthDelResponse>('/webApi/data-hub/auth/del', {
    params: pick(params, ['resource_type', 'resource_id', 'mis']),
    ...options,
  });
}

/**
 * @desc id=1329998, catid=231968, projectId=35802, created=2023-11-21 14:38:06, last-modified=2023-11-21 14:38:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1329998)
 */
export function postDataHubAuthUpsert(params: dataHub.IPostDataHubAuthUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubAuthUpsertResponse>('/webApi/data-hub/auth/upsert', {
    data: pick(params, ['resource_type', 'resource_id', 'mis', 'role']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1330007, catid=231968, projectId=35802, created=2023-11-21 14:38:06, last-modified=2023-11-21 14:38:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/page)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1330007)
 */
export function postDataHubAuthPage(params: dataHub.IPostDataHubAuthPageParameter, options?: FlowHttpRequestOptions) {
  return http.post<dataHub.IPostDataHubAuthPageResponse>('/webApi/data-hub/auth/page', {
    data: pick(params, ['param', 'pageSize', 'pageNumber']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1330012, catid=231968, projectId=35802, created=2023-11-21 14:38:06, last-modified=2023-11-21 14:38:06
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/data-hub/auth/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1330012)
 */
export function getDataHubAuthList(params: dataHub.IGetDataHubAuthListParameter, options?: FlowHttpRequestOptions) {
  return http.get<dataHub.IGetDataHubAuthListResponse>('/webApi/data-hub/auth/list', {
    params: pick(params, ['resource_type']),
    ...options,
  });
}
