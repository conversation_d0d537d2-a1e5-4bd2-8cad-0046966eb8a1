import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1450109, catid=227025, projectId=35802, created=2024-07-30 11:00:04, last-modified=2025-04-28 16:00:53
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelDataSubSetView)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1450109)
 */
export function postModelEvalIUnifiedModelDataSubSetView(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetViewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetViewResponse>(
    '/webApi/modelEvalIUnified/modelDataSubSetView',
    {
      data: pick(params, [
        'evalModelList',
        'filterGroupConfigList',
        'dataSubSetFilterConfig',
        'comparisonGroupConfig',
        'averageScopeGroupConfig',
        'containsBadCase',
        'asyncExcelDownload',
        'queryUuid',
        'heartbeatTtl',
        'withConfidenceInterval',
        'onlyReturnGroupValue',
        'hideDebugData',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1451817, catid=227025, projectId=35802, created=2024-08-04 10:09:03, last-modified=2024-11-26 16:49:42
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelOverviewDiffTable)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1451817)
 */
export function postModelEvalIUnifiedModelOverviewDiffTable(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelOverviewDiffTableParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelOverviewDiffTableResponse>(
    '/webApi/modelEvalIUnified/modelOverviewDiffTable',
    {
      data: pick(params, [
        'evalModelList',
        'baseModel',
        'baseLineModel',
        'requestFilterGroupName',
        'filterGroupConfigList',
        'dataSubSetFilterConfig',
        'containsBadCase',
        'asyncExcelDownload',
        'queryUuid',
        'heartbeatTtl',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1451843, catid=227025, projectId=35802, created=2024-08-05 08:23:44, last-modified=2024-11-26 16:49:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelDataSubSetDetailDiffTable)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1451843)
 */
export function postModelEvalIUnifiedModelDataSubSetDetailDiffTable(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetDetailDiffTableParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetDetailDiffTableResponse>(
    '/webApi/modelEvalIUnified/modelDataSubSetDetailDiffTable',
    {
      data: pick(params, [
        'evalModelList',
        'baseModel',
        'baseLineModel',
        'filterGroupConfigList',
        'dataSubSetFilterConfig',
        'containsBadCase',
        'asyncExcelDownload',
        'queryUuid',
        'heartbeatTtl',
        'withConfidenceInterval',
        'onlyReturnGroupValue',
        'hideDebugData',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1451837, catid=227025, projectId=35802, created=2024-08-05 05:46:41, last-modified=2024-11-26 16:49:20
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelCategoryDetailDiffTable)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1451837)
 */
export function postModelEvalIUnifiedModelCategoryDetailDiffTable(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelCategoryDetailDiffTableParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelCategoryDetailDiffTableResponse>(
    '/webApi/modelEvalIUnified/modelCategoryDetailDiffTable',
    {
      data: pick(params, [
        'evalModelList',
        'baseModel',
        'baseLineModel',
        'filterGroupConfigList',
        'dataSubSetFilterConfig',
        'containsBadCase',
        'asyncExcelDownload',
        'queryUuid',
        'heartbeatTtl',
        'withConfidenceInterval',
        'onlyReturnGroupValue',
        'categoryScanLevel',
        'hideDebugData',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1451849, catid=227025, projectId=35802, created=2024-08-05 08:52:20, last-modified=2024-11-26 16:49:05
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelDetailDiffTable)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1451849)
 */
export function postModelEvalIUnifiedModelDetailDiffTable(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelDetailDiffTableParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelDetailDiffTableResponse>(
    '/webApi/modelEvalIUnified/modelDetailDiffTable',
    {
      data: pick(params, [
        'evalModelList',
        'baseModel',
        'baseLineModel',
        'requestFilterGroupName',
        'filterGroupConfigList',
        'dataSubSetFilterConfig',
        'containsBadCase',
        'asyncExcelDownload',
        'queryUuid',
        'heartbeatTtl',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1451828, catid=227025, projectId=35802, created=2024-08-04 18:11:45, last-modified=2024-11-26 16:49:36
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelCategoryView)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1451828)
 */
export function postModelEvalIUnifiedModelCategoryView(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelCategoryViewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelCategoryViewResponse>('/webApi/modelEvalIUnified/modelCategoryView', {
    data: pick(params, [
      'evalModelList',
      'filterGroupConfigList',
      'dataSubSetFilterConfig',
      'containsBadCase',
      'asyncExcelDownload',
      'queryUuid',
      'heartbeatTtl',
      'withConfidenceInterval',
      'onlyReturnGroupValue',
      'categoryScanLevel',
      'hideDebugData',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1437114, catid=227025, projectId=35802, created=2024-07-15 19:46:12, last-modified=2024-07-24 09:53:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/listModelSelectionFavorites)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1437114)
 */
export function postModelEvalIUnifiedListModelSelectionFavorites(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedListModelSelectionFavoritesParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedListModelSelectionFavoritesResponse>(
    '/webApi/modelEvalIUnified/listModelSelectionFavorites',
    {
      data: params,
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1437106, catid=227025, projectId=35802, created=2024-07-15 19:44:40, last-modified=2024-07-15 19:45:16
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/unfavoriteModelSelection)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1437106)
 */
export function postModelEvalIUnifiedUnfavoriteModelSelection(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedUnfavoriteModelSelectionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedUnfavoriteModelSelectionResponse>(
    '/webApi/modelEvalIUnified/unfavoriteModelSelection',
    {
      data: pick(params, ['name']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1455979, catid=227025, projectId=35802, created=2024-08-15 19:31:37, last-modified=2024-08-23 19:44:00
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/checkModelCategoryView)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1455979)
 */
export function postModelEvalIUnifiedCheckModelCategoryView(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedCheckModelCategoryViewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedCheckModelCategoryViewResponse>(
    '/webApi/modelEvalIUnified/checkModelCategoryView',
    {
      data: pick(params, ['filterGroupConfigList', 'dataSubSetFilterConfig']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1516119, catid=227025, projectId=35802, created=2025-03-20 13:56:58, last-modified=2025-03-25 11:21:46
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelDataSubSetCapabilityView)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516119)
 */
export function postModelEvalIUnifiedModelDataSubSetCapabilityView(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetCapabilityViewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetCapabilityViewResponse>(
    '/webApi/modelEvalIUnified/modelDataSubSetCapabilityView',
    {
      data: pick(params, ['evalModelList', 'dataSubSetId', 'evalCategoryTree']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1517498, catid=227025, projectId=35802, created=2025-04-10 15:59:30, last-modified=2025-04-11 14:33:59
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelDataSubSetCapabilityDownload)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1517498)
 */
export function postModelEvalIUnifiedModelDataSubSetCapabilityDownload(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetCapabilityDownloadParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetCapabilityDownloadResponse>(
    '/webApi/modelEvalIUnified/modelDataSubSetCapabilityDownload',
    {
      data: pick(params, ['evalModelList', 'dataSubSetId', 'runSpecSetId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1516454, catid=227025, projectId=35802, created=2025-03-25 00:19:19, last-modified=2025-03-25 11:16:26
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelDataSubSetListView)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516454)
 */
export function postModelEvalIUnifiedModelDataSubSetListView(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetListViewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetListViewResponse>(
    '/webApi/modelEvalIUnified/modelDataSubSetListView',
    {
      data: pick(params, ['filterGroupConfig', 'evalModelList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1516456, catid=227025, projectId=35802, created=2025-03-25 00:23:59, last-modified=2025-03-25 00:26:11
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/modelDataSubSetCapabilityEvalCategoryTreeView)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1516456)
 */
export function postModelEvalIUnifiedModelDataSubSetCapabilityEvalCategoryTreeView(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetCapabilityEvalCategoryTreeViewParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedModelDataSubSetCapabilityEvalCategoryTreeViewResponse>(
    '/webApi/modelEvalIUnified/modelDataSubSetCapabilityEvalCategoryTreeView',
    {
      data: pick(params, ['evalModelList', 'dataSubSetId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1437093, catid=227025, projectId=35802, created=2024-07-15 19:31:34, last-modified=2024-07-24 09:49:18
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/favoriteModelSelection)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1437093)
 */
export function postModelEvalIUnifiedFavoriteModelSelection(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedFavoriteModelSelectionParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedFavoriteModelSelectionResponse>(
    '/webApi/modelEvalIUnified/favoriteModelSelection',
    {
      data: pick(params, ['name', 'type', 'detailJson']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1430207, catid=227025, projectId=35802, created=2024-07-02 15:07:48, last-modified=2024-09-18 10:22:07
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalIUnified/setMasterForModel)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1430207)
 */
export function postModelEvalIUnifiedSetMasterForModel(
  params: model.modelEvalIUnified.IPostModelEvalIUnifiedSetMasterForModelParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalIUnified.IPostModelEvalIUnifiedSetMasterForModelResponse>('/webApi/modelEvalIUnified/setMasterForModel', {
    data: pick(params, [
      'modelFamily',
      'modelName',
      'statNameList',
      'evalDataSizeList',
      'dataSubSetIdList',
      'dataSubSetList',
      'modelId',
      'runSpecSetName',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
