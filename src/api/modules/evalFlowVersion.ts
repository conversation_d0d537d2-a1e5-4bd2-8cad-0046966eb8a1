import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1426858, catid=246786, projectId=35802, created=2024-06-25 16:39:07, last-modified=2024-07-02 16:52:15
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVersion/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426858)
 */
export function postEvalFlowVersionList(params: model.evalFlowVersion.IPostEvalFlowVersionListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlowVersion.IPostEvalFlowVersionListResponse>('/webApi/evalFlowVersion/list', {
    data: pick(params, ['flowId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426851, catid=246786, projectId=35802, created=2024-06-25 16:33:05, last-modified=2024-06-25 16:34:13
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVersion/setStatus)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426851)
 */
export function postEvalFlowVersionSetStatus(
  params: model.evalFlowVersion.IPostEvalFlowVersionSetStatusParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVersion.IPostEvalFlowVersionSetStatusResponse>('/webApi/evalFlowVersion/setStatus', {
    data: pick(params, ['id', 'status']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426896, catid=246786, projectId=35802, created=2024-06-25 16:50:09, last-modified=2024-07-17 11:17:35
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVersion/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426896)
 */
export function postEvalFlowVersionGet(params: model.evalFlowVersion.IPostEvalFlowVersionGetParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.evalFlowVersion.IPostEvalFlowVersionGetResponse>('/webApi/evalFlowVersion/get', {
    data: pick(params, ['id', 'flowId', 'version']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426915, catid=246786, projectId=35802, created=2024-06-25 16:54:45, last-modified=2024-07-05 16:19:25
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowVersion/create)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426915)
 */
export function postEvalFlowVersionCreate(
  params: model.evalFlowVersion.IPostEvalFlowVersionCreateParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowVersion.IPostEvalFlowVersionCreateResponse>('/webApi/evalFlowVersion/create', {
    data: pick(params, ['flowId', 'vertexList', 'edgeList']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
