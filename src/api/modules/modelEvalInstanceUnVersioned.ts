import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1452951, catid=227025, projectId=35802, created=2024-08-07 13:49:23, last-modified=2025-04-03 11:10:43
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstanceUnVersioned/evalKeyList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1452951)
 */
export function postModelEvalInstanceUnVersionedEvalKeyList(
  params: model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedEvalKeyListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedEvalKeyListResponse>(
    '/webApi/modelEvalInstanceUnVersioned/evalKeyList',
    {
      data: pick(params, ['model', 'compareModelList', 'dataSubSetId', 'runSpecSetId', 'parentCategoryIdList', 'evalDataSize', 'statName']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1452963, catid=227025, projectId=35802, created=2024-08-07 14:11:21, last-modified=2025-04-28 16:03:58
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstanceUnVersioned/compareList)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1452963)
 */
export function postModelEvalInstanceUnVersionedCompareList(
  params: model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedCompareListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedCompareListResponse>(
    '/webApi/modelEvalInstanceUnVersioned/compareList',
    {
      data: pick(params, [
        'model',
        'compareModelList',
        'dataSubSetId',
        'parentCategoryIdList',
        'evalDataSize',
        'statName',
        'scoreFilter',
        'runSpecSetId',
        'offset',
        'limit',
      ]),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1418037, catid=227025, projectId=35802, created=2024-06-06 15:39:30, last-modified=2024-08-07 15:14:27
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstanceUnVersioned/getMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418037)
 */
export function postModelEvalInstanceUnVersionedGetMeta(
  params: model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedGetMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedGetMetaResponse>(
    '/webApi/modelEvalInstanceUnVersioned/getMeta',
    {
      data: pick(params, ['dataSubSetId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1418084, catid=227025, projectId=35802, created=2024-06-06 16:23:06, last-modified=2024-11-05 14:30:22
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstanceUnVersioned/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418084)
 */
export function postModelEvalInstanceUnVersionedList(
  params: model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedListResponse>(
    '/webApi/modelEvalInstanceUnVersioned/list',
    {
      data: pick(params, ['model', 'dataSubSetId', 'evalDataSize', 'statName', 'runSpecSetId']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}

/**
 * @desc id=1418149, catid=227025, projectId=35802, created=2024-06-06 19:41:21, last-modified=2024-11-07 15:05:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalInstanceUnVersioned/export)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1418149)
 */
export function postModelEvalInstanceUnVersionedExport(
  params: model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedExportParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalInstanceUnVersioned.IPostModelEvalInstanceUnVersionedExportResponse>(
    '/webApi/modelEvalInstanceUnVersioned/export',
    {
      data: pick(params, ['exportInfoList']),
      ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
    }
  );
}
