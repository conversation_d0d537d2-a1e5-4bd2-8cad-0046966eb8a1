import { FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1425321, catid=227034, projectId=35802, created=2024-06-20 15:03:06, last-modified=2024-06-20 15:03:46
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalUnVersioned/listStatMeta)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425321)
 */
export function postModelEvalUnVersionedListStatMeta(
  params: model.modelEvalUnVersioned.IPostModelEvalUnVersionedListStatMetaParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalUnVersioned.IPostModelEvalUnVersionedListStatMetaResponse>('/webApi/modelEvalUnVersioned/listStatMeta', {
    data: params,
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
