export default class ModelColumnTableWorker {
  worker: Worker;
  workerMessageMap: { [k: string]: { workerPromise: Promise<any>; workerPromiseResolver: (...args: any[]) => any } };
  listeners: { [k: string]: ((...args: any[]) => any)[] };
  constructor() {
    const { protocol, origin } = location;
    const publicPath = process.env.PUBLIC_PATH;
    const baseUrl = publicPath ? `${protocol}${publicPath}` : origin;
    const workerBlob = new Blob([`importScripts("${baseUrl}/threeInOneWorker.table-worker.js?t=${Date.now()}")`], {
      type: 'application/javascript',
    });
    this.worker = new Worker(URL.createObjectURL(workerBlob));
    this.workerMessageMap = {};
    this.listeners = {};
    this.worker.onmessage = ({ data }) => {
      const { messageId, type } = data;
      if (this.workerMessageMap[messageId]) {
        this.workerMessageMap[messageId].workerPromiseResolver(data);
        delete this.workerMessageMap[messageId];
      }

      this.listeners[type]?.forEach((item) => item(data));
    };
  }

  sendMessage(type: string, data: any) {
    this.worker.postMessage({ type, ...data });
  }

  sendMessageSync(type: string, data?: any, transfer?: boolean): Promise<any> {
    const messageId = `${type}_${Math.random()}`;
    let workerPromiseResolver: any;
    const workerPromise = new Promise((resolve) => (workerPromiseResolver = resolve));
    this.workerMessageMap[messageId] = { workerPromise, workerPromiseResolver };
    if (transfer && data) {
      data.type = type;
      data.messageId = messageId;
      const jsonStr = JSON.stringify(data);
      this.worker.postMessage(jsonStr, [new TextEncoder().encode(jsonStr).buffer]);
    } else {
      this.worker.postMessage({ messageId, type, ...data });
    }

    return workerPromise;
  }

  addListener(type: string, callBack: (...args: any[]) => any) {
    if (!this.listeners[type]) {
      this.listeners[type] = [];
    }

    this.listeners[type].push(callBack);
  }

  removeListener(type: string, callBack: (...args: any[]) => any) {
    const listeners = this.listeners[type];
    if (listeners) {
      const index = listeners.indexOf(callBack);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  terminate() {
    this.worker.terminate();
    this.workerMessageMap = undefined as any;
    this.listeners = undefined as any;
    this.worker.onmessage = undefined as any;
    this.worker = undefined as any;
  }
}
