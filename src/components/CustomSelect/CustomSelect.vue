<template>
  <Dropdown
    ref="dropdown"
    :class="{
      [prefix]: true,
      [`${prefix}-disabled`]: disabled,
      [`${prefix}-selected`]: hasSelected,
    }"
    :disabled="disabled"
    :append-to-container="appendToContainer"
    :get-popup-container="getPopupContainer"
    :visible="visible"
    :popper-class="`${prefix}-popper ${popperClass || ''}`"
    :should-computed-width="false"
    :popper-options="popperOptions"
    use-show
    @input="handleVisibleChange"
  >
    <div :class="`${prefix}-selection`" @mouseenter="hovering = true" @mouseleave="hovering = false">
      <div :class="`${prefix}-rendered`">
        <span v-show="!hasSelected" :class="`${prefix}-placeholder`">
          <slot name="placeholder">
            {{ placeholder }}
          </slot>
        </span>
        <div v-show="hasSelected" :class="`${prefix}-values`">
          <slot name="selected" :selected="filteredSelected">
            {{ valueText
            }}<slot v-if="omittedValues.length" name="maxPlaceholder" :omitted-values="omittedValues" :selected="filteredSelected"
              >，共{{ filteredSelected.length }}项</slot
            >
          </slot>
        </div>
      </div>
      <span :class="`${prefix}-icon`">
        <Icon v-if="showClear" :class="`${prefix}-clear`" name="error-circle" @click.stop="handleClearClick" />
        <slot v-else name="icon">
          <i :class="_icon" />
        </slot>
      </span>
    </div>
    <div slot="dropdown" :class="`${prefix}-panel`">
      <slot name="prefix-input" />
      <div v-if="filterable" :class="`${prefix}-filter`">
        <mtd-input
          v-bind="filterInputProps"
          ref="filter"
          v-model="query"
          :clearable="true"
          autocomplete="off"
          :prefix-icon="iconPrefix('search')"
          @compositionstart="handleComposition"
          @compositionupdate="handleComposition"
          @compositionend="handleComposition"
          @clear="resetQuery"
          @keyup="handleInputChange"
          @keydown.down.prevent="navigateOptions('next')"
          @keydown.up.prevent="navigateOptions('prev')"
          @keydown.enter.prevent="selectOption"
        />
      </div>
      <ul ref="menus" :class="`${dropdownPrefix}-menu ${prefix}-menus`">
        <mtd-option
          v-show="!empty && !query && !loading"
          v-if="canSelectAll"
          :value="SELECT_ALL_VALUE"
          created
          is-select-all
          :indeterminate="!isSelectAll && value && !!value.length"
          >全选</mtd-option
        >
        <div v-show="!loading"><slot /></div>
        <!-- <li v-if="loading" :class="`${dropdownPrefix}-menu-item ${prefix}-loading`">
          <slot name="loading">{{ loadingText }}</slot>
        </li>
        <li v-else-if="!hasMatched" :class="`${dropdownPrefix}-menu-item ${prefix}-no-matched`">
          <slot name="noMatched">{{ noMatchText }}</slot>
        </li>
        <li v-else-if="empty" :class="`${dropdownPrefix}-menu-item ${prefix}-empty`">
          <slot name="empty">{{ noDataText }}</slot>
        </li> -->
      </ul>
    </div>
  </Dropdown>
</template>
<script>
import Dropdown from '@ss/mtd-vue/es/components/dropdown';
import Option from '@ss/mtd-vue/es/components/option';
import { CONFIG_PROVIDER, getIconCls, getPrefixCls } from '@ss/mtd-vue/es/utils/config';
import { isArray, isExist, isObject } from '@ss/mtd-vue/es/utils/type';
import { getValueByPath } from '@ss/mtd-vue/es/utils/util';
import Input from '@ss/mtd-vue/es/components/input';
import Icon from '@ss/mtd-vue/es/components/icon';
import NavigationMixin from '@ss/mtd-vue/es/components/select/navigation-mixin';
import scrollIntoView from '@ss/mtd-vue/es/utils/scroll-into-view';
import { hasProps } from '@ss/mtd-vue/es/utils/vnode';

function getRealValue(value, valueKey) {
  return isObject(value) && valueKey ? getValueByPath(value, valueKey) : value;
}

const SELECT_ALL_VALUE = '__SELECT_ALL__';

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Picker',
  components: {
    Dropdown,
    MtdInput: Input,
    MtdOption: Option,
    Icon,
  },
  mixins: [NavigationMixin],
  provide() {
    return {
      select: this,
    };
  },
  inject: {
    config: {
      from: CONFIG_PROVIDER,
      default: {
        getPrefixCls,
        getIconCls,
      },
    },
  },
  inheritAttrs: false,
  props: {
    value: [Number, String, Boolean, Object, Array],
    valueKey: String,
    disabled: Boolean,
    size: String,
    icon: String,
    placeholder: String,
    clearable: Boolean,
    debounce: {
      type: Number,
      default: 0,
    },
    filterable: Boolean,
    filterMethod: {
      type: Function,
      default(query, value) {
        const parsedQuery = String(query).replace(/(\^|\(|\)|\[|\]|\$|\*|\+|\.|\?|\\|\{|\}|\|)/g, '\\$1');
        return new RegExp(parsedQuery, 'i').test(value);
      },
    },
    filterInputProps: {
      type: Object,
      default() {
        return {};
      },
    },
    remote: Boolean,
    remoteMethod: Function,
    multiple: Boolean,
    multipleLimit: {
      type: Number,
      default: 0,
    },
    appendToContainer: {
      type: Boolean,
      default: true,
    },
    getPopupContainer: Function,
    visible: Boolean,
    placement: String,
    popperOption: Object,
    showSelectAll: Boolean,
    popperClass: String,
    formatter: Function,
    separator: {
      type: String,
      default: '、',
    },
    noMatchText: {
      type: String,
      default() {
        return '暂无搜索结果';
      },
    },
    noDataText: {
      type: String,
      default() {
        return '暂无数据';
      },
    },
    maxCount: {
      type: Number,
      default: 0,
    },
    reserveKeyword: Boolean,
    loading: Boolean,
    loadingText: {
      type: String,
      default() {
        return '搜索中';
      },
    },
    popperOptions: Object,
    filterAutofocus: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      options: [],
      cachedOptions: [],
      query: '',
      showCheckbox: true,
      SELECT_ALL_VALUE,
      isOnComposition: false,
      isMounted: false,
      hovering: false,
      previousQuery: null,
      filteredOptionsCount: 1,
      selected: this.multiple ? [] : undefined,
      isSelectAll: false,
    };
  },
  computed: {
    prefix() {
      return this.config.getPrefixCls('picker');
    },
    iconPrefix() {
      return this.config.getIconCls;
    },
    _icon() {
      return hasProps(this, 'icon') ? this.icon : this.config.getIconCls('down-thick');
    },
    dropdownPrefix() {
      return this.config.getPrefixCls('dropdown');
    },
    canSelectAll() {
      return this.showSelectAll && this.multiple;
    },
    filteredOptions() {
      if (this.canSelectAll) {
        return this.options.filter((option) => !option.isSelectAll);
      }

      return this.options;
    },
    filteredSelected() {
      if (!this.canSelectAll) {
        return this.selected;
      }

      return this.selected.filter((item) => {
        return item.value !== SELECT_ALL_VALUE;
      });
    },
    hasValue() {
      return isExist(this.value) && this.value !== '';
    },
    showClear() {
      return !this.disabled && this.clearable && this.hasValue && (this.visible || this.hovering);
    },
    empty() {
      if (!this.isMounted) {
        return false;
      }

      return !this.filteredOptions.length;
    },
    hasMatched() {
      if (this.filterable && this.query) {
        if (this.remote) {
          return Boolean(this.filteredOptions.length);
        }

        return Boolean(this.filteredOptionsCount);
      }

      return true;
    },
    hasSelected() {
      return this.multiple ? this.selected && this.selected.length : isExist(this.value) && this.value !== '';
    },
    valueText() {
      if (this.multiple) {
        if (isArray(this.selected)) {
          const selected = this.maxCount ? this.filteredSelected.slice(0, this.maxCount) : this.filteredSelected;
          return selected
            .map((item) => {
              if (this.formatter) {
                return this.formatterOption(item);
              }

              return item.currentLabel;
            })
            .join(this.separator);
        }
      } else if (isExist(this.selected)) {
        return this.formatter ? this.formatterOption(this.selected) : this.selected.currentLabel;
      }

      return '';
    },
    omittedValues() {
      if (!this.hasValue || !this.multiple || !this.maxCount) {
        return [];
      }

      const omittedLength = this.value.length - this.maxCount;
      if (omittedLength > 0) {
        return this.value.slice(-omittedLength);
      }

      return [];
    },
  },
  watch: {
    value() {
      if (this.isMounted) {
        this.setSelected();
        this.updatePopper();
      }
    },
    options() {
      if (this.isMounted) {
        this.setSelected();
      }

      if (this.visible) {
        this.updatePopper();
      }
    },
    visible(visible) {
      if (!visible) {
        this.resetHover();
      } else {
        if (this.filterable) {
          if (this.filterAutofocus) {
            this.$nextTick(() => {
              // wait apply visible
              this.$nextTick(this.$refs.filter.focus); // wait apply position to dom
            });
          }

          this.resetQuery();
        }
      }
    },
  },
  created() {
    this.$on('addOption', this.addOption);
    this.$on('removeOption', this.onOptionDestroy);
    this.$on('optionClick', this.handleOptionClick);
    this.debouncedQueryChange = this.handleQueryChange;
  },
  mounted() {
    this.isMounted = true;
    this.setSelected();
  },
  destroyed() {
    this.isMounted = false;
  },
  methods: {
    addOption(option) {
      this.options.push(option);
    },
    formatterOption(option) {
      return this.formatter({
        value: option.value,
        label: option.currentLabel,
        currentLabel: option.currentLabel,
      });
    },
    setSelectedAll() {
      const options = this.filteredOptions.filter((o) => !o._disabled);
      if (this.canSelectAll && this.value && this.value.length && this.value.length >= options.length) {
        const realValues = this.value.map((val) => getRealValue(val, this.realValue));
        this.isSelectAll = options.every((option) => {
          return realValues.indexOf(option.realValue) > -1;
        });
        return;
      }

      this.isSelectAll = false;
    },
    setSelected() {
      this.setSelectedAll();
      const value = this.isSelectAll ? [SELECT_ALL_VALUE, ...this.value] : this.value;
      this.selected = this.multiple ? (value || []).map(this.getOption) : this.getOption(value);
    },
    getOption(value) {
      const realValue = getRealValue(value, this.valueKey);
      const equal = (option) => {
        return realValue === option.realValue;
      };

      let opt = this.options.find(equal);
      if (!opt) {
        // eslint-disable-next-line no-nested-ternary
        const cached = Array.isArray(this.selected) ? this.selected : isExist(this.selected) ? [this.selected] : [];

        opt = cached.find(equal);
      } else {
        opt = {
          value: opt.value,
          realValue: opt.realValue,
          currentLabel: opt.currentLabel,
        };
      }

      return (
        opt || {
          value,
          realValue,
          currentLabel: !isExist(value) ? '' : value.toString(),
          __DEFAULT_OPTION__: true,
        }
      );
    },
    selectOption() {
      if (!this.isOnComposition && this.hoverOption && this.hoverOption.visible) {
        this.handleOptionClick(this.hoverOption);
      }
    },
    handleOptionClick(option) {
      if (this.disabled) {
        return;
      }

      const { value: optionValue, realValue: optionRealValue } = option;

      if (option.isSelectAll) {
        // 取反
        const nextValues = this.isSelectAll
          ? []
          : this.options
              // eslint-disable-next-line @typescript-eslint/no-shadow
              .filter((option) => {
                return !option.isSelectAll && !option._disabled;
              })
              // eslint-disable-next-line @typescript-eslint/no-shadow
              .map((option) => option.value);

        this.$emit('input', nextValues);
        this.$emit('change', nextValues);
      } else if (this.multiple) {
        const copyiedValue = [...(this.value || [])];
        const realValues = copyiedValue.map((val) => {
          return getRealValue(val, this.valueKey);
        });

        let index = -1;
        realValues.some((v, i) => {
          const r = v === optionRealValue;
          if (r) {
            index = i;
          }

          return r;
        });
        if (index > -1) {
          copyiedValue.splice(index, 1);
        } else if (!this.multipleLimit || copyiedValue.length < this.multipleLimit) {
          copyiedValue.push(optionValue);
        }

        if (!this.reserveKeyword) {
          this.query = '';
          // this.handleQueryInput();
        }

        this.$emit('input', copyiedValue);
        this.$emit('change', copyiedValue);
      } else {
        const realValue = getRealValue(this.value, this.valueKey);
        if (realValue !== optionRealValue) {
          this.$emit('input', option.value);
          this.$emit('change', option.value);
        }

        this.$emit('update:visible', false);
      }
    },
    scrollToOption(option) {
      const target = Array.isArray(option) && option[0] ? option[0].$el : option.$el;
      if (this.visible && target) {
        const { menus } = this.$refs;
        scrollIntoView(menus, target);
      }
    },
    onOptionDestroy(option) {
      if (this.hoverOption === option) {
        this.resetHover();
      }

      const index = this.options.indexOf(option);
      if (index > -1) {
        this.options.splice(index, 1);
      }
    },

    handleComposition(e) {
      const { type } = e;
      if (type === 'compositionend') {
        this.isOnComposition = false;
        // 当混合输入前后值不变时，不会触发后续的 input 事件，所以需要再次触发 query
        this.debouncedQueryChange(e.target.value);
      } else {
        this.isOnComposition = true;
      }
    },
    handleInputChange() {
      if (this.visible && this.filterable) {
        this.debouncedQueryChange(this.query);
      }
    },

    handleQueryChange(val) {
      if (this.previousQuery === val || this.isOnComposition) {
        return;
      }

      this.previousQuery = val;
      if (this.remote && typeof this.remoteMethod === 'function') {
        this.remoteMethod(val);
      } else if (typeof this.filterMethod === 'function') {
        const filteredOptions = this.options.filter((item) => {
          if (item.isSelectAll) {
            item.visible = !val;
            return false;
          }

          if (item.created) {
            return false;
          }

          item.visible = val !== '' ? this.filterMethod(val, item.currentLabel) : true;
          return item.visible;
        });
        this.filteredOptionsCount = filteredOptions.length;
      }

      this.updatePopper();
      this.$emit('filter', val);
    },
    updatePopper() {
      this.$nextTick(() => {
        if (this.visible) {
          this.$refs.dropdown && this.$refs.dropdown.updatePopper();
        }
      });
    },

    handleClearClick() {
      if (this.disabled) {
        return;
      }

      this.$emit('clear');
      const nextValue = this.multiple ? [] : '';
      this.$emit('input', nextValue);
      this.$emit('change', nextValue);
    },

    handleVisibleChange(v) {
      this.$emit('update:visible', v);
      v ? this.$emit('focus') : this.$emit('blur');
    },

    resetQuery() {
      this.query = '';
      this.handleQueryChange('');
    },
    focus() {
      this.handleVisibleChange(true);
    },
    blur() {
      this.handleVisibleChange(false);
    },
  },
};
</script>

<style lang="scss" scoped>
.mtd-picker-rendered {
  white-space: normal;
}
.model-manage-layout-block .model-selector .mtd-picker {
  height: max-content;
}
.mtd-dropdown-menu {
  &::v-deep .mtd-option-label-wrapper {
    display: block !important;
    text-overflow: ellipsis;
    max-width: 350px;
    word-break: break-all !important;
    overflow: hidden;
  }
}
</style>
