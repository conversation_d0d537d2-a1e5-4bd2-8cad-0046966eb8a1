import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1519198, catid=227021, projectId=35802, created=2025-04-25 14:11:24, last-modified=2025-04-29 09:58:04
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalData/multipartUploadSign)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1519198)
 */
export function postModelEvalDataMultipartUploadSign(
  params: model.modelEvalData.IPostModelEvalDataMultipartUploadSignParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalData.IPostModelEvalDataMultipartUploadSignResponse>('/webApi/modelEvalData/multipartUploadSign', {
    data: pick(params, ['key', 'contentType', 'type', 'partNumber', 'uploadId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1452610, catid=227021, projectId=35802, created=2024-08-06 16:45:32, last-modified=2024-08-06 16:57:10
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalData/listStatName)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1452610)
 */
export function postModelEvalDataListStatName(
  params: model.modelEvalData.IPostModelEvalDataListStatNameParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalData.IPostModelEvalDataListStatNameResponse>('/webApi/modelEvalData/listStatName', {
    data: pick(params, ['runSpecSetIdFilter', 'dataSubSetIdListFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1519185, catid=227021, projectId=35802, created=2025-04-25 14:08:33, last-modified=2025-04-29 09:58:23
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalData/singleUploadSign)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1519185)
 */
export function postModelEvalDataSingleUploadSign(
  params: model.modelEvalData.IPostModelEvalDataSingleUploadSignParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalData.IPostModelEvalDataSingleUploadSignResponse>('/webApi/modelEvalData/singleUploadSign', {
    data: pick(params, ['fileName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1519201, catid=227021, projectId=35802, created=2025-04-25 14:14:23, last-modified=2025-04-25 14:15:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalData/downloadTempUrl)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1519201)
 */
export function postModelEvalDataDownloadTempUrl(
  params: model.modelEvalData.IPostModelEvalDataDownloadTempUrlParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalData.IPostModelEvalDataDownloadTempUrlResponse>('/webApi/modelEvalData/downloadTempUrl', {
    data: pick(params, ['index', 'expireSeconds']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1519175, catid=227021, projectId=35802, created=2025-04-25 14:02:25, last-modified=2025-04-25 18:50:33
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/modelEvalData/generateIndex)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1519175)
 */
export function postModelEvalDataGenerateIndex(
  params: model.modelEvalData.IPostModelEvalDataGenerateIndexParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.modelEvalData.IPostModelEvalDataGenerateIndexResponse>('/webApi/modelEvalData/generateIndex', {
    data: pick(params, ['fileName']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
