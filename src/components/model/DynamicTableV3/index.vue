<template>
  <mtd-table v-export-table="`DynamicTableV3`" :data="tableData" bordered :row-col-span="objectRowColSpan">
    <column-item v-for="col in colHeader" :key="col.key" :col="col" alignType="center" />
  </mtd-table>
</template>
<script lang="ts">
import { Compo<PERSON>, <PERSON><PERSON>, <PERSON>, Spectra } from '@/decorators';
import { autoMergeRow } from './core';
import ColumnItem from './ColumnItem.vue';

@Component({ components: { ColumnItem } })
@Spectra
export default class DynamicTableV3 extends Weaver() {
  @Prop(Object) PARAMS!: any;

  get dynamicTableList() {
    return this.PARAMS.tableData || {};
  }
  get tableData() {
    return autoMergeRow(this.dynamicTableList);
  }
  get colHeader() {
    return this.dynamicTableList?.colHeader;
  }
  objectRowColSpan({ row, column }) {
    // row: 行数据信息
    // column: 列数据信息
    if (typeof this.PARAMS.objectRowColSpan === 'function') {
      const boundMethod = this.PARAMS.objectRowColSpan({ row, column });
      const htmlContent = boundMethod;
      return htmlContent;
    }

    const cellData = row[column.label];
    if (cellData && cellData.rowSpan > -1) {
      return {
        rowspan: cellData.rowSpan,
        colspan: cellData.colSpan,
      };
    }

    return {
      rowspan: 1,
      colspan: 1,
    };
  }
}
</script>
