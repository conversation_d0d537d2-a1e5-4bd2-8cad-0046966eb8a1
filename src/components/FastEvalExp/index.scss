::v-deep {
  .mtd-tree-node-content:hover {
    background-color: rgba(0, 0, 0, 0) !important;
  }
  .mtd-tree-node-selected > .mtd-tree-node-content {
    color: #666666 !important;
  }
}
.evalset-list-item {
  padding: 4px 10px;
  border-radius: 4px;
  margin-bottom: 2px;
}
.modal-title-fast-exp {
  .title-text {
    font-size: 20px;
    font-weight: 600;
  }
  .mtd-icon-btn-secondary:hover {
    color: #ef9700;
  }
}
.fast-eval-exp-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  .mtd-steps {
    width: 100%;
    ::v-deep .mtd-step-head-number {
      border-width: 2px;
      font-weight: 400;
      font-size: 16px;
    }
    ::v-deep .mtd-step-process > .mtd-step-head-number {
      border-color: #ef9700;
      background-color: #ef9700;
    }
    ::v-deep .mtd-step-finish > .mtd-step-head-number {
      border-color: #ef9700;
      color: #ef9700;
    }
    ::v-deep .mtd-step-finish > .mtd-step-head-line {
      background-color: #ef9700;
    }
    ::v-deep .mtd-step-process > .mtd-step-head-line {
      background-color: #ef9700;
    }
    ::v-deep .mtd-step-process > .mtd-step-main-title {
      color: #ef9700;
    }
  }
  .step-form-container {
    height: calc(100% - 86px);
    width: 100%;
    padding-top: 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
  }
}
.stepBtn {
  background-color: #ef9700;
  color: #fff;
  &:hover {
    border-color: #ffdd00;
  }
}
.model-select-container {
  width: 80vw;
  ::v-deep .mtd-form {
    .form-item-evalBenchmarks {
      display: none;
    }
  }
  ::v-deep .mtd-modal-wrapper {
    position: unset;
  }
  ::v-deep .mtd-modal {
    width: 60vw !important;
    box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.04), 0 12px 16px 16px rgba(0, 0, 0, 0.04), 0 10px 16px 0 rgba(0, 0, 0, 0.04);
    .mtd-modal-close {
      display: none;
    }
    .title-wrapper {
      padding-right: 0;
    }
    .mtd-modal-footer {
      display: none;
    }
  }
  &.fullscreen-model-select {
    width: 80vw;
    ::v-deep > .mtd-modal-wrapper {
      > .mtd-modal {
        width: 80vw !important;
        height: calc(100vh - 240px) !important;
        .mtd-modal-content-wrapper {
          height: calc(100% - 80px);
        }
        .mtd-modal-content {
          height: 100%;
          .content {
            height: 100%;
            .mtd-loading-nested {
              height: calc(100% - 250px);
              .mtd-loading-container {
                height: 100%;
                .model-container {
                  height: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
}
.shadow-step-container {
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.04), 0 12px 16px 16px rgba(0, 0, 0, 0.04), 0 10px 16px 0 rgba(0, 0, 0, 0.04);
  border-radius: 10px;
  width: 80vw;
  height: calc(100% - 30px);
  padding: 20px;
  > .title {
    padding-bottom: 20px;
    font-size: 22px;
    font-weight: 600;
  }
}
.fullscreen-step-form-container {
  // width: 80vw !important;
  height: calc(100vh - 320px) !important;
  overflow: auto;
  .shadow-step-container {
    width: 80vw !important;
    height: 100% !important;
  }
}
.step2 {
  height: 100%;
  overflow: scroll;
  .line {
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #e4e4e4;
    margin-bottom: 20px;
  }
  .eval-set-select {
    width: 100%;
    margin: 10px 0 40px;
    &::-webkit-scrollbar {
      display: none;
    }
    ::v-deep .set-select-container {
      .mtd-btn {
        display: none;
      }
    }
    ::v-deep .run-spec-grid {
      max-height: calc(100% - 63px);
      overflow-y: scroll;
      grid-template-columns: repeat(2, 1fr);
      .mtd-card {
        pointer-events: none;
        button {
          pointer-events: auto;
        }
        .opt-btns {
          button {
            display: none;
          }
          .delete-btn {
            display: inline-block;
          }
        }
        .mtd-dropdown {
          pointer-events: auto;
        }
        .tag-container {
          .mtd-tag-green {
            display: none;
          }
        }
      }
    }
  }
}
.step3 {
  height: 100%;
  display: flex;
  flex-direction: column;
  .source-cfg-container {
    .sub-title {
      margin-left: 20px;
    }
  }
  .mtd-tree {
    flex-grow: 1;
    overflow-y: scroll;
    ::v-deep .node-lv-0 {
      > .mtd-tree-node-content {
        .mtd-tree-node-content-wrapper {
          font-size: 18px;
          font-weight: 600;
        }
      }
    }
  }
}
.step4 {
  .canvas-cfg-container {
    height: calc(100% - 66px);
    overflow-y: scroll;
    padding: 6px;
    .mtd-tree {
      ::v-deep .node-lv-0 {
        > .mtd-tree-node-content {
          .title {
            font-size: 16px;
            font-weight: 600;
            margin-right: 10px;
          }
        }
        .canvas-name {
          display: flex;
          .mtd-input-wrapper {
            margin-left: 4px;
            flex-grow: 1;
          }
        }
      }
      ::v-deep .node-lv-1 {
        > .mtd-tree-node-content {
          .title {
            font-size: 16px;
            font-weight: 600;
          }
        }
      }
    }
  }
}
.step5 {
  .mtd-loading {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    ::v-deep .mtd-loading-circle {
      color: #ef9700;
    }
  }
}
.sub-title {
  padding-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
}
.create-exp-tip {
  .title {
    margin-top: 20px;
    display: flex;
    align-items: center;
    font-size: 42px;
    justify-content: center;
  }
  .tip {
    margin: 20px 0;
    text-align: center;
  }
}
.demo-popover-content {
  .popover-0 {
    .p-item {
      font-weight: 500;
      font-size: 16px;
    }
    .p-child {
      margin-left: 12px;
      padding-left: 6px;
      font-size: 14px;
      font-weight: 400;
      border-left: 1px solid #ccc;
    }
  }
  .popover-2 {
    .mtd-form {
      .mtd-form-item {
        margin-bottom: 0;
      }
    }
  }
}

.step4-edit {
  display: flex;
  width: 100%;
  height: 100%;
}

.step4-edit1 {
  flex: 2;
  margin-right: 10px;
}

.step4-edit2 {
  flex: 1;
  overflow-y: scroll;
}

.editParameter {
  > .title {
    font-size: 22px;
    font-weight: 600;
  }
  position: relative;
  h4 {
    transform: translateY(-4px);
    background: #fff;
    color: rgb(239, 151, 0);
    margin: 0;
    line-height: 50px;
  }
}
