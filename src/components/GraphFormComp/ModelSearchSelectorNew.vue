<template>
  <div>
    <div class="flex mr-[57px]" v-if="isEdit">
      <mtd-select
        v-model="modelValue"
        class="grow"
        :loading="loading"
        :filterable="true"
        :remote="true"
        :debounce="500"
        clearable
        :remote-method="queryData"
        placeholder="请输入模型名，搜索模型"
        @change="fetchData"
        :default-active-first-option="false"
      >
        <div class="text-sm text-gray-500 p-2 pl-4" v-if="options.length > 50">结果超过 50 条，请输入更精确的关键词</div>
        <mtd-option v-for="item in options" :key="item.metaId" :label="item.modelName" :value="item.metaId" />
        <template #loading>
          <mtd-dropdown-menu-item style="line-height: inherit; padding: 5px 0">
            <mtd-loading loading message="" type="line-scale"></mtd-loading>
          </mtd-dropdown-menu-item>
        </template>
      </mtd-select>
      <div>
        <mtd-popover trigger="hover" placement="top">
          <mtd-button icon="mtdicon mtdicon-question-circle-o" type="text" @click="handleAddModelMeta">注册新模型</mtd-button>
          <div slot="content" class="demo-popover-content">如未找到合适的模型名，可点此新增模型名。</div>
        </mtd-popover>
      </div>
    </div>

    <div v-if="!isEdit || (!model && statusModelMeta)">
      {{ modelName }}
      <mtd-tag v-if="!model && statusModelMeta" theme="red">临时输出模型</mtd-tag>
      <mtd-tag v-else-if="model">固定输出模型</mtd-tag>
      <mtd-tag v-else theme="red">临时输出模型(执行后生成)</mtd-tag>
      <mtd-popover trigger="hover" placement="top" v-if="isEdit && !model && statusModelMeta">
        <mtd-button icon="mtdicon mtdicon-question-circle-o" type="text" @click="syncStatusModelMeta">固定临时模型</mtd-button>
        <div slot="content" class="demo-popover-content">
          将该临时输出Meta模型固定为该节点的输出Meta模型。重新执行精调不会再生成新的临时Meta模型。
        </div>
      </mtd-popover>
    </div>

    <mtd-button type="text-primary" @click="handleModelMetaJump" v-if="model || statusModelMeta"
      >全局推理参数配置
      <mtd-tag v-if="foundedMeta?.pluginBindList?.length" theme="green">已配置</mtd-tag>
      <mtd-tag v-else theme="red">未配置</mtd-tag>
    </mtd-button>
    <mtd-button type="text-primary" @click="handleModelMetaJump" v-if="model || statusModelMeta"
      >prompt前缀：
      <mtd-tag v-if="foundedMeta?.promptPrefix" theme="green">{{ foundedMeta?.promptPrefix }}</mtd-tag>
      <mtd-tag v-else theme="red">未配置</mtd-tag>
    </mtd-button>
    <mtd-button type="text-primary" @click="handleModelMetaJump" v-if="model || statusModelMeta"
      >prompt后缀：
      <mtd-tag v-if="foundedMeta?.promptSuffix" theme="green">{{ foundedMeta?.promptSuffix }}</mtd-tag>
      <mtd-tag v-else theme="red">未配置</mtd-tag>
    </mtd-button>
    <mtd-button type="text-primary" @click="handleModelMetaJump" v-if="model || statusModelMeta"
      >评测数据规模：
      <mtd-tag v-if="foundedMeta?.autoEvalDataSize" theme="green">{{ getMetaLabel(foundedMeta?.autoEvalDataSize) }}</mtd-tag>
      <mtd-tag v-else theme="red">未配置</mtd-tag>
    </mtd-button>
  </div>
</template>

<script lang="ts">
import {
  getModelExperimentTaskGetOutputModelMetaId,
  getModelManagerGetModelMeta,
  getModelManagerListModelNames,
} from '@/api/modules/model';
import { Component, Prop, Spectra, Watch, Weaver } from '@/decorators';
import { IGetModelManagerListModelNamesResponse } from '@/model/modules/model';
import modelManage from '@/store/modules/modelManage';

@Component
@Spectra
export default class ModelSearchSelectorNew extends Weaver(modelManage) {
  @Prop({ type: [String, Number] }) model?: string | number;
  @Prop({ type: Boolean, required: true }) isEdit!: boolean;
  @Prop()
  context!: any;

  private modelName = '';
  private foundedMeta: any = {};
  private loading = false;
  private options: IGetModelManagerListModelNamesResponse['data'] = [];
  statusModelMeta: number | undefined = 0;

  get modelValue(): string | number | undefined {
    return this.model;
  }

  set modelValue(val: string | number | undefined) {
    this.$emit('change', val);
  }

  async queryData(modelName: string): Promise<void> {
    if (!modelName) return;
    this.loading = true;
    const res = await getModelManagerListModelNames({ modelName });
    this.options = res.data.slice(0, 100);
    this.loading = false;
  }

  handleAddModelMeta(): void {
    window.open(
      this.$router.resolve({
        name: 'model-manage-model-register',
      }).href
    );
  }

  async getGeneratedModelMeta() {
    const nodeInfo = (this.taskStatLists || this.experimentConfigDetail?.status || this.taskDetail?.status)?.nodeStatus?.find(
      (x) => x.nodeId === Number(this.context.id)
    );

    if (nodeInfo?.taskDetailId) {
      const res = await getModelExperimentTaskGetOutputModelMetaId({
        taskDetailId: String(nodeInfo.taskDetailId),
      });
      return Number(res.data?.logUrl);
    }
    return undefined;
  }

  async fetchData(e = this.model): Promise<void> {
    if (e) {
      const res = await getModelManagerGetModelMeta({ modelMetaId: e as string });
      this.modelName = res.data.modelName || '';
      if (this.model)
        this.options = [
          {
            metaId: this.model as number,
            modelName: res.data.modelName || '',
          },
        ];
      this.foundedMeta = res.data;
    } else {
      this.modelValue = undefined;
      this.options = [];
    }
  }

  handleModelMetaJump(): void {
    window.open(
      this.$router.resolve({
        name: 'model-manage-model-detail',
        params: {
          metaId: String(this.modelValue || this.statusModelMeta),
        },
      }).href,
      '_blank',
      'noopener'
    );
  }

  @Watch('isEdit', { immediate: true })
  async onIsEditChange(val: boolean): Promise<void> {
    if (this.model === '') {
      this.modelName = '';
      return;
    }
    this.fetchData();
  }

  async mounted() {
    this.statusModelMeta = await this.getGeneratedModelMeta();
    this.fetchData(this.statusModelMeta || this.model);
  }

  async syncStatusModelMeta() {
    this.options = [
      {
        metaId: this.statusModelMeta!,
        modelName: this.modelName,
      },
    ];
    this.modelValue = this.statusModelMeta;
  }

  getMetaLabel(meta) {
    return this.modelEvalListEvalDataSizeMeta.evalDataSizeMetaList.find((x) => x.name === meta)?.label || meta;
  }
}
</script>
