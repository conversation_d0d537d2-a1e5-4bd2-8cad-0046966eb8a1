import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1425979, catid=246786, projectId=35802, created=2024-06-24 10:32:00, last-modified=2024-07-02 17:12:12
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJobConsumer/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1425979)
 */
export function postEvalFlowJobConsumerList(
  params: model.evalFlowJobConsumer.IPostEvalFlowJobConsumerListParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJobConsumer.IPostEvalFlowJobConsumerListResponse>('/webApi/evalFlowJobConsumer/list', {
    data: pick(params, ['jobId', 'clientConsumerId', 'vertexIdFilter', 'servingTypeFilter', 'clusterNameFilter', 'statusFilter']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1426001, catid=246786, projectId=35802, created=2024-06-24 10:53:49, last-modified=2024-07-02 11:28:41
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/evalFlowJobConsumer/heartbeat)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1426001)
 */
export function postEvalFlowJobConsumerHeartbeat(
  params: model.evalFlowJobConsumer.IPostEvalFlowJobConsumerHeartbeatParameter,
  options?: FlowHttpRequestOptions
) {
  return http.post<model.evalFlowJobConsumer.IPostEvalFlowJobConsumerHeartbeatResponse>('/webApi/evalFlowJobConsumer/heartbeat', {
    data: pick(params, ['clientConsumerId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
