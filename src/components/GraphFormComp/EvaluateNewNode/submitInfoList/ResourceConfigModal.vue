<template>
  <mtd-modal v-model="modal" class="config-modal" width="1500px" :closable="false">
    <div slot="title" class="flex justify-between">
      <div>
        <span style="font-size: 18px; font-weight: bold"> {{ runspecSetContext.flowName }}</span
        >&nbsp; <span style="color: #3888ff"> {{ `数据集 ${runspecSetContext.benchmarkList?.length}` }} </span>&nbsp;
        <span style="color: grey">
          {{ `执行单元 ${runspecSetContext.runSpecFlowConfigList?.length}` }}
        </span>
      </div>
      <div v-if="isEdit">
        <mtd-button style="width: 80px" @click="cancel">取消</mtd-button>&nbsp;&nbsp;&nbsp;<mtd-button
          type="primary"
          style="width: 80px"
          @click="confirm"
          >确定</mtd-button
        >
      </div>
      <div v-else><mtd-button @click="confirm">关闭</mtd-button></div>
    </div>
    <mtd-tag v-if="coveredCount" type="pure" theme="red" style="background-color: #ff000080">非默认配置:{{ coveredCount }}</mtd-tag>
    <mtd-button type="text-primary" v-if="isEdit && coveredCount" @click.stop="resetResourceConfig">全部配置恢复默认</mtd-button>
    <resource-config-list
      ref="resourceConfigList"
      :isEdit="isEdit"
      :context="runspecSetContext"
      :nodeContext="context"
      @change="setResourceConfigList"
    />
  </mtd-modal>
</template>
<script lang="ts">
import { Component, Prop, PropSync, Ref, Spectra, Watch, Weaver } from '@/decorators';
import { cloneDeep } from 'lodash';
import ResourceConfigList from './ResourceConfigList.vue';

@Component({
  components: {
    ResourceConfigList,
  },
})
@Spectra
export default class ResourceConfigModal extends Weaver() {
  @Prop()
  context!: any;
  @Prop()
  isEdit!: boolean;
  @Prop()
  initConfig;
  @Prop()
  saveRunspecSetConfig;
  @PropSync('openModal')
  modal!: boolean;

  @Ref()
  resourceConfigList!: ResourceConfigList;

  coveredCount = 0;
  runspecSetContext: any = {};

  @Watch('initConfig', { deep: true, immediate: true })
  initConfigChanged() {
    this.runspecSetContext = cloneDeep(this.initConfig);
    this.resourceConfigList?.initData();
  }

  setResourceConfigList(val, defaultVal) {
    this.updateCoveredCount(val);
    this.$set(this.runspecSetContext, 'resourceConfigList', val);
    this.$set(this.runspecSetContext, 'defaultResourceConfigList', defaultVal);
    this.$emit('init', val, defaultVal);
  }

  resetResourceConfig() {
    this.$set(
      this.runspecSetContext,
      'resourceConfigList',
      this.runspecSetContext.resourceConfigList.map(
        ({ queue, workerCpuCore, workerCpuMem, workerGpuCore, workerInstanceCount, ...ext }) => ({ ...ext })
      )
    );
    this.resourceConfigList.initData();
  }

  updateCoveredCount(newResource) {
    const checkParamList = ['queue', 'workerCpuCore', 'workerCpuMem', 'workerGpuCore', 'workerInstanceCount'];
    this.coveredCount = newResource.reduce((prevCount, curResource) => {
      let curCount = prevCount;
      checkParamList.forEach((key) => curResource[key] && curCount++);
      return curCount;
    }, 0);
  }

  cancel() {
    this.modal = false;
    this.initConfigChanged();
    this.resourceConfigList.initData();
  }

  confirm() {
    this.modal = false;
    this.saveRunspecSetConfig(this.runspecSetContext);
  }
}
</script>
<style lang="less" scoped></style>
