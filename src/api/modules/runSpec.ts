import { pick, FlowHttpRequestOptions, mixHeadersOptions } from '@snfe/flow-yapi-helpers';
import * as model from '@/model';
import http from '../http';

/**
 * @desc id=1373271, catid=227021, projectId=35802, created=2024-02-27 14:56:30, last-modified=2024-03-07 10:38:14
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpec/list)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373271)
 */
export function postRunSpecList(params: model.runSpec.IPostRunSpecListParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.runSpec.IPostRunSpecListResponse>('/webApi/runSpec/list', {
    data: pick(params, [
      'metaVersionId',
      'keyword',
      'statTypeFilter',
      'publicStatusFilter',
      'statusFilter',
      'categoryIdsFilter',
      'subSetTags',
      'dataSetId',
      'dataSubSetId',
    ]),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1373303, catid=227021, projectId=35802, created=2024-02-27 15:07:06, last-modified=2024-02-27 15:08:35
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpec/upsert)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373303)
 */
export function postRunSpecUpsert(params: model.runSpec.IPostRunSpecUpsertParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.runSpec.IPostRunSpecUpsertResponse>('/webApi/runSpec/upsert', {
    data: pick(params, ['id', 'dataSubSetId', 'description', 'extraParam']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}

/**
 * @desc id=1373292, catid=227021, projectId=35802, created=2024-02-27 15:04:33, last-modified=2024-02-28 14:57:09
 * @see [Yapi接口mock地址](http://yapi.sankuai.com/mock/35802/webApi/runSpec/get)
 * @see [Yapi接口地址](http://yapi.sankuai.com/project/35802/interface/api/1373292)
 */
export function postRunSpecGet(params: model.runSpec.IPostRunSpecGetParameter, options?: FlowHttpRequestOptions) {
  return http.post<model.runSpec.IPostRunSpecGetResponse>('/webApi/runSpec/get', {
    data: pick(params, ['id', 'dataSubSetId']),
    ...mixHeadersOptions(options, { 'Content-Type': 'application/json' }),
  });
}
