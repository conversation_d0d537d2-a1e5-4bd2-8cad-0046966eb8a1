<template>
  <div style="padding: 10px 30px 10px 10px">
    <div v-if="evalModelList?.length > modelMax">
      模型数量 > {{ modelMax }} ，或者查询无数据时，无法显示折线图。请进一步筛选模型或确认查询条件。
    </div>
    <div v-if="filterGroup.selectGroup.firstIndex === -1">请在上面表格里点击展开想查看的筛选组</div>
    <div v-else-if="evalModelList?.length <= modelMax">
      <div class="showFilter">
        <mtd-select v-if="showFilter" v-model="columnSortOrderData.rowKey" style="width: 600px">
          <mtd-option v-for="(item, index) in columnsFilter" :key="index" :value="item.key" :label="item.label" />
        </mtd-select>
        <mtd-select v-if="showFilter" v-model="columnSortOrderData.order" style="width: 100px; margin-left: 20px">
          <mtd-option v-for="item in columnsSort" :key="item.id" :value="item.order" :label="item.label" />
        </mtd-select>
        <mtd-button class="btn-demo-item" ghost type="primary">{{ filterGroup.selectGroup.title }}</mtd-button>
        <div class="filterOptionContent-name" v-if="stateOption?.length">选择展示内容</div>
        <mtd-select v-if="stateOption?.length" v-model="conditionValue" style="width: 180px" :append-to-container="false">
          <mtd-option v-for="item in stateOption" :key="item.value" :label="item.value" :value="item.key" />
        </mtd-select>
      </div>
      <v-chart v-if="showChart" ref="chart" :option="option" class="chart" autoresize style="width: 100%; height: 900px" />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Ref, Spectra, Watch, Weaver } from '@/decorators';
import ModelEvalIUnified from '@/store/modules/modelEvalIUnified';
import type { ColumnLineChartParams } from '@/types/modelCommonComponent';

import { LineChart } from 'echarts/charts';
import {
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { cloneDeep } from 'lodash';
import VChart from 'vue-echarts';

echarts.use([
  LineChart,
  GridComponent,
  UniversalTransition,
  CanvasRenderer,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
  DataZoomComponent,
]);
@Component({
  components: {
    VChart,
  },
})
@Spectra
export default class ThreeInOneLineChart extends Weaver(ModelEvalIUnified) {
  @Ref('chart')
  $chart: any;

  @Prop()
  PARAMS!: ColumnLineChartParams;
  @Prop({ default: () => ({ rowKey: null, order: '' }) })
  columnSortOrderData!: {
    rowKey: null | string;
    order: 'ascending' | 'descending' | '';
  };
  @Prop()
  title!: string;
  @Prop({
    default: () => ({
      selectGroup: {
        firstIndex: -1,
        secondIndex: 0,
        title: '',
      },
      type: 'OFF',
    }),
  })
  filterGroup!: any;
  option: any = {};
  columnsFilter: any[] = [];
  columnsSort = [
    { id: 1, label: '降序', order: 'descending' },
    { id: 2, label: '默认', order: '' },
    { id: 3, label: '升序', order: 'ascending' },
  ];
  get lineCount() {
    return this.PARAMS.lineCount || Infinity;
  }

  get tooltip() {
    return this.PARAMS.tooltip !== false;
  }

  get showFilter() {
    return this.PARAMS.isFilter !== false && this.PARAMS.modelDataSubSetTableChart !== false;
  }

  get modelMax() {
    return this.PARAMS.modelMax || Infinity;
  }

  get evalModelList() {
    return this.PARAMS.tableDataDataSubSet?.evalModelList || [];
  }

  @Watch('conditionValue')
  handleConditionValue(value) {
    if (this.series?.length) {
      if (value === 'CLEAN&DIRTY') {
        this.filteredSeries = cloneDeep(this.series);
        this.filterValueList = cloneDeep(this.valueList);
        this.setOption(this.filteredSeries, this.filterValueList);
      } else {
        this.filteredSeries = cloneDeep(
          this.series.filter((item) => {
            return item.data.length > 0 && item.data[0].commonQuery.dataStatus === value;
          })
        );
        this.setOption(this.filteredSeries, this.filterValueList);
      }
    }
  }
  @Watch('columnSortOrderData', { deep: true })
  @Watch('filterGroup', { deep: true })
  @Watch('PARAMS.tableDataDataSubSet', { immediate: true, deep: false })
  handleTableDataChange() {
    const firstIndex = this.filterGroup.selectGroup.firstIndex;
    if (firstIndex > -1 && this.PARAMS?.tableDataDataSubSet?.evalModelList.length <= 10) {
      this.showChart = true;
      this.$nextTick(() => {
        this.handleTable();
      });
    }
  }
  stateOption1 = [
    { value: '仅clean', key: 'CLEAN' },
    { value: '仅dirty', key: 'DIRTY' },
    { value: 'clean和dirty同时显示', key: 'CLEAN&DIRTY' },
  ];
  conditionValue = '';
  stateOption: any = [];
  series: any = [];
  filteredSeries: any = [];
  sortSeries: any = [];
  valueList: any = [];
  filterValueList: any = [];
  isMean = false;
  showChart = false;
  getRunSpecSetId(res) {
    if (res?.length) {
      const runSpecSetId = res[0].runSpecSetId;
      return runSpecSetId;
    } else {
      return '';
    }
  }

  handleTable() {
    this.stateOption = [];
    this.series = [];
    const firstIndex = this.filterGroup.selectGroup.firstIndex;
    const secondIndex = this.filterGroup.selectGroup.secondIndex;
    const tableData = this.PARAMS?.tableDataDataSubSet;
    if (!tableData || tableData.evalModelList.length === 0) return;
    const evalModelList = tableData.evalModelList;
    if (evalModelList[0]?.filterGroupResultList) {
      this.valueList = evalModelList[0].filterGroupResultList[firstIndex].groupList[secondIndex].valueList;
    } else {
      this.valueList = evalModelList[0].dataSubSetFilterResult.valueList;
    }
    this.filterValueList = cloneDeep(this.valueList);
    this.columnsFilter = [];
    this.conditionValue = '';
    evalModelList.map((model, dataIndex) => {
      // let label = `${model.llmModel.modelInfo?.modelMeta.modelName}/${model.llmModel.modelInfo?.modelCheckpoint}/${model.llmModel.label}`;
      let label = `${model.llmModel.label}`;
      this.columnsFilter.push({
        label,
        key: `row_${dataIndex}`,
      });
      let valueList: any = [];
      let groupList: any = {};
      if (model.filterGroupResultList) {
        valueList = model.filterGroupResultList[firstIndex]?.groupList[secondIndex].valueList;
        groupList = model.filterGroupResultList[firstIndex]?.groupList[secondIndex];

        this.isMean = false;
      } else {
        valueList = model.dataSubSetFilterResult.valueList;
        this.isMean = true;
      }
      let obj: any = {
        name: label,
        type: 'line',
        data: [],
      };
      let obj1: any = {
        name: label,
        type: 'line',
        data: [],
      };
      valueList.forEach((item) => {
        if (item?.unitList?.length === 2) {
          this.stateOption = this.stateOption1;
          this.conditionValue = 'CLEAN&DIRTY';
        }

        item.unitList.forEach((key, index) => {
          if (index === 0) {
            const row = {
              value: key?.value || 0,
              commonQuery: {
                label: model.label,
                modelCheckpoint: model.llmModel?.modelInfo?.modelCheckpoint,
                modelName: model.llmModel.modelInfo?.modelMeta.modelName,
                evalDataSize: key.result.evalDataSize || this.PARAMS.evalDataSizeLineChart,
                dataSubSetId: item.dataSubSetId,
                dataStatus: key.dataSetOpennessStatus,
                family: model.family,
                name: model.name,
                modelId: model.modelId,
                master: model.master || false,
                masterModelName: groupList?.masterModelName || '',
                runSpecSetId: this.getRunSpecSetId(key?.result?.calculateUnitList || []),
              },
            };
            obj.data.push(row);
          }
          if (index === 1) {
            const row = {
              value: key?.value || 0,
              commonQuery: {
                label: model.label,
                modelCheckpoint: model.llmModel.modelInfo?.modelCheckpoint,
                modelName: model.llmModel.modelInfo?.modelMeta.modelName,
                evalDataSize: key.result.evalDataSize || this.PARAMS.evalDataSizeLineChart,
                dataSubSetId: item.dataSubSetId,
                dataStatus: key.dataSetOpennessStatus,
                family: model.family,
                name: model.name,
                modelId: model.modelId,
                master: model.master || false,
                masterModelName: groupList?.masterModelName || '',
                runSpecSetId: this.getRunSpecSetId(key?.result?.calculateUnitList || []),
              },
            };
            obj1.data.push(row);
          }
        });
      });
      this.series.push(obj);
      if (valueList[0]?.unitList.length === 2 && this.isMean === false) {
        this.series.push(obj1);
      }
    });
    this.filteredSeries = cloneDeep(this.series);
    this.handleSortOrder(this.series, this.valueList);
  }

  handleSortOrder(series, valueList) {
    if (this.columnSortOrderData.order) {
      const evalModelList = this.PARAMS?.tableDataDataSubSet?.evalModelList || [];
      const { rowKey, order } = this.columnSortOrderData;

      const sortRow = evalModelList.find((_, dataIndex) => `row_${dataIndex}` === rowKey);
      // let label = `${sortRow?.llmModel.modelInfo?.modelMeta.modelName}/${sortRow?.llmModel.modelInfo?.modelCheckpoint}/${sortRow?.llmModel.label}`;
      let label = `${sortRow?.llmModel.label}`;
      let sortedRowIndex = [];

      this.series.some((item) => {
        if (item.name === label) {
          sortedRowIndex = item.data
            .map((item, idx) => ({ value: item?.value || 0, idx }))
            .sort((a, b) => {
              const compareResult = Number(a.value) - Number(b.value);
              return order === 'descending' ? -compareResult : compareResult;
            })
            .map((item) => item.idx);
          return true; // 找到匹配的项，停止迭代
        }
        return false; // 继续迭代
      });

      this.series.forEach((seriesItem) => {
        const currentData = seriesItem.data;
        const sortedData = sortedRowIndex.map((index) => currentData[index]);
        seriesItem.data = sortedData;
      });
      const sortedValueList = sortedRowIndex.map((index) => this.valueList[index]);
      this.valueList = cloneDeep(sortedValueList);

      this.setOption(this.series, this.valueList);
    } else {
      this.setOption(this.series, this.valueList);
    }
  }

  setOption(series, valueList) {
    const maxPercent = (50 / valueList.length) * 100;
    this.option = {
      title: this.title || this.PARAMS.title || '',
      grid: {
        left: 30, // 左侧边距
        right: 20, // 右侧边距
        bottom: 480,
      },
      toolbox: { show: true, feature: { saveAsImage: {} } },
      legend: {
        type: this.PARAMS.legendScroll ? 'scroll' : 'plain',
        selector: ['all', 'inverse'],
        padding: [5, 5, 5, 50],
        left: 0,
        width: '95%',
        bottom: 60,
        selected: this.columnsFilter.reduce((obj, item, index) => {
          obj[item.label] = index < (this.lineCount as number);
          return obj;
        }, {}),
      },
      dataZoom: [
        { type: 'inside', start: 0, end: maxPercent, left: 'center', right: 'auto' },
        { start: 0, end: maxPercent, left: 'center', right: 'auto' },
      ],
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: valueList.map((item) => item.name),
        triggerEvent: true,
        axisLabel: {
          // interval: 0,
          minInterval: 1, // 设置最小间隔
          fontSize: 12, // 设置字体大小
          rotate: 90,
          fontWeight: 'bold',
          color: '#555',
          overflow: 'truncate',
          width: 350,
          rich: {
            clickable: {
              width: 50,
              align: 'center',
              verticalAlign: 'middle',
              cursor: 'pointer',
            },
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}',
        },
      },
      series: series,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params) {
          // 初始化结果字符串，包含轴标签信息
          let result = `<div><strong>${params[0].axisValueLabel}</strong></div>`;

          // 遍历每一个数据项
          params.forEach((item) => {
            // 取出数据项中的相关信息
            const dataStatus = item.data.commonQuery.dataStatus || ''; // 获取dataStatus，若没有则为空字符串
            const value = item.data?.value || 0; // 获取值
            const seriesName = item.data.commonQuery.label; // 获取系列名称
            const marker = item.marker; // 获取图标标记

            // 拼接并生成数据项的HTML字符串
            result += `<div>${marker}${seriesName} ${
              dataStatus ? `(${dataStatus})` : ''
            } <span style="float:right;margin-left: 30px">${value}</span></div>`;
          });

          // 返回生成的HTML字符串作为tooltip内容
          return result;
        },
      },
    };
    this.$nextTick(() => {
      const chartInstance = this.$chart.chart;
      if (chartInstance) {
        chartInstance.setOption(this.option, true);
        if (this.PARAMS.isXAxisClick) {
          chartInstance.off('click');
          chartInstance.on('click', this.handleValueClick);
        }
      }
    });
  }

  async handleValueClick(e) {
    const firstIndex = this.filterGroup.selectGroup.firstIndex;
    let statName: string = '';
    let evalDataSize: string = '';
    let category: any = {};
    let runSpecSetId: any = null;

    const params = this.PARAMS.params;

    if (params.radio === '0' && params.filterGroupConfigList?.length) {
      statName = params.filterGroupConfigList[firstIndex]?.statName;
      runSpecSetId = params.filterGroupConfigList[firstIndex]?.runSpecSetId;
      if (params.filterGroupConfigList[firstIndex]?.instanceFilterConditionTree.leaves?.length >= 2) {
        const leaves = params.filterGroupConfigList[firstIndex]?.instanceFilterConditionTree.leaves[1];

        category.categoryId = leaves.rightParam.data;

        const res = await this.action$modelEvalCategoryGet({
          id: Number(category.categoryId),
          statusFilter: '',
        });
        category.categoryName = res.category.name;
        category.versionID = res.category.metaVersionId;
        category.categoryType = res.category.type;
      }
    }
    if (params.radio === '1') {
      statName = params.dataSubSetFilterConfig.statName;
      if (params.dataSubSetFilterConfig.instanceFilterConditionTree.leaves?.length >= 2) {
        const leaves = params.dataSubSetFilterConfig.instanceFilterConditionTree.leaves[1];
        category.categoryId = leaves.rightParam.data;
        const res = await this.action$modelEvalCategoryGet({
          id: Number(category.categoryId),
          statusFilter: '',
        });
        category.categoryName = res.category.name;
        category.versionID = res.category.metaVersionId;
        category.categoryType = res.category.type;
      }
    }
    if (e.componentType === 'series') {
      const seriesName = e.seriesName;
      const res = this.option.series.find((model) => {
        return model.name === seriesName;
      });
      const diffModel = this.option.series.filter((item) => {
        return item.name !== seriesName;
      });
      const diffModelArr: any = [];
      diffModel.forEach((model) => {
        const commonQuery = model.data[0].commonQuery;
        const obj = {
          family: commonQuery.family,
          name: commonQuery.master && commonQuery.masterModelName ? commonQuery.masterModelName : commonQuery.name,
          modelId: commonQuery.modelId,
          master: commonQuery.master,
          label: commonQuery.label,
        };
        diffModelArr.push(obj);
      });

      const uniqueDiffModelArr = this.uniqueArray(diffModelArr);

      const evalModel = {
        family: res.data[0].commonQuery.family,
        label: res.data[0].commonQuery.label,
        name:
          res.data[0].commonQuery.master && res.data[0].commonQuery.masterModelName
            ? res.data[0].commonQuery.masterModelName
            : res.data[0].commonQuery.name,
        modelId: res.data[0].commonQuery.modelId,
        master: res.data[0].commonQuery.master,
      };
      console.log('点点', evalModel, res.data);

      const query = {
        model: [evalModel],
        compareModelList: uniqueDiffModelArr.slice(0, 4),
        dataSubSetId: e.data.commonQuery.dataSubSetId,
        evalDataSize: res.data[0].commonQuery.evalDataSize,
        statName: statName,
        category: JSON.stringify(category),
        runSpecSetId: res.data[0].commonQuery.runSpecSetId ? res.data[0].commonQuery.runSpecSetId : runSpecSetId,
      };

      const encodedQuery = encodeURIComponent(JSON.stringify(query));
      window.open(
        this.$router.resolve({
          name: 'data-sub-set-evaluation-result-detail-new',
          query: {
            modelColumnLineChart: encodedQuery,
          },
        }).href,
        '_blank',
        'noopener'
      );
    }
    if (e.componentType == 'xAxis') {
      const xAxisIndex = e.dataIndex;
      const dataFromSeries = this.option.series.map((serie) => ({
        commonQuery: serie.data[xAxisIndex].commonQuery,
      }));
      const uniqueData = this.uniqueDataFromSeries(dataFromSeries);
      uniqueData.forEach(({ commonQuery }) => {
        window.open(
          this.$router.resolve({
            name: 'data-sub-set-evaluation-result-detail-new',
            query: {
              modelFamily: commonQuery.family,
              modelName: commonQuery.master ? commonQuery.masterModelName : commonQuery.name,
              evalDataSize: commonQuery.evalDataSize,
              dataSubSetId: commonQuery.dataSubSetId,
              modelId: commonQuery.modelId,
              master: commonQuery.master,
              label: commonQuery.label,
              statName: statName,
              //关于评测项的参数
              category: JSON.stringify(category),
              runSpecSetId: commonQuery.runSpecSetId ? commonQuery.runSpecSetId : runSpecSetId,
            },
            params: {
              metaVersionId: '0',
            },
          }).href,
          '_blank',
          'noopener'
        );
      });
    }
  }

  uniqueArray(arr) {
    const map = new Map();
    (arr || []).forEach((item) => {
      if (!map.has(item.modelId)) {
        map.set(item.modelId, item);
      }
    });
    return Array.from(map.values());
  }

  uniqueDataFromSeries(dataFromSeries) {
    if (!dataFromSeries || dataFromSeries.length === 0) {
      return [];
    }

    const map = new Map();

    dataFromSeries.forEach((item) => {
      const modelId = item.commonQuery?.modelId;

      if (modelId && !map.has(modelId)) {
        map.set(modelId, item);
      }
    });

    return Array.from(map.values());
  }
}
</script>
<style scoped lang="less">
.showFilter {
  text-align: left;
  display: flex;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
}
.filterOptionContent-name {
  margin-left: 20px;
  margin-right: 10px;
}
</style>
